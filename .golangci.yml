version: "2"
run:
  modules-download-mode: readonly
  tests: true
linters:
  default: none
  enable:
    - errcheck
    - errname
    - errorlint
    - exhaustive
    - goconst
    - govet
    - ineffassign
    - misspell
    - predeclared
    - revive
    - staticcheck
    - unconvert
    - unused
    - whitespace
  settings:
    errcheck:
      check-type-assertions: true
    goconst:
      min-len: 2
      min-occurrences: 4
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - staticcheck
        text: 'SA1019:'
    paths:
      - third_party$
      - builtin$
      - examples$
issues:
  new-from-rev: origin/master
  new: true
  fix: false
formatters:
  enable:
    - goimports
  settings:
    goimports:
      local-prefixes:
        - cloud.tencent.com/lb-controller git.woa.com/kateway/pkg git.woa.com/kateway/tke-ingress-controller
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
