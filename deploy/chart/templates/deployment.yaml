apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-controller
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      qcloud-app: service-controller
  template:
    metadata:
      labels:
        qcloud-app: service-controller
    spec:
      containers:
      - args:
        - -v=2
        - --region={{ .Values.region }}
        - --vpcid={{ .Values.vpc }}
        - --project-id={{ .Values.project }}
        - --clusterName={{ .Values.clusterName }}
        - --owner-uin={{ .Values.uin }}
        - --secret-id={{ .Values.secretID }}
        - --secret-key={{ .Values.secretKey}}
        - --disable-admit
        - --cluster-support-direct
        - --enable-ingress-controller-default=true
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: Always
        name: service-controller
        resources:
          {{- toYaml .Values.resources | nindent 10 }}
      restartPolicy: Always
      tolerations:
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
      serviceAccount: lb-service
