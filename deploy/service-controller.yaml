apiVersion: app/v1
kind: Deployment
metadata:
  labels:
    qcloud-app: service-controller
  name: service-controller
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      qcloud-app: service-controller
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        qcloud-app: service-controller
    spec:
      containers:
        - args:
            - -c
            - source /etc/kubernetes/service-controller && /service-controller ${LISTENER_QUOTA}
              ${CLUSTERNAME} ${MASTER} ${BACKEND_QUOTA}
          command:
            - bash
          env:
            - name: PATH
              value: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
            - name: VPC_CIDR
              value: 10.0.0.0/8 **********/12 ***********/16
            - name: CLUSTER_ID
              value: cls-rn9u2x4i
            - name: APPID
              value: "**********"
          image: ccr.ccs.tencentyun.com/paas/service-controller:2a746c68
          imagePullPolicy: IfNotPresent
          livenessProbe:
            exec:
              command:
                - bash
                - -c
                - curl --connect-timeout 1 -s  http://kube-apiserver:60001/healthz | grep
                  ok || exit 1
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: service-controller
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 250m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /root/.kube/
              name: clientconfig
            - mountPath: /etc/kubernetes/
              name: config
            - mountPath: /etc/localtime
              name: tz-config
        - command:
            - /sshuttle.sh
          env:
            - name: PATH
              value: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
            - name: VPC_CIDR
              value: 10.0.0.0/8 **********/12 ***********/16
            - name: CLUSTER_ID
              value: cls-rn9u2x4i
            - name: APPID
              value: "**********"
          image: ccr.ccs.tencentyun.com/ccs-dev/sshc:latest
          imagePullPolicy: Always
          name: sshc
          resources: {}
          securityContext:
            privileged: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      hostAliases:
        - hostnames:
            - cbs.api.qcloud.com
            - cvm.api.qcloud.com
            - lb.api.qcloud.com
            - tag.api.qcloud.com
            - snapshot.api.qcloud.com
            - monitor.api.qcloud.com
            - scaling.api.qcloud.com
            - ccs.api.qcloud.com
          ip: ************
        - hostnames:
            - cbs.tencentcloudapi.com
          ip: ************
      initContainers:
        - args:
            - -c
            - NET=$(ip addr |grep -e eth0 | grep inet| awk '{print $2}') && GW=$(ipcalc
              $NET | grep HostMin | awk '{print $2}') && ip route | grep '***********/9'
              || ip route add ***********/9 dev eth0 via $GW
          command:
            - sh
          env:
            - name: PATH
              value: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
          image: ccr.ccs.tencentyun.com/ccs-dev/sshc:latest
          imagePullPolicy: IfNotPresent
          name: make127enable
          resources: {}
          securityContext:
            privileged: true
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: master
      serviceAccountName: master
      terminationGracePeriodSeconds: 30
      volumes:
        - configMap:
            defaultMode: 420
            name: config
          name: config
        - configMap:
            defaultMode: 420
            name: clientconfig
          name: clientconfig
        - hostPath:
            path: /etc/localtime
            type: ""
          name: tz-config