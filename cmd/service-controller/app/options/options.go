package options

import (
	"encoding/json"
	"fmt"
	"os"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/options"
	"github.com/spf13/pflag"
	"k8s.io/klog/v2"
)

type Options struct {
	*options.Options

	// 存量老逻辑，不应该再改动 >>>
	CloudConfig     string // 集群安装时生成的配置文件，用来获取Region和VPCID
	EnableClusterIP bool   // eks1.0，是否开启内网clb模拟ClusterIP
	ClusterIPSubnet string // eks1.0，用内网clb模拟ClusterIP的子网段，用于Service的annotation qcloud-loadbalancer-internal-subnetid
	// 存量老逻辑，不应该再改动 <<<

	EnableReadinessGate bool // 是否启用pod添加ReadinessGate能力

	EnableMultiClusterMaster bool // 是否开启多集群master管理模式

	DryRunService                  bool   // 是否开启模拟运行模式
	DryRunIngress                  bool   // 是否开启ingress-controller模拟运行模式
	DryRunLogToStdout              bool   // 是否将模拟运行日志输出到stdout
	DryRunPath                     string // 模拟运行日志输出路径
	DryRunOldVersion               string // 当前运行版本，用于模拟运行时，判断是否需要更新
	EnableIngressControllerDefault bool   // 是否默认启用融合版本的ingress controller
}

func (o *Options) AddFlags(fs *pflag.FlagSet) {
	o.Options.AddFlags(fs)

	fs.StringVar(&o.CloudConfig, "cloud-config", "/etc/kubernetes/qcloud.conf", "cloud config")
	fs.StringVar(&o.ClusterIPSubnet, "clusterip-subnetid", "", `Default value for Service annotation qcloud-loadbalancer-internal-subnetid`)
	fs.BoolVar(&o.EnableClusterIP, "enable-clusterip", false, `Enable clusterIP by allocating internal CLB. Depend on kube-apiserver and webhook(or valid --clusterip-subnetid)!!!`)

	fs.BoolVar(&o.EnableReadinessGate, "enable-readiness-gate", true, `Enable readiness gate to check the pod is ready on loadbalancer.`)

	fs.BoolVar(&o.EnableMultiClusterMaster, "enable-multi-cluster-master", false, "enable multi cluster service manager.")

	fs.BoolVar(&o.DryRunService, "mock-run", false, "Launch the service controller in dry-run mode and print out all write operations.")
	fs.BoolVar(&o.DryRunIngress, "dry-run-ingress", false, "Launch the ingress controller in dry-run mode and print out all write operations.")
	fs.BoolVar(&o.DryRunLogToStdout, "mock-log-to-stdout", false, "Send dry run log to stdout.")
	fs.StringVar(&o.DryRunPath, "mock-path", "/data", "The output path of the dry run log.")
	fs.StringVar(&o.DryRunOldVersion, "mock-old-version", "", "current running version of service controller")
	fs.BoolVar(&o.EnableIngressControllerDefault, "enable-ingress-controller-default", false, "whether to enable ingress controller by default")
}

func New() *Options {
	return &Options{
		Options: options.New("service-controller", 10260),
	}
}

func (o *Options) Complete() error {
	err := o.Options.Complete()
	if err != nil {
		return err
	}

	// 如果没有通过选项指定region或VPCID，同时指定了云配置，则从云配置中获取Region和VPCID
	if (o.VPCID == "" || o.Region == "") && o.CloudConfig != "" {
		tkeConfig, err := GetTkeConfig(o.CloudConfig)
		if err != nil {
			return err
		}

		if o.VPCID == "" {
			if tkeConfig.VpcID == "" {
				return fmt.Errorf("vpcid is empty")
			}
			o.VPCID = tkeConfig.VpcID
		}
		if o.Region == "" {
			if tkeConfig.RegionName == "" {
				return fmt.Errorf("RegionName is empty")
			}
			o.Region = tkeConfig.RegionName
		}
	}

	features := map[featuregates.Feature]bool{}
	for _, f := range featuregates.KnownFeatures {
		features[f] = o.FeatureGates.Enabled(f)
	}
	features[featuregates.DryRunService] = o.DryRunService
	features[featuregates.DryRunIngress] = o.DryRunIngress
	features[featuregates.MultiClusterService] = o.EnableMultiClusterMaster
	features[featuregates.MultiClusterIngress] = o.EnableMultiClusterIngress
	features[featuregates.ClusterIPSimulation] = o.EnableClusterIP
	o.FeatureGates = featuregates.NewFeatureGates(features)

	return nil
}

func (o *Options) Validate() (errs []error) {
	errs = o.Options.Validate()

	return
}

type TkeConfig struct {
	Region     string `json:"region"`
	RegionName string `json:"regionName"`
	Zone       string `json:"zone"`
	VpcID      string `json:"vpcId"`
}

func GetTkeConfig(cloudConfig string) (*TkeConfig, error) {
	byteValue, err := os.ReadFile(cloudConfig)
	if err != nil {
		return nil, err
	}
	var config TkeConfig
	err = json.Unmarshal(byteValue, &config)
	if err != nil {
		return nil, err
	}
	klog.Infof("config:%v", config)
	return &config, nil
}
