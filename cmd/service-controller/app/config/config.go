package config

import (
	"fmt"
	"sync/atomic"
	"time"

	gclone "github.com/huandu/go-clone/generic"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/norm"
	"github.com/davecgh/go-spew/spew"
	"github.com/opentracing/opentracing-go"
	"github.com/segmentio/ksuid"
	jaegerconfig "github.com/uber/jaeger-client-go/config"
	"k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/options"
)

var Global *Config

func Init(opts *options.Options) error {
	Global = &Config{
		Options: opts,
		ID:      ksuid.New().String(),
	}

	Global.backendQuota.Store(NewBackendQuota(100, opts.BackendQuota))

	Global.JaegerConfig = jaeger.Config{
		Enable: opts.EnableTracing,
		Web:    "https://zhiyan.woa.com/apm_monitor",
		ZhiYan: &jaeger.<PERSON>hi<PERSON>an{
			ID:        Global.ID,
			Env:       "prod",
			ProjectID: "14315",
		},
		Configuration: jaegerconfig.Configuration{
			ServiceName: "service-controller",
			Reporter: &jaegerconfig.ReporterConfig{
				QueueSize:           1000,
				BufferFlushInterval: 1,
				LogSpans:            false,
				LocalAgentHostPort:  "trace.zhiyan.tencent-cloud.net:6831",
			},
			Tags: []opentracing.Tag{
				{
					Key:   "tps.tenant.id",
					Value: "4138#apm-log-bfd69ec5fdc2gab2#14315_109284___apm",
				},
			},
		},
	}

	Global.Norm = norm.Config{
		URL: "http://************:80/norm/api",
	}
	norm.Init(Global.Norm)
	err := Global.setOwnerUin()
	if err != nil {
		return err
	}

	klog.V(4).Infof("service-controller config: %s", spew.Sdump(Global))

	return nil
}

type BackendQuota struct {
	LBUserQuota     int
	OptionQuota     int
	LBInstanceQuota map[string]int
}

func NewBackendQuota(LBUserQuota int, OptionQuota int) *BackendQuota {
	return &BackendQuota{
		LBUserQuota:     LBUserQuota,
		OptionQuota:     OptionQuota,
		LBInstanceQuota: make(map[string]int),
	}
}

func (q *BackendQuota) GetBackendQuota(lbID string, isDirectAccess bool) int {
	if instanceQuota, exist := q.LBInstanceQuota[lbID]; exist {
		return instanceQuota
	}
	if isDirectAccess {
		return q.LBUserQuota
	}
	return min(q.OptionQuota, q.LBUserQuota)
}

type Config struct {
	*options.Options
	ID string
	// kateway: 实际上起作用的quota, options的设置和clb平台的设置，二者取最小值
	// BackendQuotaInUse["default"]用户指定的quota,其它key为clb实例级别的；优先级实例维度>账号维度>默认全局维度
	// kateway: CLB 那边的 quota 配置: https://cloud.tencent.com/document/api/214/30694#Quota 一条转发规则下可绑定设备的配额
	backendQuota atomic.Value

	// 合法的Region列表
	ValidRegion map[string]bool

	// Webhook Status
	// 0: not open
	// 1: open
	// -1: controller close the readiness gate
	WebhookStatus int

	JaegerConfig jaeger.Config

	Norm norm.Config
}

func (c *Config) setOwnerUin() error {
	if c.OwnerUin != 0 {
		return nil
	}

	var (
		err error
		uin *int64
	)
	for i := 0; i < 3; i++ {
		uin, err = norm.GetClusterUIN()
		if err == nil {
			c.OwnerUin = *uin
			return nil
		}
		time.Sleep(time.Second * 5)
	}

	return fmt.Errorf("get cluster's uin error: %w", err)
}

// 获取全局单例Config的资源限额：
// 当service或ingress处于直连模式，忽略启动参数backend-quota的影响，优先级 1.clb实例的配额值, 2.clb默认账户配额值
// 当service或ingress处于非直连模式，考虑启动参数backend-quota的影响，优先级 1.clb实例的配额值， 2.min(clb默认账户配额值， 启动参数backend-quota值)
func (c *Config) GetBackendQuota(lbID string, isDirectAccess bool) int {
	backendQuota := c.backendQuota.Load().(*BackendQuota)
	return backendQuota.GetBackendQuota(lbID, isDirectAccess)
}

func (c *Config) GetListenerQuota() int {
	return c.ListenerQuota
}

func (c *Config) GetBackendQuotaObject() *BackendQuota {
	backendQuota := c.backendQuota.Load().(*BackendQuota)
	return gclone.Clone(backendQuota)
}

func (c *Config) UpdateBackendQuota(backendQuota *BackendQuota) error {
	if backendQuota == nil {
		return fmt.Errorf("BackendQuota is nil")
	}
	quota := gclone.Clone(backendQuota)
	// 当启动参数 Option 中 backend-quota 值为0时，忽略启动参数
	if quota.OptionQuota == 0 {
		quota.OptionQuota = quota.LBUserQuota
	}
	c.backendQuota.Store(quota)
	return nil
}
