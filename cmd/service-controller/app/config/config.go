package config

import (
	"fmt"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/opentracing/opentracing-go"
	"github.com/segmentio/ksuid"
	jaegerconfig "github.com/uber/jaeger-client-go/config"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/norm"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/options"
)

var Global *Config

func Init(opts *options.Options) error {
	Global = &Config{
		Options: opts,
		ID:      ksuid.New().String(),
	}

	Global.BackendQuotaInUse = NewBackendQuota(opts.BackendQuota)
	Global.BackendQuotaInClb = NewBackendQuota(100)

	Global.JaegerConfig = jaeger.Config{
		Enable: opts.EnableTracing,
		Web:    "https://zhiyan.woa.com/apm_monitor",
		ZhiYan: &jaeger.<PERSON>hi<PERSON>an{
			ID:        Global.ID,
			Env:       "prod",
			ProjectID: "14315",
		},
		Configuration: jaegerconfig.Configuration{
			ServiceName: "service-controller",
			Reporter: &jaegerconfig.ReporterConfig{
				QueueSize:           1000,
				BufferFlushInterval: 1,
				LogSpans:            false,
				LocalAgentHostPort:  "trace.zhiyan.tencent-cloud.net:6831",
			},
			Tags: []opentracing.Tag{
				{
					Key:   "tps.tenant.id",
					Value: "4138#apm-log-bfd69ec5fdc2gab2#14315_109284___apm",
				},
			},
		},
	}

	Global.Norm = norm.Config{
		URL: "http://************:80/norm/api",
	}
	norm.Init(Global.Norm)
	err := Global.setOwnerUin()
	if err != nil {
		return err
	}

	klog.V(4).Infof(spew.Sdump(Global))

	return nil
}

type BackendQuota struct {
	UserQuota     int
	InstanceQuota map[string]int
}

func NewBackendQuota(userQuota int) *BackendQuota {
	return &BackendQuota{
		UserQuota:     userQuota,
		InstanceQuota: map[string]int{},
	}
}

func (quota *BackendQuota) GetQuotaInUse(lbId string) int {
	if v, exist := Global.BackendQuotaInUse.InstanceQuota[lbId]; exist {
		return v
	}
	return Global.BackendQuotaInUse.UserQuota
}

type Config struct {
	*options.Options
	ID string
	// kateway: 实际上起作用的quota, options的设置和clb平台的设置，二者取最小值
	// BackendQuotaInUse["default"]用户指定的quota,其它key为clb实例级别的；优先级实例维度>账号维度>默认全局维度
	BackendQuotaInUse *BackendQuota
	// kateway: CLB 那边的 quota 配置: https://cloud.tencent.com/document/api/214/30694#Quota 一条转发规则下可绑定设备的配额
	BackendQuotaInClb *BackendQuota

	// 合法的Region列表
	ValidRegion map[string]bool

	// Webhook Status
	// 0: not open
	// 1: open
	// -1: controller close the readiness gate
	WebhookStatus int

	JaegerConfig jaeger.Config

	Norm norm.Config
}

func (c *Config) setOwnerUin() error {
	if c.OwnerUin != 0 {
		return nil
	}

	var (
		err error
		uin *int64
	)
	for i := 0; i < 3; i++ {
		uin, err = norm.GetClusterUIN()
		if err == nil {
			c.OwnerUin = *uin
			return nil
		}
		time.Sleep(time.Second * 5)
	}

	return fmt.Errorf("get cluster's uin error: %w", err)
}
