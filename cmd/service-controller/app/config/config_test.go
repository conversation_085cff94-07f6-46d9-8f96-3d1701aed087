package config

import (
	"fmt"
	"reflect"
	"testing"
)

func TestBackendQuota_GetBackendQuota(t *testing.T) {
	tests := []struct {
		name           string
		quota          *BackendQuota
		lbID           string
		isDirectAccess bool
		want           int
	}{
		{
			name: "存在实例配额",
			quota: &BackendQuota{
				LBUserQuota: 10,
				OptionQuota: 20,
				LBInstanceQuota: map[string]int{
					"lb1": 5,
				},
			},
			lbID:           "lb1",
			isDirectAccess: false,
			want:           5,
		},
		{
			name: "无实例配额，直连访问模式",
			quota: &BackendQuota{
				LBUserQuota:     10,
				OptionQuota:     20,
				LBInstanceQuota: map[string]int{},
			},
			lbID:           "lb2",
			isDirectAccess: true,
			want:           10,
		},
		{
			name: "无实例配额，非直连访问模式，optionQuota 小于 userQuota，返回 optionQuota",
			quota: &BackendQuota{
				LBUserQuota:     10,
				OptionQuota:     8,
				LBInstanceQuota: map[string]int{},
			},
			lbID:           "lb3",
			isDirectAccess: false,
			want:           8,
		},
		{
			name: "无实例配额，非直连访问模式，userQuota 小于 optionQuota，返回 userQuota",
			quota: &BackendQuota{
				LBUserQuota:     5,
				OptionQuota:     8,
				LBInstanceQuota: map[string]int{},
			},
			lbID:           "lb4",
			isDirectAccess: false,
			want:           5,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.quota.GetBackendQuota(tt.lbID, tt.isDirectAccess)
			if got != tt.want {
				t.Errorf("GetBackendQuota() = %d, want %d", got, tt.want)
			}
		})
	}
}

func TestConfig_GetBackendQuota(t *testing.T) {
	c := &Config{}
	initialQuota := &BackendQuota{
		LBUserQuota: 10,
		OptionQuota: 20,
		LBInstanceQuota: map[string]int{
			"lb1": 7,
		},
	}
	c.backendQuota.Store(initialQuota)

	tests := []struct {
		name           string
		lbID           string
		isDirectAccess bool
		want           int
	}{
		{
			name:           "实例配额命中",
			lbID:           "lb1",
			isDirectAccess: false,
			want:           7,
		},
		{
			name:           "直连访问模式",
			lbID:           "lb2",
			isDirectAccess: true,
			want:           10,
		},
		{
			name:           "非直连访问为假，返回 optionQuota 和 userQuota 的最小值",
			lbID:           "lb2",
			isDirectAccess: false,
			want:           10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := c.GetBackendQuota(tt.lbID, tt.isDirectAccess)
			if got != tt.want {
				t.Errorf("GetBackendQuota() = %d, want %d", got, tt.want)
			}
		})
	}
}

func TestConfig_GetBackendQuotaObj(t *testing.T) {
	tests := []struct {
		name          string
		storedQuota   *BackendQuota
		modifyClone   func(*BackendQuota)
		wantUnchanged *BackendQuota
	}{
		{
			name: "返回的对象与存储对象相等",
			storedQuota: &BackendQuota{
				LBUserQuota: 10,
				OptionQuota: 20,
				LBInstanceQuota: map[string]int{
					"lb1": 7,
				},
			},
			modifyClone: func(clone *BackendQuota) {
				// 不修改
			},
			wantUnchanged: &BackendQuota{
				LBUserQuota: 10,
				OptionQuota: 20,
				LBInstanceQuota: map[string]int{
					"lb1": 7,
				},
			},
		},
		{
			name: "修改返回对象不影响原始对象",
			storedQuota: &BackendQuota{
				LBUserQuota: 10,
				OptionQuota: 20,
				LBInstanceQuota: map[string]int{
					"lb1": 7,
				},
			},
			modifyClone: func(clone *BackendQuota) {
				clone.LBUserQuota = 100
				clone.LBInstanceQuota["lb1"] = 100
			},
			wantUnchanged: &BackendQuota{
				LBUserQuota: 10,
				OptionQuota: 20,
				LBInstanceQuota: map[string]int{
					"lb1": 7,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Config{}
			c.backendQuota.Store(tt.storedQuota)

			got := c.GetBackendQuotaObject()

			if !reflect.DeepEqual(got, tt.storedQuota) {
				t.Errorf("GetBackendQuotaObj() 返回对象与存储对象不相等，got=%+v, want=%+v", got, tt.storedQuota)
			}

			// 修改返回对象
			tt.modifyClone(got)

			// 再次获取原始对象，确保未被修改
			original := c.backendQuota.Load().(*BackendQuota)
			if !reflect.DeepEqual(original, tt.wantUnchanged) {
				t.Errorf("修改返回对象后，原始对象被修改了，original=%+v, want=%+v", original, tt.wantUnchanged)
			}
		})
	}
}

func TestConfig_UpdateBackendQuota(t *testing.T) {
	c := &Config{}

	tests := []struct {
		name          string
		inputQuota    *BackendQuota
		wantOption    int
		wantUserQuota int
		wantErr       error
	}{
		{
			name: "OptionQuota 非零",
			inputQuota: &BackendQuota{
				LBUserQuota: 10,
				OptionQuota: 5,
			},
			wantOption:    5,
			wantUserQuota: 10,
			wantErr:       nil,
		},
		{
			name: "OptionQuota 为零，替换为 LBUserQuota",
			inputQuota: &BackendQuota{
				LBUserQuota: 15,
				OptionQuota: 0,
			},
			wantOption:    15,
			wantUserQuota: 15,
			wantErr:       nil,
		},
		{
			name:          "BackendQuota 为 nil, 更新失败",
			inputQuota:    nil,
			wantOption:    15,
			wantUserQuota: 15,
			wantErr:       fmt.Errorf("BackendQuota is nil"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := c.UpdateBackendQuota(tt.inputQuota)
			if !reflect.DeepEqual(err, tt.wantErr) {
				t.Errorf("UpdateBackendQuota() error = %v, wantErr %v", err, tt.wantErr)
			}
			stored := c.backendQuota.Load().(*BackendQuota)
			if stored.OptionQuota != tt.wantOption {
				t.Errorf("OptionQuota = %d, want %d", stored.OptionQuota, tt.wantOption)
			}
			if stored.LBUserQuota != tt.wantUserQuota {
				t.Errorf("LBUserQuota = %d, want %d", stored.LBUserQuota, tt.wantUserQuota)
			}
		})
	}
}
