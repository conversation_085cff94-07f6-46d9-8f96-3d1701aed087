package app

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"time"

	gclone "github.com/huandu/go-clone/generic"
	"github.com/spf13/pflag"
	"golang.org/x/sync/errgroup"
	apiextensionsClient "k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/component-base/logs"
	"k8s.io/klog/v2"

	crd2 "git.woa.com/kateway/loadbalancer-resource-api/pkg/crd"
	crd3 "git.woa.com/kateway/multi-cluster-service-api/crd"
	"git.woa.com/kateway/pkg/app"
	"git.woa.com/kateway/pkg/domain/event"
	"git.woa.com/kateway/pkg/domain/server"
	clusterservice "git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/webhook"
	"git.woa.com/kateway/pkg/k8s/leaderelection"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/telemetry/log"
	ingcontrollerapp "git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app"
	ingcontrolleropts "git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/options"
	"git.woa.com/kateway/tke-service-config/pkg/crd"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/cmd/service-controller/app/options"
	lbController "cloud.tencent.com/lb-controller/pkg/lb-controller"
	"cloud.tencent.com/lb-controller/pkg/plugin/tencent"
	"cloud.tencent.com/lb-controller/pkg/readinessgate"
	"cloud.tencent.com/lb-controller/pkg/service/cluster"
	"cloud.tencent.com/lb-controller/pkg/service/crossregion"
	"cloud.tencent.com/lb-controller/pkg/service/processing"
	"cloud.tencent.com/lb-controller/pkg/service/track"
)

// kateway(bitliu[1]):
// 0. 初始化基础配置 from flags、configmap、env
// 1. 可观测性：初始化 metrics collector、pprof
// 2. 开启 healthcheck
// 3. 初始化当前地域云 API 服务
// 4. 同步配置到 tke-service-controller-config configmap
// 5. 自动注册 CRD：LBR、TKEServiceConfig、MCS（如果开启 mcs）
// 6. service controller 选主
// 7. 启动 webhook
// 8. 启动 controllers
func run(opts *options.Options) app.RunFunc {
	return func(ctx context.Context) error {
		logs.InitLogs()
		defer logs.FlushLogs()
		logs.AddFlags(pflag.CommandLine)

		err := config.Init(opts)
		if err != nil {
			return err
		}

		jaeger.Init(config.Global.JaegerConfig)
		span, ctx := jaeger.StartSpanFromContext(ctx, jaeger.WithOperationName("run"))
		jaeger.LogSystem(ctx, config.Global.ID)

		server.Init(&server.Config{
			Metrics: server.Metrics{
				IngressClass: opts.IngressClass,
				MetricsPort:  opts.MetricsPort,
			},
			Port: 80,
		})

		event.Init(opts.RESTConfig, opts.EventType, opts.Name)

		processing.InitProcessingService()

		serviceType := cluster.TaskQueue
		if opts.DryRunService {
			serviceType = cluster.TaskTraversal
		}

		err = initCRD(ctx)
		if err != nil {
			return fmt.Errorf("init crd error: %w", err)
		}

		basic, err := clusterservice.NewBasic(&clusterservice.Config{
			Ctx:            ctx,
			Name:           opts.ClusterName,
			Region:         config.Global.Region,
			ControllerName: opts.Name,
			KubeConfig:     opts.RESTConfig,
			FeatureGates:   config.Global.FeatureGates,
		})
		if err != nil {
			return err
		}
		if err = cluster.Init(ctx, basic, serviceType); err != nil {
			return fmt.Errorf("InitClusterServiceInstance error: %w", err)
		}

		err = tencentapi.Init(&tencentapi.Config{
			Admit:    !opts.DisableAdmit,
			ReadOnly: opts.DryRunService,
			Region:   config.Global.Region,
			Config:   opts.AuthConfig(),
		})
		if err != nil {
			return fmt.Errorf("InitTencentAPIService error: %w", err)
		}
		crossregion.InitCrossRegionService()

		var (
			ingController = buildIngressController(basic)
			svcController = lbController.New(opts.ClusterName, opts.Workers)
		)

		// 预检不需要拿锁执行，也不用启动webhook server
		if opts.DryRunService || opts.DryRunIngress {
			basic.Run(ctx.Done())

			wg := errgroup.Group{}
			if opts.DryRunService {
				wg.Go(func() error {
					svcController.Run(ctx)
					return nil
				})
			}
			if opts.DryRunIngress {
				wg.Go(func() error {
					return ingController.Run(ctx)
				})
			}
			return wg.Wait()
		}

		kubeClient := kubernetes.NewForConfigOrDie(opts.RESTConfig)
		go startWebhookServer(ctx, kubeClient)

		track.Init(&track.Config{Dir: "track"})

		basic.ControllerConfigMapSyncFunc = cluster.BuildConfigMapSyncFunc(ingController)
		basic.Run(ctx.Done())
		span.Finish()

		return leaderelection.Run(ctx, opts.RESTConfig, &opts.LeaderElection, func(ctx context.Context) {
			defer jaeger.Close()

			clusterType, err := tencent.GetClusterType(opts.ClusterName)
			if err != nil {
				panic(err)
			}
			go startWebhookComponentsMgr(ctx, kubeClient, opts.ClusterName, clusterType)
			// ingress controller 和 service  controller 必须运行在同一个进程内，因此在 service controller 选主成功后再启动两个控制器
			if err = ingController.Run(ctx); err != nil {
				panic(err)
			}

			if err = svcController.Run(ctx); err != nil {
				panic(err)
			}
		})
	}
}

func buildIngressController(clusterService clusterservice.Interface) *ingcontrollerapp.IngressController {
	opts := ingcontrolleropts.New()
	opts.Options = gclone.Clone(config.Global.Options.Options)
	opts.MergeMode = true
	opts.DryRun = config.Global.DryRunIngress
	opts.LeaderElection.ResourceName = "ingress-controller" // 需要修改锁名称，否则和service-controller一样，会冲突

	return ingcontrollerapp.NewIngressController(clusterService, *opts) // 合并版本
}

func initCRD(ctx context.Context) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	apiextensionsKubeClient := apiextensionsClient.NewForConfigOrDie(config.Global.RESTConfig)
	err := crd.TkeServiceConfigInitTransaction(apiextensionsKubeClient)
	if err != nil {
		return fmt.Errorf("TkeServiceConfigInitTransaction error: %w", err)
	}

	err = crd2.LoadBalancerResourceInitTransaction(apiextensionsKubeClient)
	if err != nil {
		return fmt.Errorf("LoadBalancerResourceInitTransaction error: %w", err)
	}

	if config.Global.EnableMultiClusterMaster { // kateway(bitliu[3]): 如果开启 mcs，按集群版本创建 mcs crd
		err = crd3.MultiClusterServiceInitTransaction(apiextensionsKubeClient)
		if err != nil {
			return fmt.Errorf("MultiClusterServiceInitTransaction error: %w", err)
		}
	}

	return nil
}

func startWebhookComponentsMgr(ctx context.Context, cli *kubernetes.Clientset, clusterName, clusterType string) {
	supported, err := clusterservice.Instance.IsMutatingWebhookSupported()
	if err != nil {
		klog.Errorf("IsMutatingWebhookSupported error: %v", err)
	}
	mgr := webhook.NewManager(cli, clusterName, clusterType, supported)
	ctx = log.WithContext(ctx, log.WithName("webhook-components-mgr"))
	mgr.Start(ctx)
}

func startWebhookServer(ctx context.Context, cli *kubernetes.Clientset) {
	// Webhook存活监控
	go func() {
		tryDial := func() int {
			conf := &tls.Config{
				InsecureSkipVerify: true,
			}
			dialer := &net.Dialer{
				Timeout: 3 * time.Second,
			}
			conn, err := tls.DialWithDialer(dialer, "tcp", "127.0.0.1:17443", conf)
			if err != nil {
				klog.Errorf("tryDial webhook error. %v", err)
				return 0
			}

			defer func(conn net.Conn) {
				err := conn.Close()
				if err != nil {
					klog.Errorf("tryDial webhook close error. %v", err)
				}
			}(conn)
			return 1
		}

		wait.PollInfiniteWithContext(ctx, 60*time.Second, func(_ context.Context) (done bool, err error) {
			config.Global.WebhookStatus = tryDial()
			return
		})
	}()
	// 启动原有的用于Pod ReadniessGate的webhook server
	go readinessgate.NewReadinessGateWebhookServer().StartWebhookServer()

	// 启动集群webhook server
	ctx = log.WithContext(ctx, log.WithName("webhook-server"))
	svr := webhook.NewServer(cli)
	if err := svr.ListenAndServe(ctx); err != nil {
		panic(err)
	}
}
