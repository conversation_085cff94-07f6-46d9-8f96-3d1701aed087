VERSION = v1.0.0
IMAGE_NAME = base
REGISTRY_PREFIX ?= mirrors.tencent.com/kateway

.PHONY: all
all: build push

.PHONY: build
build:
	@echo "===========> Building $(IMAGE_NAME) $(VERSION) docker image"
	docker build --platform=linux/amd64 -t $(REGISTRY_PREFIX)/$(IMAGE_NAME):$(VERSION) --build-arg VERSION=$(VERSION) -f ./Dockerfile .

.PHONY: push
push: build
	@echo "===========> Pushing $(IMAGE_NAME) $(VERSION) image to $(REGISTRY_PREFIX)"
	docker push $(REGISTRY_PREFIX)/$(IMAGE_NAME):$(VERSION)

.PHONY: run
run:
	@echo "===========> Running $(IMAGE_NAME) $(VERSION)"
	docker run --rm -it $(REGISTRY_PREFIX)/$(IMAGE_NAME):$(VERSION)
