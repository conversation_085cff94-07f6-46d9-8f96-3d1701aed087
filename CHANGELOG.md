# [v2.6.0](https://git.woa.com/kateway/service-controller/compare/v2.5.3...v2.6.0) (2025-05-19)

* 发布范围: 增量发布
* 发布负责人: qingyangwu
* 镜像哈希：
  * (2025-05-19 初始发布) sha256:4e522541ab4918d624e1c2f6edb276d4d21b7a56d10c4a6d96723c4209b807d9
  * (2025-05-26 修复CVM类型原生节点绑定问题、修复删除保护webhook拦截问题) sha256:01f632fec2cbab81f796e806df7bbd37bf08f8d5ef824b00caed8b7f1565c8a7
  * (2025-05-26 修复terminating状态service/ingress反复入队) sha256:ecf34276bc327a88e41f48608719154271ddc764fd1476c249f29971123ba7b5

### Bug Fixes

* 修复 terminating 状态 service/ingress 反复入队 ([56fa320](https://git.woa.com/kateway/service-controller/commits/56fa320ec6136cb816516c4ecf439fd3035acf44))
* 修复 ingress 在关联无 ep service 时的报错信息 ([f0cf4b2](https://git.woa.com/kateway/service-controller/commits/f0cf4b26db2beb4cba8d0c8dc8a598c76c685e98))
* 修复 service selector 变化事件不入队的问题 ([ecf32a6](https://git.woa.com/kateway/service-controller/commits/ecf32a619152d022f44ed78e3096f55008aecd50))
* 修复删除错误没有记录到service状态 ([34dc5e5](https://git.woa.com/kateway/service-controller/commits/34dc5e53c7f471feea2a49d45842184930c4029e))
* 修复新建mcs后无法更新状态信息 ([28c7dc2](https://git.woa.com/kateway/service-controller/commits/28c7dc2919a92375f46dfd7aa71cc88b09b34751))
* 修复选项没有在补全后在校验导致失败 ([ae4794d](https://git.woa.com/kateway/service-controller/commits/ae4794d3705404dac7e5bb93346c0c19003fa31f))
* **ingress controller:** 证书长度不合法错误暴露到ingress ([757bce0](https://git.woa.com/kateway/service-controller/commits/757bce070792e4e44a994766fef9d75a30f7a48a))
* **ingress controller:** service写入注解失败报错需要向上返回 ([806325b](https://git.woa.com/kateway/service-controller/commits/806325b9951628a64128bb155952b54be76ed5ba))


### Features

* 融合版ingress支持pod自定义权重 ([5219a92](https://git.woa.com/kateway/service-controller/commits/5219a9282f60e6c372700a843d3bf6f59f61df6e))
* 识别Register以及Deregister targets接口以防panic ([45ef566](https://git.woa.com/kateway/service-controller/commits/45ef566d2dae1aa2148114b43287da34f1abe060))
* 修复dryrun时没有兼容BatchModify类接口导致panic ([4a39061](https://git.woa.com/kateway/service-controller/commits/4a39061ce80ab5ac143d99648ae72abffd439066))
* 支持非tke集群类型 ([08df30c](https://git.woa.com/kateway/service-controller/commits/08df30cba5a223e5a17e352e0d56b7e5f16d6ed0))
* 支持关闭tke审计删除保护 ([a91a65a](https://git.woa.com/kateway/service-controller/commits/a91a65aa77ca8dae17dbb0f96019d94a4ad285f6))
* 支持强制删除 CLB 实例 ([a46aee2](https://git.woa.com/kateway/service-controller/commits/a46aee24f98b3d2c8a093f949bb7594d5a3b5fe7))
* 支持删除保护开关 ([0d8bbf6](https://git.woa.com/kateway/service-controller/commits/0d8bbf62597b8ef1d572ff23fac0445b41797953))
* 支持跳过 RS 调整权重 ([ad01548](https://git.woa.com/kateway/service-controller/commits/ad01548dabda7e337176a9c8fe690038bc9d68e8))
* 支持显式配置集群是否支持直连 ([f8f0496](https://git.woa.com/kateway/service-controller/commits/f8f04964310b1399a809f0b22f06527ea6b4e6c6))
* 支持选项指定项目id ([cda4244](https://git.woa.com/kateway/service-controller/commits/cda42447e98a90b0615c682427427cf3e0d863f4))
* 支持选项指定region和vpc信息,避免强依赖qcloud.conf文件 ([8bdcac9](https://git.woa.com/kateway/service-controller/commits/8bdcac9ae265cc2ae8e9471f24a62307445678c4))
* 支持自动化输出各种格式annotation信息 ([91f0b35](https://git.woa.com/kateway/service-controller/commits/91f0b35914454663b575f70a3cd43ef7f20c621c))
* 支持clb类型输出到service/ingress label上以支持前端过滤 ([c4f7473](https://git.woa.com/kateway/service-controller/commits/c4f7473fa42291cb193b3f15101baec5ab7be0eb))
* 支持helm部署 ([8833f92](https://git.woa.com/kateway/service-controller/commits/8833f92391308cb3c6cb2e1a8bd39c3cab442324))
* ingress支持可用区级别自定义权重 ([fd95f8a](https://git.woa.com/kateway/service-controller/commits/fd95f8a8e216be6605692f70b02738a3acac1818))
* **service controller:** 控制面apiserver跨租户服务接入 ([fc24376](https://git.woa.com/kateway/service-controller/commits/fc2437686769c39414311d590c50cb00c2e0cf97))



# [v2.5.3](https://git.woa.com/kateway/service-controller/compare/v2.5.2...v2.5.3) (2025-04-15)

* 发布范围: 存量按需更新
* 发布负责人: lwbowenyan 

# [v2.5.2](https://git.woa.com/kateway/service-controller/compare/v2.5.1...v2.5.2) (2025-02-28)

* 发布范围: 增量发布
* 发布负责人: qingyangwu
* 镜像哈希：
  * (2025-02-28 初始发布)sha256:407cbd58dcda83ee2aed9b99f5b485c6670fb787c4fc0ce4fd8216d3693337c7
  * (2025-03-26 修复融合版本健康检查失败问题)sha256:374dc54a40e44e8f4398701aa0184d3a810669c7e3ef2ed87d12973a227ed45f
  * (2025-04-14 修复创建监听器 tsc 配置失效问题)sha256:a1ae635e817cac97e71657b21c5ed1fae8524ef20973d4961c9aaca6b9396cac

### Bug Fixes

* 修复 service selector 变化事件不入队的问题 ([6449c45](https://git.woa.com/kateway/service-controller/commits/6449c458c85b870c817b8e81d8a4c5ca4b16ba1a))
* 修复创建监听器 tsc 配置失效问题 ([e884e18](https://git.woa.com/kateway/service-controller/commits/e884e18d658bef5e4c183134ce80dd59ef9f64d1))
* 修复融合版本健康检查失败问题 ([0472652](https://git.woa.com/kateway/service-controller/commits/04726529c3b8c7e88c2b4542deb696d34480f5df))
* 修复mcs重复对账问题 ([4d21d83](https://git.woa.com/kateway/service-controller/commits/4d21d83757b997307bc6bd9f7bc98e69e7c33d3e))
* 修复更新service status.conditions异常 ([4d21d83](https://git.woa.com/kateway/service-controller/commits/4d21d83757b997307bc6bd9f7bc98e69e7c33d3e))
* 修复service reuse场景下没立即重试问题 ([4d21d83](https://git.woa.com/kateway/service-controller/commits/4d21d83757b997307bc6bd9f7bc98e69e7c33d3e))
* 修复创建service对账过程中概率性覆盖status.Ingress ([02b0037](https://git.woa.com/kateway/service-controller/commits/02b00376fc7122a853089a04f006a0ae86341ce8))

# [v2.5.1](https://git.woa.com/kateway/service-controller/compare/v2.5.0...v2.5.1) (2025-02-12)

* 发布范围: 增量发布
* 发布负责人: qingyangwu

### Bug Fixes

* 服务端证书切片未排序，导致 Ingress 对账不一致 ([8630971](https://git.woa.com/kateway/service-controller/commits/8630971472f92d9b8c4d1a8842a659def0eab6fd))
* 解决 Deleted LoadBalancer Event 误告问题 ([6fa2ba7](https://git.woa.com/kateway/service-controller/commits/6fa2ba7c80d4be07504992467957cf6da8ee27e8))
* 扩展协议校验失败无法终止调谐 ([ca9bd55](https://git.woa.com/kateway/service-controller/commits/ca9bd5585a30fcdc47e0d46de2e83662335e62cb))
* 新增 IPDC 类型 Target 避免 panic ([bb8ad17](https://git.woa.com/kateway/service-controller/commits/bb8ad178b7c2f1d67cc57a52c124824263c116b6))
* 修复非qcloud类型ingress造成pod健康检查失败问题 ([91213ca](https://git.woa.com/kateway/service-controller/commits/91213ca8fafb24bf047340c3a0d00d57d2910284))
* 修复service新增泛域名转发规则健康检查失效问题 ([9277564](https://git.woa.com/kateway/service-controller/commits/9277564400f6c8dae75949f58d54424ef14524c7))
* 修复TCP类型健康检查HTTP字段引发校验错误问题 ([cd5e89f](https://git.woa.com/kateway/service-controller/commits/cd5e89fc9e4d940c0719134c4298ee3f3a6b8a9b))
* 修复TKE环境下service的会话保持配置无效的问题 ([4fddf05](https://git.woa.com/kateway/service-controller/commits/4fddf05c4014c96979a191feaf684110cc4c3b43))


### Features

* 忽略复用启动参数并默认开启复用 ([06be61d](https://git.woa.com/kateway/service-controller/commits/06be61d7e8753dd3ee1e7785ceac52d083585fff))
* 在直连健康检查中兼容融合版本ingress ([9cc5734](https://git.woa.com/kateway/service-controller/commits/9cc5734000ca69c98924820e8f555ffeeb63f873))
* 增加.code.yaml以屏蔽错误识别的扫描风险 ([c82880d](https://git.woa.com/kateway/service-controller/commits/c82880dd8bb22ffbccf94b408c5ce725fe46644b))
* 支持对指定service进行dryrun ([ded1490](https://git.woa.com/kateway/service-controller/commits/ded149085ae497d130ce080abd8d5358e0cf8517))
* 支持工作队列可观测 ([041d41a](https://git.woa.com/kateway/service-controller/commits/041d41a584a6d546390555fdc28c836616ffbab1))
* 指标融合 ([f61581b](https://git.woa.com/kateway/service-controller/commits/f61581b6c5940536cefc18213dc206a40b756c6a))
* pod rs 优雅注册，等待pod里的容器都ready后再注册rs，避免出现clb rs 不健康告警。 ([968cd9f](https://git.woa.com/kateway/service-controller/commits/968cd9fbe8f00c077830e4eba8ce4e9f281b2e44))



# [v2.5.0](https://git.woa.com/kateway/service-controller/compare/v2.4.3...v2.5.0) (2024-11-26)

* 发布范围: 增量发布
* 发布负责人: qingyangwu
* 镜像哈希: sha256:b8afc0459a4e1736137c19a4a3877dbafca2462b53112fb73ed2c673318bd54a

### Bug Fixes

* 优化 RS patch 标签逻辑 ([999420f](https://git.woa.com/kateway/service-controller/commits/999420f5e838b344f6bbf08cbee72ad100114a92))
* 修复在ingress使用自定义端口场景下Pod健康检查不通过的问题 ([1c86700](https://git.woa.com/kateway/service-controller/commits/1c867007436f41dbac8854d7838707b8546c740b))
* 修复解析节点providerID异常 ([ffc935c](https://git.woa.com/kateway/service-controller/commits/ffc935c5e4bfa8577c0243f932e0c3e39a5d7708))
* 解决node优雅下线node长时间无法删除的问题 ([fc5f616](https://git.woa.com/kateway/service-controller/commits/fc5f616c1e604efe4d425648e97883cfc9b6159d))
* 修复 '删除特殊协议注解，七层监听器无法会退到四层监听器' 的问题（[a54a7e7b](https://git.woa.com/kateway/service-controller/commit/a54a7e7b7557c5ec46f3d9ce01ca86c93bb63d3b)）
* readiness gate 检查时，忽略 lb 不存在的情况 ([92d9ae6](https://git.woa.com/kateway/service-controller/commits/92d9ae69f6111826bffef3ea631a32730f55940b))


### Features

* 增加 pvgw-pro 支持 ([7b8b171](https://git.woa.com/kateway/service-controller/commits/7b8b1712fed1948611df333af82f2e5433515379))
* 增加非直连场景 'node单点rs风险' 事件 ([68f82bb](https://git.woa.com/kateway/service-controller/commits/68f82bb2ffa2de862ab8469f26b8d0448a0d558d))
* 支持admin服务 ([242b21c](https://git.woa.com/kateway/service-controller/commits/242b21c74236fad80dd45ff14c2b1e6ccb4c2ac4))
* 支持pod优雅删除 ([18bcfaf](https://git.woa.com/kateway/service-controller/commits/18bcfafd62d9db569ebce872966acd2256b8a7f0))
* 支持健康探测服务 ([5770278](https://git.woa.com/kateway/service-controller/commits/5770278792113c82b9379d0f80d4c440f21facc6))
* 支持批量创建相同配置的监听器 ([c032480](https://git.woa.com/kateway/service-controller/commits/c03248084853166defea2c7d5e2d192ee433bb2f))  
* 接入层组件融合 ([c20a623](https://git.woa.com/kateway/service-controller/commits/c20a6233c3490b5cbe5822e52e14a8332a4f5f55))
* 记录service的错误信息并通过server暴露 ([4ff0f76](https://git.woa.com/kateway/service-controller/commits/4ff0f7614fa61cb6ff89fe108aa4041613b61d07))
* 强制 ingress controller 和 service controller 运行在同一个进程 ([fefefe4](https://git.woa.com/kateway/service-controller/commits/fefefe422b00085eae568b7ece83332054b65085))



# [v2.4.3](https://git.woa.com/kateway/service-controller/compare/v2.4.2...v2.4.3) (2024-10-14)

* 发布范围: caas 存量集群
* 发布负责人: foxzhong

### Bug Fixes

* fix pkg mod version ([cdeebba](https://git.woa.com/kateway/service-controller/commits/cdeebbadc492e0d87e445cc66d773019d19bab54))


### Features

* 支持tapp自定义权重 ([2ba6b36](https://git.woa.com/kateway/service-controller/commits/2ba6b365187887a5a31c03d9913fbc60f86eb25c))
* 支持不依赖norm，直接使用服务角色鉴权 ([6c4e9d6](https://git.woa.com/kateway/service-controller/commits/6c4e9d6c26aeba9bce0bdab0cd745d0610fe0199))



# [v2.4.2](https://git.woa.com/kateway/service-controller/compare/v2.4.1...v2.4.2) (2024-09-20)


### Bug Fixes

* 解决panic捕获以及输出的相关问题 ([5ae0159](https://git.woa.com/kateway/service-controller/commits/5ae0159fd06009b824376bd8206e56405518e655))
* 增加node优雅删除，修复荣耀缩容bug ([6d8571a])(https://git.woa.com/kateway/service-controller/commits/6d8571a0854b838f8a7f07d6406d7855da4aa1c1)


### Features

* support patch rs tags ([a80a42e](https://git.woa.com/kateway/service-controller/commits/a80a42e703d84ef677ad70531fb0189d7015f91c))
* 删除对cluster ip 的更新逻辑 ([724ce79](https://git.woa.com/kateway/service-controller/commits/724ce797688cab767497f4936415282aed2dc1d6))



# [v2.4.1](https://git.woa.com/kateway/service-controller/compare/v2.4.0...v2.4.1) (2024-08-20)


### Features

* mock 加速, 有条件的跳过某些 ClusterIP 类型的 service ([b0d0937](https://git.woa.com/kateway/service-controller/commits/b0d0937ae1e55cadd69690a70edcbac9fca126aa))
* 优化全死全活逻辑 ([c858f65](https://git.woa.com/kateway/service-controller/commits/c858f65264555da7e58eedf8d7ea98427d7ab49e))



# [v2.4.0](https://git.woa.com/kateway/service-controller/compare/v2.3.4...v2.4.0) (2024-07-18)

### Bug Fixes

- 对mock时修改云标签的调用进行过滤 ([9164b76](https://git.woa.com/kateway/service-controller/commits/9164b76c9b190eeff8567e8a664efd1268d30d7b))
- 更新pkg版本,更新webhook校验逻辑 ([8790e7e](https://git.woa.com/kateway/service-controller/commits/8790e7efa794e942d45c3d5ad6d7779ec32c373a))
- 将CRD初始化提前 ([5259fee](https://git.woa.com/kateway/service-controller/commits/5259fee457e7e1e4b46be04ce68e7cc86e3638fb))
- 删除webhook对client-token annotation 的校验 ([9da4382](https://git.woa.com/kateway/service-controller/commits/9da4382a9d56ea3986b904e4be776e50ff2704e0))
- 修复GetCrossVPCId annotation错误 ([72cf71f](https://git.woa.com/kateway/service-controller/commits/72cf71fec03b36c882ae32dd1ee800d5a6aa9323))
- 修复tke-service-controller-config没有自动创建的问题 ([d499695](https://git.woa.com/kateway/service-controller/commits/d4996951d381575e1e8d7ab7e17b17dee2deb9ea))
- 修复隔离、原地升级升级场景下全死全活异常的问题([e77407b](https://git.woa.com/kateway/service-controller/commits/e77407b39b0ebede9e4c53026208a4b617a42609))
- ipv6Nat64 LB 不支持开启双向RST, IPv6FullChain 仅支持 VIP 探测 ([84bda07](https://git.woa.com/kateway/service-controller/commits/84bda07d40986249724d5d11a4705afd1d310cda))

### Features

- 调整4层监听器以及7层rule健康检查的源IP默认值 ([debe1ba](https://git.woa.com/kateway/service-controller/commits/debe1ba9334ef233157c8a6cfa0cd47dbab19b76))
- 将MockError改为即时输出 ([4abbf56](https://git.woa.com/kateway/service-controller/commits/4abbf564b1660e433ec5965d3cae110c396b5794))
- 开启mutating以及validating webhook, 多副本webhook支持 ([24e2320](https://git.woa.com/kateway/service-controller/commits/24e2320d503050ab7720babb59e913d2dd8d3ca3))
- 在创建LB实例时默认开启PassToTarget功能 ([d4ea844](https://git.woa.com/kateway/service-controller/commits/d4ea844aa37365c0dfe2ac79a297fc2ec68c8315))
- 增加防误删逻辑 ([ec1dba6](https://git.woa.com/kateway/service-controller/commits/ec1dba62588608bd9b04d04037a13a6514d3a632))
- 支持可用区级别自定义权重 ([c1aa1f3](https://git.woa.com/kateway/service-controller/commits/c1aa1f30ca8ca029b5f8287707704be04dcbc9dd))
- 支持Pod自定义权重 ([2404d34](https://git.woa.com/kateway/service-controller/commits/2404d344ab4770925e9d45dd33eb10b2d5be892a))
- add mcs metrics support ([755f3eb](https://git.woa.com/kateway/service-controller/commits/755f3eb59e615bf9e621003d80507715b65a8ae1))
- pod 优雅删除, 先在clb解绑rs，然后再删除对应的pod ([0c5f026](https://git.woa.com/kateway/service-controller/commits/0c5f026decc7aeafba4318366b64e209ecbea940))
- service-controller支持忽略指定service ([ece32b9](https://git.woa.com/kateway/service-controller/commits/ece32b9663de603526139ec3aa9e9d380f6d45d3))

# [v2.3.4](https://git.woa.com/kateway/service-controller/compare/v2.3.3...v2.3.4) (2024-06-03)

### Bug Fixes

- 修复eks 1.0 的service ClusterIP 为空问题 ([8e22e90](https://git.woa.com/kateway/service-controller/commits/8e22e90fcd7edc950a5df5132de7f7cc68175441))
- 修复监听器后端rs超限之后新增rs导致已有rs解关联的问题 ([fe686f7](https://git.woa.com/kateway/service-controller/commits/fe686f786600f420442d7393f1309aae866e3446))
- 创建LBR失败时不删除LB ([ad70b44](https://git.woa.com/kateway/service-controller/commits/ad70b4451939bd0526089fb75bc3bf56629de2d5))

### Features

- 支持HTTP2配置 ([3172b64](https://git.woa.com/kateway/service-controller/commits/3172b64800e6528f74341ed5ec2de865bdb9250c))
- 新建的4层监听器默认开启双向reset ([45babab](https://git.woa.com/kateway/service-controller/commits/45bababdec08d4d43b4188948b8f3ef1db0d8806))

# [v2.3.3](https://git.woa.com/kateway/service-controller/compare/v2.3.2...v2.3.3) (2024-05-08)

### Features

- 支持修改七层rule的健康检查类型, TCP或者HTTP ([aa56f5f](https://git.woa.com/kateway/service-controller/commits/aa56f5f73d95cb474ef531114bdd99a62e9313d4))
- 支持更多的构建信息记录 ([b020955](https://git.woa.com/kateway/service-controller/commits/b020955f9368f25477dc712d10be2e20a0a29a0a))
- 支持 zones参数，用于内网LB多可用区接入 [707dca65c00794a6a5f5a52725f9f4b795ecd3c6](https://git.woa.com/kateway/service-controller/commit/707dca65c00794a6a5f5a52725f9f4b795ecd3c6)
- 健康检查如果发现某些rs不存在，则将对应的service入队再次调谐 [08d9e963b3d22bfc6a4941a7550e284d74854945](https://git.woa.com/kateway/service-controller/commit/08d9e963b3d22bfc6a4941a7550e284d74854945)

# v2.3.2

发布日期： 2024.4.8

发布镜像：[ccr.ccs.tencentyun.com/paas/service-controller:v2.3.2](http://ccr.ccs.tencentyun.com/paas/service-controller:v2.3.2)     sha256:da2ebe82af66599c7388b2ad1ae88fd45c1c36fd491bab6f967c68a8c005f23c

缺陷修复：

- 自动限制service中SessionAffinityConfig.ClientIP.TimeoutSeconds为30~3600 [9701b87825b8b0d450057c1662e2bb187de36119](https://git.woa.com/kateway/service-controller/commit/9701b87825b8b0d450057c1662e2bb187de36119)

主要功能：

- 支持对TCP_SSL以及QUIC协议开启PP功能 [6884ba6f7e879c57f01bc8cb29b4e1700bc0bcf7](https://git.woa.com/kateway/service-controller/merge_requests/2)
- 兼容1.20以下老版本restconfig构建逻辑，支持从masterurl非安全端口构建 [46f8978962412d738e562a7b80f34dce62ba4b04](https://git.woa.com/kateway/service-controller/commit/46f8978962412d738e562a7b80f34dce62ba4b04)

# v2.3.1

发布日期：2024.3.1

发布镜像：[ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.1](http://ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.1)

主要功能：

- 支持 sub eni [c360e2f57cc9eb2287c54b509bdd218e97ef5e9d](https://git.woa.com/kateway/service-controller/commit/c360e2f57cc9eb2287c54b509bdd218e97ef5e9d)

# v2.3.0

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.3.0
- 发布日期: 2023.12.19
- 发布镜像：（支持ARM结构）
- 发布内容

1.  【主要功能】 新增直连场景下对 hostnetwork 的支持
2.  【主要功能】 适配 CAMP 四层多集群的场景，支持 Target Tag 的后端管理的模式
3.  【主要功能】 适配 CAMP 四层多集群的场景，支持只管理后端不管理监听器的模式
4.  【主要功能】 支持 ManagerOnly 的跨域类型，CLB 地域和后端地域 VPC 相同，与归属集群不同

# v2.2.2 【TKE\\EKS增量版本】

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.2.2
- 发布日期: 2023.12.19~2024.1.4
- 发布镜像：（支持ARM结构）
- 发布内容

1.  【组件优化】支持负载均衡绑定CVM类型的原生节点。
2.  【主要功能】 适配保饭碗项目，重点写接口支持审计回调与拦截机制。
3.  【组件优化】 添加节点截流能力，节点上的服务可优雅下线功能。
4.  【缺陷修复】 修复clientToken幂等导致创建负载均衡时死锁的问题。
5.  【组件优化】Service的协议无效导致解析失败时，输出ServiceSpecifyProtocolFormatError的错误事件。

# v2.2.1

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.2.1
- 发布日期: 2023.9.20
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】Local模式下默认强制开启Local绑定，并强制开启Local加权平衡。
2.  【主要功能】Local模式下强制开启优雅停机和优雅剔除逻辑。
3.  【主要功能】增加Finalizer，避免资源删除事件丢失导致资源泄露。
4.  【主要功能】创建负载均衡时，添加ClientToken。保证接口多次调用的事务性，避免资源重复创建。
5.  【性能优化】优化Metrics全表扫描带来的性能问题。对删除资源的Metrics进行回收。
6.  支持Service的AllocateLoadBalancerNodePorts新特性，避免用户关闭NodePort的情况下绑定报错。
7.  Service资源的同步结果优化，减少同步对Service资源造成的频繁变更，减少同步状态。
8.  部署策略更新，增量开始支持组件部署在超级节点上。（独立集群场景）
9.  预检避免锁冲突，支持多队列并发，提高预检效率。
10. 提供组件版本信息。

# v2.2.0

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.2.0
- 发布日期: 2023.6.28
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】优雅停机、优雅剔除两个特性将变为默认行为强制开启。
2.  Pod ReadinessGate 未通过的相关信息也会通过Event的方式展示出来。
3.  **去除组件启动时的标签转换逻辑（注意不能从1.x及更低版本升级）**
4.  增加go process相关的监控Metrics数据
5.  缺陷修复：忽略EKS的Node变化事件，防止资源反复入列
6.  缺陷修复：未将Service创建监听器的接口报错返回，导致资源同步的错误被忽略。
7.  缺陷修复：优化健康检查队列，避免Pending、被驱逐等异常状态的Pod阻塞在队列中。
8.  缺陷修复：EKS Pod绑定时状态异常的错误已经包装为错误码，避免反馈内部错误。
9.  缺陷修复：No Selector Service 同步过程报错。因为增加了后端的兜底逻辑，导致检查直连后端时Endpoint获取失败报错。
10. 缺陷修复：在集群中存在云原生节点时，传统型负载均衡，绑定会出现内部错误。可能会导致资源同步卡死。

# v2.1.3【TKE、EKS线上主要版本】

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.1.3
- 发布日期: 2023.4.21
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  生命周期管理的优化，避免用户不预期更新切换负载均衡。
2.  TKE弹EKS场景，Local转发兜底
3.  Service资源的同步结果输出到Condition（兼容1.18及以下版本K8s集群）
4.  Readiness Webhook 只对有 Service/Ingress 资源配置直连的 Pod 生效。
5.  缺陷修复：修复已就绪Pod错误进入HealthCheck队列的问题。

# v2.1.2

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.1.2
- 发布日期：2023.3.7
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  缺陷修复：修复Pod ReadinessGate的Status Condition重复创建的问题。

# v2.1.1

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.1.1
- 发布日期：2023.2.28
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】支持CLB域名化改造
2.  缺陷修复：Webhook 的死锁问题优化
3.  添加 Webhook 的性能监控指标
4.  添加 Webhook 的监听状态监控
5.  域名化实例不支持部分SourceIpType字段，该字段逻辑改为不再强制指定。
6.  将CLB管理属性判断(create-by)从标签迁移至CRD

# v2.1.0

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.1.0
- 发布日期：2023.1.18
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】对资源同步处理的产品策略更新，在同步时忽略资源的部分错误配置。
2.  【主要功能】将资源的同步状态显示在资源的Status中。
3.  缺陷修复：更新了Event模块，避免在更新Event的时候大量List Event，容易对API Server造成压力。
4.  缺陷修复：修复了直接更新从Informer中获取到的资源的情况。从Informer中获取的是缓存的直接副本，更新前需要对其进行深复制。
5.  缺陷修复：修复了健康检查队列中，对已经就绪资源进行重复检查的问题。

# v2.0.5

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.0.5
- 发布日期：2023.1.10
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】增加对零节点托管集群的支持

# v2.0.4

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.0.4
- 发布日期：2022.11.12
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  缺陷修复：用户跨集群使用已有负载均衡时，回收逻辑没有校验资源的集群归属，导致资源或监听器资源被错误释放。
2.  缺陷修复：LoadBalancerResource CRD锁的抢占错误，没有Event和监控事件透出

# v2.0.3

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.0.3
- 发布日期：2022.9.7
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】增加对LoadBalancerResource CRD资源的灾难恢复能力
2.  增加CRD被删除场景下的自动恢复能力
3.  增加LoadBalancerResource CRD锁的资源重入机制。
4.  增加Webhook证书过期时间校验，增加证书自动轮转。
5.  在管理七层协议监听器时，增加用户是否开启扩展协议的检查。不再管理用户自建监听器下的七层转发规则。

# v2.0.2

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.0.2
- 发布日期：2022.8.12
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  缺陷修复：兼容CRD对Subresources的使用限制，支持1.10及以下集群。

# v2.0.1

- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.0.1
- 发布日期：2022.8.11
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  缺陷修复：直连场景下，使用已有负载均衡的Ingress，就绪检查失败导致滚动更新卡住。

# v2.0.0

- **如果出现 1.x 版本升级 2.x 版本，需要先联系TKE运维和misakazhou。**
- **除特殊情况外，不能从2.x版本降级到1.x版本，需要先联系TKE运维和misakazhou。**
- ccr.ccs.tencentyun.com/tkeimages/service-controller:v2.0.0**（开始启用新仓库）**
- 发布日期：2022.8.8
- 发布镜像：（支持ARM结构）
- 发布内容：
- **特别注意：**

1.  【主要功能】标签依赖改造，接入层资源管理不再依赖标签。
2.  【主要功能】集群支持原生节点接入。
3.  【主要功能】集群支持云联网场景下的IDC节点接入。
4.  【主要功能】增加证书更新逻辑，应用启动时自动更新Webhook证书。
5.  ReadinessGate的注册逻辑中增加幂等逻辑，避免用户自己注册导致出错。
6.  缺陷修复：TKE超级节点场景下，用户在特定场景下出现绑定EKS节点报错。
7.  缺陷修复：同步Event更新时出现错误，可能导致同步结果被更新到同名资源的Event中。

# v1.8.3

- ccr.ccs.tencentyun.com/paas/service-controller:v1.8.3
- 发布日期：2022.7.2
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】EKS跨集群复用特性收拢。不允许用户直接在EKS集群中进行跨集群复用。
2.  【主要功能】更新Pod绑定规则。以下类型的Pod不会再进行绑定。Pod已经失效被驱逐等（PodFailed）、Job Pod已经结束任务（PodSucceeded）。
3.  新增CLB创建参数：ExclusiveCluster。

# v1.8.2

- ccr.ccs.tencentyun.com/paas/service-controller:v1.8.2
- 发布日期：2022.4.28
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】组件支持透出运营数据
2.  缺陷修复：不允许用户直连HostNetwork类型的工作负载（之前的版本没有阻止该行为，导致出现异常）
3.  缺陷修复：TKEServiceConfig的CRD定义，部分字段没有声明 nullable:true，在EKS集群升级之后影响CRD资源对象创建。
4.  缺陷修复：在没有声明TLS的情况下使用混合协议功能时。声明重定向或者其他PathType的高级特性，会使得Service的健康检查探测逻辑异常。

# v1.8.1

- ccr.ccs.tencentyun.com/paas/service-controller:v1.8.1
- 发布日期：2022.4.15
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】接入层组件支持IPv6。
2.  【主要功能】支持配置负载均衡安全组、负载均衡的默认放通功能
3.  【主要功能】支持配置四层监听器下的双向Rst配置。
4.  【主要功能】直连场景下，支持跳过工作负载的就绪检查。ReadinessGate Skip。
5.  缺陷修复：修复手动重定向功能和直连功能同时使用时，工作负载就绪检查失败的问题。
6.  缺陷修复：云联网关联VPC超过20个时，云联网下VPC获取不完整。导致跨地域接入失败的问题。
7.  不支持七层监听器场景下的直连、不支持GlobalRoute场景下的直连。

# v1.8.0

- ccr.ccs.tencentyun.com/paas/service-controller:v1.8.0
- 发布日期：2022.2.28
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要功能】支持Kubernetes 1.22版本集群
2.  【主要功能】支持社区规范，根据节点标签屏蔽接入层节点。（node.kubernetes.io/exclude-from-external-load-balancers）
3.  【组件优化】适配EKS原地重建的场景，不绑定NodeLost状态下的工作负载。
4.  【组件优化】集群将调整NodePort模式下的默认策略。默认绑定Unschedulable节点作为NodePort后端。
5.  【组件优化】GlobalRoute直连功能不再依赖负载均衡开启SNAT Pro功能。使用GlobalRoute直连功能也不再要求用户申请CLB白名单。
6.  支持配置Service资源脱离当前ServiceController管理。
7.  兼容 EKS 负载均衡模拟ClusterIP功能。将LoadBalancer类型的负载均衡VIP反写到ClusterIP中。
8.  缺陷修复：跨地域场景下，增加地域属性校验，避免用户指定错误地域之后，资源同步出现死锁。
9.  缺陷修复：修复复用场景下的标签回收缺陷。

# v1.7.4

- ccr.ccs.tencentyun.com/paas/service-controller:v1.7.4
- 发布日期：2021.12.13
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要能力】支持IDC混合云场景（内部PVGW打通）
2.  【主要能力】支持跨地域绑定场景（内部PVGW打通）。通过跨地域2.0方案进行接入
3.  【组件优化】默认TKE扩展EKS集群，在存在TKE节点时，为保证流量均衡不绑定EKS节点。在Local模式下导致EKS Pod失去流量入口。

# v1.7.3

- ccr.ccs.tencentyun.com/paas/service-controller:v1.7.3
- 发布日期：2021.11.30
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  更换基础镜像到 alpine:3.13.7，解决镜像安全相关问题。
2.  【主要能力】同时支持优雅停机和优雅踢除。
3.  【主要能力】在优雅停机和优雅踢除场景下，避免权重全部设置为0。
4.  【主要能力】支持资源开启配置保护能力。
5.  【主要能力】支持配置健康访问来源IP类型，防止出现四层回环。
6.  【组件优化】直连场景下，拒绝用户使用NoSelector类型Service。
7.  缺陷修复：修复GR直连场景下，错误丢弃Pod销毁事件，导致IP未解绑的情况。

# v1.7.2

- ccr.ccs.tencentyun.com/paas/service-controller:v1.7.2
- 发布日期：2021.11.4
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  更换基础镜像到 alpine:3.12.8，解决镜像安全相关问题。
2.  【主要能力】支持配置负载均衡健康检查参数（HttpVersion）
3.  【组件优化】优化Event事件的分类，降低并发冲突等用户不关心Event的事件级别。
4.  缺陷修复：修改后端权重的接口分批策略与服务端不匹配。

# v1.7.1

- ccr.ccs.tencentyun.com/paas/service-controller:v1.7.1
- 发布日期：2021.11.1
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  支持负载均衡七层长连接配置

# v1.7.0

- [ccr.ccs.tencentyun.com/paas/service-controller:v1.7.0](http://ccr.ccs.tencentyun.com/paas/service-controller:v1.7.0)
- 发布日期：2021.10.25
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  支持CLB产品计费转移
2.  修复缺陷：一个域名下使用多个Service时，会因为深拷贝问题导致Service名称错乱，导致就绪检查失败。（v1.6.4引入）

# v1.6.4

- [ccr.ccs.tencentyun.com/paas/service-controller:v1.6.4](http://ccr.ccs.tencentyun.com/paas/service-controller:v1.6.4)
- 发布日期：2021.08.19
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要能力】对负载均衡跨域绑定1.0方案提供支持，内部专线打通场景可以通过这个方案跨地域绑定负载均衡资源。
2.  【主要能力】为ReadinessGate能力提供开关配置
3.  修复UpdateStatus是非原子操作导致的问题，Pod的字段可能出现部分更新的情况
4.  证书过期时间调整为10年
5.  动态轮询ProjectID，避免集群ProjectID变更导致资源创建失败。
6.  规避标签服务的落库延迟问题，创建负载均衡之后确认能够通过标签检索到资源。
7.  规避负载均衡清理标签资源的失败情况，检查标签检索到资源的存在性。

# v1.6.3

- [ccr.ccs.tencentyun.com/paas/service-controller:v1.6.3](http://ccr.ccs.tencentyun.com/paas/service-controller:v1.6.3)
- 发布日期：2021.06.09
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要能力】支持四层使用QUIC协议类型监听器
2.  【性能优化】复用场景的性能优化、同时解决死锁问题

# v1.6.2

- [ccr.ccs.tencentyun.com/paas/service-controller:v1.6.2](http://ccr.ccs.tencentyun.com/paas/service-controller:6098b699)
- 发布日期：2021.05.07
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要能力】支持云联网方案下，跨地域的负载均衡管理
2.  【主要能力】支持配置后端转发协议
3.  【主要能力】【性能优化】EKS\\TKE 实现架构统一
4.  修复缺陷：默认域名在特殊场景下会略过配置（新建规则为默认域名时）。
5.  修复缺陷：Service Type，从LoadBalancer到NodePort\\ClusterIP时，未能删除对应负载均衡。
6.  修复缺陷：修改修改后端权重时的批量个数过多超过API单次调用上限的问题。
7.  修复缺陷：defaultWeight只对StatefulSet生效。不能对所有工作负载生效。

# v1.6.0

- [ccr.ccs.tencentyun.com/paas/service-controller:dddb49a7](http://ccr.ccs.tencentyun.com/paas/service-controller:6098b699)
- [ccr.ccs.tencentyun.com/paas/service-controller:0c887fc0](http://ccr.ccs.tencentyun.com/paas/service-controller:6098b699)（BugFix）
- 发布日期：2021.03.17
- 发布镜像：（支持ARM结构）
- 发布内容：

1.  【主要能力】支持集群容器网络工作负载，通过GlobalRouter方式进行直连
2.  【主要能力】指定独立指定后端权重
3.  【主要能力】【性能优化】优化任务队列为优先级队列，避免低优先级任务过多导致阻塞。
4.  【性能优化】优化组件架构，通过上下文方式避免重复的API调用。
5.  优化EventRecorder，避免重复事件上报导致Event限频。

# v1.5.0

- [ccr.ccs.tencentyun.com/paas/service-controller:afa644fe](http://ccr.ccs.tencentyun.com/paas/service-controller:6098b699)
- 发布日期：2020.12.16
- 发布镜像：（支持ARM架构）
- 发布内容：

1.  【主要能力】支持通过注解指定四层监听器协议。
2.  【主要能力】支持在Service中支持七层协议的配置。
3.  【主要能力】支持同一监听器下不同协议族的混合配置
4.  【主要能力】适配TKE扩EKS特性：对于非直连模式过滤VK nodes，并且在无CVM节点时退化成负载均衡直连运行在EKS资源池中的工作负载
5.  【主要能力】以新增注解的方式，支持了适配tkex和公有云用户的pod优雅退出特性：在pod退出过程中权重为0，彻底消失时解绑。
6.  【性能优化】性能优化，避免在同一个同步任务中多次查询负载均衡状态信息。

# v1.4.1

- [ccr.ccs.tencentyun.com/paas/service-controller:76818e0c](http://ccr.ccs.tencentyun.com/paas/service-controller:6098b699)
- 发布日期：2020.09.24
- 发布镜像：
- 发布内容：

1.  修复集群处于1.10到1.12版本升级中时，无法使用ReadinessGate特性，却依旧开启直连的情况。
2.  修复对Node状态突变的容忍期间，并发的同步任务摘除后端后。因为节点状态容忍导致没有重新挂载后端的问题。
3.  修复对集群默认网络类型的判断

# v1.4.0

- [ccr.ccs.tencentyun.com/paas/service-controller:65ac92e2](http://ccr.ccs.tencentyun.com/paas/service-controller:6098b699)
- 发布日期：2020.09.23
- 发布镜像：
- 发布内容：

1.  【主要能力】对新的独立网卡的网络模式直连的支持。
2.  【主要能力】对TkeServiceConfig功能提供同步更新的支持。
3.  【性能优化】提高Service组件的API Server访问频率限制。
4.  根据负载均衡配额查询接口进行优化。避免更新后端时，绑定后端数量超过限制导致错误。
5.  ReadinessGate的错误信息优化，明确健康检查失败的负载均衡和监听器。
6.  针对泛域名或关闭健康检查导致Pod状态不通过的情况。在关闭健康检查时，直接通过ReadinessGate的检查。
7.  修复Service标签优先级问题，支持用户通过注解传入负载均衡的标签参数。新增负载均衡对自研提供的特殊能力的参数支持。（ZhiTong）
8.  错误处理以及其他易用性优化。
9.  增加对Node状态突变的容忍，节点状态异常一分钟之后再处理NodePort的摘除。

# v1.3.2

- [ccr.ccs.tencentyun.com/paas/service-controller:378fa74a](http://ccr.ccs.tencentyun.com/paas/service-controller:6098b699)
- 发布日期：2020.08.06
- 发布镜像：
- 发布内容：

1.  【主要能力】直连场景，支持使用非数字类型的名称端口
2.  【主要能力】支持Local模式下，按节点Pod数量进行权重配置
3.  【性能优化】针对节点信息查询的性能优化实际上线
4.  创建负载均衡时，增加对非公开参数 TgwGroupName 的支持
5.  增加复用功能是否开启的标识
6.  直连场景，避免处于删除状态的节点被挂载

# v1.3.1

- [ccr.ccs.tencentyun.com/paas/service-controller:6098b699](http://ccr.ccs.tencentyun.com/paas/service-controller:6098b699)
- 发布日期：2020.06.01
- 发布镜像：
- 发布内容：

1.  集群API Server故障时，可能出现的NPT
2.  组件启动时，Norm8002故障码的处理
3.  集群默认使用ENI网络时，(Ingress\\Service)的后端判断有缺陷。
4.  直连场景下。Ingress挂载了Port，而不是TargetPort。
5.  Service 为Pod添加Readiness Status时，修复可能出现的并发问题。
6.  Service 跨集群复用标签会出现冲突。
7.  滚动更新时可能出现并发问题，导致错误绑定旧版本的Pod。
8.  依赖NodePort类型的Service时，没有等待Ingress后端的健康检查状态。
9.  没有负载均衡资源依赖时，Readiness Status直接标记为True，不要关注Pod状态。

# v1.3.0

- ccr.ccs.tencentyun.com/paas/service-controller:13f786bc
- 发布日期：2020.05.12
- 发布镜像：
- 发布内容：

1.  【主要能力】Service直绑功能上线
2.  【主要能力】Service外挂负载均衡配置上线
3.  【测试运维】Service的核心回归测试用例上线
4.  【测试运维】Mock的实现改造。

# v1.2.1

- ccr.ccs.tencentyun.com/paas/service-controller:b7701756
- ccr.ccs.tencentyun.com/paas/service-controller:7b1c981c（BUGFIX）
- 发布日期：2020.04.07
- 发布镜像：
- 发布内容：

1.  【测试运维】更新静默发布能力
2.  修复标签并发处理的问题

# v1.2.0

- 发布日期：2020.03.16
- 发布镜像：ccr.ccs.tencentyun.com/paas/service-controller:7c916618
- 发布内容：

1.  【主要能力】更新支持NAT IPv6，解决KubeProxy对于混合IPv4\\IPv6的支持问题。
2.  【测试运维】更新Mock支持，支持集群预发布的检查

# v1.1.0

- 发布日期：2019.12.25
- 发布镜像：ccr.ccs.tencentyun.com/paas/service-controller:b980f397
- 发布内容：

1.  【主要能力】支持NAT IPv6
2.  【主要能力】支持创建用户指定的lb类型（传统型、应用型），且支持切换
3.  【性能优化】大幅提升并发处理性能和其他优化
4.  创建的lb自动继承集群的标签
5.  支持关闭多个service复用一个clb来解决标签key过多的问题
6.  内网类型的service支持切换子网(若在此版本之前手动修改过子网ID，本次更新将会导致旧的LB被删除并在新的子网中重新创建LB
7.  service controller调用云API自动频率控制
8.  标签服务接口，V2切换V3

# v1.0.2

- 发布日期：2019.11.14
- 发布镜像：ccr.ccs.tencentyun.com/paas/service-controller:eb726107
- 发布内容：

1.  修改metrics
2.  修复删除复用lb的svc导致其他svc的lb被删除的bug

# v1.0.1

- 发布日期：2019.11.04
- 发布镜像：ccr.ccs.tencentyun.com/paas/service-controller:8a62a653
- 发布内容：

1.  增加metrics，默认worker数量调整为5

# v1.0.0

- 发布日期：2019.09.06
- 发布镜像：ccr.ccs.tencentyun.com/paas/service-controller:bc704110
- 发布内容：

1.  【主要能力】支持用户指定传统型或者应用型

- 史前版本

- 发布日期：2019.08.22
- 发布镜像：ccr.ccs.tencentyun.com/paas/service-controller:a752b5a9
- 发布内容：

1.  切换到应用型

- 史前版本

- 发布日期：2019.06.06
- 发布镜像：ccr.ccs.tencentyun.com/paas/service-controller:f6012413
- 发布内容：

1.  支持不同协议同端口复用

- 史前版本

- 发布日期：2019.01.16
- 发布镜像：[ccr.ccs.tencentyun.com/paas/service-controller:2a746c68](http://ccr.ccs.tencentyun.com/paas/service-controller:2a746c68)
- 发布内容：

1.  【主要能力】norm切换到tag

- 史前版本

- 发布日期：2018.11.22
- 发布镜像：[ccr.ccs.tencentyun.com/paas/service-controller:8bec6136](http://ccr.ccs.tencentyun.com/paas/service-controller:2a746c68)
- 发布内容：

1.  ???
