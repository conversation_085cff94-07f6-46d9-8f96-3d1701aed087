package webhook

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"

	admissionv1 "k8s.io/api/admission/v1"
	admissionv1beta1 "k8s.io/api/admission/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"

	"git.woa.com/kateway/pkg/jsonpatch"
	"git.woa.com/kateway/pkg/runtime/conv"
)

var (
	ErrSkipRemaining = errors.New("skip remaining handlers")
)

type contextKey int

const (
	contextKeyKubeCli contextKey = iota
	contextKeyRequestBody
)

func RequestBodyFromContext(ctx context.Context) []byte {
	return ctx.Value(contextKeyRequestBody).([]byte)
}

func WithRequestBody(ctx context.Context, body []byte) context.Context {
	return context.WithValue(ctx, contextKeyRequestBody, body)
}

func KubeClientFromContext(ctx context.Context) *kubernetes.Clientset {
	return ctx.Value(contextKeyKubeCli).(*kubernetes.Clientset)
}

func WithKubeClient(ctx context.Context, cli *kubernetes.Clientset) context.Context {
	return context.WithValue(ctx, contextKeyKubeCli, cli)
}

var Scheme = scheme.Scheme

func init() {
	admissionv1.AddToScheme(Scheme)
	admissionv1beta1.AddToScheme(Scheme)
}

type AdmissionRequest[T runtime.Object] struct {
	*admissionv1.AdmissionRequest
	Object    T
	OldObject T
}

func newAdmissionRequest[T runtime.Object](req *admissionv1.AdmissionRequest) (*AdmissionRequest[T], error) {
	var (
		cur          T
		old          T
		deserializer = serializer.NewCodecFactory(Scheme).UniversalDeserializer()
	)
	if len(req.OldObject.Raw) != 0 {
		obj, _, err := deserializer.Decode(req.OldObject.Raw, nil, nil)
		if err != nil {
			return nil, err
		}
		old = obj.(T)
	}
	if req.Operation == admissionv1.Delete {
		cur = old
	} else {
		obj, _, err := deserializer.Decode(req.Object.Raw, nil, nil)
		if err != nil {
			return nil, err
		}
		cur = obj.(T)
	}
	return &AdmissionRequest[T]{
		AdmissionRequest: req,
		Object:           cur,
		OldObject:        old,
	}, nil
}

type handler struct {
	gvk                metav1.GroupVersionKind
	fn                 reflect.Value
	requestConstructor reflect.Value
}

func (h handler) GetGroupVersionKind() metav1.GroupVersionKind {
	return h.gvk
}

func (h handler) Handle(ctx context.Context, req *admissionv1.AdmissionRequest) ([]reflect.Value, error) {
	results := h.requestConstructor.Call([]reflect.Value{reflect.ValueOf(req)})
	if err, ok := results[1].Interface().(error); ok {
		return nil, fmt.Errorf("failed to construct admission request: %w", err)
	}
	results = h.fn.Call([]reflect.Value{reflect.ValueOf(ctx), results[0]})
	return results, nil
}

func newHandler[T runtime.Object](fn any) (*handler, error) {
	var t T
	conv.InitialPointerValue(reflect.ValueOf(&t))
	gvks, unversioned, err := Scheme.ObjectKinds(t)
	if err != nil {
		return nil, err
	}
	if unversioned {
		return nil, fmt.Errorf("unversioned type %T", t)
	}
	if len(gvks) > 1 {
		return nil, fmt.Errorf("multiple gvks detected %v", gvks)
	}
	return &handler{
		gvk: metav1.GroupVersionKind{
			Group:   gvks[0].Group,
			Version: gvks[0].Version,
			Kind:    gvks[0].Kind,
		},
		fn:                 reflect.ValueOf(fn),
		requestConstructor: reflect.ValueOf(newAdmissionRequest[T]),
	}, nil
}

type Mutator struct {
	handler
}

func (m Mutator) Handle(ctx context.Context, ar *admissionv1.AdmissionRequest) (jsonpatch.Patch, error) {
	results, err := m.handler.Handle(ctx, ar)
	if err != nil {
		return nil, err
	}
	var patch jsonpatch.Patch
	if p, ok := results[0].Interface().(jsonpatch.Patch); ok {
		patch = p
	}
	if e, ok := results[1].Interface().(error); ok {
		err = e
	}
	return patch, err
}

type MutateFunc[T runtime.Object] func(context.Context, *AdmissionRequest[T]) (jsonpatch.Patch, error)

func WrapMutateFuncs[T runtime.Object](fns ...MutateFunc[T]) MutateFunc[T] {
	if len(fns) == 0 {
		return func(context.Context, *AdmissionRequest[T]) (jsonpatch.Patch, error) {
			return nil, nil
		}
	}
	return func(ctx context.Context, req *AdmissionRequest[T]) (jsonpatch.Patch, error) {
		var patch jsonpatch.Patch
		for _, fn := range fns {
			p, err := fn(ctx, req)
			if err != nil {
				return nil, err
			}
			patch = append(patch, p...)
		}
		return patch, nil
	}
}

func NewMutator[T runtime.Object](fns ...MutateFunc[T]) Mutator {
	h, err := newHandler[T](WrapMutateFuncs(fns...))
	if err != nil {
		panic(err)
	}
	return Mutator{handler: *h}
}

type Validator struct {
	handler
}

type ValidateFunc[T runtime.Object] func(context.Context, *AdmissionRequest[T]) (failedMsg string, err error)

func (v Validator) Handle(ctx context.Context, req *admissionv1.AdmissionRequest) (string, error) {
	results, err := v.handler.Handle(ctx, req)
	if err != nil {
		return "", err
	}
	msg := results[0].String()
	if e, ok := results[1].Interface().(error); ok {
		err = e
	}
	return msg, err
}

func WrapValidateFuncs[T runtime.Object](fns ...ValidateFunc[T]) ValidateFunc[T] {
	if len(fns) == 0 {
		return func(context.Context, *AdmissionRequest[T]) (string, error) {
			return "", nil
		}
	}
	if len(fns) == 1 {
		return fns[0]
	}
	return func(ctx context.Context, ar *AdmissionRequest[T]) (string, error) {
		msgs := []string{}
		var skip bool
		for _, fn := range fns {
			failedMsg, err := fn(ctx, ar)
			if err != nil {
				if errors.Is(err, ErrSkipRemaining) {
					skip = true
				} else {
					return "", err
				}
			}
			if failedMsg != "" {
				msgs = append(msgs, fmt.Sprintf("- %s", failedMsg))
			}
			if skip {
				break
			}
		}
		return strings.Join(msgs, "\n"), nil
	}
}

func NewValidator[T runtime.Object](fns ...ValidateFunc[T]) Validator {
	h, err := newHandler[T](WrapValidateFuncs(fns...))
	if err != nil {
		panic(err)
	}
	return Validator{handler: *h}
}
