package components

import (
	_ "embed"

	admissionregistrationv1 "k8s.io/api/admissionregistration/v1"
)

var (
	//go:embed manifests/validating_webhook_config.yaml
	tmplValidatingWebhookConfig string

	//go:embed manifests/mutating_webhook_config.yaml
	tmplMutatingWebhookConfig string

	//go:embed manifests/webhook_service.yaml
	tmplWebhookService string

	//go:embed manifests/webhook_secret.yaml
	tmplWebhookSecret string
)

type webhookSecretInput struct {
	Name   string
	Key    string
	Cert   string
	CACert string
}

type webhookServiceInput struct {
	ServiceMeta
	Selector map[string]string
}

type webhookConfigInput struct {
	Name             string
	Version          string
	CABundle         string
	ServiceNamespace string
	ServicePort      int
	Rules            []admissionregistrationv1.RuleWithOperations
}
