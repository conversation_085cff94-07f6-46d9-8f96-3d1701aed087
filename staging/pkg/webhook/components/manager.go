package components

import (
	"bytes"
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"math/big"
	"net"
	"reflect"
	"text/template"
	"time"

	goversion "github.com/hashicorp/go-version"
	"github.com/samber/lo"
	admissionregistrationv1 "k8s.io/api/admissionregistration/v1"
	admissionregistrationv1beta1 "k8s.io/api/admissionregistration/v1beta1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"

	"git.woa.com/kateway/pkg/telemetry/log"
)

const (
	resyncPeriod        = 24 * time.Hour
	DataNameWebhookKey  = "tls.key"
	DataNameWebhookCert = "tls.crt"
	DataNameCACert      = "ca.crt"
)

type Manager struct {
	cli     *kubernetes.Clientset
	meta    ServiceMeta
	params  Params
	decoder runtime.Decoder
}

type ServiceMeta struct {
	Name      string
	Namespace string
	Port      int
}

type ServiceParams struct {
	Enabled  bool
	Selector map[string]string
}

type WebhookConfigParams struct {
	Enabled bool
	Rules   []admissionregistrationv1.RuleWithOperations
}

type Params struct {
	Service                 ServiceParams
	ValidatingWebhookConfig WebhookConfigParams
	MutatingWebhookConfig   WebhookConfigParams
}

func NewManager(cli *kubernetes.Clientset, meta ServiceMeta, params Params) *Manager {
	scheme := runtime.NewScheme()
	_ = corev1.AddToScheme(scheme)
	_ = admissionregistrationv1.AddToScheme(scheme)
	_ = admissionregistrationv1beta1.AddToScheme(scheme)
	return &Manager{
		cli:     cli,
		meta:    meta,
		params:  params,
		decoder: serializer.NewCodecFactory(scheme).UniversalDeserializer(),
	}
}

func (m *Manager) Start(ctx context.Context) {
	log.FromContext(ctx).Info("Webhook components manager started successfully.")
	wait.PollImmediateInfiniteWithContext(ctx, resyncPeriod, func(ctx context.Context) (done bool, err error) {
		if err := m.syncWebhookService(ctx); err != nil {
			log.FromContext(ctx).Error(err, "Failed to create webhook service")
		}
		if err := m.syncWebhookCertsAndConfigs(ctx); err != nil {
			log.FromContext(ctx).Error(err, "Failed to create webhook certs and configs")
		}
		return false, nil
	})
}

func (m Manager) syncWebhookService(ctx context.Context) error {
	if !m.params.Service.Enabled {
		return nil
	}
	input := webhookServiceInput{
		ServiceMeta: m.meta,
		Selector:    m.params.Service.Selector,
	}
	obj, err := m.decode(tmplWebhookService, input)
	if err != nil {
		return err
	}
	svc := obj.(*corev1.Service)
	_, err = m.cli.CoreV1().Services(svc.Namespace).Get(ctx, svc.Name, metav1.GetOptions{})
	if err == nil {
		return nil
	}
	if !apierrors.IsNotFound(err) {
		return err
	}
	_, err = m.cli.CoreV1().Services(svc.Namespace).Create(ctx, svc, metav1.CreateOptions{})
	return err
}

func (m Manager) admissionRegistrationV1beta1Available() (bool, error) {
	svrVersion, err := m.cli.ServerVersion()
	if err != nil {
		return false, err
	}
	svrSemver, err := goversion.NewVersion(svrVersion.GitVersion)
	if err != nil {
		return false, err
	}
	target, _ := goversion.NewVersion("1.22")
	return svrSemver.LessThan(target), nil
}

func needRenew(s *corev1.Secret) bool {
	_, exists := s.Data[DataNameCACert]
	if !exists {
		return true
	}
	certBytes, exists := s.Data[DataNameWebhookCert]
	if !exists {
		return true
	}
	block, _ := pem.Decode(certBytes)
	if block == nil {
		return true
	}
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return true
	}
	return time.Now().AddDate(0, 0, 180).After(cert.NotAfter)
}

func (m Manager) createWebhookCerts(ctx context.Context) (caCert string, err error) {
	var notFound bool
	s, err := m.cli.CoreV1().Secrets(metav1.NamespaceSystem).Get(ctx, m.meta.Name, metav1.GetOptions{})
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return
		}
		notFound = true
	}
	if !notFound {
		if !needRenew(s) {
			return string(s.Data[DataNameCACert]), nil
		}
		log.FromContext(ctx).Info("Certificates need to be renewed.")
	}
	log.FromContext(ctx).Info("Generating certificates for webhook.", "name", m.meta.Name)
	// secret does not exist or certs need to be renewed
	caCert, key, cert, err := generateCertificates(m.meta.Name, m.meta.Namespace)
	if err != nil {
		return
	}
	obj, err := m.decode(tmplWebhookSecret, webhookSecretInput{
		Name:   m.meta.Name,
		Key:    key,
		Cert:   cert,
		CACert: caCert,
	})
	if err != nil {
		return "", err
	}
	expectSecret := obj.(*corev1.Secret)
	if notFound {
		_, err = m.cli.CoreV1().Secrets(expectSecret.Namespace).Create(ctx, expectSecret, metav1.CreateOptions{})
	} else {
		s.Data = expectSecret.Data
		s.StringData = expectSecret.StringData
		_, err = m.cli.CoreV1().Secrets(s.Namespace).Update(ctx, s, metav1.UpdateOptions{})
		log.FromContext(ctx).Info("Secret data updated successfully.", "ns", s.Namespace, "name", s.Name)
	}
	return
}

func (m Manager) decode(tmpl string, input any) (runtime.Object, error) {
	ins, err := template.New("").Parse(tmpl)
	if err != nil {
		return nil, err
	}
	bs := &bytes.Buffer{}
	if err := ins.Execute(bs, input); err != nil {
		return nil, err
	}
	obj, _, err := m.decoder.Decode(bs.Bytes(), nil, nil)
	if err != nil {
		return nil, err
	}
	return obj, nil
}

func (m Manager) buildWebhookConfigInput(caCert string) (*webhookConfigInput, error) {
	v1beta1Available, err := m.admissionRegistrationV1beta1Available()
	if err != nil {
		return nil, err
	}
	input := &webhookConfigInput{
		Version:          lo.Ternary(v1beta1Available, "v1beta1", "v1"),
		Name:             m.meta.Name,
		CABundle:         caCert,
		ServiceNamespace: m.meta.Namespace,
		ServicePort:      m.meta.Port,
	}
	return input, nil
}

func (m Manager) syncMutatingConfig(ctx context.Context, caCert string) error {
	remove := !m.params.MutatingWebhookConfig.Enabled
	input, err := m.buildWebhookConfigInput(caCert)
	if err != nil {
		return err
	}
	input.Rules = m.params.MutatingWebhookConfig.Rules
	obj, err := m.decode(tmplMutatingWebhookConfig, input)
	if err != nil {
		return err
	}
	switch expect := obj.(type) {
	case *admissionregistrationv1beta1.MutatingWebhookConfiguration:
		current, err := m.cli.AdmissionregistrationV1beta1().MutatingWebhookConfigurations().Get(ctx, m.meta.Name, metav1.GetOptions{})
		if err == nil {
			if remove {
				m.cli.AdmissionregistrationV1beta1().MutatingWebhookConfigurations().Delete(ctx, m.meta.Name, metav1.DeleteOptions{})
			} else if !reflect.DeepEqual(expect.Webhooks, current.Webhooks) {
				current.Webhooks = expect.Webhooks
				if _, err = m.cli.AdmissionregistrationV1beta1().MutatingWebhookConfigurations().Update(ctx, current, metav1.UpdateOptions{}); err != nil {
					return err
				}
				log.FromContext(ctx).Info("Mutating webhook configuration updated successfully.")
			}
		} else if !remove {
			if _, err := m.cli.AdmissionregistrationV1beta1().MutatingWebhookConfigurations().Create(ctx, expect, metav1.CreateOptions{}); err != nil {
				return err
			}
		}
	case *admissionregistrationv1.MutatingWebhookConfiguration:
		current, err := m.cli.AdmissionregistrationV1().MutatingWebhookConfigurations().Get(ctx, m.meta.Name, metav1.GetOptions{})
		if err == nil {
			if remove {
				m.cli.AdmissionregistrationV1().MutatingWebhookConfigurations().Delete(ctx, m.meta.Name, metav1.DeleteOptions{})
			} else if !reflect.DeepEqual(expect.Webhooks, current.Webhooks) {
				current.Webhooks = expect.Webhooks
				if _, err = m.cli.AdmissionregistrationV1().MutatingWebhookConfigurations().Update(ctx, current, metav1.UpdateOptions{}); err != nil {
					return err
				}
				log.FromContext(ctx).Info("Mutating webhook configuration updated successfully.")
			}
		} else if !remove {
			if _, err := m.cli.AdmissionregistrationV1().MutatingWebhookConfigurations().Create(ctx, expect, metav1.CreateOptions{}); err != nil {
				return err
			}
		}
	default:
		return fmt.Errorf("unexpected kind %s", obj.GetObjectKind().GroupVersionKind().String())
	}
	return nil
}

func (m Manager) syncValidatingConfig(ctx context.Context, caCert string) error {
	remove := !m.params.ValidatingWebhookConfig.Enabled
	input, err := m.buildWebhookConfigInput(caCert)
	if err != nil {
		return err
	}
	input.Rules = m.params.ValidatingWebhookConfig.Rules
	obj, err := m.decode(tmplValidatingWebhookConfig, input)
	if err != nil {
		return err
	}
	switch expect := obj.(type) {
	case *admissionregistrationv1beta1.ValidatingWebhookConfiguration:
		current, err := m.cli.AdmissionregistrationV1beta1().ValidatingWebhookConfigurations().Get(ctx, m.meta.Name, metav1.GetOptions{})
		if err == nil {
			if remove {
				m.cli.AdmissionregistrationV1beta1().ValidatingWebhookConfigurations().Delete(ctx, m.meta.Name, metav1.DeleteOptions{})
			} else if !reflect.DeepEqual(expect.Webhooks, current.Webhooks) {
				current.Webhooks = expect.Webhooks
				if _, err = m.cli.AdmissionregistrationV1beta1().ValidatingWebhookConfigurations().Update(ctx, current, metav1.UpdateOptions{}); err != nil {
					return err
				}
				log.FromContext(ctx).Info("Validating webhook configuration updated successfully.")
			}
		} else if !remove {
			if _, err := m.cli.AdmissionregistrationV1beta1().ValidatingWebhookConfigurations().Create(ctx, expect, metav1.CreateOptions{}); err != nil {
				return err
			}
		}
	case *admissionregistrationv1.ValidatingWebhookConfiguration:
		current, err := m.cli.AdmissionregistrationV1().ValidatingWebhookConfigurations().Get(ctx, m.meta.Name, metav1.GetOptions{})
		if err == nil {
			if remove {
				m.cli.AdmissionregistrationV1().ValidatingWebhookConfigurations().Delete(ctx, m.meta.Name, metav1.DeleteOptions{})
			} else if !reflect.DeepEqual(expect.Webhooks, current.Webhooks) {
				current.Webhooks = expect.Webhooks
				if _, err = m.cli.AdmissionregistrationV1().ValidatingWebhookConfigurations().Update(ctx, current, metav1.UpdateOptions{}); err != nil {
					return err
				}
				log.FromContext(ctx).Info("Validating webhook configuration updated successfully.")
			}
		} else if !remove {
			if _, err := m.cli.AdmissionregistrationV1().ValidatingWebhookConfigurations().Create(ctx, expect, metav1.CreateOptions{}); err != nil {
				return err
			}
		}
	default:
		return fmt.Errorf("unexpected kind %s", obj.GetObjectKind().GroupVersionKind().String())
	}
	return nil
}

func (m Manager) syncWebhookCertsAndConfigs(ctx context.Context) error {
	caCert, err := m.createWebhookCerts(ctx)
	if err != nil {
		return err
	}
	if err := m.syncValidatingConfig(ctx, caCert); err != nil {
		return err
	}
	if err := m.syncMutatingConfig(ctx, caCert); err != nil {
		return err
	}
	return nil
}

func generateCertificates(name, namespace string) (caCert, key, cert string, err error) {
	caKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return
	}
	now := time.Now()
	subject := pkix.Name{
		CommonName:         "CA",
		Country:            []string{"CN"},
		Province:           []string{"GuangDong"},
		Locality:           []string{"ShenZhen"},
		Organization:       []string{"Tencent Technology (Shenzhen) Company Limited"},
		OrganizationalUnit: []string{"TKE"},
	}
	caTemplate := &x509.Certificate{
		SerialNumber:          big.NewInt(1),
		Subject:               subject,
		NotBefore:             now,
		NotAfter:              now.AddDate(10, 0, 0),
		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageDataEncipherment | x509.KeyUsageCertSign,
		BasicConstraintsValid: true,
		IsCA:                  true,
	}
	caDer, err := x509.CreateCertificate(rand.Reader, caTemplate, caTemplate, &caKey.PublicKey, caKey)
	if err != nil {
		return
	}
	caCert = base64.StdEncoding.EncodeToString(pem.EncodeToMemory(&pem.Block{
		Type:  "CERTIFICATE",
		Bytes: caDer,
	}))
	caCertificate, err := x509.ParseCertificate(caDer)
	if err != nil {
		return
	}
	webhookKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return
	}
	key = base64.StdEncoding.EncodeToString(pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: x509.MarshalPKCS1PrivateKey(webhookKey),
	}))
	subject.CommonName = name + "." + namespace
	webhookTemplate := &x509.Certificate{
		SerialNumber: big.NewInt(2),
		Subject:      subject,
		DNSNames: []string{
			fmt.Sprintf("%s.%s", name, namespace),
			fmt.Sprintf("%s.%s.svc", name, namespace),
			fmt.Sprintf("%s.%s.svc.cluster", name, namespace),
			fmt.Sprintf("%s.%s.svc.cluster.local", name, namespace),
		},
		IPAddresses: []net.IP{
			net.ParseIP("127.0.0.1"),
		},
		NotBefore:             now,
		NotAfter:              now.AddDate(10, 0, 0),
		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageKeyEncipherment,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
	}
	webhookDer, err := x509.CreateCertificate(rand.Reader, webhookTemplate, caCertificate, &webhookKey.PublicKey, caKey)
	if err != nil {
		return
	}
	cert = base64.StdEncoding.EncodeToString(pem.EncodeToMemory(&pem.Block{
		Type:  "CERTIFICATE",
		Bytes: webhookDer,
	}))
	return
}
