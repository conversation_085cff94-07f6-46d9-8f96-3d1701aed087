apiVersion: admissionregistration.k8s.io/{{ .Version }}
kind: MutatingWebhookConfiguration
metadata:
  name: {{ .Name }}
webhooks:
  - admissionReviewVersions:
      - v1
      - v1beta1
    clientConfig:
      caBundle: {{ .CABundle }}
      url: https://{{ .Name }}.{{ .ServiceNamespace }}.svc.cluster.local:{{ .ServicePort }}/mutate
    failurePolicy: Ignore
    matchPolicy: Equivalent
    name: {{ .Name }}.{{ .ServiceNamespace }}.svc.cluster.local
    sideEffects: None
    namespaceSelector: {}
    objectSelector: {}
    timeoutSeconds: 10
    reinvocationPolicy: Never
    rules:
{{- range .Rules }}
      - apiGroups:
{{- range .APIGroups }}
          - "{{ . }}"
{{- end }}
        apiVersions:
{{- range .APIVersions }}
          - "{{ . }}"
{{- end }}
        operations:
{{- range .Operations }}
          - {{ . }}
{{- end }}
        resources:
{{- range .Resources }}
          - "{{ . }}"
{{- end }}
        scope: "{{ .Scope }}"
{{- end }}
