package server

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/samber/lo"
	admissionv1 "k8s.io/api/admission/v1"
	admissionv1beta1 "k8s.io/api/admission/v1beta1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/client-go/informers"
	infcorev1 "k8s.io/client-go/informers/core/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"

	"git.woa.com/kateway/pkg/telemetry/log"
	"git.woa.com/kateway/pkg/webhook"
	"git.woa.com/kateway/pkg/webhook/components"
)

type Config struct {
	Client     *kubernetes.Clientset
	Addr       string
	SecretName string
}

type Server struct {
	Config
	mutators       map[metav1.GroupVersionKind]webhook.Mutator
	validators     map[metav1.GroupVersionKind]webhook.Validator
	m              sync.Mutex
	secretInformer infcorev1.SecretInformer
	factory        informers.SharedInformerFactory
	certs          *tls.Certificate
	deserializer   runtime.Decoder
}

func New(cfg Config) *Server {
	factory := informers.NewSharedInformerFactoryWithOptions(cfg.Client, 0, informers.WithNamespace(metav1.NamespaceSystem))
	return &Server{
		Config:         cfg,
		factory:        factory,
		secretInformer: factory.Core().V1().Secrets(),
		deserializer:   serializer.NewCodecFactory(webhook.Scheme).UniversalDeserializer(),
		mutators:       map[metav1.GroupVersionKind]webhook.Mutator{},
		validators:     map[metav1.GroupVersionKind]webhook.Validator{},
	}
}

func (svr *Server) RegisterValidator(v webhook.Validator) {
	svr.validators[v.GetGroupVersionKind()] = v
}

func (svr *Server) RegisterMutator(m webhook.Mutator) {
	svr.mutators[m.GetGroupVersionKind()] = m
}

func (svr *Server) startUpdatingCerts(ctx context.Context) error {
	ready := make(chan struct{})
	once := sync.Once{}
	updateCerts := func(obj any) {
		s, ok := obj.(*corev1.Secret)
		if !ok {
			return
		}
		if s.Name != svr.SecretName {
			return
		}
		log.FromContext(ctx).Info("Updating certs data from secret.", "name", s.Name)
		var (
			cert = s.Data[components.DataNameWebhookCert]
			key  = s.Data[components.DataNameWebhookKey]
		)
		certs, err := tls.X509KeyPair(cert, key)
		if err != nil {
			log.FromContext(ctx).Error(err, "Failed to load certs")
			return
		}
		svr.m.Lock()
		defer svr.m.Unlock()
		svr.certs = &certs
		once.Do(func() { close(ready) })
	}
	svr.secretInformer.Informer().AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    func(obj interface{}) { updateCerts(obj) },
		UpdateFunc: func(_, newObj interface{}) { updateCerts(newObj) },
	})
	go svr.factory.Start(ctx.Done())
	svr.factory.WaitForCacheSync(ctx.Done())
	select {
	case <-ready:
		return nil
	case <-ctx.Done():
		return context.Canceled
	}
}

func (svr *Server) handleMutating(ctx context.Context, req *admissionv1.AdmissionRequest) (resp *admissionv1.AdmissionResponse, err error) {
	now := time.Now()
	defer func() {
		if err == nil {
			log.FromContext(ctx).Info("Object mutated successfully.", "costs", time.Since(now).String())
		}
	}()

	m, exists := svr.mutators[req.Kind]
	if !exists {
		return nil, fmt.Errorf("mutator of kind %q does not exist", req.Kind)
	}
	p, err := m.Handle(ctx, req)
	if err != nil {
		return
	}
	var bs []byte
	if len(p) > 0 {
		bs, err = json.Marshal(p)
		if err != nil {
			return
		}
	}
	resp = &admissionv1.AdmissionResponse{
		UID:       req.UID,
		Allowed:   true,
		Patch:     bs,
		PatchType: lo.Ternary(len(bs) == 0, nil, lo.ToPtr(admissionv1.PatchTypeJSONPatch)),
	}
	return
}

func (svr *Server) handleValidating(ctx context.Context, req *admissionv1.AdmissionRequest) (resp *admissionv1.AdmissionResponse, err error) {
	now := time.Now()
	defer func() {
		if err == nil {
			log.FromContext(ctx).Info("Object validated successfully.", "op", req.Operation,
				"response", resp.Result.Message, "costs", time.Since(now).String())
		} else {
			log.Error(err, "Object validated failed")
		}
	}()

	validator, exists := svr.validators[req.Kind]
	if !exists {
		return nil, fmt.Errorf("validator of kind %q does not exist", req.Kind)
	}
	msg, err := validator.Handle(ctx, req)
	if err != nil {
		return
	}
	resp = &admissionv1.AdmissionResponse{
		UID:     req.UID,
		Allowed: msg == "",
		Result: &metav1.Status{
			Status:  lo.Ternary(msg == "", metav1.StatusSuccess, metav1.StatusFailure),
			Message: msg,
		},
	}
	return
}

func (svr *Server) buildHandler(fn func(context.Context, *admissionv1.AdmissionRequest) (*admissionv1.AdmissionResponse, error)) http.HandlerFunc {
	return func(rw http.ResponseWriter, req *http.Request) {
		ctx := webhook.WithKubeClient(req.Context(), svr.Client)
		if req.Method != http.MethodPost {
			rw.WriteHeader(http.StatusMethodNotAllowed)
			log.Error(nil, "Unsupported request method", "method", req.Method)
			return
		}
		body, err := io.ReadAll(req.Body)
		if err != nil {
			rw.WriteHeader(http.StatusBadRequest)
			log.Error(err, "Failed to read the request body")
			return
		}
		if contentType := req.Header.Get("Content-Type"); contentType != runtime.ContentTypeJSON {
			rw.WriteHeader(http.StatusBadRequest)
			log.Error(nil, "Invalid content type", "type", contentType)
			return
		}
		obj, _, err := svr.deserializer.Decode(body, nil, nil)
		if err != nil {
			rw.WriteHeader(http.StatusBadRequest)
			log.Error(err, "Invalid request body")
			return
		}
		ctx = webhook.WithRequestBody(ctx, body)
		var (
			admissionRequest *admissionv1.AdmissionRequest
			responseBuilder  func(r *admissionv1.AdmissionResponse) any
		)
		switch review := obj.(type) {
		case *admissionv1.AdmissionReview:
			admissionRequest = review.Request
			responseBuilder = func(r *admissionv1.AdmissionResponse) any {
				return &admissionv1.AdmissionReview{
					TypeMeta: review.TypeMeta,
					Response: r,
				}
			}
		case *admissionv1beta1.AdmissionReview:
			admissionRequest = convertAdmissionRequestV1beta1ToV1(review.Request)
			responseBuilder = func(r *admissionv1.AdmissionResponse) any {
				return &admissionv1beta1.AdmissionReview{
					TypeMeta: review.TypeMeta,
					Response: &admissionv1beta1.AdmissionResponse{
						UID:              r.UID,
						Allowed:          r.Allowed,
						Result:           r.Result,
						Patch:            r.Patch,
						PatchType:        (*admissionv1beta1.PatchType)(r.PatchType),
						AuditAnnotations: r.AuditAnnotations,
						Warnings:         r.Warnings,
					},
				}
			}
		default:
			panic("unsupported admission review type")
		}
		resp, err := fn(log.WithContext(ctx,
			log.WithValues("object",
				fmt.Sprintf("%s/%s", admissionRequest.Namespace, admissionRequest.Name),
				"GVK",
				admissionRequest.Kind,
				"source",
				admissionRequest.UserInfo.Username,
			)),
			admissionRequest,
		)
		if err != nil {
			rw.WriteHeader(http.StatusInternalServerError)
			log.Error(err, "Internal error occurred")
			return
		}
		rw.Write(lo.Must(json.Marshal(responseBuilder(resp))))
	}
}

func (svr *Server) ListenAndServe(ctx context.Context) error {
	mux := http.NewServeMux()
	mux.HandleFunc("/validate", svr.buildHandler(svr.handleValidating))
	mux.HandleFunc("/mutate", svr.buildHandler(svr.handleMutating))
	server := &http.Server{
		Addr:    svr.Addr,
		Handler: mux,
		TLSConfig: &tls.Config{
			GetCertificate: svr.GetCertificate,
		},
	}
	if err := svr.startUpdatingCerts(ctx); err != nil {
		return err
	}
	go func() {
		if err := server.ListenAndServeTLS("", ""); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.FromContext(ctx).Error(err, "ListenAndServe returned error")
			os.Exit(1)
		}
	}()
	log.FromContext(ctx).Info("Webhook server started successfully.")

	<-ctx.Done()
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	_ = server.Shutdown(ctx)
	return nil
}

func (svr *Server) GetCertificate(_ *tls.ClientHelloInfo) (*tls.Certificate, error) {
	svr.m.Lock()
	defer svr.m.Unlock()
	return svr.certs, nil
}

func convertAdmissionRequestV1beta1ToV1(reqV1beta1 *admissionv1beta1.AdmissionRequest) *admissionv1.AdmissionRequest {
	reqV1 := &admissionv1.AdmissionRequest{
		UID:                reqV1beta1.UID,
		Kind:               reqV1beta1.Kind,
		Resource:           reqV1beta1.Resource,
		SubResource:        reqV1beta1.SubResource,
		RequestKind:        reqV1beta1.RequestKind,
		RequestResource:    reqV1beta1.RequestResource,
		RequestSubResource: reqV1beta1.RequestSubResource,
		Name:               reqV1beta1.Name,
		Namespace:          reqV1beta1.Namespace,
		Operation:          admissionv1.Operation(reqV1beta1.Operation),
		UserInfo:           reqV1beta1.UserInfo,
		Object:             reqV1beta1.Object,
		OldObject:          reqV1beta1.OldObject,
		DryRun:             reqV1beta1.DryRun,
		Options:            reqV1beta1.Options,
	}
	return reqV1
}
