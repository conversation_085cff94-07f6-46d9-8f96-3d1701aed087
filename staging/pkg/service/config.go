package service

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/samber/lo"
)

type TLSConfig struct {
	// Trusted root certificates for server
	CAFile string `yaml:"caFile"`
	// Server requires TLS client certificate authentication
	CertFile string `yaml:"certFile"`
	// Server requires TLS client certificate authentication
	KeyFile string `yaml:"keyFile"`
}

type Config struct {
	// Host must be a host string, a host:port pair, or a URL to the base of the server.
	// If a URL is given then the (optional) Path of that URL represents a prefix that must
	// be appended to all request URIs used to access the server. This allows a frontend
	// proxy to easily relocate all of the server endpoints.
	Host string `yaml:"host"`
	// The maximum length of time to wait before giving up on a server request. A value of zero means no timeout.
	Timeout time.Duration `yaml:"timeout"`
	// Server requires Bearer authentication.
	BearerToken string `yaml:"bearerToken"`
	// The graceful shutdown timeout of the http server.
	ShutdownTimeout time.Duration `yaml:"shutdownTimeout" default:"5s"`

	TLSConfig `yaml:",inline"`
}

func (c Config) GetHost() string {
	return lo.Ternary(c.Host == "", fmt.Sprintf("0.0.0.0:%d", lo.Ternary(c.IsSecure(), 443, 80)), c.Host)
}

func (c Config) GetURL() (*url.URL, error) {
	host := c.GetHost()
	scheme := c.scheme()
	if !strings.HasPrefix(host, "http") {
		host = fmt.Sprintf("%s://%s", scheme, host)
	} else if !strings.HasPrefix(host, fmt.Sprintf("%s://", scheme)) {
		return nil, errors.New("invalid scheme of host")
	}
	u, err := url.Parse(host)
	if err != nil {
		return nil, err
	}
	return u, nil
}

func (c Config) scheme() string {
	return lo.Ternary(c.IsSecure(), "https", "http")
}

func (c Config) IsSecure() bool {
	return c.CertFile != "" && c.KeyFile != ""
}

type Server struct {
	*http.Server
	*http.ServeMux
	listenAndServe func() error
	shutdown       func(context.Context) error
}

func (c Config) NewHTTPServer() (*Server, error) {
	var (
		mux = &http.ServeMux{}
		svr = &http.Server{
			Addr:    c.GetHost(),
			Handler: mux,
		}
		listenAndServe = func() error { return svr.ListenAndServe() }
	)

	if c.IsSecure() {
		listenAndServe = func() error { return svr.ListenAndServeTLS(c.CertFile, c.KeyFile) }
		if c.CAFile != "" {
			tlsConfig := &tls.Config{
				ClientCAs:  x509.NewCertPool(),
				ClientAuth: tls.RequireAndVerifyClientCert,
			}
			ca, err := os.ReadFile(c.CAFile)
			if err != nil {
				return nil, fmt.Errorf("failed to load ca file: %w", err)
			}
			tlsConfig.ClientCAs.AppendCertsFromPEM(ca)
			svr.TLSConfig = tlsConfig
		}
	}
	return &Server{
		Server:         svr,
		ServeMux:       mux,
		listenAndServe: listenAndServe,
		shutdown: func(ctx context.Context) error {
			ctx, cancel := context.WithTimeout(ctx, c.ShutdownTimeout)
			defer cancel()
			return svr.Shutdown(ctx)
		},
	}, nil
}

func (s Server) GenericListenAndServe(ctx context.Context) (err error) {
	errCh := make(chan error, 1)
	go func() {
		if err := s.listenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			errCh <- err
		}
		close(errCh)
	}()
	select {
	case <-ctx.Done():
		err = s.shutdown(context.Background())
	case err = <-errCh:
	}
	return
}

func (c Config) GetRoundTripper() (http.RoundTripper, error) {
	if c.IsSecure() {
		cert, err := tls.LoadX509KeyPair(c.CertFile, c.KeyFile)
		if err != nil {
			return nil, err
		}
		transport := &http.Transport{
			TLSClientConfig: &tls.Config{
				Certificates: []tls.Certificate{cert},
			},
		}

		if c.CAFile != "" {
			caCert, err := os.ReadFile(c.CAFile)
			if err != nil {
				return nil, err
			}
			caCertPool := x509.NewCertPool()
			caCertPool.AppendCertsFromPEM(caCert)
			transport.TLSClientConfig.RootCAs = caCertPool
		} else {
			transport.TLSClientConfig.InsecureSkipVerify = true
		}
		return transport, nil
	}
	return http.DefaultTransport, nil
}

func (c Config) NewHTTPClient() (*http.Client, error) {
	client := &http.Client{
		Timeout: c.Timeout,
	}
	rt, err := c.GetRoundTripper()
	if err != nil {
		return nil, err
	}
	client.Transport = rt
	return client, nil
}
