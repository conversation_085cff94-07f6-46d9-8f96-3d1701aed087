package leaderelection

import (
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/pflag"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	componentbaseconfig "k8s.io/component-base/config"
)

type Config struct {
	componentbaseconfig.LeaderElectionConfiguration
	ExitOnStoppedLeading bool
}

func NewConfig() *Config {
	return &Config{
		LeaderElectionConfiguration: componentbaseconfig.LeaderElectionConfiguration{
			LeaderElect:       true,
			ResourceName:      strings.ReplaceAll(filepath.Base(os.Args[0]), "_", "-"),
			ResourceNamespace: metav1.NamespaceSystem,
			ResourceLock:      resourcelock.LeasesResourceLock,
			LeaseDuration:     metav1.Duration{Duration: 15 * time.Second},
			RenewDeadline:     metav1.Duration{Duration: 10 * time.Second},
			RetryPeriod:       metav1.Duration{Duration: 2 * time.Second},
		},
	}
}

// AddFlags binds the LeaderElectionConfiguration struct fields to a flagset
func AddFlags(cfg *Config, fs *pflag.FlagSet) {
	fs.BoolVar(&cfg.LeaderElect, "leader-elect", cfg.LeaderElect, ""+
		"Start a leader election client and gain leadership before "+
		"executing the main loop. Enable this when running replicated "+
		"components for high availability.")
	fs.DurationVar(&cfg.LeaseDuration.Duration, "leader-elect-lease-duration", cfg.LeaseDuration.Duration, ""+
		"The duration that non-leader candidates will wait after observing a leadership "+
		"renewal until attempting to acquire leadership of a led but unrenewed leader "+
		"slot. This is effectively the maximum duration that a leader can be stopped "+
		"before it is replaced by another candidate. This is only applicable if leader "+
		"election is enabled.")
	fs.DurationVar(&cfg.RenewDeadline.Duration, "leader-elect-renew-deadline", cfg.RenewDeadline.Duration, ""+
		"The interval between attempts by the acting master to renew a leadership slot "+
		"before it stops leading. This must be less than or equal to the lease duration. "+
		"This is only applicable if leader election is enabled.")
	fs.DurationVar(&cfg.RetryPeriod.Duration, "leader-elect-retry-period", cfg.RetryPeriod.Duration, ""+
		"The duration the clients should wait between attempting acquisition and renewal "+
		"of a leadership. This is only applicable if leader election is enabled.")
	fs.StringVar(&cfg.ResourceLock, "leader-elect-resource-lock", cfg.ResourceLock, ""+
		"The type of resource object that is used for locking during "+
		"leader election. Supported options are `endpoints` (default) and `configmaps`.")
	fs.StringVar(&cfg.ResourceName, "leader-elect-resource-name", cfg.ResourceName, ""+
		"The name of resource object that is used for locking during "+
		"leader election.")
	fs.StringVar(&cfg.ResourceNamespace, "leader-elect-resource-namespace", cfg.ResourceNamespace, ""+
		"The namespace of resource object that is used for locking during "+
		"leader election.")
}
