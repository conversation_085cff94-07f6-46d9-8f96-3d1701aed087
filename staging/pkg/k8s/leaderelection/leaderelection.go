package leaderelection

import (
	"context"
	"fmt"
	"os"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/klog/v2"
)

func Run(ctx context.Context,
	restConfig *rest.Config,
	conf *Config,
	run func(ctx context.Context),
) error {
	if !conf.LeaderElect {
		go run(ctx)
		<-ctx.Done()
		os.Exit(0)
	}

	client, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return err
	}

	id, err := os.Hostname()
	if err != nil {
		return err
	}

	lock, err := resourcelock.New(conf.ResourceLock,
		conf.ResourceNamespace,
		conf.ResourceName,
		client.CoreV1(),
		client.CoordinationV1(),
		resourcelock.ResourceLockConfig{
			Identity: id,
		})
	if err != nil {
		return fmt.Errorf("error creating lock: %w", err)
	}

	leaderelection.RunOrDie(ctx, leaderelection.LeaderElectionConfig{
		Lock:            lock,
		ReleaseOnCancel: true,
		LeaseDuration:   conf.LeaseDuration.Duration,
		RenewDeadline:   conf.RenewDeadline.Duration,
		RetryPeriod:     conf.RetryPeriod.Duration,
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				klog.Infof("%s: leading", id)
				run(ctx)
			},
			OnStoppedLeading: func() {
				klog.Infof("%s: lost", id)
				klog.Flush()
				if conf.ExitOnStoppedLeading {
					klog.Exit()
				}
			},
			OnNewLeader: func(identity string) {
				if identity == id {
					return
				}
				klog.Infof("new leader elected: %v", identity)
			},
		},
	})

	return nil
}
