package config

import (
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"

	"git.woa.com/kateway/pkg/service"
)

type Config struct {
	service.Config `yaml:",inline"`

	// QPS indicates the maximum QPS to the master from this client.
	// If it's zero, the created RESTClient will use DefaultQPS: 5
	QPS float32 `yaml:"qps"`
	// Maximum burst for throttle.
	// If it's zero, the created RESTClient will use DefaultBurst: 10.
	Burst int `yaml:"burst"`
}

func (c *Config) RESTConfig() *rest.Config {
	if c == nil || c.Host == "" {
		cfg, err := rest.InClusterConfig()
		if err != nil {
			panic(err)
		}
		return cfg
	}

	return &rest.Config{
		Host:        c.Host,
		BearerToken: c.BearerToken,
		QPS:         c.QPS,
		Burst:       c<PERSON>,
		TLSClientConfig: rest.TLSClientConfig{
			Insecure: c.CAFile == "",
			CertFile: c.CertFile,
			KeyFile:  c.KeyFile,
			CAFile:   c.CAFile,
		},
	}
}

func (c *Config) NewClientset() (*kubernetes.Clientset, error) {
	return kubernetes.NewForConfig(c.RESTConfig())
}

func (c *Config) NewClientsetOrDie() *kubernetes.Clientset {
	return kubernetes.NewForConfigOrDie(c.RESTConfig())
}

func (c *Config) NewDynamicClient() (dynamic.Interface, error) {
	return dynamic.NewForConfig(c.RESTConfig())
}

func (c *Config) NewDynamicClientOrDie() dynamic.Interface {
	return dynamic.NewForConfigOrDie(c.RESTConfig())
}
