package k8sutil

import metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

// HasFinalizer check if metav1 object has the finalizer
func HasFinalizer(o metav1.Object, finalizer string) bool {
	f := o.GetFinalizers()
	for _, e := range f {
		if e == finalizer {
			return true
		}
	}
	return false
}

// AddFinalizer accepts a metav1 object and adds the provided finalizer if not present.
// return if object modified
func AddFinalizer(o metav1.Object, finalizer string) (modified bool) {
	found := HasFinalizer(o, finalizer)
	if !found {
		o.SetFinalizers(append(o.GetFinalizers(), finalizer))
	}
	return !found
}

// RemoveFinalizer accepts a metav1 object and removes the provided finalizer if present.
// return if object modified
func RemoveFinalizer(o metav1.Object, finalizer string) (modified bool) {
	f := o.GetFinalizers()
	for i := 0; i < len(f); i++ {
		if f[i] == finalizer {
			f = append(f[:i], f[i+1:]...)
			i--
			modified = true
		}
	}
	if modified {
		o.SetFinalizers(f)
	}
	return modified
}
