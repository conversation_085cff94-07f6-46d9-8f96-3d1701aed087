package annotation

import (
	"reflect"
	"strconv"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParse(t *testing.T) {
	m := map[string]string{
		"key0": "value",
		"key1": "100",
		"key2": "[1,2,3,4]",
	}
	v, err := Parse[string](m, "key0")
	assert.<PERSON>l(t, err)
	assert.Equal(t, "value", *v)

	vv, err := Parse[int](m, "key1")
	assert.<PERSON>l(t, err)
	assert.Equal(t, 100, *vv)

	vvv, err := Parse[[]int](m, "key2")
	assert.<PERSON><PERSON>(t, err)
	assert.Equal(t, []int{1, 2, 3, 4}, *vvv)

	vvvv, err := Parse[[]int](m, "keyNotFound")
	assert.<PERSON>l(t, err)
	assert.Nil(t, vvvv)
}

func TestParseInto(t *testing.T) {
	tests := []struct {
		name    string
		from    map[string]string
		key     string
		to      interface{}
		want    interface{}
		opts    []ParseOption
		wantErr bool
	}{
		{
			name: "String",
			from: map[string]string{"key": "value"},
			key:  "key",
			to:   new(string),
			want: "value",
		},
		{
			name: "Bool",
			from: map[string]string{"key": "true"},
			key:  "key",
			to:   new(bool),
			want: true,
		},
		{
			name: "Int",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(int),
			want: 42,
		},
		{
			name: "Int8",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(int8),
			want: int8(42),
		},
		{
			name: "Int16",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(int16),
			want: int16(42),
		},
		{
			name: "Int32",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(int32),
			want: int32(42),
		},
		{
			name: "Int64",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(int64),
			want: int64(42),
		},
		{
			name: "Uint",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(uint),
			want: uint(42),
		},
		{
			name: "Uint8",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(uint8),
			want: uint8(42),
		},
		{
			name: "Uint16",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(uint16),
			want: uint16(42),
		},
		{
			name: "Uint32",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(uint32),
			want: uint32(42),
		},
		{
			name: "Uint64",
			from: map[string]string{"key": "42"},
			key:  "key",
			to:   new(uint64),
			want: uint64(42),
		},
		{
			name: "Float32",
			from: map[string]string{"key": "42.5"},
			key:  "key",
			to:   new(float32),
			want: float32(42.5),
		},
		{
			name: "Float64",
			from: map[string]string{"key": "42.5"},
			key:  "key",
			to:   new(float64),
			want: float64(42.5),
		},
		{
			name: "Complex64",
			from: map[string]string{"key": "23+42i"},
			key:  "key",
			to:   new(complex64),
			want: complex64(23 + 42i),
		},
		{
			name: "CustomParse",
			from: map[string]string{"key": "1,2,3,4,5"},
			key:  "key",
			to:   &[]int{},
			want: []int{1, 2, 3, 4, 5},
			opts: []ParseOption{WithCustomParseFunc(func(s string) ([]int, error) {
				var v []int
				for _, s := range strings.Split(s, ",") {
					i, err := strconv.Atoi(s)
					if err != nil {
						return nil, err
					}
					v = append(v, i)
				}
				return v, nil
			})},
		},
		{
			name: "JSON",
			from: map[string]string{"key": `{"name": "John", "age": 30}`},
			key:  "key",
			to: &struct {
				Name string
				Age  int
			}{},
			want: struct {
				Name string
				Age  int
			}{Name: "John", Age: 30},
		},
		{
			name:    "KeyNotFound",
			from:    map[string]string{},
			key:     "key",
			to:      new(string),
			wantErr: false,
		},
		{
			name:    "InvalidType",
			from:    map[string]string{"key": "value"},
			key:     "key",
			to:      new(complex64),
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			exists, err := ParseInto(tt.from, tt.key, tt.to, tt.opts...)
			if err != nil {
				if !tt.wantErr {
					t.Errorf("ParseInto() error = %v, wantErr %v", err, tt.wantErr)
				}
				return
			}
			if exists && !reflect.DeepEqual(reflect.ValueOf(tt.to).Elem().Interface(), tt.want) {
				t.Errorf("ParseInto() got = %v, want %v", reflect.ValueOf(tt.to).Elem().Interface(), tt.want)
			}
		})
	}
}
