package annotation

import (
	"fmt"
	"runtime"
	"strings"

	"github.com/iancoleman/strcase"
	"github.com/samber/lo"
	"k8s.io/apimachinery/pkg/util/errors"

	"git.woa.com/kateway/pkg/template"
)

type Stage int

const (
	AlphaStage Stage = iota
	BetaStage
	ReleaseStage
	DeprecatedStage
)

type Type string

const (
	IntType    Type = "int"
	BoolType   Type = "bool"
	StringType Type = "string"
	EnumType   Type = "enum"
	TimeType   Type = "time"
	JSONType   Type = "json"
)

type Item struct {
	Name   string
	Key    string
	Prefix string
	// 类型
	Type Type
	// 默认值
	DefaultValue string
	// 是否只读，只读的注解不能被修改，一般是contronller暴露给用户观测的数据
	ReadOnly bool
	// 是否对外展示
	Public bool
	// 是否必填
	// 文档说明
	Document string
	// 示例
	Example string
	// 示例值
	ExampleValue string
	// 示例的额外信息
	ExampleExtra []string
	// 外部文档地址
	PublicDocURL string
	// 内部说明
	InternalDocument string
	// 内部文档地址
	InternalDocURL string
	// 版本要求
	RequireVersion string
	// 阶段
	Stage Stage
	// 用来记录梳理时的提交
	Commit string
	// 用来记录开发者
	Author string
}

func (item *Item) Validate() (errs []error) {
	if item.Name == "" {
		errs = append(errs, fmt.Errorf("name is required"))
	}
	if item.Key == "" {
		errs = append(errs, fmt.Errorf("key is required"))
	}
	if item.Document == "" {
		errs = append(errs, fmt.Errorf("document is required"))
	}

	return
}

func (item *Item) Default() {
}

type Items []Item

func (items Items) Len() int {
	return len(items)
}

func (items Items) Swap(i, j int) {
	items[i], items[j] = items[j], items[i]
}

func (items Items) Less(i, j int) bool {
	if items[i].Prefix != items[j].Prefix {
		return items[i].Prefix < items[j].Prefix
	}

	return items[i].Key < items[j].Key
}

func (item *Item) String() string {
	return fmt.Sprintf("%s/%s", item.Prefix, item.Key)
}

type Registry struct {
	defaultPrefix       string
	exampleTemplate     string
	annotations         Items
	annotationsMap      map[string]Item
	readOnlyAnnotations map[string]Item
}

func NewRegistry(defaultPrefix string, exampleTemplate string) *Registry {
	return &Registry{
		defaultPrefix:       defaultPrefix,
		exampleTemplate:     exampleTemplate,
		annotationsMap:      make(map[string]Item),
		readOnlyAnnotations: make(map[string]Item),
	}
}

type ListOptions struct {
	Public bool // 是否只展示公共的
}

func (r *Registry) List(options ListOptions) Items {
	return lo.Filter(r.annotations, func(item Item, _ int) bool {
		if options.Public {
			return item.Public
		}
		return true
	})
}

func (r *Registry) IsReadOnly(key string) bool {
	// 判断某个key是否只读，移除前缀
	index := strings.LastIndex(key, "/")
	if index > 0 {
		key = key[index+1:]
	}
	_, ok := r.readOnlyAnnotations[key]

	return ok
}

func (r *Registry) Register(items Items) {
	for _, item := range items {
		r.add(item)
	}
}

func (r *Registry) add(item Item) {
	if errs := item.Validate(); errs != nil {
		panic(errors.NewAggregate(errs))
	}

	if item.Prefix == "" {
		item.Prefix = r.defaultPrefix
	}

	r.complete(&item)

	if _, ok := r.annotationsMap[item.Key]; ok {
		panic(fmt.Errorf("annotation %s already exists", item.Key))
	}

	r.annotationsMap[item.Key] = item
	r.annotations = append(r.annotations, item)
	if item.ReadOnly {
		r.readOnlyAnnotations[item.Key] = item
	}
}

func (r *Registry) complete(item *Item) {
	if item.Example != "" {
		return
	}

	if item.ExampleValue == "" {
		return
	}

	str := fmt.Sprintf("%s: '%s'", item.String(), item.ExampleValue)
	annotations := append([]string{str}, item.ExampleExtra...)

	data, err := template.ParseString(r.exampleTemplate, annotations)
	if err != nil {
		panic(err)
	}
	item.Example = string(data)
}

func (r *Registry) Get(key string) *Item {
	anno, ok := r.annotationsMap[key]
	if !ok {
		panic(fmt.Errorf("annotation %s not found", key))
	}
	return &anno
}

func (r *Registry) GetByCaller() *Item {
	pc, _, _, ok := runtime.Caller(1)
	if !ok {
		panic("failed to get caller")
	}

	funcName := runtime.FuncForPC(pc).Name()
	index := strings.LastIndex(funcName, ".")
	funcName = strcase.ToKebab(funcName[index+1:])
	return r.Get(funcName)
}
