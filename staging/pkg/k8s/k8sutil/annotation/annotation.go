package annotation

import (
	"encoding/json"
	"reflect"
	"strconv"
)

type ParseOptions struct {
	ignoreEmptyValue bool
	customParseFuncs map[reflect.Type]reflect.Value
}

type ParseOption func(*ParseOptions)

func IgnoreEmptyValue(o *ParseOptions) {
	o.ignoreEmptyValue = true
}

func WithCustomParseFunc[T any](fn func(string) (T, error)) ParseOption {
	return func(o *ParseOptions) {
		var t T
		o.customParseFuncs[reflect.TypeOf(t)] = reflect.ValueOf(fn)
	}
}

// nolint: funlen,gocyclo
func ParseInto(from map[string]string, key string, to any, opts ...ParseOption) (exists bool, err error) {
	o := ParseOptions{
		customParseFuncs: map[reflect.Type]reflect.Value{},
	}
	for _, opt := range opts {
		opt(&o)
	}

	rt := reflect.TypeOf(to)
	if rt.Kind() != reflect.Ptr {
		panic("to must be a pointer")
	}
	rt = rt.Elem()
	rv := reflect.ValueOf(to)
	v, exists := from[key]
	if !exists {
		return
	}
	if v == "" && o.ignoreEmptyValue {
		return false, nil
	}

	if rfn, exists := o.customParseFuncs[rt]; exists {
		rvs := rfn.Call([]reflect.Value{reflect.ValueOf(v)})
		if err, ok := rvs[1].Interface().(error); ok {
			return false, err
		}
		rv.Elem().Set(rvs[0])
		return true, nil
	}

	switch rt.Kind() {
	case reflect.String:
		err = setValue(rv, v, func(s string) (string, error) { return s, nil })
	case reflect.Bool:
		err = setValue(rv, v, strconv.ParseBool)
	case reflect.Int:
		err = setValue(rv, v, strconv.Atoi)
	case reflect.Int8:
		err = setValue(rv, v, func(s string) (int8, error) {
			v, err := strconv.ParseInt(s, 10, 8)
			return int8(v), err
		})
	case reflect.Int16:
		err = setValue(rv, v, func(s string) (int16, error) {
			v, err := strconv.ParseInt(s, 10, 16)
			return int16(v), err
		})
	case reflect.Int32:
		err = setValue(rv, v, func(s string) (int32, error) {
			v, err := strconv.ParseInt(s, 10, 32)
			return int32(v), err
		})
	case reflect.Int64:
		err = setValue(rv, v, func(s string) (int64, error) {
			v, err := strconv.ParseInt(s, 10, 64)
			return v, err
		})
	case reflect.Uint:
		err = setValue(rv, v, func(s string) (uint, error) {
			v, err := strconv.ParseUint(s, 10, 0)
			return uint(v), err
		})
	case reflect.Uint8:
		err = setValue(rv, v, func(s string) (uint8, error) {
			v, err := strconv.ParseUint(s, 10, 8)
			return uint8(v), err
		})
	case reflect.Uint16:
		err = setValue(rv, v, func(s string) (uint16, error) {
			v, err := strconv.ParseUint(s, 10, 16)
			return uint16(v), err
		})
	case reflect.Uint32:
		err = setValue(rv, v, func(s string) (uint32, error) {
			v, err := strconv.ParseUint(s, 10, 32)
			return uint32(v), err
		})
	case reflect.Uint64:
		err = setValue(rv, v, func(s string) (uint64, error) {
			v, err := strconv.ParseUint(s, 10, 64)
			return v, err
		})
	case reflect.Float32:
		err = setValue(rv, v, func(s string) (float32, error) {
			v, err := strconv.ParseFloat(s, 32)
			return float32(v), err
		})
	case reflect.Float64:
		err = setValue(rv, v, func(s string) (float64, error) {
			v, err := strconv.ParseFloat(s, 64)
			return float64(v), err
		})
	case reflect.Complex64:
		err = setValue(rv, v, func(s string) (complex64, error) {
			v, err := strconv.ParseComplex(s, 64)
			return complex64(v), err
		})
	case reflect.Complex128:
		err = setValue(rv, v, func(s string) (complex128, error) {
			v, err := strconv.ParseComplex(s, 64)
			return complex128(v), err
		})
	default:
		err = json.Unmarshal([]byte(v), rv.Interface())
	}
	return
}

// Parse parses the value of the given key from the annotation, it returns nil if the key does not exist.
func Parse[T any](from map[string]string, key string, opts ...ParseOption) (*T, error) {
	var t T
	exists, err := ParseInto(from, key, &t, opts...)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, nil
	}
	return &t, nil
}

func setValue[T any](dest reflect.Value, raw string, parseFn func(string) (T, error)) error {
	v, err := parseFn(raw)
	if err != nil {
		return err
	}
	dest.Elem().Set(reflect.ValueOf(v))
	return nil
}
