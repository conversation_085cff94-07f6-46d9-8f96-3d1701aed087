package ingress

import (
	"net/url"

	fakeit "github.com/brianvoe/gofakeit/v7"
	"github.com/samber/lo"
	corev1 "k8s.io/api/core/v1"
	netv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"git.woa.com/kateway/pkg/rand"
)

type Environment struct {
	Ingress           netv1.Ingress
	Services          []corev1.Service
	Secrets           []corev1.Secret
	candidateSvcNames []string
}

func New() *Environment {
	env := &Environment{
		Ingress: netv1.Ingress{
			TypeMeta: metav1.TypeMeta{
				Kind:       "Ingress",
				APIVersion: netv1.SchemeGroupVersion.String(),
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      fakeit.LetterN(5),
				Namespace: fakeit.RandomString([]string{"default", "kube-system", "test"}),
			},
		},
	}
	for range fakeit.IntN(5) + 1 {
		env.candidateSvcNames = append(env.candidateSvcNames, fakeit.LetterN(5))
	}
	hosts := []string{""}
	for range fakeit.IntN(5) + 1 {
		host := fakeit.DomainName()
		host = rand.Ternary(.7, "www."+host, "*."+host)
		hosts = append(hosts, host)
	}
	for _, h := range hosts {
		env.Ingress.Spec.Rules = append(env.Ingress.Spec.Rules, newNetworkingIngressRule(env, h))
	}
	rand.Invoke(0.5, func() {
		env.Ingress.Spec.TLS = append(env.Ingress.Spec.TLS, newNetworkingIngressTLS(env, hosts))
	})
	return env
}

func newNetworkingIngressTLS(env *Environment, hosts []string) netv1.IngressTLS {
	tls := netv1.IngressTLS{
		SecretName: fakeit.LetterN(5),
		Hosts:      rand.Subset(hosts),
	}

	rand.Invoke(0.9, func() {
		buildSecret(env, tls.SecretName)
	})
	return tls
}

func buildSecret(env *Environment, name string) {
	_, exists := lo.Find(env.Secrets, func(s corev1.Secret) bool { return s.Name == name })
	if !exists {
		env.Secrets = append(env.Secrets, corev1.Secret{
			TypeMeta: metav1.TypeMeta{
				Kind:       "Secret",
				APIVersion: corev1.SchemeGroupVersion.String(),
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: env.Ingress.Namespace,
			},
		})
	}
}

func newNetworkingIngressRule(env *Environment, host string) netv1.IngressRule {
	rule := netv1.IngressRule{
		Host: host,
	}
	rule.HTTP = &netv1.HTTPIngressRuleValue{}
	for range fakeit.IntN(3) + 1 {
		rule.HTTP.Paths = append(rule.HTTP.Paths, newNetworkingIngressPath(env, fakeit.RandomString(env.candidateSvcNames)))
	}
	return rule
}

func newNetworkingIngressPath(env *Environment, backendServiceName string) netv1.HTTPIngressPath {
	p := netv1.HTTPIngressPath{}
	u, _ := url.Parse(fakeit.URL())
	svcPort := netv1.ServiceBackendPort{}
	rand.InvokeElse(0.5,
		func() {
			svcPort.Name = fakeit.LetterN(5)
		},
		func() {
			svcPort.Number = int32(fakeit.IntN(65534)) + 1
		},
	)
	p.Path = rand.Pick1([]string{"", "/", u.Path, "/regex/[a-z]+/api", "/badregex/[a-]z?+="}...)
	p.PathType = lo.ToPtr(rand.Pick1(netv1.PathTypePrefix, netv1.PathTypeExact, netv1.PathTypeImplementationSpecific))
	p.Backend = netv1.IngressBackend{
		Service: &netv1.IngressServiceBackend{
			Name: backendServiceName,
			Port: svcPort,
		},
	}
	rand.Invoke(0.8, func() {
		buildService(env, backendServiceName, p.Backend.Service.Port)
	})
	return p
}

func buildService(env *Environment, name string, port netv1.ServiceBackendPort) {
	if name == "" {
		return
	}
	_, i, exists := lo.FindIndexOf(env.Services, func(svc corev1.Service) bool { return svc.Name == name })
	var svc *corev1.Service
	if exists {
		svc = &env.Services[i]
	} else {
		env.Services = append(env.Services, corev1.Service{
			TypeMeta: metav1.TypeMeta{
				Kind:       "Service",
				APIVersion: corev1.SchemeGroupVersion.String(),
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      name,
				Namespace: env.Ingress.Namespace,
			},
			Spec: corev1.ServiceSpec{
				Type: rand.Pick1([]corev1.ServiceType{corev1.ServiceTypeExternalName}...),
			},
		})
		svc = &env.Services[len(env.Services)-1]
	}
	_, exists = lo.Find(svc.Spec.Ports, func(p corev1.ServicePort) bool {
		if port.Name != "" {
			return p.Name == port.Name
		}
		return p.Port == port.Number
	})
	if exists {
		return
	}
	rand.Invoke(0.8, func() {
		svc.Spec.Ports = append(svc.Spec.Ports, corev1.ServicePort{
			Name:       port.Name,
			Protocol:   rand.Pick1(corev1.ProtocolTCP, corev1.ProtocolUDP, corev1.ProtocolSCTP),
			Port:       port.Number,
			TargetPort: intstr.FromInt(fakeit.IntN(65535) + 1),
		})
	})
}
