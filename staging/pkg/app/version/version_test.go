package version

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCheckGitVersion(t *testing.T) {
	GitVersion = "v2.3.4"
	cases := []struct {
		constraint string
		expect     bool
	}{
		{
			constraint: "= 2.3.4",
			expect:     true,
		},
		{
			constraint: ">= 2.3.5",
			expect:     false,
		},
		{
			constraint: "> 2.3.1",
			expect:     true,
		},
		{
			constraint: "> 2.3.1, <= 2.3.6",
			expect:     true,
		},
	}
	for _, tc := range cases {
		t.Run("", func(t *testing.T) {
			assert.Equal(t, tc.expect, Get().CheckVersion(tc.constraint))
		})
	}
}

func TestCheck(t *testing.T) {
	type args struct {
		version           string
		versionConstraint string
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			"version error",
			args{
				"version",
				"<1.0",
			},
			false,
			true,
		},
		{
			"versionConstraint error",
			args{
				"1.0",
				"<versionConstraint",
			},
			false,
			true,
		},
		{
			"1.0 < 1.0",
			args{
				"1.0",
				"<1.0",
			},
			false,
			false,
		},
		{
			"1.0  1.0 <=> 1.0 == 1.0",
			args{
				"1.0",
				"1.0",
			},
			true,
			false,
		},
		{
			"1.0 == 1.0",
			args{
				"1.0",
				"=1.0",
			},
			true,
			false,
		},
		{
			"1.0 >= 1.0",
			args{
				"1.0",
				">=1.0",
			},
			true,
			false,
		},
		{
			"v1.18.3-tke.16 >= 1.12 会自动移除-tke.16 避免比较失败",
			args{
				"v1.18.3-tke.16",
				">=1.12",
			},
			true,
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := Check(tt.args.version, tt.args.versionConstraint)
			if (err != nil) != tt.wantErr {
				t.Errorf("Check() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Check() got = %v, want %v", got, tt.want)
			}
		})
	}
}
