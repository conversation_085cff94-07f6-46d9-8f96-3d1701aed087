/*
 * <PERSON><PERSON> is pleased to support the open source community by making TKE
 * available.
 *
 * Copyright (C) 2012-2019 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use
 * this file except in compliance with the License. You may obtain a copy of the
 * License at
 *
 * https://opensource.org/licenses/Apache-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OF ANY KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations under the License.
 */

package version

import (
	"fmt"
	"os"
	"runtime"
	"strings"

	"github.com/Masterminds/semver"
	"github.com/gosuri/uitable"
	"github.com/samber/lo"
)

var (
	// GitVersion is semantic version.
	GitVersion = "v0.0.0-master+$Format:%h$"
	// GitCommit sha1 from git, output of $(git rev-parse HEAD)
	GitCommit = "$Format:%H$"
	// GitTreeState state of git tree, either "clean" or "dirty"
	GitTreeState = ""
	// BuildDate in ISO8601 format, output of $(date -u +'%Y-%m-%dT%H:%M:%SZ')
	BuildDate   = "1970-01-01T00:00:00Z"
	BuildUser   = ""
	BuildHost   = ""
	BuildJobURL = ""
)

// Info contains versioning information.
type Info struct {
	GoVersion string `json:"goVersion"`
	Platform  string `json:"platform"`

	GitVersion   string `json:"gitVersion"`
	GitCommit    string `json:"gitCommit"`
	GitTreeState string `json:"gitTreeState"`

	BuildDate   string `json:"buildDate"`
	BuildUser   string `json:"buildUser"`
	BuildHost   string `json:"buildHost"`
	BuildJobURL string `json:"buildJobURL"`

	Hostname string `json:"hostname"`
}

// BaseName returns info as a human-friendly version string.
func (info Info) String() string {
	if s, err := info.Text(); err == nil {
		return string(s)
	}
	return info.GitVersion
}

// Text encodes the version information into UTF-8-encoded text and
// returns the result.
func (info Info) Text() ([]byte, error) {
	table := uitable.New()
	table.RightAlign(0)
	table.MaxColWidth = 80
	table.Separator = " "
	table.AddRow("go:", info.GoVersion)
	table.AddRow("state:", info.GitTreeState)
	table.AddRow("commit:", info.GitCommit)
	table.AddRow("version:", info.GitVersion)
	table.AddRow("platform:", info.Platform)
	table.AddRow("hostname:", info.Hostname)
	table.AddRow("buildDate:", info.BuildDate)
	table.AddRow("buildUser:", info.BuildUser)
	table.AddRow("buildHost:", info.BuildHost)
	table.AddRow("buildJobURL:", info.BuildJobURL)
	return table.Bytes(), nil
}

// Get returns the overall codebase version. It's for detecting
// what code a binary was built from.
func Get() Info {
	hostname, _ := os.Hostname()
	// These variables typically come from -ldflags settings and in
	// their absence fallback to the settings in pkg/version/base.go
	return Info{
		GoVersion: fmt.Sprintf("%s/%s", strings.TrimPrefix(runtime.Version(), "go"), runtime.Compiler),
		Platform:  fmt.Sprintf("%s/%s", runtime.GOOS, runtime.GOARCH),

		GitVersion:   GitVersion,
		GitCommit:    GitCommit,
		GitTreeState: GitTreeState,

		BuildDate:   BuildDate,
		BuildUser:   BuildUser,
		BuildHost:   BuildHost,
		BuildJobURL: BuildJobURL,

		Hostname: hostname,
	}
}

func (info Info) CheckVersion(constraint string) bool {
	return CheckOrDie(info.GitVersion, constraint)
}

func CheckOrDie(version string, versionConstraint string) bool {
	ok, err := Check(version, versionConstraint)
	if err != nil {
		panic(err)
	}

	return ok
}

func Parse(version string) (*semver.Version, error) {
	i := strings.Index(version, "-")
	if i > 0 {
		version = version[:i]
	}
	return semver.NewVersion(version)
}

func MustParse(version string) *semver.Version {
	return lo.Must(Parse(version))
}

// Check 比较版本规则 https://github.com/Masterminds/semver?tab=readme-ov-file#basic-comparisons
func Check(version string, versionConstraint string) (bool, error) {
	// 重要！移除预发布版本信息，在TKE集群中都带有这个，所以统一处理下，否则比较版本限制会失败
	// 具体参考：https://github.com/Masterminds/semver/issues/21
	v, err := Parse(version)
	if err != nil {
		return false, err
	}
	c, err := semver.NewConstraint(versionConstraint)
	if err != nil {
		return false, err
	}

	return c.Check(v), nil
}
