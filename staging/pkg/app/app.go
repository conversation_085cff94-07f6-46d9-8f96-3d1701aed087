package app

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"

	"github.com/fatih/color"
	"github.com/spf13/cobra"

	"git.woa.com/kateway/pkg/app/signal"
	"git.woa.com/kateway/pkg/app/version"
)

var (
	progressMessage = color.GreenString("==>")
	usageTemplate   = fmt.Sprintf(`%s{{if .Runnable}}
  %s{{end}}{{if .HasAvailableSubCommands}}
  %s{{end}}{{if gt (len .Aliases) 0}}

%s
  {{.NameAndAliases}}{{end}}{{if .HasExample}}

%s
{{.Example}}{{end}}{{if .HasAvailableSubCommands}}

%s{{range .Commands}}{{if (or .IsAvailableCommand (eq .Name "help"))}}
  %s {{.Short}}{{end}}{{end}}{{end}}{{if .HasAvailableLocalFlags}}

%s
{{.LocalFlags.FlagUsages | trimTrailingWhitespaces}}{{end}}{{if .HasAvailableInheritedFlags}}

%s
{{.InheritedFlags.FlagUsages | trimTrailingWhitespaces}}{{end}}{{if .HasHelpSubCommands}}

%s{{range .Commands}}{{if .IsAdditionalHelpTopicCommand}}
  {{rpad .CommandPath .CommandPathPadding}} {{.Short}}{{end}}{{end}}{{end}}{{if .HasAvailableSubCommands}}

Use "%s --help" for more information about a command.{{end}}
`,
		color.CyanString("Usage:"),
		color.GreenString("{{.UseLine}}"),
		color.GreenString("{{.CommandPath}} [command]"),
		color.CyanString("Aliases:"),
		color.CyanString("Examples:"),
		color.CyanString("Available Commands:"),
		color.GreenString("{{rpad .Name .NamePadding }}"),
		color.CyanString("Flags:"),
		color.CyanString("Global Flags:"),
		color.CyanString("Additional help topics:"),
		color.GreenString("{{.CommandPath}} [command]"),
	)
)

// App is the main structure of a cli application.
// It is recommended that an app be created with the app.New() function.
type App struct {
	name        string
	description string
	options     Options
	run         RunFunc
	silence     bool
	noVersion   bool
	commands    []*Command
}

// Option defines optional parameters for initializing the application structure.
type Option func(*App)

// WithOptions to open the application's function to read from the command line or read parameters from the
// configuration file.
func WithOptions(opt Options) Option {
	return func(a *App) {
		a.options = opt
	}
}

// RunFunc defines the application's startup callback function.
type RunFunc func(ctx context.Context) error

// WithRun is used to set the application startup callback function option.
func WithRun(run RunFunc) Option {
	return func(a *App) {
		a.run = run
	}
}

// WithName is used to set the name of the application.
func WithName(name string) Option {
	return func(a *App) {
		a.name = name
	}
}

// WithDescription is used to set the description of the application.
func WithDescription(desc string) Option {
	return func(a *App) {
		a.description = desc
	}
}

// WithSilence sets the application to silent mode, in which the program startup information, configuration information,
// and version information are not printed in the console.
func WithSilence() Option {
	return func(a *App) {
		a.silence = true
	}
}

// WithNoVersion set the application does not provide version flag.
func WithNoVersion() Option {
	return func(a *App) {
		a.noVersion = true
	}
}

// New creates a new application instance based on the options.
func New(opts ...Option) *App {
	a := &App{}

	for _, o := range opts {
		o(a)
	}

	if a.name == "" {
		a.name = filepath.Base(os.Args[0])
	}

	return a
}

// Run is used to launch the application.
func (a *App) Run() {
	initFlag()

	cmd := cobra.Command{
		Use:           FormatBaseName(a.name),
		Long:          a.description,
		SilenceUsage:  true,
		SilenceErrors: true,
	}
	cmd.SetUsageTemplate(usageTemplate)
	cmd.Flags().SortFlags = false
	if len(a.commands) > 0 {
		for _, command := range a.commands {
			cmd.AddCommand(command.cobraCommand())
		}
		cmd.SetHelpCommand(helpCommand(a.name))
	}
	if a.run != nil {
		cmd.Run = a.runCommand
	}

	cmd.Flags().AddGoFlagSet(flag.CommandLine)
	if a.options != nil {
		if _, ok := a.options.(ConfigurableOptions); ok {
			addConfigFlag(a.name, cmd.Flags())
		}
		a.options.AddFlags(cmd.Flags())
	}

	if !a.noVersion {
		version.AddFlags(cmd.Flags())
	}
	addHelpFlag(a.name, cmd.Flags())

	if err := cmd.Execute(); err != nil {
		fmt.Printf("%v %v\n", color.RedString("Error:"), err)
		os.Exit(1)
	}
}

func (a *App) runCommand(_ *cobra.Command, _ []string) {
	if !a.noVersion {
		version.PrintAndExitIfRequested(a.name)
	}
	if !a.silence {
		fmt.Printf("%v Starting %s...\n", progressMessage, a.name)
		wd, _ := os.Getwd()
		if !a.noVersion {
			fmt.Printf("%v Version:\n", progressMessage)
			fmt.Printf("%s\n", version.Get())
		}
		fmt.Printf("%v WorkingDir: %s\n", progressMessage, wd)
		fmt.Printf("%v Args: %v\n", progressMessage, os.Args)
	}

	// merge configuration and print it
	if a.options != nil {
		if configurableOptions, ok := a.options.(ConfigurableOptions); ok {
			if errs := configurableOptions.ApplyFlags(); len(errs) > 0 {
				for _, err := range errs {
					fmt.Printf("%v %v\n", color.RedString("options.ApplyFlags error:"), err)
				}
				os.Exit(1)
			}
			if !a.silence {
				printConfig()
			}
		}

		if completer, ok := a.options.(OptionCompleter); ok {
			err := completer.Complete()
			if err != nil {
				fmt.Printf("%v %v\n", color.RedString("options.Complete error:"), err)
				os.Exit(1)
			}
		}

		if validater, ok := a.options.(OptionValidater); ok {
			if errs := validater.Validate(); len(errs) > 0 {
				for _, err := range errs {
					fmt.Printf("%v %v\n", color.RedString("options.Validate error:"), err)
				}
				os.Exit(1)
			}
		}
	}

	if a.run != nil {
		if !a.silence {
			fmt.Printf("%v Log data will now stream in as it occurs:\n", progressMessage)
		}

		stopCh := signal.SetupSignalHandler()
		ctx, cancel := context.WithCancel(context.Background())
		go func() {
			<-stopCh
			cancel()
		}()

		if err := a.run(ctx); err != nil {
			fmt.Printf("%v %v\n", color.RedString("Error:"), err)
			os.Exit(1)
		}
	}
}
