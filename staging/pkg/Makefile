SHELL := /bin/bash
ROOT_PACKAGE = $(shell go list -m)
BIN_PATH = build/bin
OS = $(shell go env GOOS)
ARCH = $(shell go env GOARCH)
LINT = $(BIN_PATH)/$(OS)/$(ARCH)/golangci-lint

.PHONY: all
all: lint test build

.PHONY: build
build:
	@echo "===========> Run $@"
	go build ./...

.PHONY: test
test:
	@echo "===========> Run $@"
	go test ./...

.PHONY: lint
lint: 
	@echo "===========> Run $@"
	./$(LINT) run

.PHONY: lint.fix
lint.fix: 
	@echo "===========> Run $@"
	./$(LINT) run

.PHONY: lint.verify
lint.verify:
ifeq (,$(shell which golangci-lint))
	@echo "===========> Installing golangci lint"
	curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $$(go env GOPATH)/bin v1.56.2
endif

.PHONY: check
check: check.go.mod
	@echo "===========> Run $@"

.PHONY: check.go.mod
check.go.mod:
	@echo "===========> Run $@"
	@go mod tidy
	@if git status -s | grep -E 'go(.mod)|go(.sum)' ; then \
   		echo 'Error: must run `go mod tidy` before commit!'; \
   		exit 1; \
   	fi

