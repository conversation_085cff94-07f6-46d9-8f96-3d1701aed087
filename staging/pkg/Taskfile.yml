version: 3

includes:
  tools:
    taskfile: "{{.INCLUDE_REPO}}/tools/Taskfile.yml"
    internal: true
  go:
    taskfile: "{{.INCLUDE_REPO}}/go/Taskfile.yml"
    internal: true
    vars:
      VERSION_PACKAGE: "git.woa.com/kateway/pkg/app/version"
  tag:
    taskfile: "{{.INCLUDE_REPO}}/git/tag.yml"
    internal: true

set:
  - nounset
  - errexit
  - pipefail

env:
  ENV:
    sh: echo ${ENV:-dev}

dotenv: [".env", "{{.ENV}}.env.", "{{.HOME}}/.env"]

vars:
  INCLUDE_REPO: https://mirrors.tencent.com/repository/generic/kateway/taskfile/v1.24.2

tasks:
  default:
    desc: "默认"
    cmds:
      - task: check
      - task: test

  init:
    desc: "项目初始化，安装依赖工具，初始化相关配置"
    cmds:
      - task: tools:init

  check:
    desc: "检查"
    cmds:
      - task: go:check

  test:
    desc: "go:test"
    cmds:
      - task: go:test
