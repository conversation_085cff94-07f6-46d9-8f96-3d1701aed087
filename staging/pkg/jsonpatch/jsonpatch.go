package jsonpatch

import "encoding/json"

type OperationType string

const (
	OperationTypeAdd     OperationType = "add"
	OperationTypeRemove  OperationType = "remove"
	OperationTypeReplace OperationType = "replace"
	OperationTypeMove    OperationType = "move"
	OperationTypeCopy    OperationType = "copy"
)

type Operation struct {
	Op    OperationType    `json:"op"`
	Path  string           `json:"path"`
	Value *json.RawMessage `json:"value,omitempty"`
	From  string           `json:"from,omitempty"`
}

type Patch []Operation
