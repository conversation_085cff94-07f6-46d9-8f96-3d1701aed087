package object

import (
	"bytes"
	"encoding/gob"
	"fmt"
	"hash/fnv"
	"reflect"

	"golang.org/x/exp/maps"
)

// GobGroup 将给定的对象切片进行分组，组内对象具有相同的 Gob 编码哈希值。
// 如果存在哈希冲突（即不同对象的哈希值相同），则这些对象将被单独分组。
// 参数：
// objets []T - 待分组的对象切片。
// 返回值：
// [][]T - 分组后的对象切片，组内对象具有相同的 Gob 编码哈希值。
func GobGroup[T any](objets []T) [][]T {
	groups := make(map[string][]T)
	uniqObjects := make([][]T, 0)

	for _, obj := range objets {
		hash, err := GobHash(obj)
		fmt.Println("hash is ", hash)
		if err != nil {
			uniqObjects = append(uniqObjects, []T{obj})
			continue
		}

		key := fmt.Sprint(hash)
		group := groups[key]
		if len(group) > 0 && !reflect.DeepEqual(obj, group[0]) { // 防止 hash 冲突，将目标和 group 中的第一个做一下比较
			uniqObjects = append(uniqObjects, []T{obj})
		} else {
			groups[key] = append(groups[key], obj)
		}
	}

	return append(maps.Values(groups), uniqObjects...)
}

// GobGroupBy 按照指定的迭代函数对给定的对象切片进行分组。
// 迭代函数会将对象从类型 T 转换为类型 U，并使用 U 的 Gob 编码哈希值进行分组。
// 如果存在哈希冲突（即不同对象的哈希值相同），则这些对象将被单独分组。
// 参数：
// objets []T - 待分组的对象切片。
// iteratee func(T) U - 将对象从类型 T 转换为类型 U 的迭代函数。
// 返回值：
// [][]T - 分组后的对象切片，组内对象具有相同的 Gob 编码哈希值。
func GobGroupBy[T, U any](objets []T, iteratee func(T) U) [][]T {
	groups := make(map[string][]T)
	uniqObjects := make([][]T, 0)

	for _, obj := range objets {
		keyObj := iteratee(obj)
		hash, err := GobHash(keyObj)
		if err != nil {
			uniqObjects = append(uniqObjects, []T{obj})
			continue
		}

		key := fmt.Sprint(hash)
		group := groups[key]
		if len(group) > 0 && !reflect.DeepEqual(keyObj, iteratee(group[0])) { // 防止 hash 冲突，将目标和 group 中的第一个做一下比较
			uniqObjects = append(uniqObjects, []T{obj})
		} else {
			groups[key] = append(groups[key], obj)
		}
	}

	return append(maps.Values(groups), uniqObjects...)
}

func GobHash[T any](object T) (uint32, error) {
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)

	if err := enc.Encode(object); err != nil {
		return 0, err
	}

	h := fnv.New32a()
	if _, err := h.Write(buf.Bytes()); err != nil {
		return 0, err
	}

	return h.Sum32(), nil
}
