package object

import (
	"reflect"
	"testing"
)

func TestGroupByGob(t *testing.T) {
	type innerStruct struct {
		X int
		Y string
	}

	type testStruct struct {
		A int
		B string
		C innerStruct
		D map[string]int
		E *innerStruct
	}

	tests := []struct {
		name   string
		input  []testStruct
		output [][]testStruct
	}{
		{
			name:   "empty slice",
			input:  []testStruct{},
			output: [][]testStruct{},
		},
		{
			name:   "single element",
			input:  []testStruct{{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}}},
			output: [][]testStruct{{{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}}}},
		},
		{
			name: "all unique elements",
			input: []testStruct{
				{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
				{A: 2, B: "two", C: innerStruct{X: 20, Y: "twenty"}, D: map[string]int{"key2": 2}, E: &innerStruct{X: 200, Y: "two hundred"}},
				{A: 3, B: "three", C: innerStruct{X: 30, Y: "thirty"}, D: map[string]int{"key3": 3}, E: &innerStruct{X: 300, Y: "three hundred"}},
			},
			output: [][]testStruct{
				{{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}}},
				{{A: 2, B: "two", C: innerStruct{X: 20, Y: "twenty"}, D: map[string]int{"key2": 2}, E: &innerStruct{X: 200, Y: "two hundred"}}},
				{{A: 3, B: "three", C: innerStruct{X: 30, Y: "thirty"}, D: map[string]int{"key3": 3}, E: &innerStruct{X: 300, Y: "three hundred"}}},
			},
		},
		{
			name: "all identical elements",
			input: []testStruct{
				{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
				{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
				{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
			},
			output: [][]testStruct{
				{{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
					{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
					{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}}},
			},
		},
		{
			name: "mixed elements",
			input: []testStruct{
				{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
				{A: 2, B: "two", C: innerStruct{X: 20, Y: "twenty"}, D: map[string]int{"key2": 2}, E: &innerStruct{X: 200, Y: "two hundred"}},
				{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
				{A: 3, B: "three", C: innerStruct{X: 30, Y: "thirty"}, D: map[string]int{"key3": 3}, E: &innerStruct{X: 300, Y: "three hundred"}},
				{A: 2, B: "two", C: innerStruct{X: 20, Y: "twenty"}, D: map[string]int{"key2": 2}, E: &innerStruct{X: 200, Y: "two hundred"}},
			},
			output: [][]testStruct{
				{{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}},
					{A: 1, B: "one", C: innerStruct{X: 10, Y: "ten"}, D: map[string]int{"key1": 1}, E: &innerStruct{X: 100, Y: "hundred"}}},
				{{A: 2, B: "two", C: innerStruct{X: 20, Y: "twenty"}, D: map[string]int{"key2": 2}, E: &innerStruct{X: 200, Y: "two hundred"}},
					{A: 2, B: "two", C: innerStruct{X: 20, Y: "twenty"}, D: map[string]int{"key2": 2}, E: &innerStruct{X: 200, Y: "two hundred"}}},
				{{A: 3, B: "three", C: innerStruct{X: 30, Y: "thirty"}, D: map[string]int{"key3": 3}, E: &innerStruct{X: 300, Y: "three hundred"}}},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GobGroup(tt.input)
			if len(result) != len(tt.output) {
				t.Errorf("expected %d groups, got %d", len(tt.output), len(result))
			}
			for _, group := range tt.output {
				found := false
				for _, resGroup := range result {
					if reflect.DeepEqual(group, resGroup) {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("expected group %v not found in result %v", group, result)
				}
			}
		})
	}
}

func TestGobGroupBy(t *testing.T) {
	type testStruct struct {
		A int
		B string
	}

	tests := []struct {
		name     string
		input    []testStruct
		iteratee func(testStruct) string
		output   [][]testStruct
	}{
		{
			name: "group by field B with unique values",
			input: []testStruct{
				{A: 1, B: "one"},
				{A: 2, B: "two"},
				{A: 3, B: "three"},
			},
			iteratee: func(ts testStruct) string {
				return ts.B
			},
			output: [][]testStruct{
				{{A: 1, B: "one"}},
				{{A: 2, B: "two"}},
				{{A: 3, B: "three"}},
			},
		},
		{
			name: "group by field B with duplicate values",
			input: []testStruct{
				{A: 1, B: "one"},
				{A: 2, B: "one"},
				{A: 3, B: "two"},
			},
			iteratee: func(ts testStruct) string {
				return ts.B
			},
			output: [][]testStruct{
				{{A: 1, B: "one"}, {A: 2, B: "one"}},
				{{A: 3, B: "two"}},
			},
		},
		{
			// "hXnZR", "HrnGc" 的 hash 值都是 2877397320, hash 冲突
			name: "hash collision with different content",
			input: []testStruct{
				{A: 1, B: "hXnZR"},
				{A: 2, B: "HrnGc"},
			},
			iteratee: func(ts testStruct) string {
				return ts.B
			},
			output: [][]testStruct{
				{{A: 1, B: "hXnZR"}},
				{{A: 2, B: "HrnGc"}},
			},
		},
		{
			name: "hash collision mixed with identical elements",
			input: []testStruct{
				{A: 1, B: "hXnZR"},
				{A: 2, B: "HrnGc"},
				{A: 3, B: "hXnZR"},
			},
			iteratee: func(ts testStruct) string {
				return ts.B
			},
			output: [][]testStruct{
				{{A: 1, B: "hXnZR"}, {A: 3, B: "hXnZR"}},
				{{A: 2, B: "HrnGc"}},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GobGroupBy(tt.input, tt.iteratee)
			if len(result) != len(tt.output) {
				t.Errorf("expected %d groups, got %d", len(tt.output), len(result))
			}
			for _, group := range tt.output {
				found := false
				for _, resGroup := range result {
					if reflect.DeepEqual(group, resGroup) {
						found = true
						break
					}
				}
				if !found {
					t.Errorf("expected group %v not found in result %v", group, result)
				}
			}
		})
	}
}
