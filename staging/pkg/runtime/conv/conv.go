package conv

import (
	"fmt"
	"reflect"

	"github.com/jinzhu/copier"
	"github.com/samber/lo"
)

var (
	converters = map[typePair]any{}
)

type typePair struct {
	source reflect.Type
	dest   reflect.Type
}

func makePair[T, U any]() typePair {
	return typePair{
		source: reflect.TypeOf(*new(T)),
		dest:   reflect.TypeOf(*new(U)),
	}
}

func Register[T, U any](f func(T, *U)) {
	converters[makePair[T, U]()] = f
}

func dereferenceValue(rv reflect.Value) (reflect.Value, bool) {
	for rv.Kind() == reflect.Ptr {
		rv = rv.Elem()
	}
	return rv, rv.IsValid()
}

func dereferenceType(rt reflect.Type) reflect.Type {
	for rt.Kind() == reflect.Ptr {
		rt = rt.Elem()
	}
	return rt
}

func ConvertInto[U, T any](source T, dest *U) {
	if dest == nil {
		panic("Nil dest")
	}
	f, exist := converters[makePair[T, U]()]
	if exist {
		f.(func(T, *U))(source, dest)
		return
	}
	srv, valid := dereferenceValue(reflect.ValueOf(source))
	if !valid {
		panic("Invalid source object")
	}
	drt := dereferenceType(reflect.TypeOf(*dest))
	drv := reflect.ValueOf(dest)
	InitialPointerValue(drv)
	for drv.Elem().Kind() == reflect.Ptr {
		drv = drv.Elem()
	}
	if f, exist := converters[typePair{
		source: srv.Type(),
		dest:   drt,
	}]; exist {
		reflect.ValueOf(f).Call([]reflect.Value{srv, drv})
		return
	}
	lo.Must0(copier.Copy(dest, &source))
}

func Convert[U, T any](source T) U {
	u := new(U)
	ConvertInto(source, u)
	return *u
}

func List() {
	for pair := range converters {
		fmt.Println("Source kind:", pair.source.String(), "->", "Destination kind:", pair.dest.String())
	}
}

func InitialPointerValue(v reflect.Value) {
	for v.Kind() == reflect.Ptr {
		if v.IsNil() {
			v.Set(reflect.New(v.Type().Elem()))
		}
		v = v.Elem()
	}
}
