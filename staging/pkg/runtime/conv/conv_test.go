package conv

import (
	"reflect"
	"strconv"
	"testing"
)

func TestFallbackToCopy(t *testing.T) {
	type A struct {
		Name string
		Age  int
	}
	type B struct {
		Age  int64
		Name string
	}
	a := A{
		Name: "hello",
		Age:  100,
	}
	b := Convert[*B](&a)
	if e, a := a.Name, b.Name; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}
	if e, a := a.Age, b.Age; e != int(a) {
		t.<PERSON><PERSON>("expected %v, got %v", e, a)
	}
}

func TestConverter_byteSlice(t *testing.T) {
	src := []byte{1, 2, 3}
	dest := Convert[[]byte](src)
	if e, a := src, dest; !reflect.DeepEqual(e, a) {
		t.<PERSON>rrorf("expected %#v, got %#v", e, a)
	}
}

func TestConverter_MismatchedTypes(t *testing.T) {
	Register(func(in []string, out *int) {
		v, err := strconv.Atoi(in[0])
		if err != nil {
			panic(err)
		}
		*out = v
	})

	src := []string{"5"}
	v := Convert[int](src)
	if v != 5 {
		t.Errorf("expected %#v, got %#v", 5, v)
	}

	v = Convert[int](&src)
	if v != 5 {
		t.Errorf("expected %#v, got %#v", 5, v)
	}

	vv := Convert[**int](&src)
	if **vv != 5 {
		t.Errorf("expected %#v, got %#v", 5, v)
	}
}

func TestConverter_CallsRegisteredFunctions(t *testing.T) {
	type A struct {
		Foo string
		Baz int
	}
	type B struct {
		Bar string
		Baz int
	}
	type C struct{}
	Register(func(a A, b *B) {
		b.Bar = a.Foo
		b.Baz = a.Baz
	})
	Register(func(b B, a *A) {
		a.Foo = b.Bar
		a.Baz = b.Baz
	})

	x := A{"hello, intrepid test reader!", 3}

	y := Convert[B](x)
	if e, a := x.Foo, y.Bar; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}
	if e, a := x.Baz, y.Baz; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}

	var ptr2 **B
	var ptr1 *B
	b := B{}
	ptr1 = &b
	ptr2 = &ptr1
	ConvertInto(x, ptr2)
	if e, a := x.Foo, b.Bar; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}
	if e, a := x.Baz, b.Baz; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}

	z := B{"all your test are belong to us", 42}

	w := Convert[A](z)
	if e, a := z.Bar, w.Foo; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}
	if e, a := z.Baz, w.Baz; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}

	w = Convert[A](&z)
	if e, a := z.Bar, w.Foo; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}
	if e, a := z.Baz, w.Baz; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}

	ptr := Convert[***A](&z)
	if e, a := z.Bar, (***ptr).Foo; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}
	if e, a := z.Baz, (***ptr).Baz; e != a {
		t.Errorf("expected %v, got %v", e, a)
	}

	defer func() {
		if s := recover(); s == nil {
			t.Error("unexpected nil-panic")
		}
	}()

	Register(func(a A, _ *C) {
		panic("C can't store an A, silly")
	})
	Convert[C](A{})
}
