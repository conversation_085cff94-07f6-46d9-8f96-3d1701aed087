package runtime

import (
	"fmt"
	"runtime"
)

// GetPanicMessage logs the caller tree when a panic occurs.
func GetPanicError(r interface{}) error {
	const size = 64 << 10
	stacktrace := make([]byte, size)
	stacktrace = stacktrace[:runtime.Stack(stacktrace, false)]
	if _, ok := r.(string); !ok {
		return fmt.Errorf("observed a panic: %#v (%v)\n%s", r, r, stacktrace)
	}
	return fmt.<PERSON><PERSON><PERSON>("observed a panic: %s\n%s", r, stacktrace)
}
