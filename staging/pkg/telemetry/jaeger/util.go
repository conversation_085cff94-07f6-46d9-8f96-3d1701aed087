package jaeger

import (
	"encoding/json"
	"fmt"
	"reflect"
)

func transValue(in any) (out any) {
	rvalue := reflect.ValueOf(in)
	switch rvalue.Type().Kind() {
	case reflect.Pointer, reflect.Struct, reflect.Map, reflect.Array, reflect.Interface:
		out = jsonMarshal(in)
	case reflect.Slice:
		if rvalue.Type().Elem() == reflect.TypeOf(byte(1)) {
			out = string(rvalue.Bytes())
		} else {
			out = in
		}
	default:
		out = in
	}

	return
}

// Deprecated: use span.LogAny
func JSON(v interface{}) string {
	return jsonMarshal(v)
}

func jsonMarshal(v interface{}) string {
	const limit = 10000
	data, err := json.Marshal(v)
	if err != nil {
		panic(fmt.<PERSON>rrorf("json.Marshal error: %w", err))
	}
	if len(data) > limit {
		return fmt.Sprintf("data is too large, first %d characters: %s", limit, string(data[:limit]))
	}

	return string(data)
}
