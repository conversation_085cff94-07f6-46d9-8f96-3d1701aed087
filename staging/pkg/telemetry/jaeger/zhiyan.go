package jaeger

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"time"
)

type <PERSON><PERSON><PERSON><PERSON> struct {
	ID        string
	Env       string
	ProjectID string
}

func ZhiYanURL() string {
	if defaultConfig.ZhiYan == nil {
		panic("<PERSON><PERSON>Yan is nil")
	}
	params := url.Values{}
	params.Add("env", fmt.Sprint(defaultConfig.ZhiYan.Env))
	params.Add("service", defaultConfig.ServiceName)
	customTags, _ := json.Marshal([]map[string]interface{}{
		{
			"tag":   "id",
			"value": defaultConfig.ZhiYan.ID,
			"op":    "eq",
		},
	})
	params.Add("customTags", fmt.Sprintf("_$%s", customTags))
	timeRange, _ := json.Marshal(map[string]interface{}{
		"type":  "fixed",
		"start": defaultConfig.startTime.UnixMilli(),
		"end":   time.Now().Add(time.Minute).UnixMilli(),
	})
	params.Add("range", base64.StdEncoding.EncodeToString(timeRange))

	return fmt.Sprintf("%s/%s/trace/#/trace/range_search?%s", defaultConfig.Web, defaultConfig.ZhiYan.ProjectID, params.Encode())
}
