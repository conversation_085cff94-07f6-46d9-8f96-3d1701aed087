package jaeger

import (
	"encoding/json"
	"fmt"
	"net/url"
	"time"
)

type QueryOption func(options *queryOptions)

type queryOptions struct {
	Web      string
	Service  string
	Start    time.Time
	Loopback time.Duration
	Limit    int
	Tags     map[string]string
}

func WithTags(keysAndValues ...interface{}) QueryOption {
	return func(options *queryOptions) {
		kvLen := len(keysAndValues)
		if kvLen%2 == 1 {
			panic("keysAndValues must be even")
		}

		if kvLen > 0 {
			if options.Tags == nil {
				options.Tags = make(map[string]string, kvLen/2)
			}
			for i := 0; i < kvLen; i += 2 {
				key, ok := keysAndValues[i].(string)
				if !ok {
					panic(fmt.Errorf("key[%d](%T) must be string", i, keysAndValues[i]))
				}

				options.Tags[key] = fmt.Sprint(keysAndValues[i+1])
			}
		}
	}
}

func QueryURL(options ...QueryOption) string {
	opts := new(queryOptions)
	for _, option := range options {
		option(opts)
	}

	if opts.Web == "" {
		opts.Web = defaultConfig.Web
	}

	if opts.Service == "" {
		opts.Service = defaultConfig.ServiceName
	}

	if opts.Loopback == 0 {
		opts.Loopback = 24 * time.Hour
	}

	if opts.Start.IsZero() {
		opts.Start = time.Now().Add(-opts.Loopback)
	}

	if opts.Limit == 0 {
		opts.Limit = 20
	}

	params := url.Values{}
	params.Add("limit", fmt.Sprint(opts.Limit))
	params.Add("lookback", fmt.Sprintf("%vh", opts.Loopback.Hours()))
	params.Add("service", opts.Service)
	tagsData, _ := json.Marshal(opts.Tags)
	params.Add("tags", string(tagsData))
	if !opts.Start.IsZero() {
		params.Add("start", fmt.Sprint(opts.Start.UnixNano()/1000))
	}

	return fmt.Sprintf("%s/search?%s", opts.Web, params.Encode())
}
