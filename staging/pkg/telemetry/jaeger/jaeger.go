package jaeger

import (
	"context"
	"fmt"
	"io"
	"os"
	"os/user"
	"regexp"
	"runtime"
	"strings"
	"time"

	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/log"
	"github.com/uber/jaeger-client-go/config"
	jaegerlog "github.com/uber/jaeger-client-go/log"
)

type Config struct {
	Enable               bool
	Priority             Priority
	MatchHighPriority    *regexp.Regexp
	Web                  string `yaml:"web"`
	ZhiYan               *<PERSON>hiYan
	config.Configuration `yaml:",inline"`
	startTime            time.Time
}

type Priority int

const (
	LowPriority  = Priority(0)
	HighPriority = Priority(99)
)

var defaultConfig *Config

var (
	noopSpan *Span
	tracer   opentracing.Tracer
	closer   io.Closer
)

// Init returns an instance of Jaeger tracer that samples 100% of traces and logs all spans to stdout.
func Init(c Config) {
	defaultConfig = &c

	c.Web = strings.TrimSuffix(c.Web, "/")
	if c.<PERSON> == nil {
		c.Sam<PERSON> = &config.SamplerConfig{
			Type:  "const",
			Param: 1,
		}
	}
	// c.Priority = 1
	if c.MatchHighPriority == nil {
		// 如果是云API接口或者client-go接口，则设为高优先级
		c.MatchHighPriority = regexp.MustCompile(`(^[a-z]{3}\.[[:alpha:]]+$)|(^cls-\w{8})`)
	}

	noopSpan = &Span{Span: opentracing.NoopTracer{}.StartSpan("noop")}

	if !c.Enable {
		return
	}

	Enable()
}

func Enable() error {
	defaultConfig.Enable = true
	defaultConfig.startTime = time.Now()

	var err error
	tracer, closer, err = defaultConfig.NewTracer(config.Logger(jaegerlog.StdLogger))
	if err != nil {
		return fmt.Errorf("init jaeger error: %w", err)
	}

	opentracing.SetGlobalTracer(tracer)

	return nil
}

func Disable() {
	defaultConfig.Enable = false
	opentracing.SetGlobalTracer(opentracing.NoopTracer{})
}

func SetPriority(n int) {
	defaultConfig.Priority = Priority(n)
}

func SetMatch(match *regexp.Regexp) {
	defaultConfig.MatchHighPriority = match
}

func Close() {
	if !defaultConfig.Enable {
		return
	}
	closer.Close()
}

type SpanOption func(options *spanOptions)

type spanOptions struct {
	skip          int
	operationName string
	priority      Priority
	options       []opentracing.StartSpanOption
}

func WithSkip(n int) SpanOption {
	return func(options *spanOptions) {
		if n > options.skip {
			options.skip = n
		}
	}
}

func WithOperationName(name string) SpanOption {
	return func(options *spanOptions) {
		options.operationName = name
	}
}

func WithPriority(priority Priority) SpanOption {
	return func(options *spanOptions) {
		options.priority = priority
	}
}

func WithStartSpanOptions(opts ...opentracing.StartSpanOption) SpanOption {
	return func(options *spanOptions) {
		options.options = opts
	}
}

func LogSystem(ctx context.Context, id string) {
	if !defaultConfig.Enable {
		return
	}

	span, _ := StartSpanFromContext(ctx)
	defer span.Finish()

	span.SetTag("id", id)
	span.LogSystem()
}

type Span struct {
	opentracing.Span
}

func (span *Span) LogSystem() {
	u, _ := user.Current()
	span.LogKV("user", u.Username)
	wd, _ := os.Getwd()
	span.LogKV("workDir", wd)
	span.LogKV("env", os.Environ())
	span.LogKV("args", os.Args)
}

func (span *Span) LogError(err error) {
	if err == nil {
		return
	}

	span.SetTag("error", true)
	span.LogFields(log.Error(err))
}

func (span *Span) LogAny(key string, value any) {
	span.LogKV(key, transValue(value))
}

func (span *Span) Finish() {
	span.Span.Finish()
}

func getOperationInfo() (string, string, int) {
	for i := 1; true; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}

		fullName := runtime.FuncForPC(pc).Name()
		re := regexp.MustCompile(`[^/]+$`)
		operationName := re.FindString(fullName)

		if !strings.Contains(operationName, "StartSpan") {
			return operationName, file, line
		}
	}

	return "Unknown", "Unknown", -1
}

func StartSpanFromContext(ctx context.Context, opts ...SpanOption) (*Span, context.Context) {
	options := new(spanOptions)
	for _, o := range opts {
		o(options)
	}

	// 如果span是root span，则设置最高优先级，保证可以生成
	if opentracing.SpanFromContext(ctx) == nil {
		options.priority = HighPriority
	}

	operationName, file, line := getOperationInfo()
	if options.operationName != "" {
		operationName = options.operationName
	}

	if defaultConfig.MatchHighPriority.MatchString(operationName) {
		options.priority = HighPriority
	}

	// 如果span的优先级低于配置的优先级，则不生成新span
	if options.priority < defaultConfig.Priority {
		return noopSpan, ctx
	}

	span, ctx := opentracing.StartSpanFromContext(ctx, operationName)
	span.LogKV("file:line", fmt.Sprintf("%v:%v", file, line))

	return &Span{
		Span: span,
	}, ctx
}
