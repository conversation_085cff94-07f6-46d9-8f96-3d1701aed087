package jaeger

import (
	"reflect"
	"testing"

	"github.com/samber/lo"
)

func Test_transValue1(t *testing.T) {
	tests := []struct {
		name    string
		in      any
		wantOut any
	}{
		{
			name:    "int",
			in:      1,
			wantOut: 1,
		},
		{
			name:    "*int",
			in:      lo.ToPtr(1),
			wantOut: "1",
		},
		{
			name:    "[]int",
			in:      []int{1},
			wantOut: []int{1},
		},
		{
			name:    "[]byte",
			in:      []byte("1"),
			wantOut: "1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotOut := transValue(tt.in); !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.<PERSON>rf("transValue() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}

func Test_transValue(t *testing.T) {
	type args struct {
		in any
	}
	tests := []struct {
		name    string
		args    args
		wantOut any
	}{
		{
			"1",
			args{1},
			1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotOut := transValue(tt.args.in); !reflect.DeepEqual(gotOut, tt.wantOut) {
				t.Errorf("transValue() = %v, want %v", gotOut, tt.wantOut)
			}
		})
	}
}
