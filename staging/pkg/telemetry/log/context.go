package log

import (
	"context"

	"github.com/go-logr/logr"
)

type key int

const (
	logKey key = iota
)

// WithLogger returns a copy of parent in which the logger value is set
func WithContext(parent context.Context, logger logr.Logger) context.Context {
	return context.WithValue(parent, logKey, logger)
}

// Ctx returns the logger from context.
func Ctx(ctx context.Context) logr.Logger {
	return FromContext(ctx)
}

// FromContext returns the logger from context.
func FromContext(ctx context.Context) logr.Logger {
	log, ok := ctx.Value(logKey).(logr.Logger)
	if !ok {
		return Logger()
	}

	return log
}
