package main

import (
	_ "embed"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/gosuri/uitable"
	"github.com/spf13/pflag"

	ingressannotation "git.woa.com/kateway/pkg/domain/ingress/annotation"
	"git.woa.com/kateway/pkg/k8s/k8sutil/annotation"
	"git.woa.com/kateway/pkg/template"
)

var (
	//go:embed template.tpl
	templateDoc string

	options struct {
		Public bool
		Format string
	}
)

func init() {
	pflag.BoolVarP(&options.Public, "public", "p", false, "是否只输出公共注解")
	pflag.StringVarP(&options.Format, "output", "o", "table", "指定输出格式，可选值：table, json, markdown")
}

func main() {
	pflag.Parse()

	items := ingressannotation.Ingress().List(annotation.ListOptions{
		Public: options.Public,
	})

	var output string
	switch options.Format {
	case "table":
		table := uitable.New()
		table.MaxColWidth = 120
		table.Separator = " "
		table.AddRow("序号", "名称", "只读", "对外", "版本", "说明")
		for i, item := range items {
			table.AddRow(i, item.String(), item.ReadOnly, item.Public, item.RequireVersion, strings.TrimSpace(item.Document))
		}
		output = table.String()
	case "json":
		data, _ := json.MarshalIndent(items, "", " ")
		output = string(data)
	case "markdown":
		data, err := template.ParseString(templateDoc, items)
		if err != nil {
			panic(err)
		}
		output = string(data)
	default:
		fmt.Println("不支持的输出格式：", options.Format)
		os.Exit(1)
	}

	fmt.Println(output)
}
