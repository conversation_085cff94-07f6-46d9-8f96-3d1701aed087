package format

import (
	"bytes"
	"fmt"
)

func KV(keysAndValues ...interface{}) string {
	var buf bytes.Buffer
	kvLen := len(keysAndValues)
	if kvLen%2 == 1 {
		panic("keysAndValues must be even")
	}

	for i := 0; i < kvLen; i += 2 {
		key, ok := keysAndValues[i].(string)
		if !ok {
			panic(fmt.Errorf("key[%d](%T) must be string", i, keysAndValues[i]))
		}

		if i > 0 {
			buf.WriteString(" ")
		}
		buf.WriteString(fmt.Sprintf("%s=%s", key, keysAndValues[i+1]))
	}

	return buf.String()
}
