package options

import (
	"errors"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/spf13/pflag"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	componentbaseconfig "k8s.io/component-base/config"
	"k8s.io/component-base/logs"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/k8s/leaderelection"
	"git.woa.com/kateway/pkg/tencent/cloud/clientset"
)

func New(name string, metricPort int) *Options {
	return &Options{
		Name:        name,
		MetricsPort: metricPort,
		LeaderElection: leaderelection.Config{
			LeaderElectionConfiguration: componentbaseconfig.LeaderElectionConfiguration{
				LeaderElect:       true,
				ResourceName:      name,
				ResourceNamespace: metav1.NamespaceSystem,
				ResourceLock:      resourcelock.LeasesResourceLock,
				LeaseDuration:     metav1.Duration{Duration: 15 * time.Second},
				RenewDeadline:     metav1.Duration{Duration: 10 * time.Second},
				RetryPeriod:       metav1.Duration{Duration: 2 * time.Second},
			},
			ExitOnStoppedLeading: true,
		},
		FeatureGates: featuregates.NewFeatureGates(nil),
	}
}

type Options struct {
	Name string

	LeaderElection leaderelection.Config
	FeatureGates   featuregates.FeatureGates

	Kubeconfig string
	MasterURL  string
	QPS        int
	Burst      int

	OwnerUin    int64
	Region      string
	VPCID       string
	ProjectID   int64
	ClusterName string

	MetricsPort int

	BackendQuota  int
	ListenerQuota int

	Workers int

	EnableTracing bool

	IngressClass              string
	EnableMultiClusterIngress bool // 是否开启多集群Ingress管理模式

	RESTConfig *rest.Config
	EventType  string

	Debug              bool
	RateLimit          int
	SecretID           string
	SecretKey          string
	ServiceRole        string
	CredentialDuration time.Duration // 凭证有效期

	DisableAdmit         bool // 关闭tke资源中心删除保护能力，在独立部署时使用
	ClusterSupportDirect bool // 集群是否开启直连模式，在独立部署时，无法通过集群内信息判定是否支持直连，需要用户显式配置
}

func (o *Options) AddFlags(fs *pflag.FlagSet) {
	logs.AddFlags(fs)

	leaderelection.AddFlags(&o.LeaderElection, fs)
	o.FeatureGates.AddFlags(fs)

	fs.StringVar(&o.Kubeconfig, "kubeconfig", "", "Path to a kubeconfig. Only required if out-of-cluster.")  // tke service-controller
	fs.StringVar(&o.Kubeconfig, "kube-config", "", "Path to a kubeconfig. Only required if out-of-cluster.") // eks ingress-controller
	fs.StringVar(&o.MasterURL, "master", "", "The address of the Kubernetes API server. Overrides any value in kubeconfig. Only required if out-of-cluster.")
	fs.IntVar(&o.QPS, "qps", 1000, "qps")
	fs.IntVar(&o.Burst, "burst", 10000, "burst")

	fs.Int64Var(&o.OwnerUin, "owner-uin", 0, "owner uin")
	fs.StringVar(&o.Region, "region", "", "region full name, e.g. ap-guangzhou")
	fs.StringVar(&o.VPCID, "vpcid", "", "vpcid, e.g. vpc-xxxxxxxx")
	fs.Int64Var(&o.ProjectID, "project-id", 0, "project-id")
	fs.StringVar(&o.ClusterName, "clusterName", "", "clusterName")
	fs.StringVar(&o.ClusterName, "cluster-name", "", "clusterName")

	fs.IntVar(&o.MetricsPort, "metric-port", o.MetricsPort, "prometheus metrics port for service controller")

	fs.IntVar(&o.BackendQuota, "backend-quota", 0, "lb backend quota")
	fs.IntVar(&o.ListenerQuota, "listener-quota", 0, "lb listener quota")

	fs.IntVar(&o.Workers, "workers", 20, "service queue workers")

	fs.StringVar(&o.IngressClass, "ingress-class", "qcloud", "used as parameter to create metric collector")
	fs.BoolVar(&o.EnableMultiClusterIngress, "enable-multiclusteringress", false, "whether to enable multiclusteringress")

	fs.StringVar(&o.EventType, "event-type", "default", "event-type")
	fs.BoolVar(&o.EnableTracing, "enable-tracing", false, "whether to report tracing spans")

	fs.BoolVar(&o.Debug, "debug", o.Debug, "debug")
	fs.IntVar(&o.RateLimit, "rate-limit", 18, "qps limit for qcloud api")
	fs.StringVar(&o.SecretID, "secret-id", "", "yunapi secret-id")
	fs.StringVar(&o.SecretKey, "secret-key", "", "yunapi secret-key")
	fs.StringVar(&o.ServiceRole, "service-role", "", "tencent cloud service role name")
	fs.DurationVar(&o.CredentialDuration, "credential-duration", 12*time.Hour, "credential duration")

	fs.BoolVar(&o.DisableAdmit, "disable-admit", false, "whether to disable admit")
	fs.BoolVar(&o.ClusterSupportDirect, "cluster-support-direct", false, "cluster whether support direct")
}

func (o *Options) Complete() error {
	var err error

	o.RESTConfig, err = clientcmd.BuildConfigFromFlags(o.MasterURL, o.Kubeconfig)
	if err != nil {
		return err
	}
	o.RESTConfig.Cluster = o.ClusterName
	o.RESTConfig.QPS = float32(o.QPS)
	o.RESTConfig.Burst = o.Burst

	if o.ClusterName != "" && os.Getenv("CLUSTER_ID") == "" {
		os.Setenv("CLUSTER_ID", o.ClusterName)
	}

	return nil
}

func (o *Options) Validate() (errs []error) {
	// 如果使用service-role，则owner-uin和secret-id和secret-key不能为空
	if o.ServiceRole != "" {
		if o.OwnerUin == 0 {
			errs = append(errs, fmt.Errorf("--owner-uin is required if use service-role"))
		}
		if o.SecretID == "" {
			errs = append(errs, fmt.Errorf("--secret-id is required if use service-role"))
		}
		if o.SecretKey == "" {
			errs = append(errs, fmt.Errorf("--secret-key is required if use service-role"))
		}
	}
	// 如果使用secret-id和secret-key，则secret-id和secret-key不能为空
	if o.SecretID != "" && o.SecretKey == "" {
		errs = append(errs, fmt.Errorf("--secret-key is required if use secret-id"))
	}
	if o.SecretKey != "" && o.SecretID == "" {
		errs = append(errs, fmt.Errorf("--secret-id is required if use secret-key"))
	}

	if o.ClusterName == "" {
		errs = append(errs, errors.New("--clusterName is required"))
	}

	if o.Region == "" {
		errs = append(errs, errors.New("--region is required"))
	}

	if o.VPCID == "" {
		errs = append(errs, errors.New("--vpcid is required"))
	}

	return
}

func (o *Options) AuthConfig() clientset.Config {
	config := clientset.Config{
		AKSKConfig:        o.akskConfig(),
		ServiceRoleConfig: o.serviceRoleConfig(),
		LockSmithConfig:   o.lockSmithConfig(),
		Debug:             o.Debug,
		InTCE:             os.Getenv("QCLOUD_PROVIDER") == "tce",
		RateLimit:         o.RateLimit,
	}

	return config
}

func (o *Options) akskConfig() *clientset.AKSKConfig {
	if o.SecretID == "" || o.SecretKey == "" {
		return nil
	}

	// 如果使用了服务角色，则对应的aksk是使用服务角色的aksk，而不是用来鉴权的aksk！
	if o.ServiceRole != "" {
		return nil
	}

	return &clientset.AKSKConfig{
		SecretID:  o.SecretID,
		SecretKey: o.SecretKey,
	}
}

func (o *Options) serviceRoleConfig() *clientset.ServiceRoleConfig {
	if o.ServiceRole == "" {
		return nil
	}

	return &clientset.ServiceRoleConfig{
		AKSKConfig: clientset.AKSKConfig{
			SecretID:  o.SecretID,
			SecretKey: o.SecretKey,
		},
		OwnerUIN:        strconv.FormatInt(o.OwnerUin, 10),
		ARN:             fmt.Sprint("qcs::cam::uin/", o.OwnerUin, ":roleName/", o.ServiceRole),
		SessionName:     fmt.Sprintf("%s_%s", o.ClusterName, o.Name),
		DurationSeconds: int64(o.CredentialDuration.Seconds()),
	}
}

func (o *Options) lockSmithConfig() *clientset.LockSmithConfig {
	// 如果使用了固定aksk或服务角色，则不使用Locksmith
	if o.SecretID != "" {
		return nil
	}

	return &clientset.LockSmithConfig{
		Name:                    o.Name,
		ClusterID:               o.ClusterName,
		RESTConfig:              o.RESTConfig,
		ServiceAccount:          "lb-service",
		ServiceAccountNamespace: "kube-system",
		CredentialDuration:      o.CredentialDuration,
	}
}
