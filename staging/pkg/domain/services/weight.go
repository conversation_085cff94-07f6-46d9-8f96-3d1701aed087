package services

import (
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

// ModifyWeightByZones 根据zones配置调整pod的权重
func ModifyWeightByZones(pod *corev1.Pod, weight *int64, zones []types.ZoneWeight) {
	if pod == nil || weight == nil {
		return
	}

	for _, item := range zones {
		if PodInZone(pod, item.Zone) {
			// 如果是百分比，则向下取整
			newWeight, err := intstr.GetScaledValueFromIntOrPercent(&item.Weight, int(*weight), false)
			// 忽略错误配置，不影响原有逻辑
			if err == nil {
				*weight = int64(newWeight)
				return
			}
		}
	}
}

// PodInZone 判定当前pod是否在指定zone中，只有成功获取到node时才进行判断，否则都返回false
func PodInZone(pod *corev1.Pod, zone string) bool {
	node, err := cluster.Instance.NodeLister().Get(pod.Spec.NodeName)
	if err != nil {
		return false
	}

	nodeZone, _ := types.NewNode(node).Zone()

	return nodeZone == zone
}
