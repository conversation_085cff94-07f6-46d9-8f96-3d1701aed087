package services

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"slices"
	"strings"

	"github.com/fvbommel/sortorder"
	"github.com/samber/lo"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/util/intstr"
	listerscorev1 "k8s.io/client-go/listers/core/v1"

	tscv1alpha1 "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/domain/errdef"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/k8s/k8sutil/annotation"
	"git.woa.com/kateway/pkg/net"
	"git.woa.com/kateway/pkg/sets"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
)

var (
	booleanParseOpt = annotation.WithCustomParseFunc(types.ParseBool)
)

const (
	DefaultPath           = "/"
	DefaultTLSSecretName  = ""
	DefaultMaxAllowedCrts = 2
)

var (
	ErrHostTLSConflict            = errors.New("host tls conflict")
	ErrHostNoMatchingTLSSecret    = errors.New("host no matching tls secret")
	ErrDefaultHostTLSConflict     = errors.New("default host tls conflict")
	ErrEmptyTLSSecretName         = errors.New("empty tls secret name")
	ErrTLSSecretNotFound          = errors.New("tls secret not found")
	ErrInvalidTLSSecretContent    = errors.New("invalid tls secret content")
	ErrInvalidPortValue           = errors.New("invalid port value")
	ErrEmptyRuleHost              = errors.New("empty rule host")
	ErrIllegalRuleHost            = errors.New("illegal rule host format")
	ErrPathLenLimitExceeded       = errors.New("path length limit exceeded")
	ErrIllegalRegexPath           = errors.New("illegal regex path")
	ErrIllegalPathFormat          = errors.New("illegal path format")
	ErrInvalidAnnotationRules     = errors.New("invalid annotation rules")
	ErrAnnotationRuleConflict     = errors.New("annotation rule conflict")
	ErrRewriteRuleConflict        = errors.New("auto rewrite rule conflict")
	ErrListenPortConflict         = errors.New("listen port conflict")
	ErrInvalidRewritePathType     = errors.New("invalid rewrite path type")
	ErrBackendServicePortNotFound = errors.New("backend service port not found")
	ErrBackendServiceNotFound     = errors.New("backend service not found")
	ErrInvalidBackendServiceType  = errors.New("invalid backend service type")
	ErrCLBPathTypeConflict        = errors.New("clb path type conflict")
	ErrCLBPathTypeUnsupported     = errors.New("clb path type unsupported")
	ErrAutoRewritePortRequired    = errors.New("auto rewrite port required")
	ErrListenProtocolUnsupported  = errors.New("listen protocol unsupported")
)

var (
	constraintRuleHost  = regexp.MustCompile(`(\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)+`)
	constraintRegexPath = regexp.MustCompile(`^[\w-_/.?=:^*$()\[\]+|\\]*$`)
	constraintExactPath = regexp.MustCompile(`^[\w-_/.?=:]*$`)
)

type ListenerService struct {
	svcLister     listerscorev1.ServiceLister
	secretLister  listerscorev1.SecretLister
	tscSvc        *TKEServiceConfigService
	defaultDomain *string
	ruleQuota     int
}

func NewListenerService(tscSvc *TKEServiceConfigService, svcLister listerscorev1.ServiceLister, secretLister listerscorev1.SecretLister,
	defaultDomain *string, ruleQuota int) *ListenerService {
	return &ListenerService{
		svcLister:     svcLister,
		secretLister:  secretLister,
		tscSvc:        tscSvc,
		defaultDomain: defaultDomain,
		ruleQuota:     ruleQuota,
	}
}

func (svc ListenerService) BuildIngressListeners(ctx context.Context, ing types.Ingress) ([]types.Listener, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	portsByProtocol, err := svc.determineIngressPorts(ctx, ing)
	if err != nil {
		return nil, errdef.NewFatal(err)
	}
	var (
		rewriteEnabled, autoRewriteEnabled, ruleMix bool
		annos                                       = map[string]string{}
	)
	if len(ing.Annotations()) != 0 {
		annos = ing.Annotations()
	}
	for _, item := range []struct {
		key string
		obj any
	}{
		{
			key: types.IngressAnnotationRuleMix,
			obj: &ruleMix,
		},
		{
			key: types.IngressAnnotationRewriteSupport,
			obj: &rewriteEnabled,
		},
		{
			key: types.IngressAnnotationAutoRewrite,
			obj: &autoRewriteEnabled,
		},
	} {
		_, _ = annotation.ParseInto(annos, item.key, item.obj, booleanParseOpt)
	}

	_, autoRewriteKeyExists := annos[types.IngressAnnotationAutoRewrite]
	// 兼容旧代码逻辑，TODO：去除这种奇怪的开关逻辑
	rewriteEnabled = rewriteEnabled || autoRewriteKeyExists

	listeners, err := svc.buildIngressListeners(ctx, ing, portsByProtocol, rewriteEnabled, autoRewriteEnabled, ruleMix)
	// 原有逻辑会默认创建HTTP:80的监听器，这里兼容这个逻辑，但如果其他HTTP监听器没有有效的转发规则，则不创建
	return lo.Filter(listeners, func(lis types.Listener, _ int) bool {
		if lis.Port == 80 && lis.Protocol == net.ProtocolHTTP {
			return true
		}
		return len(lis.Rules) > 0
	}), err
}

func (ListenerService) chooseSecret(host string, hostSecrets map[string]string) *string {
	if s, exists := hostSecrets[host]; exists {
		return lo.ToPtr(s)
	}
	// [https://cloud.tencent.com/document/product/214/9032#.E8.BD.AC.E5.8F.91.E5.9F.9F.E5.90.8D.E9.85.8D.E7.BD.AE.E8.A7.84.E5.88.99]
	// 2. 尝试使用泛域名证书。如果是泛域名只能使用完全匹配的泛域名证书。
	//		1. 只支持一级泛域名
	//		2. 只支持精准域名匹配泛域名证书
	if !strings.HasPrefix(host, "*.") {
		pointIndex := strings.Index(host, ".")
		if pointIndex != -1 {
			wildcardDomain := fmt.Sprintf("*%s", host[pointIndex:]) // 拼装域名对应泛域名
			if s, exist := hostSecrets[wildcardDomain]; exist {
				return lo.ToPtr(s)
			}
		}
	}

	// 3. 尝试使用用户默认证书
	if s, exist := hostSecrets[DefaultTLSSecretName]; exist {
		return lo.ToPtr(s)
	}

	// 没有合适的证书可以提供使用
	return nil
}

func (ListenerService) buildHostTLSSecretMap(ing types.Ingress) (map[string]string, error) {
	errs := []error{}
	secrets := map[string]string{}
	for _, tls := range ing.TLS() {
		if len(tls.Hosts) == 0 {
			if secret, exists := secrets[DefaultTLSSecretName]; exists && secret != tls.SecretName {
				errs = append(errs, fmt.Errorf("conflicting default TLS secret name %q: %w", tls.SecretName, ErrDefaultHostTLSConflict))
				continue
			}
			secrets[DefaultTLSSecretName] = tls.SecretName
		} else {
			for _, host := range tls.Hosts {
				if secret, exists := secrets[host]; exists && secret != tls.SecretName {
					errs = append(errs, fmt.Errorf("conflicting TLS secret name %q for host %q: %w", tls.SecretName, host, ErrHostTLSConflict))
					continue
				}
				secrets[host] = tls.SecretName
			}
		}
	}
	return secrets, errors.Join(errs...)
}

func (svc ListenerService) buildIngressHTTPListeners(ctx context.Context, ing types.Ingress, ports []int) ([]types.Listener, error) {
	rules, err := svc.buildIngressRules(ctx, ing)
	return lo.Map(ports, func(p int, _ int) types.Listener {
		return types.NewListener(p, net.ProtocolHTTP, rules)
	}), err
}

func (svc ListenerService) buildIngressRules(_ context.Context, ing types.Ingress) ([]types.ListenerRule, error) {
	var errs []error
	rules := []types.ListenerRule{}
	for _, r := range ing.Rules() {
		for _, p := range r.HTTPPaths {
			rule, err := svc.buildRule(r.Host, p.Path, buildCLBPathTypes(p.PathType), types.ListenerRuleAction{
				Forward: &types.ForwardActionConfig{
					ForwardBackendMeta: types.ForwardBackendMeta{
						ServiceName: p.Backend.ServiceName,
						ServicePort: p.Backend.ServicePort,
					},
				},
			})
			if err != nil {
				errs = append(errs, err)
				continue
			}
			rules = append(rules, *rule)
		}
	}
	return uniqueRules(rules), errors.Join(errs...)
}

func (svc ListenerService) buildIngressHTTPSListeners(ctx context.Context, ing types.Ingress, portsByProtocol map[string][]int, rewriteEnabled, autoRewriteEnabled bool) ([]types.Listener, error) {
	var errs []error
	httpsRules, err := svc.buildIngressRules(ctx, ing)
	if err != nil {
		errs = append(errs, err)
	}
	listeners, err := svc.buildIngressListenersWithRewrite(ctx, portsByProtocol, nil, httpsRules, nil, nil, rewriteEnabled, autoRewriteEnabled, false)
	if err != nil {
		errs = append(errs, err)
	}
	return listeners, errors.Join(errs...)
}

// nolint: funlen,gocyclo
func (svc ListenerService) buildIngressListenersWithRewrite(_ context.Context, portsByProtocol map[string][]int, httpRules, httpsRules []types.ListenerRule,
	httpRewriteRules, httpsRewriteRules []types.IngressAnnotationRule, rewriteEnabled, autoRewriteEnabled, nonAutoRewriteEnabled bool) ([]types.Listener, error) {
	var (
		errs      []error
		listeners []types.Listener
	)
	for protocol, ports := range portsByProtocol {
		listeners = append(listeners, lo.Map(ports, func(p int, _ int) types.Listener {
			return types.NewListener(p, protocol, lo.Ternary(protocol == net.ProtocolHTTP, httpRules, httpsRules))
		})...)
	}

	if !rewriteEnabled {
		return listeners, nil
	}
	if autoRewriteEnabled {
		if !lo.Contains(portsByProtocol[net.ProtocolHTTP], 80) || !lo.Contains(portsByProtocol[net.ProtocolHTTPS], 443) {
			return listeners, fmt.Errorf("auto rewrite requests for HTTP:80 and HTTPS:443: %w", ErrAutoRewritePortRequired)
		}

		_, i, _ := lo.FindIndexOf(listeners, func(l types.Listener) bool { return l.Protocol == net.ProtocolHTTP && l.Port == 80 })
		http80Listener := &listeners[i]
		for _, r := range httpsRules {
			action := types.ListenerRuleAction{
				Rewrite: &types.RewriteActionConfig{
					Host:    r.Host,
					Path:    r.Path,
					CLBPath: r.CLBPath,
					Port:    443,
					Auto:    true,
				},
			}
			sourceRules := http80Listener.FindRules(func(item types.ListenerRule) bool {
				return item.Host == r.Host && item.CLBPath == r.CLBPath
			})
			if len(sourceRules) > 0 {
				errs = append(errs, fmt.Errorf("existing rule(%s%s) of listener HTTP:80 will be overwritten by the rewrite rule with target %s%s: %w",
					r.Host, r.CLBPath, r.Host, r.Path, ErrRewriteRuleConflict))
				for _, sr := range sourceRules {
					sr.CLBPathTypes = r.CLBPathTypes
					sr.Action = action
				}
			} else {
				http80Listener.AddRules(types.NewListenerRule(r.Host, r.Path, r.CLBPath, r.CLBPathTypes, action))
			}
		}
	} else if nonAutoRewriteEnabled {
		buildNonAutoRewrite := func(sourceProtocol string, annoRewriteRules []types.IngressAnnotationRule) {
			for _, rr := range annoRewriteRules {
				if len(rr.CLBPathTypes) > 0 {
					errs = append(errs, ErrInvalidRewritePathType)
					continue
				}
				rewriteTarget := rr.Rewrite
				if targetListener, exists := lo.Find(listeners, func(lis types.Listener) bool { return lis.Port == int(rewriteTarget.Port) }); exists {
					targetHost, err := svc.determineRuleHost(rewriteTarget.Host)
					if err != nil {
						errs = append(errs, err)
						continue
					}
					if rewriteTarget.Path == "" {
						rewriteTarget.Path = DefaultPath
					}
					if targetRules := targetListener.FindRulesByMeta(types.NewListenerRuleMeta(targetHost, rewriteTarget.Path)); len(targetRules) > 0 {
						targetRule, exists := lo.Find(targetRules, func(r *types.ListenerRule) bool { return len(r.CLBPathTypes) == 0 })
						if !exists {
							errs = append(errs, ErrInvalidRewritePathType)
							continue
						}
						rewriteRule, err := svc.buildRule(rr.Host, rr.Path, nil, types.ListenerRuleAction{
							Rewrite: &types.RewriteActionConfig{
								Host:    targetRule.Host,
								Path:    targetRule.Path,
								CLBPath: targetRule.CLBPath,
								Port:    int(rewriteTarget.Port),
							},
						})
						if err != nil {
							errs = append(errs, err)
							continue
						}
						for i := range listeners {
							l := &listeners[i]
							if l.Protocol != sourceProtocol {
								continue
							}
							sourceRules := l.FindRules(func(r types.ListenerRule) bool { return r.Host == rewriteRule.Host && r.CLBPath == rewriteRule.CLBPath })
							if len(sourceRules) > 0 {
								// 已有的转发规则和自定义的重写规则冲突，添加error并覆盖
								source := sourceRules[0]
								errs = append(errs, fmt.Errorf("existing rule(%s%s) of listener %s:%d will be overwritten by the rewrite rule with target %s%s: %w",
									source.Host, source.CLBPath, l.Protocol, l.Port, rewriteRule.Host, rewriteRule.Path, ErrRewriteRuleConflict))
								for _, r := range sourceRules {
									r.CLBPathTypes = nil
									r.Action = rewriteRule.Action
								}
							} else {
								l.AddRules(*rewriteRule)
							}
						}
					}
				}
			}
		}
		buildNonAutoRewrite(net.ProtocolHTTP, httpRewriteRules)
		buildNonAutoRewrite(net.ProtocolHTTPS, httpsRewriteRules)
	}

	return listeners, errors.Join(errs...)
}

func (svc ListenerService) buildIngressMixedListeners(ctx context.Context, ing types.Ingress, portsByProtocol map[string][]int,
	httpRulesMgr, httpsRulesMgr annotationRuleMgr, rewriteEnabled, autoRewriteEnabled bool) ([]types.Listener, error) {

	var (
		errs                  []error
		httpRules, httpsRules []types.ListenerRule
	)
	for _, r := range ing.Rules() {
		host, err := svc.determineRuleHost(r.Host)
		if err != nil {
			errs = append(errs, err)
			continue
		}
		for _, p := range r.HTTPPaths {
			if p.Path == "" {
				p.Path = DefaultPath
			}
			key := types.AnnotationRuleKey{
				ListenerRuleMeta:   types.NewListenerRuleMeta(host, p.Path),
				BackendServiceName: p.Backend.ServiceName,
				BackendServicePort: p.Backend.ServicePort.String(),
			}

			var (
				annoHTTPRule     = httpRulesMgr.FindForward(key)
				annoHTTPSRule    = httpsRulesMgr.FindForward(key)
				annoHTTPRewrite  = httpRulesMgr.FindRewrite(key.ListenerRuleMeta)
				annoHTTPSRewrite = httpsRulesMgr.FindRewrite(key.ListenerRuleMeta)
			)
			action := types.ListenerRuleAction{
				Forward: &types.ForwardActionConfig{
					ForwardBackendMeta: types.ForwardBackendMeta{
						ServiceName: p.Backend.ServiceName,
						ServicePort: p.Backend.ServicePort,
					},
				},
			}
			add := func(target *[]types.ListenerRule, pathTypes []types.CLBPathType) {
				if len(pathTypes) == 0 {
					pathTypes = buildCLBPathTypes(p.PathType)
				}
				r, err := svc.buildRule(host, p.Path, pathTypes, action)
				if err != nil {
					errs = append(errs, err)
					return
				}
				*target = append(*target, *r)
			}
			if annoHTTPRule != nil {
				add(&httpRules, annoHTTPRule.CLBPathTypes)
			}
			if annoHTTPSRule != nil {
				add(&httpsRules, annoHTTPSRule.CLBPathTypes)
			}
			// 既没有在http-rules中定义也没有在https-rules中定义，默认加入到https规则中
			if annoHTTPRule == nil && annoHTTPSRule == nil && annoHTTPRewrite == nil && annoHTTPSRewrite == nil {
				add(&httpsRules, nil)
			}
		}
	}

	httpRules = uniqueRules(httpRules)
	httpsRules = uniqueRules(httpsRules)
	listeners, err := svc.buildIngressListenersWithRewrite(ctx, portsByProtocol, httpRules, httpsRules,
		httpRulesMgr.GetRewriteRules(), httpsRulesMgr.GetRewriteRules(), rewriteEnabled, autoRewriteEnabled, true)
	if err != nil {
		errs = append(errs, err)
	}
	return listeners, errors.Join(errs...)
}

// nolint: gocyclo
func (svc ListenerService) buildIngressListeners(ctx context.Context, ing types.Ingress, portsByProtocol map[string][]int,
	rewriteEnabled, autoRewriteEnabled, mixedRuleEnabled bool) ([]types.Listener, error) {
	span, _ := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	var (
		preferTLS = len(ing.TLS()) > 0
		listeners []types.Listener
		secrets   = map[string]string{}
		annos     = map[string]string{}
		errs      []error
	)
	if !mixedRuleEnabled {
		var e error
		if preferTLS {
			listeners, e = svc.buildIngressHTTPSListeners(ctx, ing, portsByProtocol, rewriteEnabled, autoRewriteEnabled)
		} else {
			listeners, e = svc.buildIngressHTTPListeners(ctx, ing, portsByProtocol[net.ProtocolHTTP])
		}
		if e != nil {
			errs = append(errs, e)
		}
	} else {
		var annoHTTPRules, annoHTTPSRules []types.IngressAnnotationRule
		if len(ing.Annotations()) > 0 {
			annos = ing.Annotations()
		}
		if _, err := annotation.ParseInto(annos, types.IngressAnnotationHTTPRules, &annoHTTPRules); err != nil {
			return nil, errdef.NewFatal(fmt.Errorf("invalid http-rules: %w", ErrInvalidAnnotationRules))
		}
		if _, err := annotation.ParseInto(annos, types.IngressAnnotationHTTPSRules, &annoHTTPSRules); err != nil {
			return nil, errdef.NewFatal(fmt.Errorf("invalid https-rules: %w", ErrInvalidAnnotationRules))
		}
		httpMgr, err := svc.buildAnnotationRuleMgr(annoHTTPRules)
		if err != nil {
			return nil, errdef.NewFatal(err)
		}

		// 原有逻辑仅对https-rules进行了校验
		if err = svc.validateAnnotationRules(annoHTTPSRules); err != nil {
			return nil, errdef.NewFatal(err)
		}
		httpsMgr, err := svc.buildAnnotationRuleMgr(annoHTTPSRules)
		if err != nil {
			return nil, errdef.NewFatal(err)
		}
		listeners, err = svc.buildIngressMixedListeners(ctx, ing, portsByProtocol, *httpMgr, *httpsMgr, rewriteEnabled, autoRewriteEnabled)
		if err != nil {
			errs = append(errs, err)
		}
	}
	if preferTLS {
		var err error
		secrets, err = svc.buildHostTLSSecretMap(ing)
		if err != nil {
			errs = append(errs, err)
		}
	}
	if ing.Type() == types.CoreIngress && !types.IsIngressBackendOnly(ing) || ing.Type() == types.MultiClusterIngress {
		if err := svc.populateIngressListenerTLS(ctx, ing, listeners, secrets); err != nil {
			errs = append(errs, err)
		}
	}
	if ing.Type() != types.MultiClusterIngress {
		if err := svc.populateTargetPorts(ctx, ing, listeners); err != nil {
			if errdef.IsFatal(err) {
				return nil, err
			}
			errs = append(errs, err)
		}
	}
	if err := svc.loadTKEServiceConfig(ctx, ing, listeners); err != nil {
		if errdef.IsFatal(err) {
			return nil, err
		}
		errs = append(errs, err)
	}
	svc.truncateRules(listeners)

	return listeners, errors.Join(errs...)
}

func (ListenerService) validateAnnotationRules(rules []types.IngressAnnotationRule) error {
	filter := func(pathTypes, targets []types.CLBPathType) []types.CLBPathType {
		return lo.Filter(pathTypes, func(t types.CLBPathType, _ int) bool { return lo.Contains(targets, t) })
	}
	for _, r := range rules {
		mainTypes := filter(r.CLBPathTypes, types.MainCLBPathTypes)
		if len(mainTypes) > 1 {
			return fmt.Errorf("main types %v conflict: %w", mainTypes, ErrCLBPathTypeConflict)
		}
		subTypes := filter(r.CLBPathTypes, types.SubCLBPathTypes)
		if len(subTypes) > 1 {
			return fmt.Errorf("sub types %v conflict: %w", subTypes, ErrCLBPathTypeConflict)
		}
		invalidTypes := lo.Filter(r.CLBPathTypes, func(t types.CLBPathType, _ int) bool { return !lo.Contains(types.ValidCLBPathTypes, t) })
		if len(invalidTypes) > 0 {
			return fmt.Errorf("invalid path types %v: %w", invalidTypes, ErrCLBPathTypeUnsupported)
		}
	}
	return nil
}

func (svc ListenerService) truncateRules(listeners []types.Listener) {
	if svc.ruleQuota == 0 {
		return
	}
	for i := range listeners {
		lis := &listeners[i]
		rules := lis.GetForwardRules()
		slices.SortFunc(rules, func(a, b types.ListenerRule) int {
			aa := fmt.Sprintf("%s_%s", a.Host, a.CLBPath)
			bb := fmt.Sprintf("%s_%s", b.Host, b.CLBPath)
			if aa == bb {
				return 0
			}
			if sortorder.NaturalLess(aa, bb) {
				return -1
			}
			return 1
		})
		if len(rules) > svc.ruleQuota {
			rules = rules[:svc.ruleQuota]
		}
		lis.SetRules(append(rules, lis.GetRewriteRules()...))
	}
}

// nolint:gocyclo
func (svc ListenerService) loadTKEServiceConfig(ctx context.Context, ing types.Ingress, listeners []types.Listener) error {
	cfg, err := svc.tscSvc.GetIngressConfig(ctx, ing)
	if err != nil {
		return err
	}
	if cfg == nil {
		return nil
	}
	listenerCfgByMeta := lo.SliceToMap(cfg.Spec.LoadBalancer.L7Listeners, func(cfg *tscv1alpha1.L7ListenerConfig) (types.ListenerMeta, *tscv1alpha1.L7ListenerConfig) {
		return types.ListenerMeta{Protocol: strings.ToUpper(cfg.Protocol), Port: int(cfg.Port)}, cfg
	})
	for i := range listeners {
		lis := &listeners[i]
		if cfg, exists := listenerCfgByMeta[lis.ListenerMeta]; exists {
			lis.ListenerConfig = cfg
			if cfg.DefaultServer != nil {
				defaultSvr := *cfg.DefaultServer
				if defaultSvr == "" && svc.defaultDomain != nil {
					defaultSvr = *svc.defaultDomain
				}
				if defaultSvr != "" {
					rules, exists := lis.Rules[defaultSvr]
					if exists {
						for i := range rules {
							rules[i].Default = true
						}
					}
				}
			}
			for _, domainCfg := range cfg.Domains {
				domain := domainCfg.Domain
				if domain == "" && svc.defaultDomain != nil {
					domain = *svc.defaultDomain
				}
				rules, exists := lis.Rules[domain]
				if exists {
					rules = lo.Map(rules, func(r types.ListenerRule, _ int) types.ListenerRule {
						r.HTTP2Enabled = domainCfg.Http2
						return r
					})
					for _, ruleCfg := range domainCfg.Rules {
						if ruleCfg.HealthCheck != nil && ruleCfg.HealthCheck.CheckType != nil &&
							*ruleCfg.HealthCheck.CheckType == net.ProtocolTCP {
							ruleCfg.HealthCheck.HttpCode = nil
							ruleCfg.HealthCheck.HttpCheckPath = nil
							ruleCfg.HealthCheck.HttpCheckDomain = nil
							ruleCfg.HealthCheck.HttpCheckMethod = nil
						}
						for i, r := range rules {
							if r.CLBPath == ruleCfg.Url {
								rules[i].RuleConfig = ruleCfg
							}
						}
					}
					lis.Rules[domain] = rules
				}
			}
		}
	}
	return nil
}

func (svc ListenerService) populateTargetPorts(_ context.Context, ing types.Ingress, listeners []types.Listener) error {
	var (
		errs          []error
		backends      []types.ForwardBackendMeta
		portByBackend = map[types.ForwardBackendMeta]intstr.IntOrString{}
	)
	for _, lis := range listeners {
		for _, r := range lis.FlattenRules() {
			if r.Action.Forward != nil {
				backends = append(backends, r.Action.Forward.ForwardBackendMeta)
			}
		}
	}
	for svcName, metas := range lo.GroupBy(backends, func(b types.ForwardBackendMeta) string { return b.ServiceName }) {
		coreSvc, err := svc.svcLister.Services(ing.Namespace()).Get(svcName)
		if err != nil {
			if k8serrors.IsNotFound(err) {
				if !types.IsIngressBackendOnly(ing) {
					errs = append(errs, fmt.Errorf("service %s/%s not found: %w", ing.Namespace(), svcName, ErrBackendServiceNotFound))
				}
				continue
			}
			return errdef.NewFatal(err)
		}
		s := types.NewService(coreSvc)
		directAccess := types.IsIngressCascadedDirectAccess(ing, s)
		if s.Spec.Type != corev1.ServiceTypeLoadBalancer && s.Spec.Type != corev1.ServiceTypeNodePort &&
			(!directAccess || (directAccess && s.Spec.Type != corev1.ServiceTypeClusterIP)) {
			errs = append(errs, fmt.Errorf("unable to use the service %s/%s as the backend of the ingress, only services of type node-port or loadbalancer are supported: %w", coreSvc.Namespace, coreSvc.Name, ErrInvalidBackendServiceType))
			continue
		}
		for _, meta := range metas {
			port := s.FindPort(meta.ServicePort)
			if port != nil {
				portByBackend[meta] = *port
			} else {
				errs = append(errs, fmt.Errorf("unable to find port %q in service %s: %w",
					meta.ServicePort.String(), meta.ServiceName, ErrBackendServicePortNotFound))
			}
		}
	}
	for i := range listeners {
		lis := &listeners[i]
		for _, rules := range lis.Rules {
			for j := range rules {
				r := &rules[j]
				if r.Action.Forward != nil {
					if p, exists := portByBackend[r.Action.Forward.ForwardBackendMeta]; exists {
						r.Action.Forward.TargetPort = &p
					}
				}
			}
		}
	}
	return errors.Join(errs...)
}

func (svc ListenerService) populateIngressListenerTLS(ctx context.Context, ing types.Ingress, listeners []types.Listener, secrets map[string]string) error {
	var (
		errs            []error
		tlsConfigByName = map[string]types.TLSConfig{}
	)
	for i := range listeners {
		lis := &listeners[i]
		if lis.Protocol != net.ProtocolHTTPS {
			continue
		}
		for host, rules := range lis.Rules {
			secretName := svc.chooseSecret(host, secrets)
			if secretName == nil {
				errs = append(errs, fmt.Errorf("no matching tls secret for host %q: %w", host, ErrHostNoMatchingTLSSecret))
				continue
			}
			tlsCfg, exists := tlsConfigByName[*secretName]
			if !exists {
				cfg, err := svc.buildTLSConfig(ctx, ing.Namespace(), *secretName)
				if err != nil {
					errs = append(errs, err)
					continue
				}
				tlsCfg = *cfg
			}
			for i := range rules {
				rules[i].TLSConfig = &tlsCfg
			}
		}
	}
	return errors.Join(errs...)
}

func (svc ListenerService) buildAnnotationRuleMgr(annotationRules []types.IngressAnnotationRule) (*annotationRuleMgr, error) {
	forwards := map[types.AnnotationRuleKey]types.IngressAnnotationRule{}
	rewrites := map[types.ListenerRuleMeta]types.IngressAnnotationRule{}
	for i := range annotationRules {
		r := &annotationRules[i]
		host, err := svc.determineRuleHost(r.Host)
		if err != nil {
			continue
		}
		r.Host = host
		if r.Path == "" {
			r.Path = DefaultPath
		}
		if (!r.IsForward() && !r.IsRewrite()) || (r.IsForward() && r.IsRewrite()) {
			return nil, fmt.Errorf("annotation rule %s%s must have either backend or rewrite: %w", r.Host, r.Path, ErrInvalidAnnotationRules)
		}
		if r.IsForward() {
			if _, exists := forwards[r.GetRuleKey()]; exists {
				return nil, fmt.Errorf("annotation rule %s%s is duplicated: %w", r.Host, r.Path, ErrAnnotationRuleConflict)
			}
			forwards[r.GetRuleKey()] = *r
		}
		if r.IsRewrite() {
			if _, exists := rewrites[r.GetRuleMeta()]; exists {
				return nil, fmt.Errorf("annotation rule %s%s is duplicated: %w", r.Host, r.Path, ErrAnnotationRuleConflict)
			}
			rewrites[r.GetRuleMeta()] = *r
		}
	}
	return &annotationRuleMgr{
		forwards: forwards,
		rewrites: rewrites,
	}, nil
}

type annotationRuleMgr struct {
	forwards map[types.AnnotationRuleKey]types.IngressAnnotationRule
	rewrites map[types.ListenerRuleMeta]types.IngressAnnotationRule
}

func (mgr annotationRuleMgr) FindForward(key types.AnnotationRuleKey) *types.IngressAnnotationRule {
	r, exists := mgr.forwards[key]
	if !exists {
		return nil
	}
	return &r
}

func (mgr annotationRuleMgr) FindRewrite(meta types.ListenerRuleMeta) *types.IngressAnnotationRule {
	r, exists := mgr.rewrites[meta]
	if !exists {
		return nil
	}
	return &r
}

func (mgr annotationRuleMgr) GetRewriteRules() []types.IngressAnnotationRule {
	return lo.Values(mgr.rewrites)
}

func (svc ListenerService) buildRule(host, path string, pathTypes []types.CLBPathType, action types.ListenerRuleAction) (*types.ListenerRule, error) {
	host, err := svc.determineRuleHost(host)
	if err != nil {
		return nil, err
	}
	if path == "" {
		path = DefaultPath
	}
	clbPath, err := svc.buildCLBPath(path, pathTypes)
	if err != nil {
		return nil, err
	}
	return lo.ToPtr(types.NewListenerRule(host, path, clbPath, pathTypes, action)), nil
}

func (ListenerService) buildCLBPath(path string, pathTypes []types.CLBPathType) (string, error) {
	_, nonAbsolute := lo.Find(pathTypes, func(t types.CLBPathType) bool { return t == types.CLBPathTypeNonAbsolute })
	if nonAbsolute {
		path = strings.TrimPrefix(path, "/")
	}
	for _, pathType := range pathTypes {
		if pathType == types.CLBPathTypeRegex || pathType == types.CLBPathTypeRegexIgnoreCase {
			if !constraintRegexPath.MatchString(path) {
				return "", fmt.Errorf("invalid path %q, path of type regex can only contain letters, numbers, and characters `-/.\\%%?=:#&^*[]$.`: %w", path, ErrIllegalRegexPath)
			}
			if _, err := regexp.Compile(path); err != nil {
				return "", fmt.Errorf("path %q is not a valid regex: %w", path, ErrIllegalRegexPath)
			}
			path = pathType.GetPrefix() + path
		}
		if pathType == types.CLBPathTypeExact {
			if nonAbsolute {
				return "", fmt.Errorf("invalid path %q, path of type exact match must be absolute: %w", path, ErrIllegalPathFormat)
			}
			if !constraintExactPath.MatchString(path) {
				return "", fmt.Errorf("invalid path %q, path of type exact match can only contain letters, numbers, and character `-/.\\%%?=:#&.`: %w", path, ErrIllegalPathFormat)
			}
			path = pathType.GetPrefix() + path
		}
	}
	if len(path) > 200 {
		return "", fmt.Errorf("path %q is too long: %w", path, ErrPathLenLimitExceeded)
	}
	return path, nil
}

func (svc ListenerService) determineRuleHost(host string) (string, error) {
	if host == "" {
		if svc.defaultDomain == nil {
			return "", ErrEmptyRuleHost
		}
		return *svc.defaultDomain, nil
	}
	if !constraintRuleHost.MatchString(host) {
		return "", fmt.Errorf("illegal host %q: %w", host, ErrIllegalRuleHost)
	}
	return host, nil
}

// processCertIDs 处理输入字符串，分割并去除每个片段的首尾空格
func ProcessCertIDs(certIDs string) ([]string, error) {
	// 空字符串判断
	if certIDs == "" {
		return nil, ErrInvalidTLSSecretContent
	}
	// 切分字符串
	result := types.SplitStrings(certIDs, ",", false)
	// check切片元素数量
	if len(result) == 0 || len(result) > 2 {
		return nil, ErrInvalidTLSSecretContent
	}
	// 返回结果
	return result, nil
}

func (svc ListenerService) buildTLSConfig(_ context.Context, ns, secretName string) (*types.TLSConfig, error) {
	if secretName == "" {
		return nil, ErrEmptyTLSSecretName
	}
	secret, err := svc.secretLister.Secrets(ns).Get(secretName)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return nil, fmt.Errorf("tls secret [%s/%s] not found: %w", ns, secretName, ErrTLSSecretNotFound)
		}
		return nil, err
	}

	certID, caCertID, err := GetCertID(secret)
	if err != nil {
		return nil, err
	}
	// 多证书逻辑
	var slices []string
	// 解析 qcloud_cert_ids 列表
	if len(certID) > 0 {
		slices, err = ProcessCertIDs(certID)
		if err != nil {
			return nil, err
		}
	}
	// Oquaqe secret逻辑
	return &types.TLSConfig{
		CertificateIDs:  sets.New(slices...),
		CACertificateID: caCertID,
		SSLMode:         lo.Ternary(caCertID == nil, types.SSLModeUnidirectional, types.SSLModeMutual),
	}, nil
}

func GetCertID(secret *v1.Secret) (string, *string, error) {
	certID, caCertID, err := GetDataCertID(secret)
	// 如果没有报错，或者secret类型不是TLS
	if err == nil || secret.Type != v1.SecretTypeTLS {
		return certID, caCertID, err
	}
	// data区域没有服务器证书ID，尝试从注解区域获取证书ID（tls secret）
	return GetAnnoCertID(secret)
}

func GetDataCertID(secret *v1.Secret) (string, *string, error) {
	certID, exists := secret.Data["qcloud_cert_id"]
	if !exists {
		return "", nil, fmt.Errorf("tls secret [%s/%s] does not contain data \"qcloud_cert_id\": %w", secret.Namespace, secret.Name, ErrInvalidTLSSecretContent)
	}
	var caCertID *string
	// 只判断键是否存在，键存在就不管内容（兼容存量逻辑）
	if id, exists := secret.Data["qcloud_ca_cert_id"]; exists {
		caCertID = lo.ToPtr(string(id))
	}
	return string(certID), caCertID, nil
}

func GetAnnoCertID(secret *v1.Secret) (string, *string, error) {
	if secret.Type != v1.SecretTypeTLS {
		return "", nil, fmt.Errorf("tls secret [%s/%s] type not match 'kubernetes.io/tls': %w", secret.Namespace, secret.Name, ErrInvalidTLSSecretContent)
	}
	// 尝试从 secret.Annotations 注解中获取证书 ID
	annoCertID, exists := secret.Annotations[types.AnnotationCertID]
	if !exists {
		return "", nil, fmt.Errorf("tls secret [%s/%s] does not contain annotation \"qcloud_cert_id\": %w", secret.Namespace, secret.Name, ErrInvalidTLSSecretContent)
	}
	// 查看Annotation区域的CA证书ID
	// 目前的代码逻辑，不会存在键存在且值为空的情况，键存在值也必然存在，所以无所谓判断 caAnnoID != ""
	if caAnnoID, exists := secret.Annotations[types.AnnotationCaCertID]; exists {
		return annoCertID, lo.ToPtr(caAnnoID), nil
	}
	return annoCertID, nil, nil
}

func (ListenerService) determineIngressPorts(_ context.Context, ing types.Ingress) (map[string][]int, error) {
	return ResolveListenPortsFromAnnotation(ing.Annotations(), len(ing.TLS()) > 0)
}

func ResolveListenPortsFromAnnotation(annotations map[string]string, preferTLS bool) (map[string][]int, error) {
	var (
		portsByProtocol = map[string][]int{}
	)
	entries, _ := annotation.Parse[[]map[string]int](annotations, types.IngressAnnotationListenerPorts, annotation.IgnoreEmptyValue)
	if entries != nil {
		for _, entry := range *entries {
			for protocol, port := range entry {
				if protocol != net.ProtocolHTTP && protocol != net.ProtocolHTTPS {
					return nil, fmt.Errorf("unsupported listen protocol %q: %w", protocol, ErrListenProtocolUnsupported)
				}
				if port < 1 || port > 65535 {
					return nil, fmt.Errorf("invalid port %d: %w", port, ErrInvalidPortValue)
				}
				ports := portsByProtocol[protocol]
				if !lo.Contains(ports, port) {
					ports = append(ports, port)
					portsByProtocol[protocol] = ports
				}
			}
		}
	}
	if _, exists := portsByProtocol[net.ProtocolHTTP]; !exists {
		portsByProtocol[net.ProtocolHTTP] = []int{80}
	}
	if preferTLS {
		if _, exists := portsByProtocol[net.ProtocolHTTPS]; !exists {
			portsByProtocol[net.ProtocolHTTPS] = []int{443}
		}
	} else {
		delete(portsByProtocol, net.ProtocolHTTPS)
	}
	httpPorts := portsByProtocol[net.ProtocolHTTP]
	httpsPorts := portsByProtocol[net.ProtocolHTTPS]
	if len(lo.Intersect(httpPorts, httpsPorts)) > 0 {
		return nil, fmt.Errorf("conflicting ports for protocol HTTP and HTTPS: %w", ErrListenPortConflict)
	}
	return portsByProtocol, nil
}

func buildCLBPathTypes(pathType string) []types.CLBPathType {
	return lo.Ternary(pathType == "Exact", []types.CLBPathType{types.CLBPathTypeExact}, nil)
}

func uniqueRules(rules []types.ListenerRule) []types.ListenerRule {
	return lo.UniqBy(rules, func(r types.ListenerRule) lo.Tuple4[string, string, string, types.ForwardBackendMeta] {
		return lo.Tuple4[string, string, string, types.ForwardBackendMeta]{A: r.Host, B: r.Path, C: r.CLBPath, D: r.Action.Forward.ForwardBackendMeta}
	})
}
