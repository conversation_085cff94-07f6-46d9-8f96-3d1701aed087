package services

import (
	"context"
	goerrors "errors"
	"fmt"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
	v12 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/util/retry"
	"k8s.io/client-go/util/workqueue"
	glog "k8s.io/klog/v2"

	"git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	loadbalancerresource "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/clientset/versioned"
	v1alpha12 "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/listers/loadbalancerresource/v1alpha1"

	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/runtime"
	"git.woa.com/kateway/pkg/sets"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
)

const (
	maxBatchLen                = 100               // DescribeLBListeners接口调用的最大批处理数量
	frequency                  = 5 * time.Second   // 工作函数触发频率
	removeHangingFinalizerFreq = 180 * time.Second // 兜底逻辑触发频率
	maxTime                    = 100 * time.Second // 100个key最大调谐耗时
)

type QueueState = bool

type ManagerType = string

// 为了更细粒度的过滤CLB以及更快速的检索，使用两层Map结构
type targetsSet = sets.Set[string]

type targetsSetByListener = map[string]targetsSet

type ListenersByCLB = map[string]targetsSetByListener

const (
	QueueStop  QueueState  = false
	QueueRun   QueueState  = true
	SvcManager ManagerType = "service"
	IngManager ManagerType = "ingress"
)

type NodeGracefulDeletionManagerParam struct {
	MockRun                    bool
	VpcID                      string
	CLBRsProtection            string // node 删除保护Finalizer
	RegionName                 string
	KubeClient                 *kubernetes.Clientset
	LoadBalancerResourceLister v1alpha12.LoadBalancerResourceLister
	LoadBalancerResourceClient loadbalancerresource.Clientset
	DescribeLBListenersAPI     func(ctx context.Context, request *clb.DescribeLBListenersRequest) (response *clb.DescribeLBListenersResponse, err error)
	NodeLister                 v12.NodeLister
	ManagerType                ManagerType // Manager类型：service 或者 ingress
}

type QueueKey struct {
	NodeName  string
	PrivateIP string
}

type TaskQueue struct {
	queueState atomic.Bool
	inner      workqueue.RateLimitingInterface
}

type NodeGracefulDeletionManager struct {
	param *NodeGracefulDeletionManagerParam
	Queue *TaskQueue
}

func NewNodeGracefulDeletionManager(param NodeGracefulDeletionManagerParam) *NodeGracefulDeletionManager {
	taskQueue := TaskQueue{
		queueState: atomic.Bool{},
		inner:      workqueue.NewNamedRateLimitingQueue(workqueue.NewItemExponentialFailureRateLimiter(time.Second, 15*time.Minute), "finalizer"),
	}
	taskQueue.queueState.Store(QueueStop)
	return &NodeGracefulDeletionManager{
		param: &param,
		Queue: &taskQueue,
	}
}

func (f *NodeGracefulDeletionManager) EnqueueNode(node *v1.Node) {
	nodeName := node.Name
	for _, addr := range node.Status.Addresses {
		if addr.Type == v1.NodeInternalIP {
			key := QueueKey{
				NodeName:  nodeName,
				PrivateIP: addr.Address,
			}
			f.enqueue(key)
		}
	}
}

func (f *NodeGracefulDeletionManager) enqueue(key QueueKey) {
	f.Queue.inner.Add(key)
}

func (f *NodeGracefulDeletionManager) requeue(key QueueKey) {
	f.Queue.inner.AddRateLimited(key)
}

func (f *NodeGracefulDeletionManager) Run(ctx context.Context) {
	defer func() {
		// panic捕获
		if r := recover(); r != nil {
			stack := runtime.GetPanicError(r)
			glog.Error(stack)
		}
	}()

	go func() {
		// [qingyangwu] 补充Ticker定时器的释放逻辑，否则会产生资源泄露，进而消耗CPU资源
		ticker := time.NewTicker(frequency)
		removeHangingFinalizerTicker := time.NewTicker(removeHangingFinalizerFreq)
		defer func() {
			ticker.Stop()
			removeHangingFinalizerTicker.Stop()
		}()
		for {
			select {
			case <-ctx.Done():
				glog.Info("NodeGracefulDeletionManager Done")
				return
			case <-ticker.C:
				f.worker(ctx)
			case <-removeHangingFinalizerTicker.C: // 兜底逻辑，去除掉节点上夯住的Finalizer
				f.EnsureRemoveIllegalFinalizer()
			}
		}
	}()
	err := f.enqueueAllNodes()
	if err != nil {
		panic(err)
	}
}

func (f *NodeGracefulDeletionManager) worker(ctx context.Context) {
	if f.doWork(ctx) {
		return
	}
}

func (f *NodeGracefulDeletionManager) doWork(ctx context.Context) bool {
	keyCount := f.Queue.inner.Len()
	if keyCount == 0 {
		return true
	}

	allKeys := sets.Set[QueueKey]{}
	for i := 0; i < keyCount; i++ {
		item, shutdown := f.Queue.inner.Get()
		if shutdown {
			break
		}
		key := item.(QueueKey)
		allKeys.Insert(key)
	}

	keysBatch := allKeys.UnsortedList()
	var wg sync.WaitGroup
	defer func(keysBatch []QueueKey) {
		for _, key := range keysBatch {
			f.Queue.inner.Done(key)
		}
		if r := recover(); r != nil {
			stack := runtime.GetPanicError(r)
			glog.Error(stack)
		}
	}(keysBatch)

	// 每 maxBatchLen 个元素启动一个协程
	glog.Infof("sync node protect finalizer, len:%d,isQueueRun:%v", len(keysBatch), f.isQueueRun())
	chunkSize := maxBatchLen
	for i := 0; i < len(keysBatch); i += chunkSize {
		end := i + chunkSize
		if end > len(keysBatch) {
			end = len(keysBatch)
		}
		wg.Add(1)
		go func(start, end int) {
			defer func() {
				wg.Done()
				if r := recover(); r != nil {
					stack := runtime.GetPanicError(r)
					glog.Error(stack)
				}
			}()
			chunkSet := sets.New(keysBatch[start:end]...)
			if successKeys, failedKeys, errs := f.sync(ctx, chunkSet); len(errs) != 0 {
				for key := range successKeys {
					f.Queue.inner.Forget(key)
				}
				for key := range failedKeys {
					f.requeue(key)
				}
				err := goerrors.Join(errs...)
				glog.Error(err.Error())
			} else {
				for key := range successKeys {
					f.Queue.inner.Forget(key)
				}
			}
		}(i, end)
	}

	wg.Wait()
	return false
}

func (f *NodeGracefulDeletionManager) Shutdown() {
	f.Queue.inner.ShutDown()
	f.SetQueueState(QueueStop)
}

func (f *NodeGracefulDeletionManager) SetQueueState(state QueueState) {
	f.Queue.queueState.Store(state)
}

func (f *NodeGracefulDeletionManager) isQueueRun() bool {
	return f.Queue.queueState.Load()
}

func (f *NodeGracefulDeletionManager) EnsureQueueState(EnableNodeGracefulDeletion bool) error {
	if EnableNodeGracefulDeletion && !f.isQueueRun() {
		return f.reStart()
	} else if !EnableNodeGracefulDeletion && f.isQueueRun() {
		return f.stop()
	}
	return nil
}

func (f *NodeGracefulDeletionManager) enqueueAllNodes() error {
	nodes, err := f.param.NodeLister.List(labels.Everything())
	if err != nil {
		glog.Errorf("Failed to list nodes: %v", err)
		return err
	}
	for _, node := range nodes {
		f.EnqueueNode(node)
	}
	return nil
}

func (f *NodeGracefulDeletionManager) reStart() error {
	f.SetQueueState(QueueRun)
	glog.Infof("Finalizer Queue State changed,now QueueRun")
	return f.enqueueAllNodes()
}

func (f *NodeGracefulDeletionManager) stop() error {
	f.SetQueueState(QueueStop)
	nodes, err := f.param.NodeLister.List(labels.Everything())
	if err != nil {
		glog.Errorf("stop() Failed to list nodes: %v", err)
		return err
	}
	for _, node := range nodes {
		f.EnqueueNode(node)
	}
	glog.Infof("Finalizer Queue State changed,now QueueStop")
	return nil
}

// 兜底逻辑函数
func (f *NodeGracefulDeletionManager) EnsureRemoveIllegalFinalizer() error {
	// TODO: 考虑更多的情况
	predicate := func(node *v1.Node) bool {
		// 节点删除
		cond1 := types.NewNode(node).HasWaitFinalizer() || types.NewNode(node).DeletionTimestamp != nil

		cond2 := false
		if len(node.Status.Conditions) == 0 {
			cond2 = true
		}
		// 节点状态异常
		for _, cond := range node.Status.Conditions {
			if cond.Type == v1.NodeReady && cond.Status != v1.ConditionTrue {
				cond2 = true
			}
		}
		if node.Labels != nil && types.NewNode(node).HasTargetLabel("node.kubernetes.io/exclude-from-external-load-balancers") {
			cond2 = true
		}
		// 节点异常且有目标Finalizer，筛选
		if (cond1 || cond2) && types.NewNode(node).HasTargetFinalizer(f.param.CLBRsProtection) {
			return true
		}
		return false
	}
	// lister拿取集群里的所有节点
	nodes, err := f.param.NodeLister.List(labels.Everything())
	if err != nil {
		glog.Errorf("NodeLister List error. error %v", err)
		return err
	}
	// 根据过滤条件过滤出符合条件的Node
	var filtered []*v1.Node
	for i := range nodes {
		if predicate(nodes[i]) {
			filtered = append(filtered, nodes[i])
		}
	}
	// 过滤节点批量入队
	for _, node := range filtered {
		f.EnqueueNode(node)
	}

	if len(filtered) != 0 {
		glog.Infof("The logic of the bottom line found %d nodes need to remove finalizer", len(filtered))
	}
	return nil
}

func (f *NodeGracefulDeletionManager) sync(ctx context.Context, keys sets.Set[QueueKey]) (sets.Set[QueueKey], sets.Set[QueueKey], []error) {
	successes := sets.New[QueueKey]()
	fails := sets.New[QueueKey]()
	errs := make([]error, 0)
	// [qingyangwu] 创建一个超时时间为 maxTime 的 context 上下文; 通过超时控制k8s client-go API的最大调用时长
	ctx1, cancel := context.WithTimeout(ctx, maxTime)
	defer cancel()
	if !f.isQueueRun() {
		for key := range keys {
			err := f.deleteFinalizerFromNode(ctx1, key.NodeName)
			if err != nil {
				errs = append(errs, err)
				fails.Insert(key)
				continue
			}
			successes.Insert(key)
		}
	} else {
		// CLBsByNode: privateIP->clbIDs
		// failedKeys: 通过clb/describeListeners以及查询LBR报错的key，这些key需要被加入到failedkeys中
		CLBsByNode, failedKeys, tmpErrs := f.getCLBsByNode(ctx, keys)
		keys = keys.Difference(failedKeys)
		for key := range keys {
			var err error
			if v, exist := CLBsByNode[key.PrivateIP]; !exist || len(v) == 0 {
				err = f.deleteFinalizerFromNode(ctx1, key.NodeName)
			} else {
				err = f.addFinalizerToNode(ctx1, key.NodeName)
			}
			if err != nil {
				tmpErrs = append(tmpErrs, err)
				failedKeys.Insert(key)
				continue
			}
			successes.Insert(key)
		}
		fails = fails.Union(failedKeys)
		errs = append(errs, tmpErrs...)
	}
	// 优化日志记录，一条日志打印所有成功、失败的key
	glog.Infof("NodeGracefulDeletionManager sync successKeys: %v, failedKeys: %v", successes.UnsortedList(), fails.UnsortedList())
	return successes, fails, errs
}

func (f *NodeGracefulDeletionManager) addFinalizerToNode(ctx context.Context, nodeName string) error {
	if f.param.MockRun {
		return nil
	}
	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		node, err := f.param.KubeClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
		if err != nil {
			return err
		}
		curFinalizers := node.ObjectMeta.Finalizers

		_, finalizerExist := lo.Find(curFinalizers, func(item string) bool {
			return item == f.param.CLBRsProtection
		})
		// 如果Finalizer存在，无需添加Finalizer，直接返回
		if finalizerExist {
			return nil
		}
		// 如果Finalizer不存在，添加
		node.ObjectMeta.Finalizers = append(node.ObjectMeta.Finalizers, f.param.CLBRsProtection)
		// 调用k8s client更新node资源
		if _, err := f.param.KubeClient.CoreV1().Nodes().Update(ctx, node, metav1.UpdateOptions{}); err != nil {
			glog.Error(err)
			return err
		}
		return nil
	})
	return err
}

func (f *NodeGracefulDeletionManager) deleteFinalizerFromNode(ctx context.Context, nodeName string) error {
	if f.param.MockRun {
		return nil
	}
	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		node, err := f.param.KubeClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
		if err != nil {
			return err
		}
		curFinalizers := node.ObjectMeta.Finalizers
		// 查询是否存在Finalizer
		_, finalizerExist := lo.Find(curFinalizers, func(item string) bool {
			return item == f.param.CLBRsProtection
		})
		// 如果Finalizer不存在，无需删除Finalizer，直接返回
		if !finalizerExist {
			return nil
		}
		// remove finalizer
		node.ObjectMeta.Finalizers = lo.Filter(curFinalizers, func(item string, _ int) bool {
			return item != f.param.CLBRsProtection
		})
		// 通过k8s client更新资源
		if _, err := f.param.KubeClient.CoreV1().Nodes().Update(ctx, node, metav1.UpdateOptions{}); err != nil {
			return err
		}
		// 更新成功
		glog.Infof("delete protect finalizer from node %s", nodeName)
		return nil
	})
	return err
}

func (f *NodeGracefulDeletionManager) getLoadBalancerResource(ctx context.Context, clbID string) (*v1alpha1.LoadBalancerResource, error) {
	LBR, err := f.param.LoadBalancerResourceLister.Get(clbID)
	if err != nil {
		if errors.IsNotFound(err) {
			LBR, err = f.param.LoadBalancerResourceClient.NetworkingV1alpha1().LoadBalancerResources().Get(ctx, clbID, metav1.GetOptions{})
			if err == nil {
				return LBR, nil
			}
			return nil, nil
		}
		return nil, err
	}
	return LBR, nil
}

// 删除rs绑的CLB中没有LBR的CLB
func (f *NodeGracefulDeletionManager) filterCLBs(ctx context.Context, nodeBindCLBs map[string]sets.Set[string], privateIP2Key map[string]QueueKey, lbs map[string]map[string]sets.Set[string]) (sets.Set[QueueKey], []error) {
	var errs []error
	failedKeys := sets.New[QueueKey]()
	for privateIP, clbIDs := range nodeBindCLBs {
		for clbID := range clbIDs {
			lbr, err := f.getLoadBalancerResource(ctx, clbID)
			if err != nil {
				errs = append(errs, err)
				key := privateIP2Key[privateIP]
				failedKeys.Insert(key)
				break
			}
			// 过滤CLB     1）不存在LBR的CLB      2）存在LBR，但不属于当前ManagerType的CLB
			if lbr == nil || !f.checkCLBMatchManagerType(privateIP, lbr, lbs[clbID]) {
				(nodeBindCLBs)[privateIP].Delete(clbID)
			}
		}
	}
	return failedKeys, errs
}

func (f *NodeGracefulDeletionManager) checkCLBMatchManagerType(node string, lbr *v1alpha1.LoadBalancerResource, lb map[string]sets.Set[string]) bool {
	if lbr.Labels == nil {
		return false
	}

	kindByListener := make(map[string]string)
	for _, v := range lbr.Spec.Listeners {
		for _, r := range v.References {
			kindByListener[types.GetListenerKey(v.Port, v.Protocol)] = strings.ToUpper(r.Kind)
		}
	}

	for key, targets := range lb {
		// 过滤监听器不属于tke的情况
		if _, ok := kindByListener[key]; !ok {
			continue
		}
		// 如果有当前manager类型的监听器，并且监听器的后端对应有当前node，则说明CLB不能被过滤掉
		if kindByListener[key] == strings.ToUpper(f.param.ManagerType) && targets.Has(node) {
			return true
		}
	}
	return false
}

func (f *NodeGracefulDeletionManager) getCLBsByNode(ctx context.Context, keys sets.Set[QueueKey]) (map[string]sets.Set[string], sets.Set[QueueKey], []error) {
	var errs []error
	privateIP2Key := make(map[string]QueueKey)
	CLBsByNode, lbs, err := f.describeLBListeners(keys)
	if err != nil {
		errs = append(errs, err)
		return CLBsByNode, keys, errs
	}
	for privateIP := range CLBsByNode {
		for key := range keys {
			if key.PrivateIP == privateIP {
				privateIP2Key[privateIP] = key
				break
			}
		}
	}
	failedKeys, errs := f.filterCLBs(ctx, CLBsByNode, privateIP2Key, lbs)
	return CLBsByNode, failedKeys, errs
}

func (f *NodeGracefulDeletionManager) describeLBListeners(keys sets.Set[QueueKey]) (map[string]sets.Set[string], ListenersByCLB, error) {
	request := clb.NewDescribeLBListenersRequest()
	request.Backends = make([]*clb.LbRsItem, 0)
	for key := range keys {
		request.Backends = append(request.Backends, &clb.LbRsItem{
			PrivateIp: common.StringPtr(key.PrivateIP),
			VpcId:     common.StringPtr(f.param.VpcID),
		})
	}
	rsp, err := f.param.DescribeLBListenersAPI(cloudctx.New(nil, f.param.RegionName), request)
	if err != nil {
		glog.Errorf("DescribeLBListeners error: %v", err)
		return nil, nil, err
	}
	// 第一层是CLBID，第二层是ListenerID，第三层是Targets列表
	nodeBindListeners := make(ListenersByCLB)
	nodeBindCLBs := make(map[string]sets.Set[string])
	for _, lb := range rsp.Response.LoadBalancers {
		// 在最外一层为nodeBindListeners添加了[k,v]键值对，key是ClbID
		curLbID := *lb.LoadBalancerId
		nodeBindListeners[curLbID] = make(targetsSetByListener)
		for _, listener := range lb.Listeners {
			listenerKey := types.GetListenerKey(int32(*listener.Port), *listener.Protocol)
			if _, exist := nodeBindListeners[listenerKey]; !exist {
				nodeBindListeners[curLbID][listenerKey] = make(targetsSet)
			}
			if listener.Rules != nil {
				for _, rule := range listener.Rules {
					for _, target := range rule.Targets {
						if target.PrivateIp != nil {
							curPrivateIP := *target.PrivateIp
							if _, exist := nodeBindCLBs[curPrivateIP]; !exist {
								nodeBindCLBs[curPrivateIP] = make(sets.Set[string])
							}
							// nodeBindCLBs添加的ClbID都是curLbID，且这些ID一定被添加为nodeBindListeners的键，所以不用担心nodeBindCLBs中的ID在nodeBindListeners不存在
							nodeBindCLBs[curPrivateIP].Insert(curLbID)
							nodeBindListeners[curLbID][listenerKey].Insert(curPrivateIP)
						}
					}
				}
			} else if listener.Targets != nil {
				for _, target := range listener.Targets {
					if target.PrivateIp != nil {
						curPrivateIP := *target.PrivateIp
						if _, exist := nodeBindCLBs[curPrivateIP]; !exist {
							nodeBindCLBs[curPrivateIP] = make(sets.Set[string])
						}
						nodeBindCLBs[curPrivateIP].Insert(curLbID)
						nodeBindListeners[curLbID][listenerKey].Insert(curPrivateIP)
					}
				}
			} else {
				return nil, nil, fmt.Errorf("rsp format unknown")
			}
		}
	}
	return nodeBindCLBs, nodeBindListeners, err
}

func (f *NodeGracefulDeletionManager) EnqueueTargets(targetsToEnqueue []*clb.BatchTarget, nodesByID map[string]*v1.Node) {
	if nodesByID == nil {
		return
	}
	for _, target := range targetsToEnqueue {
		var node *v1.Node
		if target.InstanceId != nil {
			node = nodesByID[*target.InstanceId] // cvm类型的rs
		} else if target.EniIp != nil {
			node = nodesByID[*target.EniIp] // 其它类型的rs
		}
		if node != nil {
			f.EnqueueNode(node)
		}
	}
}

// 得到当前集群所有节点以insID或privateIP为键，node指针为值的map
func GetNodesByID(nodeLister v12.NodeLister) (map[string]*v1.Node, error) {
	nodes, err := nodeLister.List(labels.Everything())
	if err != nil {
		return nil, err
	}
	nodesByID := GetNodesByIDFromNodes(nodes)
	return nodesByID, nil
}

func GetNodesByIDFromNodes(nodes []*v1.Node) map[string]*v1.Node {
	nodesByID := make(map[string]*v1.Node)
	for _, node := range nodes {
		for _, address := range node.Status.Addresses {
			if address.Type == "InternalIP" {
				nodesByID[address.Address] = node
			}
		}
		if v, exist := node.Labels[NodeInstanceID]; exist {
			nodesByID[v] = node
		}
	}
	return nodesByID
}

// 从ListenersBackends中获得与之关联的Nodes
func GetNodesFromListenerBackends(listenerBackends *clb.ListenerBackend, nodesByID map[string]*v1.Node) sets.Set[*v1.Node] {
	nodes := sets.New[*v1.Node]()
	if len(listenerBackends.Rules) > 0 {
		for _, ruleTargets := range listenerBackends.Rules {
			for _, target := range ruleTargets.Targets {
				if node, ok := nodesByID[*target.InstanceId]; ok {
					nodes.Insert(node)
				}
				for _, ip := range target.PrivateIpAddresses {
					if node, ok := nodesByID[*ip]; ok {
						nodes.Insert(node)
					}
				}
			}
		}
	} else {
		for _, target := range listenerBackends.Targets {
			if node, ok := nodesByID[*target.InstanceId]; ok {
				nodes.Insert(node)
			}
			for _, ip := range target.PrivateIpAddresses {
				if node, ok := nodesByID[*ip]; ok {
					nodes.Insert(node)
				}
			}
		}
	}
	return nodes
}

func GetNodesFromListenersBackendsByKey(ListenersBackendsByID map[string]*clb.ListenerBackend, nodesByID map[string]*v1.Node) sets.Set[*v1.Node] {
	nodes := sets.New[*v1.Node]()
	for _, listenerBackends := range ListenersBackendsByID {
		curNodes := GetNodesFromListenerBackends(listenerBackends, nodesByID)
		if curNodes.Len() > 0 {
			nodes = nodes.Union(curNodes)
		}
	}
	return nodes
}
