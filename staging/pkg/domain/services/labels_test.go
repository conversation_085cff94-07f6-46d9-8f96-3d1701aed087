package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSetLabel(t *testing.T) {
	type args struct {
		labels map[string]string
		key    string
		value  string
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]string
		want1 bool
	}{
		{
			name: "空labels，添加key",
			args: args{
				labels: nil,
				key:    "key",
				value:  "value",
			},
			want:  map[string]string{"key": "value"},
			want1: true,
		},
		{
			name: "空labels，添加key，值为空",
			args: args{
				labels: nil,
				key:    "key",
			},
			want:  map[string]string{"key": ""},
			want1: true,
		},
		{
			name: "非空labels，添加key，值相等",
			args: args{
				labels: map[string]string{"key": "value"},
				key:    "key",
				value:  "value",
			},
			want:  map[string]string{"key": "value"},
			want1: false,
		},
		{
			name: "非空labels，添加key，值不相等",
			args: args{
				labels: map[string]string{"key": "value"},
				key:    "key",
				value:  "value1",
			},
			want:  map[string]string{"key": "value1"},
			want1: true,
		},
		{
			name: "非空labels，添加key，值相等，多个key",
			args: args{
				labels: map[string]string{"key": "value", "key1": "value1"},
				key:    "key",
				value:  "value",
			},
			want:  map[string]string{"key": "value", "key1": "value1"},
			want1: false,
		},
		{
			name: "非空labels，同名key，值为空",
			args: args{
				labels: map[string]string{"key": "value"},
				key:    "key",
			},
			want:  map[string]string{"key": ""},
			want1: true,
		},
		{
			name: "非空labels，不同名key，值为空",
			args: args{
				labels: map[string]string{"key1": "value"},
				key:    "key",
			},
			want:  map[string]string{"key1": "value", "key": ""},
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := SetLabel(tt.args.labels, tt.args.key, tt.args.value)
			assert.Equalf(t, tt.want, got, "EnsureLabels(%v, %v, %v)", tt.args.labels, tt.args.key, tt.args.value)
			assert.Equalf(t, tt.want1, got1, "EnsureLabels(%v, %v, %v)", tt.args.labels, tt.args.key, tt.args.value)
		})
	}
}

func TestDeleteLabel(t *testing.T) {
	type args struct {
		labels map[string]string
		key    string
	}
	tests := []struct {
		name  string
		args  args
		want  map[string]string
		want1 bool
	}{
		{
			name: "空labels",
			args: args{
				labels: nil,
				key:    "key",
			},
			want:  nil,
			want1: false,
		},
		{
			name: "非空labels, key存在",
			args: args{
				labels: map[string]string{"key": "value"},
				key:    "key",
			},
			want:  map[string]string{},
			want1: true,
		},
		{
			name: "非空labels, key不存在",
			args: args{
				labels: map[string]string{"key1": "value"},
				key:    "key",
			},
			want:  map[string]string{"key1": "value"},
			want1: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := DeleteLabel(tt.args.labels, tt.args.key)
			assert.Equalf(t, tt.want, got, "DeleteLabel(%v, %v)", tt.args.labels, tt.args.key)
			assert.Equalf(t, tt.want1, got1, "DeleteLabel(%v, %v)", tt.args.labels, tt.args.key)
		})
	}
}
