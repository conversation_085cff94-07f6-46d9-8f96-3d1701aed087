package services

import (
	"context"
	"reflect"
	"testing"

	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/domain/types/fake/ingress/wraps"
	"git.woa.com/kateway/pkg/net"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/core/v1"
	netv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

var (
	tmpl = wraps.NewNetworking(types.CoreIngress, &netv1.Ingress{
		Spec: netv1.IngressSpec{
			Rules: []netv1.IngressRule{
				{
					Host: "test.com",
					IngressRuleValue: netv1.IngressRuleValue{
						HTTP: &netv1.HTTPIngressRuleValue{
							Paths: []netv1.HTTPIngressPath{
								{
									Path:     "/",
									PathType: lo.To<PERSON>tr(netv1.PathTypeExact),
									Backend: netv1.IngressBackend{
										Service: &netv1.IngressServiceBackend{
											Name: "test",
											Port: netv1.ServiceBackendPort{
												Number: 80,
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	})
)

func TestDeterminePorts(t *testing.T) {
	cases := []struct {
		name        string
		annotations map[string]string
		tls         []netv1.IngressTLS
		expect      map[string][]int
		err         error
	}{
		{
			name: "无自定义监听端口，无TLS配置",
			expect: map[string][]int{
				net.ProtocolHTTP: {80},
			},
		},
		{
			name: "无自定义监听端口，有TLS配置",
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			expect: map[string][]int{
				net.ProtocolHTTP:  {80},
				net.ProtocolHTTPS: {443},
			},
		},
		{
			name: "自定义HTTP端口，无TLS",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTP":80}, {"HTTP":8080}]`,
			},
			expect: map[string][]int{
				net.ProtocolHTTP: {80, 8080},
			},
		},
		{
			name: "自定义HTTP端口，有TLS",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTP":80}, {"HTTP":8080}]`,
			},
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			expect: map[string][]int{
				net.ProtocolHTTP:  {80, 8080},
				net.ProtocolHTTPS: {443},
			},
		},
		{
			name: "自定义HTTPS端口，有TLS",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTPS":443}, {"HTTPS":8443}]`,
			},
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			expect: map[string][]int{
				net.ProtocolHTTP:  {80},
				net.ProtocolHTTPS: {443, 8443},
			},
		},
		{
			name: "自定义HTTPS、HTTP端口，无TLS",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTPS":8443}, {"HTTP":8080}]`,
			},
			expect: map[string][]int{
				net.ProtocolHTTP: {8080},
			},
		},
		{
			name: "自定义HTTPS、HTTP端口，有TLS",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTPS":8443},{"HTTPS":8443},{"HTTP":8080}]`,
			},
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			expect: map[string][]int{
				net.ProtocolHTTP:  {8080},
				net.ProtocolHTTPS: {8443},
			},
		},
		{
			name: "端口冲突，有TLS",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTPS":8443},{"HTTP":8443}]`,
			},
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			err: ErrListenPortConflict,
		},
		{
			name: "无TLS配置，80端口被HTTPS协议占用",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTPS":80}]`,
			},
			expect: map[string][]int{
				net.ProtocolHTTP: {80},
			},
		},
		{
			name: "有TLS配置，80端口被HTTPS协议占用",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTPS":80}]`,
			},
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			err: ErrListenPortConflict,
		},
		{
			name: "无TLS配置，443端口被HTTP协议占用",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTP":443}]`,
			},
			expect: map[string][]int{
				net.ProtocolHTTP: {443},
			},
		},
		{
			name: "有TLS配置，443端口被HTTP协议占用",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTP":443}]`,
			},
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			err: ErrListenPortConflict,
		},
		{
			name: "端口值异常，有TLS",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `[{"HTTPS":-1},{"HTTP":8443}]`,
			},
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			err: ErrInvalidPortValue,
		},
		{
			name: "无效定义",
			annotations: map[string]string{
				types.IngressAnnotationListenerPorts: `{{"H`,
			},
			tls: []netv1.IngressTLS{
				{
					Hosts:      []string{"test.com"},
					SecretName: "test",
				},
			},
			expect: map[string][]int{
				net.ProtocolHTTP:  {80},
				net.ProtocolHTTPS: {443},
			},
		},
	}
	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			tmpl.ObjectMeta.Annotations = tc.annotations
			tmpl.Spec.TLS = tc.tls
			ports, err := ListenerService{}.determineIngressPorts(context.Background(), tmpl)
			if tc.err != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tc.err)
				return
			}
			assert.Equal(t, tc.expect, ports)
		})
	}
}

func TestTruncateRules(t *testing.T) {
	cases := []struct {
		name            string
		listeners       []types.Listener
		quota           int
		expectListeners []types.Listener
	}{
		{
			name: "无需截断，quota值大于rule长度",
			listeners: []types.Listener{
				{
					ListenerMeta: types.ListenerMeta{
						Port:     80,
						Protocol: net.ProtocolHTTP,
					},
					Rules: map[string][]types.ListenerRule{
						"a.com": {
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/test1",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test1",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/test2",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test2",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
						},
					},
				},
			},
			quota: 110,
			expectListeners: []types.Listener{
				{
					ListenerMeta: types.ListenerMeta{
						Port:     80,
						Protocol: net.ProtocolHTTP,
					},
					Rules: map[string][]types.ListenerRule{
						"a.com": {
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/test1",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test1",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/test2",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test2",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "无需截断，未提供quota",
			listeners: []types.Listener{
				{
					ListenerMeta: types.ListenerMeta{
						Port:     80,
						Protocol: net.ProtocolHTTP,
					},
					Rules: map[string][]types.ListenerRule{
						"a.com": {
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/test1",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test1",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/test2",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test2",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
						},
					},
				},
			},
			expectListeners: []types.Listener{
				{
					ListenerMeta: types.ListenerMeta{
						Port:     80,
						Protocol: net.ProtocolHTTP,
					},
					Rules: map[string][]types.ListenerRule{
						"a.com": {
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/test1",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test1",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/test2",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test2",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "需要截断",
			listeners: []types.Listener{
				{
					ListenerMeta: types.ListenerMeta{
						Port:     80,
						Protocol: net.ProtocolHTTP,
					},
					Rules: map[string][]types.ListenerRule{
						"a.com": {
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/zzz",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "zzz",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/aaa",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "aaa",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
						},
					},
				},
			},
			quota: 1,
			expectListeners: []types.Listener{
				{
					ListenerMeta: types.ListenerMeta{
						Port:     80,
						Protocol: net.ProtocolHTTP,
					},
					Rules: map[string][]types.ListenerRule{
						"a.com": {
							{
								Domain: types.Domain{
									Host: "a.com",
								},
								Path: "/zzz",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "zzz",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ListenerService{ruleQuota: c.quota}.truncateRules(c.listeners)
			assert.Equal(t, c.expectListeners, c.listeners)
		})
	}
}

func TestBuildIngressHTTPListeners(t *testing.T) {
	cases := []struct {
		name   string
		ports  []int
		rules  []netv1.IngressRule
		expect []types.Listener
		err    error
	}{
		{
			name:  "",
			ports: []int{8080},
			rules: []netv1.IngressRule{
				{
					Host: "hello.com",
					IngressRuleValue: netv1.IngressRuleValue{
						HTTP: &netv1.HTTPIngressRuleValue{
							Paths: []netv1.HTTPIngressPath{
								{
									Path:     "/test/api",
									PathType: lo.ToPtr(netv1.PathTypeExact),
									Backend: netv1.IngressBackend{
										Service: &netv1.IngressServiceBackend{
											Name: "test",
											Port: netv1.ServiceBackendPort{
												Name: "http",
											},
										},
									},
								},
								{
									Path:     "/abc",
									PathType: lo.ToPtr(netv1.PathTypeImplementationSpecific),
									Backend: netv1.IngressBackend{
										Service: &netv1.IngressServiceBackend{
											Name: "test",
											Port: netv1.ServiceBackendPort{
												Number: 80,
											},
										},
									},
								},
							},
						},
					},
				},
				{
					Host: "",
					IngressRuleValue: netv1.IngressRuleValue{
						HTTP: &netv1.HTTPIngressRuleValue{
							Paths: []netv1.HTTPIngressPath{
								{
									Path:     "",
									PathType: lo.ToPtr(netv1.PathTypeExact),
									Backend: netv1.IngressBackend{
										Service: &netv1.IngressServiceBackend{
											Name: "test",
											Port: netv1.ServiceBackendPort{
												Name: "http",
											},
										},
									},
								},
								{
									Path:     "/abc",
									PathType: lo.ToPtr(netv1.PathTypeImplementationSpecific),
									Backend: netv1.IngressBackend{
										Service: &netv1.IngressServiceBackend{
											Name: "test",
											Port: netv1.ServiceBackendPort{
												Number: 80,
											},
										},
									},
								},
							},
						},
					},
				},
			},
			expect: []types.Listener{
				{
					ListenerMeta: types.ListenerMeta{
						Port:     8080,
						Protocol: net.ProtocolHTTP,
					},
					Rules: map[string][]types.ListenerRule{
						"hello.com": {
							{
								Path:         "/test/api",
								Domain:       types.Domain{Host: "hello.com"},
								CLBPathTypes: []types.CLBPathType{types.CLBPathTypeExact},
								CLBPath:      "=/test/api",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test",
											ServicePort: intstr.FromString("http"),
										},
									},
								},
							},
							{
								Path:         "/abc",
								Domain:       types.Domain{Host: "hello.com"},
								CLBPathTypes: nil,
								CLBPath:      "/abc",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
						},
						"test.com": {
							{
								Path:         "/",
								Domain:       types.Domain{Host: "test.com"},
								CLBPathTypes: []types.CLBPathType{types.CLBPathTypeExact},
								CLBPath:      "=/",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test",
											ServicePort: intstr.FromString("http"),
										},
									},
								},
							},
							{
								Path:         "/abc",
								Domain:       types.Domain{Host: "test.com"},
								CLBPathTypes: nil,
								CLBPath:      "/abc",
								Action: types.ListenerRuleAction{
									Forward: &types.ForwardActionConfig{
										ForwardBackendMeta: types.ForwardBackendMeta{
											ServiceName: "test",
											ServicePort: intstr.FromInt(80),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			tmpl.Spec.Rules = tc.rules
			listeners, err := ListenerService{defaultDomain: lo.ToPtr("test.com")}.buildIngressHTTPListeners(context.Background(), tmpl, tc.ports)
			if tc.err != nil {
				assert.ErrorIs(t, err, tc.err)
				return
			}
			assert.Equal(t, tc.expect, listeners)
		})
	}
}

func TestProcessCertIDs(t *testing.T) {
	tests := []struct {
		input    string
		expected []string
		err      error
	}{
		{"", nil, ErrInvalidTLSSecretContent},
		{"cert1,cert2", []string{"cert1", "cert2"}, nil},
		{" cert1 , cert2 ", []string{"cert1", "cert2"}, nil},
		{"cert1, ,cert2", []string{"cert1", "cert2"}, nil},
		{"cert1", []string{"cert1"}, nil},
		{"cert1, cert1", []string{"cert1", "cert1"}, nil},
		{"cert1, cert1, cert3", nil, ErrInvalidTLSSecretContent},
		{"cert1,cert2,cert3", nil, ErrInvalidTLSSecretContent},
		{" , ", nil, ErrInvalidTLSSecretContent},
		{" , , , ", nil, ErrInvalidTLSSecretContent},                  // 只有空格和逗号
		{"cert1,,cert2", []string{"cert1", "cert2"}, nil},             // 多个连续的分隔符
		{"cert1, cert2, !@#$%^&*()", nil, ErrInvalidTLSSecretContent}, // 包含特殊字符
		{"  cert1  ,  cert2  ", []string{"cert1", "cert2"}, nil},      // 包含前导和尾随空格
		{"Cert1,cert2", []string{"Cert1", "cert2"}, nil},              // 包含混合大小写
		{"123, 456", []string{"123", "456"}, nil},                     // 包含数字
		{"cert1 , , cert2", []string{"cert1", "cert2"}, nil},          // 包含空格和逗号的组合
	}

	for _, test := range tests {
		result, err := ProcessCertIDs(test.input)
		if !reflect.DeepEqual(result, test.expected) || err != test.err {
			t.Errorf("ProcessCertIDs(%q) = %v, %v; expected %v, %v", test.input, result, err, test.expected, test.err)
		}
	}
}

func TestGetDataCertID(t *testing.T) {
	// 创建一个模拟的Secret对象
	secret := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-secret",
			Namespace: "default",
		},
		Type: v1.SecretTypeTLS,
		Data: map[string][]byte{
			"qcloud_cert_id": []byte("cert123"),
		},
	}

	// 测试正常情况
	t.Run("正常情况", func(t *testing.T) {
		certID, caCertID, err := GetDataCertID(secret)
		require.NoError(t, err)
		assert.Equal(t, "cert123", certID)
		assert.Nil(t, caCertID)
	})

	// 测试缺少qcloud_cert_id的情况
	t.Run("缺少qcloud_cert_id", func(t *testing.T) {
		delete(secret.Data, "qcloud_cert_id")
		_, _, err := GetDataCertID(secret)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "tls secret [default/test-secret] does not contain data \"qcloud_cert_id\"")
	})

	// 测试存在qcloud_ca_cert_id的情况
	t.Run("存在qcloud_ca_cert_id", func(t *testing.T) {
		secret.Data["qcloud_ca_cert_id"] = []byte("caCert456")
		secret.Data["qcloud_cert_id"] = []byte("cert123")
		certID, caCertID, err := GetDataCertID(secret)
		require.NoError(t, err)
		assert.Equal(t, "cert123", certID)
		assert.NotNil(t, caCertID)
		assert.Equal(t, "caCert456", *caCertID)
	})
	// 测试qcloud_cert_id为空的情况
	t.Run("qcloud_cert_id为空", func(t *testing.T) {
		secret.Data["qcloud_cert_id"] = []byte("")
		_, _, err := GetDataCertID(secret)
		require.NoError(t, err)
	})

	// 测试qcloud_ca_cert_id为空的情况
	t.Run("qcloud_ca_cert_id为空", func(t *testing.T) {
		secret.Data["qcloud_cert_id"] = []byte("cert123")
		secret.Data["qcloud_ca_cert_id"] = []byte("")
		certID, caCertID, err := GetDataCertID(secret)
		require.NoError(t, err)
		assert.Equal(t, "cert123", certID)
		assert.NotNil(t, caCertID)
		assert.Equal(t, "", *caCertID)
	})

	// 测试Secret类型不是TLS的情况
	t.Run("Secret类型不是TLS", func(t *testing.T) {
		secret.Type = "Opaque" // 修改为非TLS类型
		certID, caCertID, err := GetDataCertID(secret)
		require.NoError(t, err)
		assert.Equal(t, "cert123", certID)
		assert.NotNil(t, caCertID)
		assert.Equal(t, "", *caCertID)
	})

	// 测试Secret数据为空的情况
	t.Run("Secret数据为空", func(t *testing.T) {
		secret.Data = map[string][]byte{}
		_, _, err := GetDataCertID(secret)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "tls secret [default/test-secret] does not contain data \"qcloud_cert_id\"")
	})
}

func TestGetCertID(t *testing.T) {
	tests := []struct {
		name             string
		secret           *v1.Secret
		expectedCertID   string
		expectedCaCertID *string
		expectError      bool
	}{
		{
			name: "Valid Data Cert ID and CA Cert ID",
			secret: &v1.Secret{
				Data: map[string][]byte{
					"qcloud_cert_id":    []byte("cert-id-123"),
					"qcloud_ca_cert_id": []byte("ca-cert-id-456"),
				},
			},
			expectedCertID:   "cert-id-123",
			expectedCaCertID: lo.ToPtr("ca-cert-id-456"),
			expectError:      false,
		},
		{
			name: "Valid Data Cert ID, missing CA Cert ID",
			secret: &v1.Secret{
				Data: map[string][]byte{
					"qcloud_cert_id": []byte("cert-id-123"),
				},
			},
			expectedCertID:   "cert-id-123",
			expectedCaCertID: nil,
			expectError:      false,
		},
		{
			name: "Missing Data Cert ID, valid Annotation Cert ID",
			secret: &v1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						types.AnnotationCertID: "anno-cert-id-789",
					},
				},
				Type: v1.SecretTypeTLS,
			},
			expectedCertID:   "anno-cert-id-789",
			expectedCaCertID: nil,
			expectError:      false,
		},
		{
			name: "Missing Cert ID in both Data and Annotations",
			secret: &v1.Secret{
				Data: map[string][]byte{},
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{},
				},
				Type: v1.SecretTypeTLS,
			},
			expectedCertID:   "",
			expectedCaCertID: nil,
			expectError:      true,
		},
		{
			name: "Invalid Secret Type",
			secret: &v1.Secret{
				Type: v1.SecretTypeOpaque,
			},
			expectedCertID:   "",
			expectedCaCertID: nil,
			expectError:      true,
		},
		{
			name: "Valid Annotation Cert ID, missing CA Cert ID",
			secret: &v1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						types.AnnotationCertID: "anno-cert-id-789",
					},
				},
				Type: v1.SecretTypeTLS,
			},
			expectedCertID:   "anno-cert-id-789",
			expectedCaCertID: nil,
			expectError:      false,
		},
		{
			name: "Valid Annotation Cert ID, missing CA Cert ID 2",
			secret: &v1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						types.AnnotationCertID:   "anno-cert-id-789",
						types.AnnotationCaCertID: "",
					},
				},
				Type: v1.SecretTypeTLS,
			},
			expectedCertID:   "anno-cert-id-789",
			expectedCaCertID: lo.ToPtr(""),
			expectError:      false,
		},
		{
			name: "Vaild Data Cert ID, both Annotation Cert ID",
			secret: &v1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						types.AnnotationCertID: "anno-cert-id-789",
					},
				},
				Data: map[string][]byte{
					"qcloud_cert_id": []byte("cert-id-123"),
				},
				Type: v1.SecretTypeTLS,
			},
			expectedCertID:   "cert-id-123",
			expectedCaCertID: nil,
			expectError:      false,
		},
		{
			name: "Vaild Data Cert ID, both Annotation CA Cert ID",
			secret: &v1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{
						types.AnnotationCaCertID: "anno-ca-cert-id-789",
					},
				},
				Data: map[string][]byte{
					"qcloud_cert_id": []byte("cert-id-123"),
				},
				Type: v1.SecretTypeTLS,
			},
			expectedCertID:   "cert-id-123",
			expectedCaCertID: nil,
			expectError:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			certID, caCertID, err := GetCertID(tt.secret)

			if (err != nil) != tt.expectError {
				t.Errorf("GetCertID() error = %v, expectError %v", err, tt.expectError)
				return
			}
			if certID != tt.expectedCertID {
				t.Errorf("GetCertID() certID = %v, expected %v", certID, tt.expectedCertID)
			}
			if (caCertID != nil && *caCertID != *tt.expectedCaCertID) || (caCertID == nil && tt.expectedCaCertID != nil) {
				t.Errorf("GetCertID() caCertID = %v, expected %v", caCertID, tt.expectedCaCertID)
			}
		})
	}
}
