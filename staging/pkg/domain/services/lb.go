package services

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/pkg/domain/types"
)

type LBService struct{}

// DetermineLifecycleOwnerValue根据LB上的云标签以及对应service或ingress上的注解来判断LB的生命周期所有者。
// https://iwiki.woa.com/p/4010334278
func (LBService) DetermineLifecycleOwnerValue(_ context.Context, obj metav1.Object, lb *types.LB) string {
	owner := lb.GetLifecycleOwnerValue()
	if owner != nil {
		return *owner
	}
	isAutoCreated := lb.IsAutoCreatedByTKE()
	// 非复用的service或ingress在迁移时，会在父集群创建复用的svc或ing，此时需要根据注解来确定CLB实例最初是否是自动创建。
	originallyCreatedByTKE, exists := obj.GetAnnotations()[types.AnnotationLBOriginallyCreatedByTKE]
	if exists {
		switch originallyCreatedByTKE {
		case "true":
			isAutoCreated = true
		case "false":
			isAutoCreated = false
		default:
			panic(fmt.Sprintf("Invalid value %q of annotation %q", originallyCreatedByTKE, types.AnnotationLBOriginallyCreatedByTKE))
		}
	}
	// 如果lb不是由TKE自动创建，或者当前lb的生命周期保护已开启，则LB的生命周期所有者为用户。
	v := lo.Ternary(!isAutoCreated || lb.IsDeletionProtectionEnabled(),
		types.TagValueLifecycleOwnedByUser, types.TagValueLifecycleOwnedByTKE)

	return v
}
