package services

import (
	"os"
	"strconv"

	"git.woa.com/kateway/pkg/domain/event"
	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/types"
)

func UploadMetricsAndEvent(object types.Object, err *types.Error) {
	if metrics.Instance != nil {
		metrics.Instance.IncSyncCount(object, err.ErrorCode.Code)
	}
	event.Instance.EventError(object, err)
}

func GetSilentStart() bool {
	zone, ok := os.LookupEnv("SILENT_START")
	if !ok { // 兼容返回
		return false
	}
	parseBool, _ := strconv.ParseBool(zone)
	return parseBool
}
