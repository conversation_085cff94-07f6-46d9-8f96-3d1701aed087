package cluster

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	corev1 "k8s.io/api/core/v1"
	extv1beta1 "k8s.io/api/extensions/v1beta1"
	netv1 "k8s.io/api/networking/v1"
	apiextensionsClient "k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/informers"
	coreInformers "k8s.io/client-go/informers/core/v1"
	discoveryInformers "k8s.io/client-go/informers/discovery/v1"
	extensionInformers "k8s.io/client-go/informers/extensions/v1beta1"
	networkingInformers "k8s.io/client-go/informers/networking/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	typedv1 "k8s.io/client-go/kubernetes/typed/core/v1"
	corelisters "k8s.io/client-go/listers/core/v1"
	discovery "k8s.io/client-go/listers/discovery/v1"
	extensionListers "k8s.io/client-go/listers/extensions/v1beta1"
	networkingListers "k8s.io/client-go/listers/networking/v1"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	glog "k8s.io/klog/v2"

	loadbalancerresource "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/clientset/versioned"
	loadBalancerResourceExternalVersions "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/informers/externalversions"
	loadBalancerResourceInformers "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/informers/externalversions/loadbalancerresource/v1alpha1"
	loadBalancerResourceListers "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/listers/loadbalancerresource/v1alpha1"
	mciClient "git.woa.com/kateway/multi-cluster-ingress-api/client/clientset/versioned"
	mciFactory "git.woa.com/kateway/multi-cluster-ingress-api/client/informers/externalversions"
	mciInformer "git.woa.com/kateway/multi-cluster-ingress-api/client/informers/externalversions/multiclusteringress/v1alpha1"
	mciLister "git.woa.com/kateway/multi-cluster-ingress-api/client/listers/multiclusteringress/v1alpha1"
	multiclusterservice "git.woa.com/kateway/multi-cluster-service-api/client/clientset/versioned"
	externalversions2 "git.woa.com/kateway/multi-cluster-service-api/client/informers/externalversions"
	multiClusterServiceInformers "git.woa.com/kateway/multi-cluster-service-api/client/informers/externalversions/multiclusterservice/v1alpha1"
	multiClusterServiceListers "git.woa.com/kateway/multi-cluster-service-api/client/listers/multiclusterservice/v1alpha1"
	tkeserviceconfig "git.woa.com/kateway/tke-service-config/pkg/client/clientset/versioned"
	tkeserviceconfiginformers "git.woa.com/kateway/tke-service-config/pkg/client/informers/externalversions"
	tkeserviceInformers "git.woa.com/kateway/tke-service-config/pkg/client/informers/externalversions/tkeservice/v1alpha1"
	tkeserviceConfigListers "git.woa.com/kateway/tke-service-config/pkg/client/listers/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/app/version"
	"git.woa.com/kateway/pkg/domain/controllercm"
	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/service/annotation"
	"git.woa.com/kateway/pkg/domain/types"
)

const (
	serviceSyncPeriod = 5 * time.Minute

	TKECNIMultusConf    = "00-multus.conf"
	TKECNIConfConfigMap = "tke-cni-agent-conf"
)

type Config struct {
	Ctx            context.Context
	Name           string
	Region         string
	ControllerName string
	KubeConfig     *restclient.Config
	FeatureGates   featuregates.FeatureGates
}

func (c *Config) Validate() (errs []error) {
	if c.Name == "" {
		errs = append(errs, fmt.Errorf("Name is empty"))
	}
	if c.Region == "" {
		errs = append(errs, fmt.Errorf("Region is empty"))
	}
	if c.ControllerName == "" {
		errs = append(errs, fmt.Errorf("ControllerName is empty"))
	}
	if c.KubeConfig == nil {
		errs = append(errs, fmt.Errorf("KubeConfig is nil"))
	}

	return
}

type Basic struct {
	*Config
	featuregates.FeatureGates

	ControllerConfigMapSyncFunc func(context.Context, kubernetes.Interface) error
	hasSynced                   bool
	isTKEENISupported           bool

	kubeClient                 *kubernetes.Clientset
	tkeServiceConfigClient     *tkeserviceconfig.Clientset
	loadBalancerResourceClient *loadbalancerresource.Clientset
	apiextensionsKubeclient    *apiextensionsClient.Clientset
	multiClusterServiceClient  *multiclusterservice.Clientset

	sharedInformerFactory                     informers.SharedInformerFactory
	tkeServiceConfigSharedInformerFactory     tkeserviceconfiginformers.SharedInformerFactory
	loadBalancerResourceSharedInformerFactory loadBalancerResourceExternalVersions.SharedInformerFactory
	multiClusterServiceSharedInformerFactory  externalversions2.SharedInformerFactory
	mciSharedInformerFactory                  mciFactory.SharedInformerFactory

	podInformer coreInformers.PodInformer
	podLister   corelisters.PodLister

	serviceInformer coreInformers.ServiceInformer
	serviceLister   corelisters.ServiceLister

	secretInformer coreInformers.SecretInformer
	secretLister   corelisters.SecretLister

	configMapInformer coreInformers.ConfigMapInformer
	configMapLister   corelisters.ConfigMapLister

	nodeInformer coreInformers.NodeInformer
	nodeLister   corelisters.NodeLister

	endpointsInformer coreInformers.EndpointsInformer
	endpointsLister   corelisters.EndpointsLister

	endpointSliceInformer discoveryInformers.EndpointSliceInformer
	endpointSliceLister   discovery.EndpointSliceLister

	ingressInformer extensionInformers.IngressInformer
	ingressLister   extensionListers.IngressLister

	newIngressInformer networkingInformers.IngressInformer
	newIngressLister   networkingListers.IngressLister

	tkeServiceConfigInformer tkeserviceInformers.TkeServiceConfigInformer
	tkeServiceConfigLister   tkeserviceConfigListers.TkeServiceConfigLister

	loadBalancerResourceInformer loadBalancerResourceInformers.LoadBalancerResourceInformer
	loadBalancerResourceLister   loadBalancerResourceListers.LoadBalancerResourceLister

	multiClusterServiceInformer multiClusterServiceInformers.MultiClusterServiceInformer
	multiClusterServiceLister   multiClusterServiceListers.MultiClusterServiceLister

	multiClusterIngressClient   mciClient.Interface
	multiClusterIngressInformer mciInformer.MultiClusterIngressInformer
	multiClusterIngressLister   mciLister.MultiClusterIngressLister

	serviceIndexer cache.Indexer
	eventRecorder  record.EventRecorder
}

var _ Interface = (*Basic)(nil)

// nolint: funlen
func NewBasic(config *Config) (*Basic, error) {
	errs := config.Validate()
	if len(errs) > 0 {
		return nil, fmt.Errorf("NewBasic config errors: %v", errs)
	}

	s := &Basic{
		Config:       config,
		kubeClient:   kubernetes.NewForConfigOrDie(config.KubeConfig),
		FeatureGates: config.FeatureGates,
	}

	s.tkeServiceConfigClient = tkeserviceconfig.NewForConfigOrDie(s.KubeConfig)
	s.loadBalancerResourceClient = loadbalancerresource.NewForConfigOrDie(s.KubeConfig)
	s.apiextensionsKubeclient = apiextensionsClient.NewForConfigOrDie(s.KubeConfig)

	s.sharedInformerFactory = informers.NewSharedInformerFactory(s.kubeClient, serviceSyncPeriod)

	s.podInformer = s.sharedInformerFactory.Core().V1().Pods()
	s.podLister = s.podInformer.Lister()

	s.serviceInformer = s.sharedInformerFactory.Core().V1().Services()
	s.serviceLister = s.serviceInformer.Lister()

	s.secretInformer = s.sharedInformerFactory.Core().V1().Secrets()
	s.secretLister = s.secretInformer.Lister()

	s.configMapInformer = s.sharedInformerFactory.Core().V1().ConfigMaps()
	s.configMapLister = s.configMapInformer.Lister()

	s.nodeInformer = s.sharedInformerFactory.Core().V1().Nodes()
	s.nodeLister = s.nodeInformer.Lister()

	s.endpointsInformer = s.sharedInformerFactory.Core().V1().Endpoints()
	s.endpointsLister = s.endpointsInformer.Lister()

	var ingIndexer cache.Indexer
	if lo.Must(s.IsExtensionsAPIGroupSupported()) {
		s.ingressInformer = s.sharedInformerFactory.Extensions().V1beta1().Ingresses()
		s.ingressLister = s.ingressInformer.Lister()
		ingIndexer = s.ingressInformer.Informer().GetIndexer()
	} else {
		s.newIngressInformer = s.sharedInformerFactory.Networking().V1().Ingresses()
		s.newIngressLister = s.newIngressInformer.Lister()
		ingIndexer = s.newIngressInformer.Informer().GetIndexer()
		s.endpointSliceInformer = s.sharedInformerFactory.Discovery().V1().EndpointSlices()
		s.endpointSliceLister = s.endpointSliceInformer.Lister()
	}
	if err := ingIndexer.AddIndexers(map[string]cache.IndexFunc{"UID": ingressUIDIndexFunc}); err != nil {
		return nil, err
	}

	s.tkeServiceConfigSharedInformerFactory = tkeserviceconfiginformers.NewSharedInformerFactory(s.tkeServiceConfigClient, serviceSyncPeriod)
	s.tkeServiceConfigInformer = s.tkeServiceConfigSharedInformerFactory.Cloud().V1alpha1().TkeServiceConfigs()
	s.tkeServiceConfigLister = s.tkeServiceConfigInformer.Lister()

	s.loadBalancerResourceSharedInformerFactory = loadBalancerResourceExternalVersions.NewSharedInformerFactory(s.loadBalancerResourceClient, serviceSyncPeriod)
	s.loadBalancerResourceInformer = s.loadBalancerResourceSharedInformerFactory.Networking().V1alpha1().LoadBalancerResources()
	s.loadBalancerResourceLister = s.loadBalancerResourceInformer.Lister()

	if s.Enabled(featuregates.MultiClusterService) {
		s.multiClusterServiceClient = multiclusterservice.NewForConfigOrDie(s.KubeConfig)
		s.multiClusterServiceSharedInformerFactory = externalversions2.NewSharedInformerFactory(s.multiClusterServiceClient, serviceSyncPeriod)
		s.multiClusterServiceInformer = s.multiClusterServiceSharedInformerFactory.Cloud().V1alpha1().MultiClusterServices()
		s.multiClusterServiceLister = s.multiClusterServiceInformer.Lister()
	}

	if s.Enabled(featuregates.MultiClusterIngress) {
		s.multiClusterIngressClient = mciClient.NewForConfigOrDie(s.KubeConfig)
		s.mciSharedInformerFactory = mciFactory.NewSharedInformerFactory(s.multiClusterIngressClient, serviceSyncPeriod)
		s.multiClusterIngressInformer = s.mciSharedInformerFactory.Cloud().V1alpha1().MultiClusterIngresses()
		s.multiClusterIngressLister = s.multiClusterIngressInformer.Lister()
	}

	broadcaster := record.NewBroadcaster()
	broadcaster.StartLogging(glog.Infof)
	broadcaster.StartRecordingToSink(&typedv1.EventSinkImpl{Interface: s.kubeClient.CoreV1().Events("")})
	s.eventRecorder = broadcaster.NewRecorder(scheme.Scheme, corev1.EventSource{Component: s.ControllerName()})

	serviceIndexer := s.serviceInformer.Informer().GetIndexer()
	if err := serviceIndexer.AddIndexers(map[string]cache.IndexFunc{
		"LoadBalancerSourceEndpoints": indexByServiceByEndpoints,
	}); err != nil {
		return nil, err
	}
	s.serviceIndexer = serviceIndexer

	s.configMapInformer.Informer().AddEventHandler(
		cache.ResourceEventHandlerFuncs{
			AddFunc:    s.onAddConfigMap,
			UpdateFunc: s.onUpdateConfigMap,
		})

	return s, nil
}

func (s *Basic) Name() string {
	return s.Config.Name
}

func (s *Basic) ControllerName() string {
	return s.Config.ControllerName
}

func (s *Basic) RegionName() string {
	return s.Config.Region
}

func (s *Basic) DryRun() bool {
	return s.Enabled(featuregates.DryRunService) || s.Enabled(featuregates.DryRunIngress)
}

func (s *Basic) PodInformer() coreInformers.PodInformer {
	return s.podInformer
}

func (s *Basic) ServiceInformer() coreInformers.ServiceInformer {
	return s.serviceInformer
}

func (s *Basic) SecretInformer() coreInformers.SecretInformer {
	return s.secretInformer
}

func (s *Basic) ConfigMapInformer() coreInformers.ConfigMapInformer {
	return s.configMapInformer
}

func (s *Basic) NodeInformer() coreInformers.NodeInformer {
	return s.nodeInformer
}

func (s *Basic) EndpointsInformer() coreInformers.EndpointsInformer {
	return s.endpointsInformer
}

func (s *Basic) EndpointSliceInformer() discoveryInformers.EndpointSliceInformer {
	return s.endpointSliceInformer
}

func (s *Basic) ExtensionIngressInformer() extensionInformers.IngressInformer {
	return s.ingressInformer
}

func (s *Basic) NetworkingIngressInformer() networkingInformers.IngressInformer {
	return s.newIngressInformer
}

func (s *Basic) TkeServiceConfigInformer() tkeserviceInformers.TkeServiceConfigInformer {
	return s.tkeServiceConfigInformer
}

func (s *Basic) LoadBalancerResourceInformer() loadBalancerResourceInformers.LoadBalancerResourceInformer {
	return s.loadBalancerResourceInformer
}

func (s *Basic) MultiClusterServiceInformer() multiClusterServiceInformers.MultiClusterServiceInformer {
	return s.multiClusterServiceInformer
}

func (s *Basic) MultiClusterIngressInformer() mciInformer.MultiClusterIngressInformer {
	return s.multiClusterIngressInformer
}

func (s *Basic) MultiClusterIngressClient() mciClient.Interface {
	return s.multiClusterIngressClient
}

func (s *Basic) MultiClusterIngressLister() mciLister.MultiClusterIngressLister {
	return s.multiClusterIngressLister
}

func (s *Basic) KubeClient() *kubernetes.Clientset {
	return s.kubeClient
}

func (s *Basic) TkeServiceConfigClient() *tkeserviceconfig.Clientset {
	return s.tkeServiceConfigClient
}

func (s *Basic) LoadBalancerResourceClient() *loadbalancerresource.Clientset {
	return s.loadBalancerResourceClient
}

func (s *Basic) MultiClusterServiceClient() *multiclusterservice.Clientset {
	return s.multiClusterServiceClient
}

func (s *Basic) ApiextensionsKubeclient() *apiextensionsClient.Clientset {
	return s.apiextensionsKubeclient
}

func (s *Basic) PodLister() corelisters.PodLister {
	return s.podLister
}

func (s *Basic) ServiceLister() corelisters.ServiceLister {
	return s.serviceLister
}

func (s *Basic) SecretLister() corelisters.SecretLister {
	return s.secretLister
}

func (s *Basic) ConfigMapLister() corelisters.ConfigMapLister {
	return s.configMapLister
}

func (s *Basic) EndpointsLister() corelisters.EndpointsLister {
	return s.endpointsLister
}

func (s *Basic) EndpointSliceLister() discovery.EndpointSliceLister {
	return s.endpointSliceLister
}

func (s *Basic) NodeLister() corelisters.NodeLister {
	return s.nodeLister
}

func (s *Basic) ExtensionIngressLister() extensionListers.IngressLister {
	return s.ingressLister
}

func (s *Basic) NetworkingIngressLister() networkingListers.IngressLister {
	return s.newIngressLister
}

func (s *Basic) TkeServiceConfigLister() tkeserviceConfigListers.TkeServiceConfigLister {
	return s.tkeServiceConfigLister
}

func (s *Basic) LoadBalancerResourceLister() loadBalancerResourceListers.LoadBalancerResourceLister {
	return s.loadBalancerResourceLister
}

func (s *Basic) MultiClusterServiceLister() multiClusterServiceListers.MultiClusterServiceLister {
	return s.multiClusterServiceLister
}

func (s *Basic) EventRecorder() record.EventRecorder {
	return s.eventRecorder
}

func (s *Basic) ServiceIndexer() cache.Indexer {
	return s.serviceIndexer
}

// indexByPodIP returns pod IP for given pod.
func indexByServiceUID(obj interface{}) ([]string, error) {
	service, ok := obj.(*corev1.Service)
	if !ok {
		return []string{}, nil
	}
	return []string{string(service.GetObjectMeta().GetUID())}, nil
}

func indexByServiceByEndpoints(obj interface{}) ([]string, error) {
	service, ok := obj.(*corev1.Service)
	if !ok {
		return []string{}, nil
	}

	anno := annotation.New(&service.Annotations)
	if epBinding, err := anno.LoadbalancerSourceEndpoints(); err == nil && epBinding.Name != "" {
		return []string{types.JoinKeyStrings("/", service.Namespace, epBinding.Name)}, nil
	}
	return []string{}, nil
}

func (s *Basic) Run(stopCh <-chan struct{}) {
	s.syncControllerConfigMap()

	s.sharedInformerFactory.Start(stopCh)
	s.tkeServiceConfigSharedInformerFactory.Start(stopCh)
	s.loadBalancerResourceSharedInformerFactory.Start(stopCh)
	if s.Enabled(featuregates.MultiClusterService) {
		s.multiClusterServiceSharedInformerFactory.Start(stopCh)
	}
	if s.Enabled(featuregates.MultiClusterIngress) {
		s.mciSharedInformerFactory.Start(stopCh)
	}

	s.sharedInformerFactory.WaitForCacheSync(stopCh)
	s.tkeServiceConfigSharedInformerFactory.WaitForCacheSync(stopCh)
	s.loadBalancerResourceSharedInformerFactory.WaitForCacheSync(stopCh)
	if s.Enabled(featuregates.MultiClusterService) {
		s.multiClusterServiceSharedInformerFactory.WaitForCacheSync(stopCh)
	}
	if s.Enabled(featuregates.MultiClusterIngress) {
		s.mciSharedInformerFactory.WaitForCacheSync(stopCh)
	}

	glog.Infof("Cache synced")

	s.hasSynced = true
}

func (s *Basic) WaitForCacheSync() bool {
	err := wait.PollImmediateUntil(time.Second,
		func() (done bool, err error) {
			return s.hasSynced, nil
		},
		s.Ctx.Done())

	return err == nil
}

func (s *Basic) Stop() {
}

func (s *Basic) syncControllerConfigMap() {
	if s.ControllerConfigMapSyncFunc == nil {
		return
	}
	if err := s.ControllerConfigMapSyncFunc(s.Ctx, s.kubeClient); err != nil {
		glog.Errorf("Sync controller config map error: %v", err)
	}
}

func (s *Basic) onAddConfigMap(obj interface{}) {
	cm, _ := obj.(*corev1.ConfigMap)
	if cm.Namespace == metav1.NamespaceSystem && cm.Name == controllercm.ConfigMapService {
		s.syncControllerConfigMap()
		enableGlobalRouteDirectAccess, ok := cm.Data[controllercm.KeyEnableGlobalRouteDirectAccess]
		if ok && !cast.ToBool(enableGlobalRouteDirectAccess) {
			s.Disable(featuregates.GlobalRouteDirectAccess)
		} else {
			s.Enable(featuregates.GlobalRouteDirectAccess)
		}
	}
	if cm.Namespace == metav1.NamespaceSystem && cm.Name == TKECNIConfConfigMap {
		s.syncNetworkConfig(cm)
	}
}

func (s *Basic) onUpdateConfigMap(oldObj, curObj interface{}) {
	oldCM, _ := oldObj.(*corev1.ConfigMap)
	curCM, _ := curObj.(*corev1.ConfigMap)
	if oldCM.ResourceVersion == curCM.ResourceVersion {
		return
	}

	// 没有validating webhook 禁止用户修改，通过监听修改事件强制覆盖回去！
	if curCM.Namespace == metav1.NamespaceSystem && curCM.Name == controllercm.ConfigMapService {
		s.syncControllerConfigMap()
		enableGlobalRouteDirectAccess, ok := curCM.Data[controllercm.KeyEnableGlobalRouteDirectAccess]
		if ok && !cast.ToBool(enableGlobalRouteDirectAccess) {
			s.Disable(featuregates.GlobalRouteDirectAccess)
		} else {
			s.Enable(featuregates.GlobalRouteDirectAccess)
		}
	}
	// 检测集群网络配置，缓存相关需要状态！
	if curCM.Namespace == metav1.NamespaceSystem && curCM.Name == TKECNIConfConfigMap {
		s.syncNetworkConfig(curCM)
	}
}

func (s *Basic) syncNetworkConfig(cm *corev1.ConfigMap) {
	networkType, _ := getDefaultNetworkType(cm)
	if lo.Contains([]string{
		types.DelegatesTKERouteENI,
		types.DelegatesTKEDirectENI,
		types.DelegatesTKESubENI,
	}, networkType) {
		s.isTKEENISupported = true
	} else {
		s.isTKEENISupported = false
	}
}

func (s *Basic) IsTKEENISupported() bool {
	return s.isTKEENISupported
}

// 当前集群版本是否支持mutating webhook
// kateway(wallaceqian) 为什么是1.12？存量还有低于这个版本的嘛？这个条件是否一直满足，可以考虑移除？
func (s *Basic) IsMutatingWebhookSupported() (bool, error) {
	return s.CheckVersion(">= 1.12")
}

// SupportExtensionsGroup 1.22之后不再支持extensions/v1beta1
// 参考：https://kubernetes.io/docs/reference/using-api/deprecation-guide/#ingress-v122
func (s *Basic) IsExtensionsAPIGroupSupported() (bool, error) {
	return s.CheckVersion("< 1.22")
}

// CheckVersion 检查集群版本是否满足特定条件，如versionConstraint=">= 1.12"
func (s *Basic) CheckVersion(versionConstraint string) (bool, error) {
	serverVersion, err := s.kubeClient.ServerVersion()
	if err != nil {
		return false, err
	}

	return version.Check(serverVersion.GitVersion, versionConstraint)
}

// defaultNetworkType 集群默认网络类型
// Docs: https://github.com/qyzhaoxun/multus-cni/blob/master/doc/default-delegates.md
//
//	{
//	  "name": "multus-cni",
//	  "type": "multus",
//	  "kubeconfig": "/root/.kube/config",
//	  "logLevel": "info",
//	  "defaultDelegates": "tke-bridge",
//	  "capabilities": {
//	    "bandwidth": true,
//	    "portMappings": true
//	  }
//	}
func getDefaultNetworkType(cm *corev1.ConfigMap) (string, error) {
	config, ok := cm.Data[TKECNIMultusConf]
	if !ok {
		return "", fmt.Errorf("get %s from %v error", TKECNIMultusConf, cm.Data)
	}
	value := gjson.Get(config, "defaultDelegates")
	defaultDelegates := value.String()
	if defaultDelegates == "" {
		return "", fmt.Errorf("get `defaultDelegates` from %v error", cm.Data)
	}

	// kateway(wallaceqian) 这里cni可以配置一组，为什么只要包含对应模式就返回对应模式？如果匹配多个呢？
	if strings.Contains(defaultDelegates, types.DelegatesTKERouteENI) {
		return types.DelegatesTKERouteENI, nil
	} else if strings.Contains(defaultDelegates, types.DelegatesTKEBridge) {
		return types.DelegatesTKEBridge, nil
	} else if strings.Contains(defaultDelegates, types.DelegatesTKEDirectENI) {
		return types.DelegatesTKEDirectENI, nil
	} else if strings.Contains(defaultDelegates, types.DelegatesTKESubENI) {
		return types.DelegatesTKESubENI, nil
	}

	return "", fmt.Errorf("DefaultNetworkType error")
}

// NodesMap 可以作为后端绑定的节点
// TODO(wallaceqian) 可以考虑监听并缓存所需要数据，需要过滤掉不关注的node属性，因为informer本地有缓存数据，需要具体测试性能再评估
func (s *Basic) NodesMap() (map[string]*corev1.Node, error) {
	// kateway(wallaceqian) 需要评估是否过滤指定标签、Not Ready等情况
	nodes, err := s.nodeLister.List(labels.Everything())
	if err != nil {
		return nil, fmt.Errorf("NodeLister List error. error %w", err)
	}

	nodes = lo.Filter(nodes, func(n *corev1.Node, _ int) bool {
		node := types.NewNode(n)
		return node.CanBind()
	})
	result := lo.SliceToMap(nodes, func(item *corev1.Node) (string, *corev1.Node) {
		return item.Name, item
	})

	return result, nil
}

func (s *Basic) Pods(namespace string, selector map[string]string) (ret []*corev1.Pod, err error) {
	return s.podLister.Pods(namespace).List(labels.SelectorFromSet(selector))
}

func (s *Basic) ListServiceByPod(pod *corev1.Pod) ([]*corev1.Service, error) {
	services, err := s.ServiceLister().Services(pod.Namespace).List(labels.Everything())
	if err != nil {
		return nil, err
	}

	return lo.Filter(services, func(svc *corev1.Service, _ int) bool {
		return s.IsPodSelectByService(svc, pod)
	}), nil
}

func (s *Basic) IsPodSelectByService(service *corev1.Service, pod *corev1.Pod) bool {
	podLabels := labels.Set(pod.Labels)
	if len(service.Spec.Selector) == 0 { // Without Selector类型的Service. 参考 https://kubernetes.io/docs/concepts/services-networking/service/#services-without-selectors
		endpoints, err := s.EndpointsLister().Endpoints(pod.Namespace).Get(service.Name)
		if err != nil {
			return false
		}
		for _, subset := range endpoints.Subsets {
			for _, address := range append(subset.Addresses, subset.NotReadyAddresses...) {
				if address.IP == pod.Status.PodIP {
					return true
				}
			}
		}

		return false
	}

	return labels.SelectorFromSet(service.Spec.Selector).Matches(podLabels)
}

func ingressUIDIndexFunc(obj interface{}) ([]string, error) {
	if ingress, ok := obj.(*extv1beta1.Ingress); ok {
		return []string{string(ingress.GetUID())}, nil
	}
	if ingress, ok := obj.(*netv1.Ingress); ok {
		return []string{string(ingress.GetUID())}, nil
	}
	return []string{}, nil
}
