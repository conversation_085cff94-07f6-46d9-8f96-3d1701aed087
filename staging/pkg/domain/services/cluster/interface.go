package cluster

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	coreInformers "k8s.io/client-go/informers/core/v1"
	discoveryInformers "k8s.io/client-go/informers/discovery/v1"
	extensionInformers "k8s.io/client-go/informers/extensions/v1beta1"
	networkingInformers "k8s.io/client-go/informers/networking/v1"
	"k8s.io/client-go/kubernetes"
	v1 "k8s.io/client-go/listers/core/v1"
	discovery "k8s.io/client-go/listers/discovery/v1"
	"k8s.io/client-go/listers/extensions/v1beta1"
	networking "k8s.io/client-go/listers/networking/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"

	loadbalancerresource "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/clientset/versioned"
	loadBalancerResourceInformers "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/informers/externalversions/loadbalancerresource/v1alpha1"
	v1alpha12 "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/listers/loadbalancerresource/v1alpha1"
	mciClient "git.woa.com/kateway/multi-cluster-ingress-api/client/clientset/versioned"
	mciInformer "git.woa.com/kateway/multi-cluster-ingress-api/client/informers/externalversions/multiclusteringress/v1alpha1"
	mciLister "git.woa.com/kateway/multi-cluster-ingress-api/client/listers/multiclusteringress/v1alpha1"
	multiclusterservice "git.woa.com/kateway/multi-cluster-service-api/client/clientset/versioned"
	multiClusterServiceInformers "git.woa.com/kateway/multi-cluster-service-api/client/informers/externalversions/multiclusterservice/v1alpha1"
	v1alpha13 "git.woa.com/kateway/multi-cluster-service-api/client/listers/multiclusterservice/v1alpha1"
	tkeserviceconfig "git.woa.com/kateway/tke-service-config/pkg/client/clientset/versioned"
	tkeserviceInformers "git.woa.com/kateway/tke-service-config/pkg/client/informers/externalversions/tkeservice/v1alpha1"
	"git.woa.com/kateway/tke-service-config/pkg/client/listers/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/domain/featuregates"
)

var (
	Instance Interface
)

type Interface interface {
	featuregates.FeatureGates

	Name() string
	ControllerName() string
	RegionName() string
	DryRun() bool
	Run(stopCh <-chan struct{})
	CheckVersion(versionConstraint string) (bool, error)
	Stop()
	WaitForCacheSync() bool

	KubeClient() *kubernetes.Clientset
	ApiextensionsKubeclient() *clientset.Clientset
	TkeServiceConfigClient() *tkeserviceconfig.Clientset
	LoadBalancerResourceClient() *loadbalancerresource.Clientset
	MultiClusterServiceClient() *multiclusterservice.Clientset

	PodInformer() coreInformers.PodInformer
	PodLister() v1.PodLister

	ServiceInformer() coreInformers.ServiceInformer
	ServiceLister() v1.ServiceLister

	SecretInformer() coreInformers.SecretInformer
	SecretLister() v1.SecretLister

	ConfigMapInformer() coreInformers.ConfigMapInformer
	ConfigMapLister() v1.ConfigMapLister

	NodeInformer() coreInformers.NodeInformer
	NodeLister() v1.NodeLister

	EndpointsInformer() coreInformers.EndpointsInformer
	EndpointsLister() v1.EndpointsLister

	EndpointSliceInformer() discoveryInformers.EndpointSliceInformer
	EndpointSliceLister() discovery.EndpointSliceLister

	ExtensionIngressInformer() extensionInformers.IngressInformer
	ExtensionIngressLister() v1beta1.IngressLister

	NetworkingIngressInformer() networkingInformers.IngressInformer
	NetworkingIngressLister() networking.IngressLister

	TkeServiceConfigInformer() tkeserviceInformers.TkeServiceConfigInformer
	TkeServiceConfigLister() v1alpha1.TkeServiceConfigLister

	LoadBalancerResourceInformer() loadBalancerResourceInformers.LoadBalancerResourceInformer
	LoadBalancerResourceLister() v1alpha12.LoadBalancerResourceLister

	MultiClusterServiceInformer() multiClusterServiceInformers.MultiClusterServiceInformer
	MultiClusterServiceLister() v1alpha13.MultiClusterServiceLister

	MultiClusterIngressClient() mciClient.Interface
	MultiClusterIngressInformer() mciInformer.MultiClusterIngressInformer
	MultiClusterIngressLister() mciLister.MultiClusterIngressLister

	ServiceIndexer() cache.Indexer
	EventRecorder() record.EventRecorder

	Pods(namespace string, selector map[string]string) (ret []*corev1.Pod, err error)
	NodesMap() (map[string]*corev1.Node, error)
	IsMutatingWebhookSupported() (bool, error)
	IsTKEENISupported() bool
	IsExtensionsAPIGroupSupported() (bool, error)

	ListServiceByPod(pod *corev1.Pod) ([]*corev1.Service, error)
	IsPodSelectByService(service *corev1.Service, pod *corev1.Pod) bool
}
