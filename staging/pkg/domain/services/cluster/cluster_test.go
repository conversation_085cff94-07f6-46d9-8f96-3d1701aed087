package cluster

import (
	"context"
	"fmt"
	"os"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/clientcmd"
)

func Test_DefaultNetworkType(t *testing.T) {
	kubeconfig := os.Getenv("KUBECONFIG")
	if kubeconfig == "" {
		return
	}

	restConfig, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		t.Fatal(err)
	}

	svc, _ := NewBasic(&Config{
		KubeConfig: restConfig,
		Ctx:        context.TODO(),
	},
	)
	fmt.Println(svc.IsMutatingWebhookSupported())
}

func TestIndexByServiceByEndpoints_WithIndexer(t *testing.T) {

	// 测试用例数据
	testCases := []struct {
		name        string
		service     *corev1.Service
		expectKeys  []string
		description string
	}{
		{
			name: "有效注解服务",
			service: &corev1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "svc1",
					Namespace: "default",
					Annotations: map[string]string{
						"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"ep1"}`,
					},
				},
			},
			expectKeys:  []string{"default/ep1"},
			description: "应生成正确的命名空间/端点组合键",
		},
		{
			name: "无效JSON格式注解",
			service: &corev1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "svc2",
					Namespace: "test",
					Annotations: map[string]string{
						"service.cloud.tencent.com/loadbalancer-source-endpoints": `{name: ep2}`,
					},
				},
			},
			expectKeys:  []string{},
			description: "无效JSON应返回空索引",
		},
		{
			name: "无注解服务",
			service: &corev1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "svc3",
					Namespace: "production",
				},
			},
			expectKeys:  []string{},
			description: "无注解服务不应生成索引键",
		},
	}

	// 执行测试
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 添加服务到索引器
			// 创建自定义索引器
			indexer := cache.NewIndexer(cache.MetaNamespaceKeyFunc, cache.Indexers{
				"byEndpoints": indexByServiceByEndpoints,
			})
			err := indexer.Add(tc.service)
			assert.NoError(t, err, "添加服务到索引器失败")

			// 获取生成的索引键
			keys, err := indexByServiceByEndpoints(tc.service)
			assert.NoError(t, err, "生成索引键时发生错误")
			assert.Equal(t, tc.expectKeys, keys, tc.description)

			// 验证索引器查询
			if len(tc.expectKeys) > 0 {
				items, err := indexer.ByIndex("byEndpoints", tc.expectKeys[0])
				assert.NoError(t, err, "索引查询失败")
				assert.Len(t, items, 1, "应能查询到对应的服务")
				assert.IsType(t, &corev1.Service{}, items[0], "返回对象类型应为Service")
			}
		})
	}
}

func TestIndexByServiceByEndpoints_UpdateCase(t *testing.T) {
	// 初始化索引器
	indexer := cache.NewIndexer(cache.MetaNamespaceKeyFunc, cache.Indexers{
		"byEndpoints": indexByServiceByEndpoints,
	})

	// 初始服务对象
	originalSvc := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "update-svc",
			Namespace: "default",
			Annotations: map[string]string{
				"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"old-ep"}`,
			},
		},
	}

	// 初始添加服务
	err := indexer.Add(originalSvc)
	assert.NoError(t, err, "初始添加服务失败")

	t.Run("更新有效注解配置", func(t *testing.T) {
		// 准备更新后的服务
		updatedSvc := originalSvc.DeepCopy()
		updatedSvc.Annotations["service.cloud.tencent.com/loadbalancer-source-endpoints"] = `{"name":"new-ep"}`

		// 执行更新操作
		err := indexer.Update(updatedSvc)
		assert.NoError(t, err, "更新服务失败")

		// 验证旧索引已清除
		oldItems, err := indexer.ByIndex("byEndpoints", "default/old-ep")
		assert.NoError(t, err)
		assert.Empty(t, oldItems, "旧索引应被清除")

		// 验证新索引生成
		newItems, err := indexer.ByIndex("byEndpoints", "default/new-ep")
		assert.NoError(t, err)
		assert.Len(t, newItems, 1, "应能找到更新后的服务")
	})

	t.Run("更新为无效注解", func(t *testing.T) {
		// 准备无效注解的服务
		invalidSvc := originalSvc.DeepCopy()
		invalidSvc.Annotations["service.cloud.tencent.com/loadbalancer-source-endpoints"] = `{name: invalid-ep}`

		// 执行更新操作
		err := indexer.Update(invalidSvc)
		assert.NoError(t, err, "更新服务失败")

		// 验证索引被清除
		items, err := indexer.ByIndex("byEndpoints", "default/old-ep")
		assert.NoError(t, err)
		assert.Empty(t, items, "无效注解应清除原有索引")
	})

	t.Run("删除注解", func(t *testing.T) {
		// 准备删除注解的服务
		noAnnotationSvc := originalSvc.DeepCopy()
		delete(noAnnotationSvc.Annotations, "service.cloud.tencent.com/loadbalancer-source-endpoints")

		// 执行更新操作
		err := indexer.Update(noAnnotationSvc)
		assert.NoError(t, err, "更新服务失败")

		// 验证所有索引清除
		items, err := indexer.ByIndex("byEndpoints", "default/old-ep")
		assert.NoError(t, err)
		assert.Empty(t, items, "删除注解后应清除索引")
	})
}

func TestIndexByServiceByEndpoints_DeleteCase(t *testing.T) {
	// 初始化索引器
	indexer := cache.NewIndexer(cache.MetaNamespaceKeyFunc, cache.Indexers{
		"byEndpoints": indexByServiceByEndpoints,
	})

	// 准备测试数据
	validSvc := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "delete-svc",
			Namespace: "default",
			Annotations: map[string]string{
				"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"test-ep"}`,
			},
		},
	}

	// 初始添加服务
	err := indexer.Add(validSvc)
	assert.NoError(t, err, "初始添加服务失败")

	t.Run("删除有效服务", func(t *testing.T) {
		// 执行删除操作
		err := indexer.Delete(validSvc)
		assert.NoError(t, err, "删除服务失败")

		// 验证索引清除
		items, err := indexer.ByIndex("byEndpoints", "default/test-ep")
		assert.NoError(t, err)
		assert.Empty(t, items, "删除服务后应清除索引")
	})

	t.Run("删除无效服务", func(t *testing.T) {
		// 准备无效服务对象
		invalidSvc := &corev1.Service{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "non-exist-svc",
				Namespace: "default",
			},
		}

		// 执行删除操作
		_ = indexer.Delete(invalidSvc)

		// 验证索引器状态不变
		assert.Len(t, indexer.List(), 0, "索引器应保持空状态")
	})

	t.Run("删除无注解服务", func(t *testing.T) {
		// 准备无注解服务
		noAnnotationSvc := &corev1.Service{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "no-anno-svc",
				Namespace: "default",
			},
		}

		// 添加并删除服务
		_ = indexer.Add(noAnnotationSvc)
		err := indexer.Delete(noAnnotationSvc)
		assert.NoError(t, err, "删除无注解服务不应报错")

		// 验证索引器状态
		assert.Len(t, indexer.List(), 0, "索引器应保持空状态")
	})
}

func TestIndexByServiceByEndpoints_MultiServiceCase(t *testing.T) {
	// 初始化共享索引器
	indexer := cache.NewIndexer(cache.MetaNamespaceKeyFunc, cache.Indexers{
		"byEndpoints": indexByServiceByEndpoints,
	})

	// 公共测试数据
	baseServices := []*corev1.Service{
		{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "svc-a",
				Namespace: "default",
				Annotations: map[string]string{
					"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"shared-ep"}`,
				},
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "svc-b",
				Namespace: "default",
				Annotations: map[string]string{
					"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"shared-ep"}`,
				},
			},
		},
		{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "svc-c",
				Namespace: "test",
				Annotations: map[string]string{
					"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"shared-ep"}`,
				},
			},
		},
	}

	t.Run("多服务共享相同Endpoints", func(t *testing.T) {
		// 添加所有服务
		for _, svc := range baseServices {
			assert.NoError(t, indexer.Add(svc.DeepCopy()), "添加服务失败")
		}

		// 验证共享索引键
		items, err := indexer.ByIndex("byEndpoints", "default/shared-ep")
		assert.NoError(t, err)
		assert.Len(t, items, 2, "default命名空间应有两个服务")

		// 验证跨命名空间隔离
		testItems, err := indexer.ByIndex("byEndpoints", "test/shared-ep")
		assert.NoError(t, err)
		assert.Len(t, testItems, 1, "test命名空间应有一个服务")
	})

	t.Run("跨服务更新影响", func(t *testing.T) {
		// 准备更新服务
		updatedSvc := baseServices[0].DeepCopy()
		updatedSvc.Annotations["service.cloud.tencent.com/loadbalancer-source-endpoints"] = `{"name":"new-ep"}`

		// 执行更新
		assert.NoError(t, indexer.Update(updatedSvc), "更新服务失败")

		// 验证旧索引影响
		oldItems, err := indexer.ByIndex("byEndpoints", "default/shared-ep")
		assert.NoError(t, err)
		assert.Len(t, oldItems, 1, "更新后旧索引应只剩一个服务")

		// 验证新索引生成
		newItems, err := indexer.ByIndex("byEndpoints", "default/new-ep")
		assert.NoError(t, err)
		assert.Len(t, newItems, 1, "新索引应包含更新后的服务")
	})

	t.Run("并发操作稳定性", func(t *testing.T) {
		// 重置索引器
		for _, obj := range indexer.List() {
			_ = indexer.Delete(obj)
		}

		// 并发添加服务
		scale := 10000
		var wg sync.WaitGroup
		for i := 0; i < scale; i++ {
			wg.Add(1)
			go func(idx int) {
				defer wg.Done()
				svc := &corev1.Service{
					ObjectMeta: metav1.ObjectMeta{
						Name:      fmt.Sprintf("concurrent-svc-%d", idx),
						Namespace: "concurrent",
						Annotations: map[string]string{
							"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"concurrent-ep"}`,
						},
					},
				}
				assert.NoError(t, indexer.Add(svc), "并发添加失败")
			}(i)
		}
		wg.Wait()

		// 验证并发结果
		items, err := indexer.ByIndex("byEndpoints", "concurrent/concurrent-ep")
		assert.NoError(t, err)
		assert.Len(t, items, scale, "应正确添加所有并发服务")
	})

	t.Run("大规模数据操作", func(t *testing.T) {
		// 准备1000个测试服务
		const scale = 10000
		var services []*corev1.Service
		for i := 0; i < scale; i++ {
			services = append(services, &corev1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:      fmt.Sprintf("scale-svc-%d", i),
					Namespace: "scale",
					Annotations: map[string]string{
						"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"scale-ep"}`,
					},
				},
			})
		}

		// 批量添加
		for _, svc := range services {
			assert.NoError(t, indexer.Add(svc), "批量添加失败")
		}

		// 验证索引容量
		items, err := indexer.ByIndex("byEndpoints", "scale/scale-ep")
		assert.NoError(t, err)
		assert.Len(t, items, scale, "应正确索引所有大规模服务")

		// 大规模更新操作
		t.Run("大规模更新操作", func(t *testing.T) {
			// 准备更新后的服务列表
			updatedServices := make([]*corev1.Service, 0, scale)
			for i, svc := range services {
				newSvc := svc.DeepCopy()
				newSvc.Annotations["service.cloud.tencent.com/loadbalancer-source-endpoints"] = fmt.Sprintf(`{"name":"updated-ep-%d"}`, i)
				updatedServices = append(updatedServices, newSvc)
			}

			// 批量更新
			for i, newSvc := range updatedServices {
				assert.NoError(t, indexer.Update(newSvc), "更新服务%d失败", i)
			}

			// 验证旧索引清除
			oldItems, err := indexer.ByIndex("byEndpoints", "scale/scale-ep")
			assert.NoError(t, err)
			assert.Empty(t, oldItems, "批量更新后旧索引应清空")

			// 验证新索引生成
			for i := 0; i < scale; i++ {
				key := fmt.Sprintf("scale/updated-ep-%d", i)
				items, err := indexer.ByIndex("byEndpoints", key)
				assert.NoError(t, err)
				assert.Len(t, items, 1, "索引键%s应存在对应服务", key)
			}
		})

		// 批量删除
		for _, svc := range services {
			assert.NoError(t, indexer.Delete(svc), "批量删除失败")
		}

		// 验证清理
		items, err = indexer.ByIndex("byEndpoints", "scale/scale-ep")
		assert.NoError(t, err)
		assert.Empty(t, items, "批量删除后索引应清空")
	})
}
