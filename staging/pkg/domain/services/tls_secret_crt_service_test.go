package services

import (
	"testing"
)

// 单元测试
func TestTrimCrt(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{
			input:    "-----BEGIN CERTIFICATE-----\r\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\r\n-----END CERTIFICATE-----\r\n",
			expected: "-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----",
		},
		{
			input:    "-----BEGIN CERTIFICATE-----\n\n\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n\n\n-----END CERTIFICATE-----\n",
			expected: "-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----",
		},
		{
			input:    "-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n\n\n\n-----END CERTIFICATE-----\n",
			expected: "-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----",
		},
		{
			input:    "-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----\n\n\n",
			expected: "-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----",
		},
		{
			input:    "-----BEGIN CERTIFICATE-----\n\n-----END CERTIFICATE-----\n",
			expected: "-----BEGIN CERTIFICATE-----\n-----END CERTIFICATE-----",
		},
		{
			input:    "\n-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----\n\n\n",
			expected: "\n-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----",
		},
		{
			input:    "   \n-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----   \n",
			expected: "   \n-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----",
		},
		{
			input:    "   \n-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----   \r\t\n",
			expected: "   \n-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----",
		},
		{
			input:    "   \r\r\n\n-----BEGIN CERTIFICATE-----\r\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----   \r\t\n",
			expected: "   \n-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAL5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D1Z5D\n-----END CERTIFICATE-----",
		},
		{
			input:    "",
			expected: "",
		},
	}

	for _, test := range tests {
		result := trimCrt(test.input)
		if result != test.expected {
			t.Errorf("For input %q, expected %q but got %q", test.input, test.expected, result)
		}
	}
}
