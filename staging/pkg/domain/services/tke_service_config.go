package services

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	tscv1alpha1 "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	tscclientset "git.woa.com/kateway/tke-service-config/pkg/client/clientset/versioned"
	tsclisters "git.woa.com/kateway/tke-service-config/pkg/client/listers/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/domain/errdef"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/k8s/k8sutil/annotation"
)

var (
	ErrInvalidAutoTKEServiceConfigAnnotation = errors.New("invalid auto-tke-service-config annotation")
	ErrTKEServiceConfigNameConflict          = errors.New("tke-service-config name conflict")
	ErrTKEServiceConfigNotFound              = errors.New("tke-service-config not found")
)

type TKEServiceConfigService struct {
	cli       tscclientset.Interface
	tscLister tsclisters.TkeServiceConfigLister
}

func NewTKEServiceConfigService(cli tscclientset.Interface, tscLister tsclisters.TkeServiceConfigLister) *TKEServiceConfigService {
	return &TKEServiceConfigService{
		cli:       cli,
		tscLister: tscLister,
	}
}

func (svc TKEServiceConfigService) Create(ctx context.Context, tsc *tscv1alpha1.TkeServiceConfig) error {
	_, err := svc.cli.CloudV1alpha1().TkeServiceConfigs(tsc.Namespace).Create(ctx, tsc, metav1.CreateOptions{})
	if err != nil {
		if k8serrors.IsAlreadyExists(err) {
			return nil
		}
		return err
	}
	return nil
}

func (svc TKEServiceConfigService) Get(_ context.Context, namespace, name string) (*tscv1alpha1.TkeServiceConfig, error) {
	cfg, err := svc.tscLister.TkeServiceConfigs(namespace).Get(name)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return nil, fmt.Errorf("tke-service-config %q not found: %w", name, ErrTKEServiceConfigNotFound)
		}
		return nil, errdef.NewFatal(fmt.Errorf("failed to get tke-service-config %q: %w", name, err))
	}
	return cfg, nil
}

func (svc TKEServiceConfigService) GetIngressConfig(ctx context.Context, ing types.Ingress) (*tscv1alpha1.TkeServiceConfig, error) {
	name, _, err := svc.GetIngressConfigName(ctx, ing)
	if err != nil {
		return nil, err
	}
	if name == "" {
		return nil, nil
	}
	return svc.Get(ctx, ing.Namespace(), name)
}

func (svc TKEServiceConfigService) GetIngressConfigName(_ context.Context, ing types.Ingress) (string, bool, error) {
	anno := ing.Annotations()
	auto, err := annotation.Parse[bool](anno, types.IngressAnnotationTKEServiceConfigAuto, booleanParseOpt)
	if err != nil {
		return "", false, ErrInvalidAutoTKEServiceConfigAnnotation
	}
	if lo.FromPtr(auto) {
		var name string
		switch ing.Type() {
		case types.CoreIngress:
			name = ing.Name() + "-auto-ingress-config"
		case types.MultiClusterIngress:
			name = ing.Name() + "-auto-multiclusteringress-config"
		default:
			panic("unexpected ingress type")
		}
		return name, true, nil
	}
	configName, _ := annotation.Parse[string](anno, types.IngressAnnotationTKEServiceConfig)
	if configName != nil {
		if strings.HasSuffix(*configName, "-auto-ingress-config") || strings.HasSuffix(*configName, "-auto-service-config") {
			return "", false, ErrTKEServiceConfigNameConflict
		}
		return *configName, false, nil
	}
	return "", false, nil
}
