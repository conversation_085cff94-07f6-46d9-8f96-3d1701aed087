package services

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"regexp"
	"strings"
	"time"

	"github.com/samber/lo"
	clouderrors "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	ssl "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ssl/v20191205"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8stypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/strategicpatch"
	"k8s.io/client-go/kubernetes"
	v12 "k8s.io/client-go/listers/core/v1"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/errdef"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/runtime"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
)

// service相关常量
const (
	qcloudTLSCrt    string = "tls.crt"
	qcloudTLSKey    string = "tls.key"
	qcloudTLSCaCrt  string = "ca.crt"            // ca证书内容
	QcloudCertID    string = "qcloud_cert_id"    // 服务器证书ID
	QcloudCACertID  string = "qcloud_ca_cert_id" // ca证书ID
	maxCompareCount uint   = 5                   // 最大对账数量
)

type secretContextMeta struct {
	Secret  *v1.Secret
	updated map[string]string
	Errs    []error
}

type secretSyncContext struct {
	*secretContextMeta
	ctx context.Context
}

func BuildsecretSyncContext(secret *v1.Secret) *secretSyncContext {
	return &secretSyncContext{
		secretContextMeta: &secretContextMeta{
			Secret:  secret,
			updated: make(map[string]string),
			Errs:    make([]error, 0),
		},
		ctx: context.Background(),
	}
}

func (sc secretSyncContext) Deadline() (deadline time.Time, ok bool) {
	return sc.ctx.Deadline()
}

func (sc secretSyncContext) Done() <-chan struct{} {
	return sc.ctx.Done()
}

func (sc secretSyncContext) Err() error {
	return sc.ctx.Err()
}

func (sc secretSyncContext) Value(key any) any {
	return sc.ctx.Value(key)
}

func (sc secretSyncContext) StartSpan(opts ...jaeger.SpanOption) (*jaeger.Span, *secretSyncContext) {
	dopts := []jaeger.SpanOption{jaeger.WithSkip(2)}
	span, ctx := jaeger.StartSpanFromContext(sc.ctx, append(dopts, opts...)...)
	sc.ctx = ctx
	return span, &sc
}

var (
	crtKeys   = []string{qcloudTLSCrt, qcloudTLSKey, qcloudTLSCaCrt}
	crtIDKeys = []string{types.AnnotationCertID, types.AnnotationCaCertID}
)

const (
	SecretErrorExceededMaxAllowedUploads string = "upload failed: secret has exceeded the maximum number of allowed certificate uploads"
	SecretErrorAnnotationUnexpected      string = "upload failed: secret has unexpected annotation error"
	JSONFormatError                      string = "json format error"
)

type crtType string // 证书类型  SVR：服务器证书  CA：根证书，用于双向验证

const (
	crtTypeSVR crtType = "SVR"
	crtTypeCA  crtType = "CA"
)

// tls类型secret服务
type SecretCertService struct {
	MockRun                      bool // Mock
	OwnerUin                     int64
	VpcID                        string
	RegionName                   string
	ClusterName                  string // 集群ID
	KubeClient                   *kubernetes.Clientset
	SecretLister                 v12.SecretLister
	DescribeCertificatesAPI      func(ctx context.Context, request *ssl.DescribeCertificatesRequest) (response *ssl.DescribeCertificatesResponse, err error)
	DescribeCertificateDetailAPI func(ctx context.Context, request *ssl.DescribeCertificateDetailRequest) (response *ssl.DescribeCertificateDetailResponse, err error)
	UpdateCertificateAPI         func(ctx context.Context, request *ssl.UploadCertificateRequest) (response *ssl.UploadCertificateResponse, err error)
}

// 目前支持：单向认证、双向认证（需要额外传递CA根证书ca.crt）
// 支持MockRun
// 错误优化：写到 status.conditions 注解的错误，通常是用户侧错误；内部报错通常不写到secret
// TODO: 优化超时控制（不同的跨进程API调用设置不同的超时时长）

// 入口函数，传给TaskQueue
func (p *SecretCertService) SyncSecret(key string) (result []error) {
	klog.Infof("Syncing secret [%s]", key)
	errs := make([]error, 0)
	defer func() {
		// 捕获panic错误
		if r := recover(); r != nil {
			stack := runtime.GetPanicError(r)
			klog.Error(stack)
		}
		if len(result) > 0 {
			klog.Errorf("Finished syncing secret [%s] with errors: %v", key, result)
		} else {
			klog.Infof("Finished syncing secret [%s] successfully", key)
		}
	}()
	// step1: 获取对应的secret资源
	namespace, name, err := types.SplitKeyString(key)
	if err != nil {
		errs = append(errs, err)
		return errs
	}
	// step1.1: list从缓存拿到key对应的secret
	secret, err := p.getSecret(namespace, name)
	if err != nil {
		klog.Infof("Secret [%s/%s] get secret error: %s", namespace, name, err.Error())
		// 如果不是NotFound类型err，需要返回错误; 否则，直接返回
		if !k8serrors.IsNotFound(err) {
			errs = append(errs, err)
		}
		return errs
	}
	sc := BuildsecretSyncContext(secret.DeepCopy())
	// 结合返回值更新secret状态，使用显示命名返回值来有效在defer函数中处理res
	defer func() {
		err = p.updateSecretAnnotationsAndStatus(sc, result)
		if err != nil {
			klog.Errorf("Secret [%s/%s] update status error: %s", namespace, name, err.Error())
			result = append(result, err)
		}
	}()
	p.syncLifeCycle(sc)
	errs = append(errs, sc.Errs...)
	// step5: 走完流程，返回错误
	return errs
}

func (p *SecretCertService) syncLifeCycle(sc *secretSyncContext) {
	secret := sc.Secret
	namespace := secret.Namespace
	name := secret.Name
	// 不是类型为tls的secret或者secret开关注解不为true的情况，直接结束流程
	if err := validateSecret(secret); err != nil {
		sc.Errs = append(sc.Errs, err)
		return
	}
	crtCheckPass, caCrtCheckPass, tmpErrs := preCheck(secret)
	if len(tmpErrs) > 0 {
		sc.Errs = append(sc.Errs, tmpErrs...)
	}
	klog.Infof("Secret [%s/%s] preCheck done crt: %v, caCrt: %v", namespace, name, crtCheckPass, caCrtCheckPass)
	// SVR和CA preCheck都没通过，直接结束处理流程
	if !crtCheckPass && !caCrtCheckPass {
		return
	}
	// 调用SSL API通过指定SearchKey为Alias查询证书ID
	// step2: 判断是否需要上传 SVR|CA 证书
	uploadCrt, uploadCaCrt, tmpErrs := p.shouldUploadCertificates(sc, secret, crtCheckPass, caCrtCheckPass)
	if len(tmpErrs) > 0 {
		sc.Errs = append(sc.Errs, tmpErrs...)
	}
	klog.Infof("Secret [%s/%s] - should upload crt: %v, should upload caCrt: %v", namespace, name, uploadCrt, uploadCaCrt)
	// step3: 上传 SVR|CA 证书
	svrID, caID, tmpErrs := p.uploadCertificates(sc, secret, uploadCrt, uploadCaCrt)
	if len(tmpErrs) > 0 {
		sc.Errs = append(sc.Errs, tmpErrs...)
	}
	// step4: 回写上传证书得到的证书ID到secret
	p.updateSecretByCertID(sc, svrID, caID)
}

func (p *SecretCertService) updateSecretByCertID(sc *secretSyncContext, svrID, caID *string) {
	// 如果 secret 为 nil 或者 svrID 和 caID 都为 nil，直接返回 nil
	if svrID == nil && caID == nil {
		return
	}
	// 更新注解并检查是否有更新
	updateAnnotation(sc, types.AnnotationCertID, svrID)
	updateAnnotation(sc, types.AnnotationCaCertID, caID)
}

func (p *SecretCertService) getSecret(namespace, name string) (*v1.Secret, error) {
	secret, err := p.SecretLister.Secrets(namespace).Get(name)
	if err != nil {
		return nil, err
	}
	return secret, nil
}

func (p *SecretCertService) shouldUploadCertificates(ctx context.Context, secret *v1.Secret, svrPass, caPass bool) (bool, bool, []error) {
	errs := make([]error, 0)
	var uploadCrt, uploadCaCrt bool // 默认值为false
	var err error
	if svrPass {
		// svr证书precheck通过
		uploadCrt, err = p.shouldUploadCrt(ctx, secret)
		if err != nil {
			errs = append(errs, err)
		}
	}
	if caPass {
		// ca证书precheck通过
		uploadCaCrt, err = p.shouldUploadCaCrt(ctx, secret)
		if err != nil {
			errs = append(errs, err)
		}
	}
	return uploadCrt, uploadCaCrt, errs
}

func (p *SecretCertService) uploadCertificates(ctx *secretSyncContext, secret *v1.Secret, SVRNeedUpload, CANeedUpload bool) (*string, *string, []error) {
	var crtID *string
	uploadErrs := make([]error, 0)
	if SVRNeedUpload {
		id, err := p.uploadSVRCrtForSecret(ctx, secret)
		if err != nil {
			uploadErrs = append(uploadErrs, err)
		}
		crtID = id
	}
	if !CANeedUpload {
		return crtID, nil, uploadErrs
	}
	// step3.2: 如果需要上传CA证书，则上传
	id, err := p.uploadCACrtForSecret(ctx, secret)
	if err != nil {
		uploadErrs = append(uploadErrs, err)
		return crtID, nil, uploadErrs
	}
	return crtID, id, uploadErrs
}

// deleteAnnotation 从给定的 Secret 中删除指定的注解
func deleteAnnotation(secret *v1.Secret, annotationKey string) {
	if secret.Annotations == nil {
		return // 如果 Annotations 为 nil，直接返回
	}
	delete(secret.Annotations, annotationKey)
}

func updateAnnotation(sc *secretSyncContext, key string, value *string) {
	if value == nil {
		return
	}
	if id, exist := sc.Secret.GetAnnotations()[key]; !exist || id != *value {
		sc.updated[key] = *value
	}
}

func preCheck(secret *v1.Secret) (bool, bool, []error) {
	// 检查证书内容是否有效
	validationErrors := make([]error, 0)
	isCertificateValid := true
	isCACertificateValid := true

	// 检查证书内容是否有效
	if _, err := validateCrt(secret); err != nil {
		isCertificateValid = false
		validationErrors = append(validationErrors, err)
	}

	caCheck, caErr := validateCaCrt(secret)
	if caErr != nil {
		validationErrors = append(validationErrors, caErr)
	}
	if !caCheck {
		isCACertificateValid = false
	}

	return isCertificateValid, isCACertificateValid, validationErrors
}

// 入队条件判断逻辑，不能有耗时操作，避免阻塞事件循环
func SecretNeedEnqueue(old, cur *v1.Secret) bool {
	if err := validateSecret(cur); err != nil {
		return false
	}
	// Secret onadd event
	if old == nil {
		return true
	}
	// Secret onupdate event
	// 检查证书内容是否变化，变化了就要入队
	if checkDataChangedMultiple(old, cur, crtKeys) {
		return true
	}
	// 证书内容没有变化，同时开关状态变化 off -> on，需要入队
	if !IsTLSSecretAnnotationEnabled(old) {
		return true
	}
	// 检查证书ID
	if checkAnnotationsChangedMultiple(old, cur, crtIDKeys) {
		return true
	}
	return false
}

func validateSecret(curSecret *v1.Secret) error {
	if curSecret.Type != v1.SecretTypeTLS {
		return types.NewError(errcode.CertificateContentError, "secret type not match TLS", types.JoinKeyStrings("/", curSecret.Namespace, curSecret.Name))
	}
	if !IsTLSSecretAnnotationEnabled(curSecret) {
		return types.NewError(errcode.CertificateContentError, "secret has not 'cloud.tencent.com/enable-tls-secret' annotation", types.JoinKeyStrings("/", curSecret.Namespace, curSecret.Name))
	}
	return nil
}

// 检查给定的键在两个 Secret 的 Annotations 中是否发生变化
func checkAnnotationsChanged(oldSecret, curSecret *v1.Secret, key string) bool {
	curData, curDataExist := curSecret.GetAnnotations()[key]
	oldData, oldDataExist := oldSecret.GetAnnotations()[key]
	return curDataExist != oldDataExist || (curDataExist && curData != oldData)
}

// 检测多个注解的变化
func checkAnnotationsChangedMultiple(old, cur *v1.Secret, keys []string) bool {
	for _, key := range keys {
		if checkAnnotationsChanged(old, cur, key) {
			return true
		}
	}
	return false
}

// 检查给定的键在两个 Secret 的 Data 中是否发生变化
func checkDataChanged(oldSecret, curSecret *v1.Secret, key string) bool {
	curData, curDataExist := curSecret.Data[key]
	oldData, oldDataExist := oldSecret.Data[key]
	return curDataExist != oldDataExist || (curDataExist && !bytes.Equal(curData, oldData))
}

// 检测多个数据的变化
func checkDataChangedMultiple(old, cur *v1.Secret, keys []string) bool {
	for _, key := range keys {
		if checkDataChanged(old, cur, key) {
			return true
		}
	}
	return false
}

// secret是否打开了开关注解
func IsTLSSecretAnnotationEnabled(secret *v1.Secret) bool {
	v, exist := secret.GetAnnotations()[types.AnnotationEnableTLSSecret]
	return exist && v == "true"
}

// 上传服务器证书
func (p *SecretCertService) uploadSVRCrtForSecret(ctx *secretSyncContext, secret *v1.Secret) (*string, error) {
	// mock run
	if p.MockRun {
		return nil, nil
	}
	// 上传证书
	request := ssl.NewUploadCertificateRequest()
	request.CertificateType = lo.ToPtr(string(crtTypeSVR))
	request.CertificatePublicKey = lo.ToPtr(string(secret.Data[qcloudTLSCrt]))
	request.CertificatePrivateKey = lo.ToPtr(string(secret.Data[qcloudTLSKey]))
	// [qingyangwu] 请求备注信息填写成secret的clusterid/namespace/name，方便查询｜检测泄漏证书
	request.Alias = lo.ToPtr(types.JoinKeyStrings("/", p.ClusterName, secret.Namespace, secret.Name))

	response, err := p.UpdateCertificateAPI(cloudctx.From(ctx, nil, ""), request)
	if err != nil {
		klog.Errorf("Secret [%s/%s] upload SVR certificate error. %s", secret.GetNamespace(), secret.GetName(), err.Error())
		return nil, processUploadCrtErr(ctx, err)
	}

	// 上传成功，拿到成功上传证书的CertID
	// 更新证书ID注解
	return response.Response.CertificateId, nil
}

// 上传ca证书
func (p *SecretCertService) uploadCACrtForSecret(ctx *secretSyncContext, secret *v1.Secret) (*string, error) {
	// mock run
	if p.MockRun {
		return nil, nil
	}
	// 上传证书
	request := ssl.NewUploadCertificateRequest()
	request.CertificateType = lo.ToPtr(string(crtTypeCA))
	request.CertificatePublicKey = lo.ToPtr(string(secret.Data[qcloudTLSCaCrt]))
	// TODO: 请求携带备注信息，用于检测泄漏证书
	request.Alias = lo.ToPtr(types.JoinKeyStrings("/", p.ClusterName, secret.Namespace, secret.Name))

	response, err := p.UpdateCertificateAPI(cloudctx.From(ctx, nil, ""), request)
	if err != nil {
		klog.Errorf("Secret [%s/%s] upload CA certificate error. %s", secret.GetNamespace(), secret.GetName(), err.Error())
		return nil, processUploadCrtErr(ctx, err)
	}
	// 更新证书ID注解
	return response.Response.CertificateId, nil
}

func processUploadCrtErr(sc *secretSyncContext, err error) error {
	if sdkErr, ok := lo.ErrorsAs[*clouderrors.TencentCloudSDKError](err); ok {
		switch sdkErr.Code {
		case "FailedOperation.CertificateParseError":
			return types.NewError(errcode.CertificateContentError, sdkErr.Error(), types.JoinKeyStrings("/", sc.Secret.Namespace, sc.Secret.Name))
		case "FailedOperation.CertificateMatchError":
			return types.NewError(errcode.CertificateContentError, sdkErr.Error(), types.JoinKeyStrings("/", sc.Secret.Namespace, sc.Secret.Name))
		}
	}
	return err
}

func JSON(obj interface{}) []byte {
	return lo.Must(json.Marshal(obj))
}

// 调用client-go API更新Secret
func (p *SecretCertService) updateSecret(sc *secretSyncContext, old, latest *v1.Secret) error {
	// mock run
	if p.MockRun {
		return nil
	}
	// patch
	patch, err := strategicpatch.CreateTwoWayMergePatch(JSON(old), JSON(latest), &v1.Secret{})
	if err != nil {
		klog.Errorf("Secret [%s/%s] create patch error: %s", latest.GetNamespace(), latest.GetName(), err.Error())
		return err
	}

	updated, err := p.Patch(sc, latest.GetNamespace(), latest.GetName(), patch)
	if err != nil {
		klog.Errorf("Secret [%s/%s] patch error. %s", latest.GetNamespace(), latest.GetName(), err.Error())
		return err
	}
	sc.Secret = updated
	return nil
}

func (p *SecretCertService) Patch(ctx context.Context, namespace, name string, patch []byte) (*v1.Secret, error) {
	return p.KubeClient.CoreV1().Secrets(namespace).Patch(ctx, name, k8stypes.StrategicMergePatchType, patch, metav1.PatchOptions{})
}

// [二期会用到] 通过备注（clusterID/Namespace/Name）查询Secret所属上传证书ID列表
func (p *SecretCertService) DescribeCrtsByAlias(ctx context.Context, secret *v1.Secret) ([]*string, []*string, error) {
	// ctx上下文，超时控制
	var caCrtIDs []*string
	var crtIDs []*string
	// 创建SSL Request
	request := ssl.NewDescribeCertificatesRequest()
	// 筛选上传的证书
	request.FilterSource = lo.ToPtr("upload")
	// 填写alias
	request.SearchKey = lo.ToPtr(types.JoinKeyStrings("/", p.ClusterName, secret.Namespace, secret.Name))
	// 调用 DescribeResourcesByTagsAPI 获取响应
	response, err := p.DescribeCertificatesAPI(cloudctx.From(ctx, nil, ""), request)
	if err != nil {
		klog.Errorf("Secret [%s/%s] describe certificates error. %s", secret.GetNamespace(), secret.GetName(), err.Error())
		return crtIDs, caCrtIDs, err
	}
	// 拿到云APi响应的结果切片，开始区分过滤
	// 切片[a, b, c]，从新到旧（按上传时间从新到旧）
	for _, crt := range response.Response.Certificates {
		if crt == nil || crt.CertificateType == nil {
			continue
		}
		switch crtType(*crt.CertificateType) {
		case crtTypeCA:
			caCrtIDs = append(caCrtIDs, crt.CertificateId)
		case crtTypeSVR:
			crtIDs = append(crtIDs, crt.CertificateId)
		}
	}
	return crtIDs, caCrtIDs, nil
}

func GetSecretConditionReason(secret *v1.Secret) (string, string) {
	if secret == nil {
		return "", ""
	}
	if secret.Type != v1.SecretTypeTLS {
		return "", ""
	}
	if cond, exist := secret.GetAnnotations()[types.SecretAnnotationStatusConditions]; exist {
		var conditions []metav1.Condition
		if err := json.Unmarshal([]byte(cond), &conditions); err != nil {
			klog.Errorf("Secret [%s/%s] Annotation format error %v", secret.GetName(), secret.GetNamespace(), err) // 理论上不可能出现
			return SecretErrorAnnotationUnexpected, err.Error()
		}
		for _, condition := range conditions {
			if condition.Type == "Ready" {
				return condition.Reason, condition.Message
			}
		}
	}
	return "", ""
}

func (p *SecretCertService) updateSecretAnnotationsAndStatus(sc *secretSyncContext, errs []error) error {
	// mock run || secret pointer nil || type != "kubernetes.io/tls"
	secret := sc.Secret
	if secret.Type != v1.SecretTypeTLS || p.MockRun {
		return nil
	}
	condition := metav1.Condition{
		Type:    "Ready",
		Reason:  "Success",
		Status:  metav1.ConditionTrue,
		Message: "",
	}
	// 处理错误
	if len(errs) > 0 {
		// 如果有错误，更新状态为失败
		condition.Status = metav1.ConditionFalse
		condition.Reason = "Error"
		condition.Message = errdef.Errors(errs) // 将错误信息转换为字符串
	}

	old, err := p.getSecret(secret.Namespace, secret.Name)
	if err != nil {
		return err
	}
	updated := false
	latest := old.DeepCopy()
	if len(sc.updated) > 0 {
		updated = true
	}
	if latest.Annotations == nil {
		latest.Annotations = make(map[string]string)
	}
	for k, v := range sc.updated {
		latest.Annotations[k] = v
	}
	if ca, exist := latest.Data[qcloudTLSCaCrt]; !exist || len(ca) == 0 {
		if _, exist := latest.GetAnnotations()[types.AnnotationCaCertID]; exist {
			deleteAnnotation(latest, types.AnnotationCaCertID)
			updated = true
		}
	}
	originReason, originMessage := GetSecretConditionReason(latest)
	if originReason == "Success" && condition.Reason == "Success" {
		if originMessage == "" && condition.Message == "" && !updated {
			return nil
		}
	}
	condition.LastTransitionTime = metav1.NewTime(time.Now())
	latest.Annotations[types.SecretAnnotationStatusConditions] = ToJSON([]metav1.Condition{condition})

	if err := p.updateSecret(sc, old, latest); err != nil {
		klog.Errorf("Update secret status to end %v: %s", types.JoinKeyStrings("/", secret.Namespace, secret.Name), err.Error())
		return err
	}
	klog.Infof("Update secret status to end %v: %s", types.JoinKeyStrings("/", secret.Namespace, secret.Name), errcode.Success.ReasonDetail)
	return nil
}

// 对象转换为字符串
func ToJSON(obj interface{}) string {
	if jsonStr, jsonErr := json.Marshal(obj); jsonErr == nil {
		return string(jsonStr)
	}
	return JSONFormatError
}

// 验证服务器证书，这里的内容必须要求存在（CA证书可以不存在，单向验证）
func validateCrt(secret *v1.Secret) (bool, error) {
	crt, crtOk := secret.Data[qcloudTLSCrt]
	key, keyOk := secret.Data[qcloudTLSKey]
	if !crtOk || len(crt) == 0 {
		return false, types.NewError(errcode.CertificateContentError, "secret SVR 'tls.crt' info is empty, please check the TLS config", types.JoinKeyStrings("/", secret.Namespace, secret.Name))
	}
	if !keyOk || len(key) == 0 {
		return false, types.NewError(errcode.CertificateContentError, "secret SVR 'tls.key' info is empty, please check the TLS config", types.JoinKeyStrings("/", secret.Namespace, secret.Name))
	}
	// TODO: 二期考虑添加证书校验逻辑，检查证书内容是否错误（warning级别错误）
	return true, nil
}

func validateCaCrt(secret *v1.Secret) (bool, error) {
	caCrt, exists := secret.Data[qcloudTLSCaCrt]
	if !exists {
		return false, nil // CA 证书不存在，返回 false
	}
	if len(caCrt) == 0 {
		return false, types.NewError(errcode.CertificateContentError, "secret CA 'ca.crt' info is empty, please check the TLS config", types.JoinKeyStrings("/", secret.Namespace, secret.Name))
	}
	// TODO: 考虑添加证书校验逻辑，检查证书内容是否错误，使用 CA 证书验证 SVR 证书是否匹配
	return true, nil
}

func (p *SecretCertService) pullCrt(ctx context.Context, id *string) (*ssl.DescribeCertificateDetailResponse, error) {
	if id == nil {
		return nil, errors.New("the crtID pointer is nil")
	}
	request := ssl.NewDescribeCertificateDetailRequest()
	request.CertificateId = id
	response, err := p.DescribeCertificateDetailAPI(cloudctx.From(ctx, nil, ""), request)
	return response, err
}

func trimCrt(crt string) string {
	// 对证书内容进行trim
	certificate := strings.ReplaceAll(crt, "\r", "")

	// 使用正则表达式替换多个换行符为一个换行符
	re := regexp.MustCompile(`[\n]+`)
	certificate = re.ReplaceAllString(certificate, "\n")
	return strings.TrimRight(certificate, " \n\r\t\v\000")
}

func doesNotMatchSvrCrtWithUploadedCertificate(response *ssl.DescribeCertificateDetailResponse, secret *v1.Secret, clusterID string) bool {
	// 对账证书类型
	if response.Response.CertificateType == nil || *response.Response.CertificateType != string(crtTypeSVR) {
		return true
	}
	// 对账公钥
	if response.Response.CertificatePublicKey == nil || response.Response.CertificatePrivateKey == nil {
		return true
	}
	if *response.Response.CertificatePublicKey != trimCrt(string(secret.Data[qcloudTLSCrt])) || *response.Response.CertificatePrivateKey != string(secret.Data[qcloudTLSKey]) {
		return true
	}
	// 对账证书备注不一致或没有备注，也需要上传证书
	if response.Response.Alias == nil || *response.Response.Alias != types.JoinKeyStrings("/", clusterID, secret.Namespace, secret.Name) {
		return true
	}
	return false
}

func doesNotMatchCaCrtWithUploadedCertificate(response *ssl.DescribeCertificateDetailResponse, secret *v1.Secret, clusterID string) bool {
	// 对账证书类型
	if response.Response.CertificateType == nil || *response.Response.CertificateType != string(crtTypeCA) {
		return true
	}
	// 对账公钥
	if response.Response.CertificatePublicKey == nil || *response.Response.CertificatePublicKey != trimCrt(string(secret.Data[qcloudTLSCaCrt])) {
		return true
	}
	// 对账证书备注不一致或没有备注，也需要上传证书
	if response.Response.Alias == nil || *response.Response.Alias != types.JoinKeyStrings("/", clusterID, secret.Namespace, secret.Name) {
		return true
	}
	return false
}

func (p *SecretCertService) shouldUploadCrt(ctx context.Context, secret *v1.Secret) (bool, error) {
	// 先检查secret Data区域的证书信息
	crt, exist := secret.Data[qcloudTLSCrt]
	if !exist || len(crt) == 0 {
		return false, types.NewError(errcode.CertificateContentError, "secret SVR 'tls.crt' info is empty, please check the TLS config", types.JoinKeyStrings("/", secret.Namespace, secret.Name))
	}
	key, exist := secret.Data[qcloudTLSKey]
	if !exist || len(key) == 0 {
		return false, types.NewError(errcode.CertificateContentError, "secret SVR 'tls.key' info is empty, please check the TLS config", types.JoinKeyStrings("/", secret.Namespace, secret.Name))
	}
	// 接着检查注解区域的证书ID信息
	crtID, ok := secret.GetAnnotations()[types.AnnotationCertID]
	if !ok || len(crtID) == 0 {
		return true, nil
	}
	// 存在crtID，先通过crtID查 询证书对账，不一致则上传证书（有两种潜在可能，一种是用户修改crtID，一种是证书中心更新证书导致的回写secret），都需要对账
	response, err := p.pullCrt(ctx, &crtID)
	if err != nil {
		klog.Infof("secret [%s/%s] describe svr cert [%s] err: %s", secret.Namespace, secret.Name, crtID, err.Error())
		return processDescribeCrtErr(err)
	}
	// 对账失败，需要上传证书，返回true和nil错误信息
	if doesNotMatchSvrCrtWithUploadedCertificate(response, secret, p.ClusterName) { // 对账失败，需要上传证书，返回true和nil错误信息
		return true, nil // 对账失败，需要上传证书（有两种潜在可能）p.pullCrt(ctx, &crtID)
	}
	return false, nil
}

func (p *SecretCertService) shouldUploadCaCrt(ctx context.Context, secret *v1.Secret) (bool, error) {
	// 先检查secret Data区域的证书信息
	crt, exist := secret.Data[qcloudTLSCaCrt]
	if !exist {
		return false, nil
	}
	if len(crt) == 0 {
		return false, types.NewError(errcode.CertificateContentError, "secret CA 'ca.crt' info is empty, please check the TLS config", types.JoinKeyStrings("/", secret.Namespace, secret.Name))
	}
	// 接着检查注解区域的证书ID信息
	crtID, ok := secret.GetAnnotations()[types.AnnotationCaCertID]
	if !ok || len(crtID) == 0 {
		return true, nil
	}
	// 存在crtID，先通过crtID查询证书对账，不一致则上传证书（有两种潜在可能，一种是用户修改crtID，一种是证书中心更新证书导致的回写secret），都需要对账
	response, err := p.pullCrt(ctx, &crtID)
	if err != nil {
		klog.Infof("secret [%s/%s] describe ca cert [%s] err: %s", secret.Namespace, secret.Name, crtID, err.Error())
		return processDescribeCrtErr(err)
	}
	// 对账失败，需要上传证书，返回true和nil错误信息
	if doesNotMatchCaCrtWithUploadedCertificate(response, secret, p.ClusterName) { // 对账失败，需要上传证书，返回true和nil错误信息
		return true, nil // 对账失败，需要上传证书
	}
	return false, nil
}

func processDescribeCrtErr(err error) (bool, error) {
	if sdkErr, ok := lo.ErrorsAs[*clouderrors.TencentCloudSDKError](err); ok {
		switch sdkErr.Code {
		case "FailedOperation.CertificateNotFound":
			return true, nil
		}
	}
	return false, err
}
