package services

// SetLabel 更新labels中指定key对象，不相等的key不会影响
func SetLabel(labels map[string]string, key string, value string) (map[string]string, bool) {
	if labels == nil {
		labels = make(map[string]string)
	}

	oldValue, exist := labels[key]
	if exist {
		if oldValue != value { // 如果value不相等，则更新该key
			labels[key] = value
			return labels, true
		}

		return labels, false
	}
	labels[key] = value

	return labels, true
}

func DeleteLabel(labels map[string]string, key string) (map[string]string, bool) {
	if labels == nil {
		return labels, false
	}

	_, exist := labels[key]
	if exist {
		delete(labels, key)
		return labels, true
	}

	return labels, false
}
