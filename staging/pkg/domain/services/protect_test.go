package services

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/clientcmd"

	loadbalancerresource "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/clientset/versioned"
	lbrInformer "git.woa.com/kateway/loadbalancer-resource-api/pkg/client/informers/externalversions"

	"git.woa.com/kateway/pkg/tencent/cloudctx"
)

type Credentials struct {
	SecretID  string `json:"secretId"`
	SecretKey string `json:"secretKey"`
}

func describeLBListenersAPI(ctx context.Context, request *clb.DescribeLBListenersRequest) (response *clb.DescribeLBListenersResponse, err error) {
	region := cloudctx.Region(ctx)
	// 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
	// 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
	// 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
	credentialPath := os.Getenv("CREDENTIAL")
	jsonFile, err := os.ReadFile(credentialPath)
	if err != nil {
		fmt.Println(err)
		return
	}
	var creds Credentials
	err = json.Unmarshal(jsonFile, &creds)
	if err != nil {
		fmt.Println(err)
		return
	}

	credential := common.NewCredential(
		creds.SecretID,
		creds.SecretKey,
	)
	// 实例化一个client选项，可选的，没有特殊需求可以跳过
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "clb.tencentcloudapi.com"
	// 实例化要请求产品的client对象,clientProfile是可选的
	client, _ := clb.NewClient(credential, region, cpf)

	// 返回的resp是一个DescribeLBListenersResponse的实例，与请求对象对应
	response, err = client.DescribeLBListeners(request)
	if _, ok := err.(*errors.TencentCloudSDKError); ok {
		fmt.Printf("An API error has returned: %s", err)
		return response, err
	}
	if err != nil {
		panic(err)
	}
	return response, err
}

func NewNodeGracefulDeletionManagerParam() NodeGracefulDeletionManagerParam {
	kubeconfig := os.Getenv("KUBECONFIG")
	config, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		panic(fmt.Sprintf("Error building kubeconfig: %s", err.Error()))
	}
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		panic(fmt.Sprintf("Error creating clientset: %s", err.Error()))
	}

	// 创建一个SharedInformerFactory
	factory := informers.NewSharedInformerFactory(clientset, 0)

	// 获取Node的Lister
	nodeLister := factory.Core().V1().Nodes().Lister()
	// 启动所有的informers
	stopCh := make(chan struct{})
	go func() {
		factory.Start(stopCh)
	}()

	// 等待缓存同步
	if !cache.WaitForCacheSync(stopCh, factory.Core().V1().Nodes().Informer().HasSynced) {
		panic("Timed out waiting for caches to sync")
	}

	lbrClient, err := loadbalancerresource.NewForConfig(config)
	if err != nil {
		panic(err)
	}
	lbrInformer := lbrInformer.NewSharedInformerFactory(lbrClient, 60).Networking().V1alpha1().LoadBalancerResources()
	lbrList := lbrInformer.Lister()

	return NodeGracefulDeletionManagerParam{
		MockRun:                    false,
		VpcID:                      "vpc-54uq8apv",
		RegionName:                 "ap-guangzhou",
		KubeClient:                 clientset,
		CLBRsProtection:            "tke.cloud.tencent.com/protected-by-service:-controller",
		DescribeLBListenersAPI:     describeLBListenersAPI,
		LoadBalancerResourceClient: *lbrClient,
		LoadBalancerResourceLister: lbrList,
		NodeLister:                 nodeLister,
	}
}

func newQueue() *NodeGracefulDeletionManager {
	finalizerQueueParam := NewNodeGracefulDeletionManagerParam()
	finalizerQueue := NewNodeGracefulDeletionManager(finalizerQueueParam)
	return finalizerQueue
}

// func TestQueue_AddFinalizerToNode(t *testing.T) {
//	type args struct {
//		ctx          context.Context
//		nodeName     string
//		nodeBindCLBs sets.Set[string]
//	}
//	tests := []struct {
//		name string
//		args args
//	}{
//		{
//			name: "绑定了clb添加Finalizer",
//			args: args{
//				ctx:          context.TODO(),
//				nodeName:     "**********",
//				nodeBindCLBs: sets.New("lb-4yo8u47c", "lb-xx1"),
//			},
//		},
//		{
//			name: "已经添加了Finalizer不做处理",
//			args: args{
//				ctx:          context.TODO(),
//				nodeName:     "**********",
//				nodeBindCLBs: sets.New("lb-4yo8u47c", "lb-xx1"),
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			f := newQueue()
//			err := f.addFinalizerToNode(tt.args.ctx, tt.args.nodeName, tt.args.nodeBindCLBs)
//			fmt.Println(err)
//		})
//	}
// }
//
// func TestQueue_DeleteFinalizerFromNode(t *testing.T) {
//	type args struct {
//		ctx      context.Context
//		nodeName string
//	}
//	tests := []struct {
//		name string
//		args args
//	}{
//		{
//			name: "删除Finalizer和绑定的CLB注解",
//			args: args{
//				ctx:      context.TODO(),
//				nodeName: "**********",
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			f := newQueue()
//			nodes, _ := f.param.NodeLister.List(labels.Everything())
//			for _, node := range nodes {
//				err := f.deleteFinalizerFromNode(tt.args.ctx, node.Name)
//				fmt.Println(err)
//			}
//		})
//	}
// }

//	func TestQueue_DescribeLBListeners(t *testing.T) {
//		type args struct {
//			keys sets.Set[QueueKey]
//		}
//		keys1 := sets.Set[QueueKey]{}
//		keys1.Insert(QueueKey{NodeName: "**********", PrivateIP: "**********"})
//		tests := []struct {
//			name    string
//			args    args
//			want    map[string]sets.Set[string]
//			wantErr bool
//		}{
//			{
//				name: "获取CLB列表",
//				args: args{
//					keys: keys1,
//				},
//			},
//		}
//		for _, tt := range tests {
//			t.Run(tt.name, func(t *testing.T) {
//				f := newQueue()
//				got, err := f.describeLBListeners(tt.args.keys)
//				fmt.Println(got, err)
//			})
//		}
//	}
// func TestQueue_GetNodeBindCLBs(t *testing.T) {
//	type args struct {
//		keys sets.Set[QueueKey]
//	}
//	keys1 := sets.Set[QueueKey]{}
//	keys1.Insert(QueueKey{NodeName: "**********", PrivateIP: "**********"})
//	tests := []struct {
//		name string
//		args args
//	}{
//		{
//			name: "获取节点绑定的CLB列表",
//			args: args{
//				keys: keys1,
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			f := newQueue()
//			nodeBindCLBs, failedKeys, errs := f.getCLBsByNode(context.Background(), tt.args.keys)
//			fmt.Println(nodeBindCLBs, failedKeys, errs)
//		})
//	}
// }
