package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/samber/lo"

	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/types"
)

// ControllerCollector defines base metrics about the ingress controller
type ControllerCollector struct {
	prometheus.Collector

	syncOperation    *prometheus.CounterVec
	syncTime         *prometheus.HistogramVec
	managedIngresses *prometheus.GaugeVec
	operational      *prometheus.GaugeVec

	codeMaps *metrics.SyncCodeMaps
	class    string
}

// NewControllerCollector creates a new prometheus collector for the
// Ingress controller operations
func NewControllerCollector(class string, standalone bool) *ControllerCollector {
	return &ControllerCollector{
		syncOperation: prometheus.NewCounterVec( // 同步
			prometheus.CounterOpts{
				Name: "ingress_sync_total",
				Help: `Cumulative number of sync operations`,
			},
			[]string{"class", "ingress", "ingressType", "returnCode"},
		),

		syncTime: prometheus.NewHistogramVec( // 同步时间
			prometheus.HistogramOpts{
				Name:    "ingress_sync_time_seconds",
				Help:    "Length of time per sync per controller",
				Buckets: prometheus.ExponentialBuckets(0.1, 2, 10),
			},
			[]string{"class", "ingress", "ingressType", "returnCode"},
		),

		managedIngresses: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "managed_ingress_total",
				Help: `Total number of ingress managed by the controller`,
			},
			[]string{"class", "namespace"},
		),

		operational: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: lo.Ternary(standalone, "operational_total", "ingress_operational_total"),
				Help: `Statistics of feature usage by the controller`,
			},
			[]string{"class", "ingressType", "operational"},
		),

		codeMaps: metrics.NewSyncCodeMaps(),
		class:    class,
	}
}

// IncSyncCount increment the reconcile update counter
func (c *ControllerCollector) IncSyncCount(obj types.Object, returnCode string) {
	l := prometheus.Labels{
		"class":       c.class,
		"ingress":     obj.String(),
		"ingressType": obj.Kind(),
		"returnCode":  returnCode,
	}
	c.syncOperation.With(l).Inc()

	if exist := c.codeMaps.IsCodeExists(obj, returnCode); !exist {
		c.codeMaps.AddCode(obj, returnCode)
	}
}

// UpdateSyncTime updates prometheus metrics within the controller
func (c *ControllerCollector) UpdateSyncTime(obj types.Object, returnCode string, syncTime time.Duration) {
	l := prometheus.Labels{
		"class":       c.class,
		"ingress":     obj.String(),
		"ingressType": obj.Kind(),
		"returnCode":  returnCode,
	}
	c.syncTime.With(l).Observe(syncTime.Seconds())

	if exist := c.codeMaps.IsCodeExists(obj, returnCode); !exist {
		c.codeMaps.AddCode(obj, returnCode)
	}
}

// UpdateSyncTime updates prometheus metrics within the controller
func (c *ControllerCollector) RemoveObjectMetrics(obj types.Object) {
	returnCodeMap := c.codeMaps.GetCodeMap(obj)
	for returnCode := range returnCodeMap {
		l := prometheus.Labels{
			"class":       c.class,
			"ingress":     obj.String(),
			"ingressType": obj.Kind(),
			"returnCode":  returnCode,
		}
		c.syncOperation.Delete(l)
		c.syncTime.Delete(l)
	}
	c.codeMaps.RemoveCodeMap(obj)
}

func (c *ControllerCollector) SetOperationalCount(ingressType string, operationalMap map[string]int) {
	l := prometheus.Labels{
		"class": c.class,
	}

	for operationalName, operationalCount := range operationalMap {
		count := float64(operationalCount)
		l["operational"] = operationalName
		l["ingressType"] = ingressType
		c.operational.With(l).Set(count)
	}
}

func (*ControllerCollector) IncWebhookRequestCount(prometheus.Labels)                {}
func (*ControllerCollector) UpdateWebhookDelayTime(prometheus.Labels, time.Duration) {}
func (*ControllerCollector) IncNodeChangeCount(string)                               {}

// Collect implements the prometheus.Collector interface.
func (c *ControllerCollector) Collect(ch chan<- prometheus.Metric) {
	c.syncOperation.Collect(ch)
	c.syncTime.Collect(ch)
	c.managedIngresses.Collect(ch)
	c.operational.Collect(ch)
}

func (c *ControllerCollector) Describe(ch chan<- *prometheus.Desc) {
	c.syncOperation.Describe(ch)
	c.syncTime.Describe(ch)
	c.managedIngresses.Describe(ch)
	c.operational.Describe(ch)
}
