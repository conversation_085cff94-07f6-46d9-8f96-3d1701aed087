package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"

	"git.woa.com/kateway/pkg/domain/metrics"
)

type TencentAPICollector struct {
	prometheus.Collector

	qcloudAPIRequest   *prometheus.CounterVec
	qcloudAPIDelayTime *prometheus.HistogramVec

	qcloudTaskRequest   *prometheus.CounterVec
	qcloudTaskDelayTime *prometheus.HistogramVec
}

func NewTencentAPICollector() *TencentAPICollector {
	return &TencentAPICollector{
		qcloudAPIRequest: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "ingress_qcloud_api_requests_total",
				Help: `Cumulative number of requests made to the qcloud API`,
			},
			[]string{"ingress", "request", "type", "returnCode"},
		),

		qcloudAPIDelayTime: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "ingress_qcloud_api_time_seconds",
				Help:    "Length of time per api call",
				Buckets: prometheus.ExponentialBuckets(0.1, 2, 10),
			},
			[]string{"ingress", "request", "type", "returnCode"},
		),

		qcloudTaskRequest: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "ingress_qcloud_task_requests_total",
				Help: `Cumulative number of task made to the qcloud API`,
			},
			[]string{"ingress", "request", "type", "returnCode"},
		),

		qcloudTaskDelayTime: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "ingress_qcloud_task_time_seconds",
				Help:    "Length of time per task call",
				Buckets: prometheus.ExponentialBuckets(0.1, 2, 10),
			},
			[]string{"ingress", "request", "type", "returnCode"},
		),
	}
}

func (TencentAPICollector) buildLabels(r metrics.TencentAPIRecord) prometheus.Labels {
	return prometheus.Labels{
		"ingress":    r.Module,
		"request":    r.Action,
		"type":       r.Type,
		"returnCode": r.ReturnCode,
	}
}

// IncAPIRequestCount increment the api request counter
func (c *TencentAPICollector) IncAPIRequestCount(r metrics.TencentAPIRecord) {
	c.qcloudAPIRequest.With(c.buildLabels(r)).Inc()
}

// UpdateQcloudAPIDelayTime update the api call delay
func (c *TencentAPICollector) UpdateQcloudAPIDelayTime(r metrics.TencentAPIRecord, delayTime time.Duration) {
	c.qcloudAPIDelayTime.With(c.buildLabels(r)).Observe(delayTime.Seconds())
}

// IncTaskRequestCount increment the task request counter
func (c *TencentAPICollector) IncTaskRequestCount(r metrics.TencentAPIRecord) {
	c.qcloudTaskRequest.With(c.buildLabels(r)).Inc()
}

// UpdateQcloudTaskDelayTime update the task call delay
func (c *TencentAPICollector) UpdateQcloudTaskDelayTime(r metrics.TencentAPIRecord, delayTime time.Duration) {
	c.qcloudTaskDelayTime.With(c.buildLabels(r)).Observe(delayTime.Seconds())
}

// Describe implements prometheus.Collector
func (c *TencentAPICollector) Describe(ch chan<- *prometheus.Desc) {
	c.qcloudAPIRequest.Describe(ch)
	c.qcloudAPIDelayTime.Describe(ch)
	c.qcloudTaskRequest.Describe(ch)
	c.qcloudTaskDelayTime.Describe(ch)
}

// Collect implements the prometheus.Collector interface.
func (c *TencentAPICollector) Collect(ch chan<- prometheus.Metric) {
	c.qcloudAPIRequest.Collect(ch)
	c.qcloudAPIDelayTime.Collect(ch)
	c.qcloudTaskRequest.Collect(ch)
	c.qcloudTaskDelayTime.Collect(ch)
}
