package ingress

import "github.com/samber/lo"

type IngressErrorCode struct {
	Code         string `json:"code"`
	ReasonDetail string `json:"reasonDetail"`
	Message      string `json:"message"`
	Retry        bool   `json:"retry"`
}

var (
	SuccessReason = "Success"

	UnexpectedErrorReason    = "UnexpectedError"
	DependenceErrorReason    = "DependenceError"
	WhitelistErrorReason     = "WhitelistError"
	QuotaLimitErrorReason    = "QuotaLimitError"
	AnnotationErrorReason    = "AnnotationError"
	ConfigurationErrorReason = "ConfigurationError"
	LoadbalancerErrorReason  = "LoadbalancerError"
	ListenerErrorReason      = "ListenerError"
	BackendErrorReason       = "BackendError"
	CertErrorReason          = "CertError"
)

var IngressErrorCodeMap = make(map[string]IngressErrorCode)

var (
	Success = RegisterIngressErrorCode(IngressErrorCode{Code: "S2000", ReasonDetail: SuccessReason, Message: "Success", Retry: false})

	NoAvalibaleNodeForService     = RegisterIngressErrorCode(IngressErrorCode{Code: "W1000", ReasonDetail: BackendErrorReason, Message: "Ingress: %s. The cluster has no available node for the service. cluster nodes is not ready or unscheduled.", Retry: false})
	MultyLoadbalancer             = RegisterIngressErrorCode(IngressErrorCode{Code: "W1001", ReasonDetail: UnexpectedErrorReason, Message: "Ingress: %s. Multy loadbalancer was related this ingress. %s", Retry: true})
	CleanDirtyLabel               = RegisterIngressErrorCode(IngressErrorCode{Code: "W1002", ReasonDetail: UnexpectedErrorReason, Message: "Ingress: %s. Clean label dirty data, The component will automatically retry later.", Retry: true})
	WeightZeroError               = RegisterIngressErrorCode(IngressErrorCode{Code: "W1003", ReasonDetail: BackendErrorReason, Message: "Ingress: %s. Host: %s. Path: %s. All dead triggers all alive.", Retry: false})
	SecurityGroupsNotSupportError = RegisterIngressErrorCode(IngressErrorCode{Code: "W1004", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/security-groups' only support with loadbalancer which created by tke. This is conflict with annotation `kubernetes.io/ingress.existLbId`.", Retry: false})
	PassToTargetNotSupportError   = RegisterIngressErrorCode(IngressErrorCode{Code: "W1005", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/pass-to-target' only support with loadbalancer which created by tke. This is conflict with annotation `kubernetes.io/ingress.existLbId`.", Retry: false})
	AddressIPVersionError         = RegisterIngressErrorCode(IngressErrorCode{Code: "W1006", ReasonDetail: BackendErrorReason, Message: "Ingress: %s. CLB AddressIPVersion Do not match the backend(Service: %s). CLB IPStack %s, Service IPStack %s", Retry: true})
	ReuseConcurrentOperationError = RegisterIngressErrorCode(IngressErrorCode{Code: "W1007", ReasonDetail: UnexpectedErrorReason, Message: "Ingress: %s. Reuse loadbalancer is syncing by other service. wait for syncing.", Retry: true})
	LoadBalancerNotExistError     = RegisterIngressErrorCode(IngressErrorCode{Code: "W1008", ReasonDetail: LoadbalancerErrorReason, Message: "ingress: %s. The loadbalancer has been deleted during synchronization.", Retry: true})
	EVMUpdatingError              = RegisterIngressErrorCode(IngressErrorCode{Code: "W1009", ReasonDetail: BackendErrorReason, Message: "ingress: %s. The evm pod is updating or pod eni instance is not normal status. wait for syncing again.", Retry: true})
	NodeRSLimitExceeded           = RegisterIngressErrorCode(IngressErrorCode{Code: "W1010", ReasonDetail: QuotaLimitErrorReason,
		Message: "The number of nodes expected to be registered for the rule %q exceeds the limit(expect total: %d, current quota: %d), some nodes are ignored.",
		Retry:   false,
	})
	ExplicitCLBResourceLeak = RegisterIngressErrorCode(IngressErrorCode{Code: "W1011", ReasonDetail: LoadbalancerErrorReason,
		Message: "The ingress %q has been removed, while the CLB instance %q auto-created by TKE is not deleted because the owner of the instance is set to user.",
		Retry:   false,
	})
	SingleNodeRisk = RegisterIngressErrorCode(IngressErrorCode{Code: "W1012", ReasonDetail: LoadbalancerErrorReason,
		Message: "Only one node in the backend, may lead to single point of failure.",
		Retry:   false,
	})

	EnvTkeRegionNotFound = RegisterIngressErrorCode(IngressErrorCode{Code: "E6001", ReasonDetail: UnexpectedErrorReason, Message: "%s RequestLimitExceeded.", Retry: true})
	EnvTkeVPCIdNotFound  = RegisterIngressErrorCode(IngressErrorCode{Code: "E6002", ReasonDetail: UnexpectedErrorReason, Message: "%s RequestLimitExceeded.", Retry: true})
	UnexpectedError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E6009", ReasonDetail: UnexpectedErrorReason, Message: "UnexpectedError.", Retry: true})
	ClientTimeoutError   = RegisterIngressErrorCode(IngressErrorCode{Code: "E6006", ReasonDetail: UnexpectedErrorReason, Message: "ClientError.TimeoutError %s request: %s.", Retry: true})
	ClientNetworkError   = RegisterIngressErrorCode(IngressErrorCode{Code: "E6008", ReasonDetail: UnexpectedErrorReason, Message: "ClientError.NetworkError %s request: %s.", Retry: true})
	MockError            = RegisterIngressErrorCode(IngressErrorCode{Code: "E6000", ReasonDetail: SuccessReason, Message: "%s\t%s", Retry: false})

	CLBInternalError  = RegisterIngressErrorCode(IngressErrorCode{Code: "E5003", ReasonDetail: DependenceErrorReason, Message: "CLB InternalError. %s request: %s.", Retry: true})
	CVMInternalError  = RegisterIngressErrorCode(IngressErrorCode{Code: "E5004", ReasonDetail: DependenceErrorReason, Message: "CVM InternalError. %s request: %s.", Retry: true})
	TagInternalError  = RegisterIngressErrorCode(IngressErrorCode{Code: "E5005", ReasonDetail: DependenceErrorReason, Message: "Tag InternalError. %s request: %s.", Retry: true})
	NormInternalError = RegisterIngressErrorCode(IngressErrorCode{Code: "E5007", ReasonDetail: DependenceErrorReason, Message: "Norm InternalError. %s request: %s.", Retry: true})
	TKEInternalError  = RegisterIngressErrorCode(IngressErrorCode{Code: "E5008", ReasonDetail: DependenceErrorReason, Message: "TKE InternalError. %s request: %s.", Retry: true})
	VPCInternalError  = RegisterIngressErrorCode(IngressErrorCode{Code: "E5009", ReasonDetail: DependenceErrorReason, Message: "VPC InternalError. %s request: %s.", Retry: true})
	CLBUnknownError   = RegisterIngressErrorCode(IngressErrorCode{Code: "E5010", ReasonDetail: DependenceErrorReason, Message: "CLB UnknownError. %s request: %s.", Retry: true})
	SSLInternalError  = RegisterIngressErrorCode(IngressErrorCode{Code: "E5011", ReasonDetail: DependenceErrorReason, Message: "SSL InternalError. %s request: %s.", Retry: true})
	AdmitError        = RegisterIngressErrorCode(IngressErrorCode{Code: "E5012", ReasonDetail: DependenceErrorReason, Message: "Admit Service Prevent. %s", Retry: true})

	RequestLimitExceeded       = RegisterIngressErrorCode(IngressErrorCode{Code: "E4000", ReasonDetail: QuotaLimitErrorReason, Message: "%s RequestLimitExceeded.", Retry: true})
	LoadBalancerLimitExceeded  = RegisterIngressErrorCode(IngressErrorCode{Code: "E4003", ReasonDetail: QuotaLimitErrorReason, Message: "User LoadBalancer LimitExceeded.", Retry: true})
	ListenerLimitExceeded      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4004", ReasonDetail: QuotaLimitErrorReason, Message: "User LoadBalancerListener LimitExceeded.", Retry: true})
	RuleLimitExceeded          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4005", ReasonDetail: QuotaLimitErrorReason, Message: "User LoadBalancerRule LimitExceeded.", Retry: true})
	ListenerRedirectionError   = RegisterIngressErrorCode(IngressErrorCode{Code: "E4006", ReasonDetail: ListenerErrorReason, Message: "User LoadBalancerListener Can not be deleted. Redirection config on the listener.", Retry: true})
	NormIpMasqAgentConfigError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4007", ReasonDetail: DependenceErrorReason, Message: "The ip-masq-agent-config may be changed. can not take the TKE credential.", Retry: true})
	NormTKEQCSRoleError        = RegisterIngressErrorCode(IngressErrorCode{Code: "E4008", ReasonDetail: DependenceErrorReason, Message: "CAM authorization role(TKE_QCSRole) has been revoked or changed.", Retry: true})
	SecretEmptyError           = RegisterIngressErrorCode(IngressErrorCode{Code: "E4009", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Secret name is empty. For https rule cert secret is required.", Retry: false})

	SecretNotFoundError                    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4010", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Secret %s not found", Retry: false})
	SecretContentError                     = RegisterIngressErrorCode(IngressErrorCode{Code: "E4011", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Secret %s has no data of qcloud_cert_id.", Retry: false})
	CertificateStatusError                 = RegisterIngressErrorCode(IngressErrorCode{Code: "E4012", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Certificate Status Error. (User InvalidParameterValue)", Retry: false})
	RuleHttpEmptyError                     = RegisterIngressErrorCode(IngressErrorCode{Code: "E4013", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. Ingress rules invalid. 'spec.rules.http' is empty.", Retry: false})
	LoadBalancerLocatedError               = RegisterIngressErrorCode(IngressErrorCode{Code: "E4017", ReasonDetail: UnexpectedErrorReason, Message: "Ingress: %s. LoadBalance tag has been tampered. Cannot locate resources used by Ingress.", Retry: true})
	ReuseExistLoadBalancerNotExistError    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4018", ReasonDetail: LoadbalancerErrorReason, Message: "ingress (%s) wants to use existed lbId (%s), but not exist.", Retry: false})
	ReuseExistLoadBalancerConflictTKEError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4019", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. Can not use lb: %s created by TKE for ingress.", Retry: true})

	ReuseExistLoadBalancerConflictIngressError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4020", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. Can not use lb: %s used by another ingress %s for ingress.", Retry: true})
	// ReuseExistLoadBalancerNotEmptyError        = RegisterIngressErrorCode(IngressErrorCode{Code: "E4021", Message: "Ingress: %s. Can not lb: %s listener not empty, remove all listeners and retry.", Retry: true})
	RuleHttpAnnotationError               = RegisterIngressErrorCode(IngressErrorCode{Code: "E4022", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'kubernetes.io/ingress.http-rules' is invalid.", Retry: false})
	RuleHttpsAnnotationError              = RegisterIngressErrorCode(IngressErrorCode{Code: "E4023", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'kubernetes.io/ingress.https-rules' is invalid.", Retry: false})
	LoadBalancerNameFormateError          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4024", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer name was too long to conflict. LoadBalancerName: %s", Retry: true})
	LoadBalancerResourceInsufficientError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4025", ReasonDetail: QuotaLimitErrorReason, Message: "Ingress: %s. User LoadBalancer ResourceInsufficient.", Retry: true})
	LoadBalancerExtensiveParamError       = RegisterIngressErrorCode(IngressErrorCode{Code: "E4026", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s.Ingress extensive parameters invalid. 'annotation: kubernetes.io/ingress.extensiveParameters' is invalid.", Retry: false})
	InsufficientAccountBalanceError       = RegisterIngressErrorCode(IngressErrorCode{Code: "E4027", ReasonDetail: QuotaLimitErrorReason, Message: "Insufficient Account Balance.", Retry: true})

	RuleProtocolError                       = RegisterIngressErrorCode(IngressErrorCode{Code: "E4030", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Listener type was L4 (TCP/UDP). It should be (L7) HTTP/HTTPS Listener.", Retry: true})
	RuleFormatError                         = RegisterIngressErrorCode(IngressErrorCode{Code: "E4031", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Ingress rule invalid. Invalid path.", Retry: false})
	LoadBalancerParamAddressIPVersinError   = RegisterIngressErrorCode(IngressErrorCode{Code: "E4032", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer AddressIPVersion must be IPV4、IPV6 or IPv6FullChain. AddressIPVersion: %s", Retry: false})
	LoadBalancerAddressIPVersinSupportError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4033", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer AddressIPVersion do not support in this region.", Retry: false})
	RuleHostEmptyError                      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4034", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Ingress rules invalid. 'spec.rules.host' is empty. LoadBalance(IPv6) do not have IP for default domain.", Retry: false})
	CertificateFormatError                  = RegisterIngressErrorCode(IngressErrorCode{Code: "E4035", ReasonDetail: CertErrorReason, Message: "Ingress: %s. The length of CertificateId Error. (User InvalidParameterValue)", Retry: false})
	CertificateNotFoundError                = RegisterIngressErrorCode(IngressErrorCode{Code: "E4036", ReasonDetail: CertErrorReason, Message: "Ingress: %s. CertificateId Not Found. (User InvalidParameterValue)", Retry: false})
	DirectAccessAnnotationError             = RegisterIngressErrorCode(IngressErrorCode{Code: "E4037", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/direct-access' should be true or false.", Retry: false})
	CertificateTypeError                    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4038", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Certificate not SVR type Error. (User InvalidParameterValue)", Retry: false})
	CertificateOutOfDateError               = RegisterIngressErrorCode(IngressErrorCode{Code: "E4039", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Certificate out of date Error. (User InvalidParameterValue)", Retry: false})

	SecretNotMatchError                    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4040", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Do not have any secret match this ingress resource. (host: %s)", Retry: false})
	ServiceNotFoundError                   = RegisterIngressErrorCode(IngressErrorCode{Code: "E4041", ReasonDetail: BackendErrorReason, Message: "Ingress: %s. Service(%s) is not found.", Retry: false})
	ServicePortNotFoundError               = RegisterIngressErrorCode(IngressErrorCode{Code: "E4042", ReasonDetail: BackendErrorReason, Message: "Ingress: %s. Service(%s) do not have port %v.", Retry: false})
	TkeServiceConfigNotFoundError          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4043", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. TkeServiceConfig(%s) is not found.", Retry: false})
	RuleMixedAnnotationError               = RegisterIngressErrorCode(IngressErrorCode{Code: "E4044", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'kubernetes.io/ingress.rule-mix' is invalid. should be true or false.", Retry: false})
	InternetChargeTypeAnnotationError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4045", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. Ingress annotation invalid. annotation: 'kubernetes.io/ingress.internetChargeType' should be 'TRAFFIC_POSTPAID_BY_HOUR' or 'BANDWIDTH_POSTPAID_BY_HOUR' or 'BANDWIDTH_PACKAGE'.", Retry: false})
	InternetMaxBandwidthOutAnnotationError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4046", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. Ingress annotation invalid. annotation: 'kubernetes.io/ingress.internetMaxBandwidthOut' should be a number. and in the range [0-2048].", Retry: false})
	ServiceTypeInvalidError                = RegisterIngressErrorCode(IngressErrorCode{Code: "E4047", ReasonDetail: BackendErrorReason, Message: "Ingress: %s. Service(%s) is no able to be the backend of ingress. NodePort service or LoadBalancer service is support for ingress.", Retry: false})
	SecretConflictError                    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4048", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Milty default secret is conflict.", Retry: false})
	SecretSpecifyConflictError             = RegisterIngressErrorCode(IngressErrorCode{Code: "E4049", ReasonDetail: CertErrorReason, Message: "Ingress: %s. Milty specify secret is conflict with host %s.", Retry: false})

	TkeServiceConfigAutoAnnontationError  = RegisterIngressErrorCode(IngressErrorCode{Code: "E4050", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/tke-service-config-auto' should be true or false.", Retry: false})
	TkeServiceConfigConflictError         = RegisterIngressErrorCode(IngressErrorCode{Code: "E4051", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/tke-service-config' must not suffix with '-auto-ingress-config' or '-auto-service-config'", Retry: false})
	RuleHostFormatError                   = RegisterIngressErrorCode(IngressErrorCode{Code: "E4052", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Ingress rules invalid. 'spec.rules.host' is invalid. host(%s) do not match the regex '(\\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)+'", Retry: false})
	LoadBalancerSubnetIPInsufficientError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4053", ReasonDetail: QuotaLimitErrorReason, Message: "Ingress: %s. User LoadBalancer SubnetIP ResourceInsufficient. Subnet: %s", Retry: true})
	BackendOverLimitError                 = RegisterIngressErrorCode(IngressErrorCode{Code: "E4054", ReasonDetail: QuotaLimitErrorReason, Message: "Ingress: %s. lbId %s. The number of backend reached the upper limit.", Retry: true})
	LoadBalancerParamSubnetNotExistError  = RegisterIngressErrorCode(IngressErrorCode{Code: "E4055", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer Subnet was not exist. Subnet: %s", Retry: true})
	PublicGraceShutdownAnnotationError    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4057", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/enable-grace-shutdown' should be true or false.", Retry: false})
	TKExGraceShutdownAnnotationError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4058", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/enable-grace-shutdown-tkex' should be true or false.", Retry: false})
	CrossRegionInvalidReginError          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4059", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. Cross region config error. Invalid regionId in annotation `service.cloud.tencent.com/cross-region-id` %s", Retry: true})

	SnatProNotSupportError            = RegisterIngressErrorCode(IngressErrorCode{Code: "E4060", ReasonDetail: WhitelistErrorReason, Message: "Ingress: %s. The user account do not support the SnatPro.", Retry: true})
	LoadBalancerParamTypeInvalidError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4061", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. Annotation 'kubernetes.io/ingress.loadbalancerType' is invalid. it should be OPEN or INTERNAL.", Retry: false})
	AutoRewriteAnnotationError        = RegisterIngressErrorCode(IngressErrorCode{Code: "E4062", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'ingress.cloud.tencent.com/auto-rewrite' is invalid. should be true or false.", Retry: false})
	RewriteSupportAnnotationError     = RegisterIngressErrorCode(IngressErrorCode{Code: "E4063", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'ingress.cloud.tencent.com/rewrite-support' is invalid. should be true or false.", Retry: false})
	RuleInvalidError                  = RegisterIngressErrorCode(IngressErrorCode{Code: "E4064", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Ingress rules invalid. both Rewrite and Backend is empty. see the document support of \"ingress mixed rule\" and \"ingress rewrite rule\"", Retry: false})
	LoadBalancerSubnetInvalidError    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4065", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer Subnet was not exist.", Retry: false})
	TkeServiceConfigCRDCreateError    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4066", ReasonDetail: UnexpectedErrorReason, Message: "TkeServtruiceConfigCRD CRD Create Error.", Retry: false})
	BackendsLabelAnnotationError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4067", ReasonDetail: AnnotationErrorReason, Message: "Service: %s. Annotation 'service.kubernetes.io/qcloud-loadbalancer-backends-label' is not invalid label selector.", Retry: false})
	RewriteRuleConflictError          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4068", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Rewrite rule is conflict with other rules %s.", Retry: false})
	CustomizedWeightAnnotationError   = RegisterIngressErrorCode(IngressErrorCode{Code: "E4069", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/lb-rs-weight' Unmarshal error.", Retry: false})

	CrossRegionConfigError           = RegisterIngressErrorCode(IngressErrorCode{Code: "E4070", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Cross region config error. use annotation `ingress.cloud.tencent.com/cross-region-id` with `ingress.cloud.tencent.com/cross-vpc-id` or `ingress.kubernetes.io/tke-existed-lbid`.", Retry: false})
	CrossRegionCNNConfigError        = RegisterIngressErrorCode(IngressErrorCode{Code: "E4071", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. Cross region config error. config must meet the following conditions simultaneously: a. The VPC where the load balancer is located must be in the same CCN as the VPC where the cluster is located. b. The VPC must be in the same APPID as the TKE cluster (cross APPID VPCs are not supported).", Retry: false})
	LoadBalancerDoNotSupportCNNError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4072", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. Cross region config error. loadbalancer do not has vpc attributes, can not link to the CCN.", Retry: false})
	NetworkNotRegisterToCNNError     = RegisterIngressErrorCode(IngressErrorCode{Code: "E4073", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. The container network is not registered to the CCN.", Retry: false})
	NodeInsufficient                 = RegisterIngressErrorCode(IngressErrorCode{Code: "E4074", ReasonDetail: BackendErrorReason, Message: "Ingress: %s. Some node in cluster is locked or insufficient balance, please remove or unscheduled these node.", Retry: false})
	RegexPathIllegal                 = RegisterIngressErrorCode(IngressErrorCode{Code: "E4075", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Regex path is illegal, path can only use the letter、number and character -/.\\%%?=:#&^*[]$. and it should be a legal regular expression. invalid path: %s", Retry: false})
	PathIllegal                      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4076", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Exact path is illegal, path can only use the letter、number and character -/.\\%%?=:#&. or use it with NonAbsolutePath. invalid path: %s", Retry: false})
	PathLengthIllegal                = RegisterIngressErrorCode(IngressErrorCode{Code: "E4077", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. Path length cannot exceed 200.", Retry: false})
	ConflictPathType                 = RegisterIngressErrorCode(IngressErrorCode{Code: "E4078", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. annotation: 'kubernetes.io/ingress.http-rules' or 'kubernetes.io/ingress.http-rules' is invalid. annotation has conflict pathType. pathType: %s.", Retry: false})
	UnsupportedPathType              = RegisterIngressErrorCode(IngressErrorCode{Code: "E4079", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. annotation: 'kubernetes.io/ingress.http-rules' or 'kubernetes.io/ingress.http-rules' is invalid. unsupported pathType. pathType: %s.", Retry: false})

	RewriteInvalidPathType                     = RegisterIngressErrorCode(IngressErrorCode{Code: "E4080", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. annotation: 'kubernetes.io/ingress.http-rules' or 'kubernetes.io/ingress.https-rules' is invalid. rewrite can not use with regex path or exact path.", Retry: false})
	ConflictRule                               = RegisterIngressErrorCode(IngressErrorCode{Code: "E4081", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. annotation: 'kubernetes.io/ingress.http-rules' or 'kubernetes.io/ingress.https-rules' has conflict rule. all of the rule in annotation, (host, path, backend) must unique.", Retry: false})
	NoSelectorService                          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4082", ReasonDetail: BackendErrorReason, Message: "Ingress: %s. LoadBalancer Service(%s) without label selector won't have any endpoint, even if you set Endpoints.", Retry: false})
	LabelStoredError                           = RegisterIngressErrorCode(IngressErrorCode{Code: "E4084", ReasonDetail: DependenceErrorReason, Message: "Ingress: %s. The label data failed to be stored within one minute. The component will automatically retry later.", Retry: true})
	SnatProNotSupportForLoadbalancerError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4085", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. The Loadbalancer do not support the SnatPro.", Retry: true})
	ModificationProtectionAnnontationError     = RegisterIngressErrorCode(IngressErrorCode{Code: "E4086", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s Annotation 'ingress.cloud.tencent.com/modification-protection' should be true or false.", Retry: false})
	ReuseNotSupportModificationProtectionError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4087", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s Annotation 'ingress.cloud.tencent.com/modification-protection' do not support the service with reuse loadbalancer.", Retry: false})
	ModificationProtectionConflictError        = RegisterIngressErrorCode(IngressErrorCode{Code: "E4088", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s Loadbalancer was protected by user or another service.", Retry: true})
	ModificationProtectionDoNotSupportError    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4089", ReasonDetail: WhitelistErrorReason, Message: "Ingress: %s Please check the white list about the CLB. Function modification protection may not be open.", Retry: true})

	HybridCloudWithoutSnatProSetting = RegisterIngressErrorCode(IngressErrorCode{Code: "E4090", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/hybrid-type', but not setting the snatIp from annotation 'ingress.cloud.tencent.com/snat-pro-info', please read the document for usage help.", Retry: false})
	CrossRegionWithoutSnatProSetting = RegisterIngressErrorCode(IngressErrorCode{Code: "E4091", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/cross-type', but not setting the snatIp from annotation 'ingress.cloud.tencent.com/snat-pro-info', please read the document for usage help.", Retry: false})
	SnatIPLimitExceeded              = RegisterIngressErrorCode(IngressErrorCode{Code: "E4092", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/snat-pro-info', snat ip can't exceed 10.", Retry: false})
	PassToTargetAnnotationError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4093", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/pass-to-target' is invalid. should be true or false.", Retry: false})
	SecurityGroupsAnnotationError    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4094", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/security-groups' is invalid. security group id is over limit. all of the security group id should join by ','", Retry: false})
	MixIpTargetError                 = RegisterIngressErrorCode(IngressErrorCode{Code: "E4095", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. open MixIpTarget error, FullChain IPv6 CLB should open MixIpTarget before open SNATPro.", Retry: true})
	SecurityGroupsFormatError        = RegisterIngressErrorCode(IngressErrorCode{Code: "E4096", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/security-groups' is invalid. any of security group id format error.", Retry: false})
	SecurityGroupsNotExistError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4097", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/security-groups' is invalid. any of security group id is not exist.", Retry: false})
	ReuseFormatInvalidError          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4098", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. The loadbalancer id in annotation `ingress.kubernetes.io/tke-existed-lbid` is format invalid.", Retry: false})
	ConflictListenerError            = RegisterIngressErrorCode(IngressErrorCode{Code: "E4099", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s The loadbalancer has port conflict in the listener %s.", Retry: true})

	ReuseBelongToAnotherClusterError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4100", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. The loadbalancer in annotation `kubernetes.io/ingress.existLbId` is used by other cluster.", Retry: true})
	ReuseConflictListenerError       = RegisterIngressErrorCode(IngressErrorCode{Code: "E4101", ReasonDetail: ListenerErrorReason, Message: "Ingress: %s. The loadbalancer in annotation `kubernetes.io/ingress.existLbId` has port conflict with other resource. conflict listener info(%s)", Retry: true})
	HybridCNNConfigErrorError        = RegisterIngressErrorCode(IngressErrorCode{Code: "E4102", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/hybrid-type:CCN'. VPC do not join the CCN.", Retry: true})
	SnatIPAnnotationFormatError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4103", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/snat-pro-info', annotation format error.", Retry: false})
	PassToTargetSettingError         = RegisterIngressErrorCode(IngressErrorCode{Code: "E4104", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/pass-to-target', but ModifyLoadBalancerAttributes api error.", Retry: false})
	ServiceCloseNodePortsError       = RegisterIngressErrorCode(IngressErrorCode{Code: "E4105", ReasonDetail: ConfigurationErrorReason, Message: "Ingress: %s. Rules(%s) Service(%s) allocateLoadBalancerNodePorts is false.", Retry: false})

	LoadBalancerClientTokenHasExpiredError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4106", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. The ClientToken in annotation `ingress.cloud.tencent.com/client-token` has expired.", Retry: true})
	InvalidHealthCheckDomainError          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4107", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s. Invalid health check domain of %q.", Retry: false})
	RSLimitExceeded                        = RegisterIngressErrorCode(IngressErrorCode{Code: "E4108", ReasonDetail: QuotaLimitErrorReason,
		Message: "The number of rs expected to be register for the rule %q exceeds the limit(expect total: %d, current quota: %d), some rs are ignored.",
		Retry:   false,
	})

	TkeServiceConfigInvalidError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4109", ReasonDetail: UnexpectedErrorReason, Message: "TkeServiceConfig of Ingress: %s Invalid Error", Retry: false})

	NotBelongToSpecifiedClusterError          = RegisterIngressErrorCode(IngressErrorCode{Code: "E4200", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s is used 'ingress.cloud.tencent.com/from-other-cluster' specified from cluster, but clb is not belong the target master cluster(%s).", Retry: true})
	BackendManageOnlyMustUseExistLBError      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4201", ReasonDetail: AnnotationErrorReason, Message: "Ingress: %s use 'ingress.cloud.tencent.com/backend-manage-only' must use existed CLB.", Retry: false})
	MultiClusterDeleteWithBackend             = RegisterIngressErrorCode(IngressErrorCode{Code: "E4202", ReasonDetail: LoadbalancerErrorReason, Message: "Ingress: %s MultiClusterIngress is deleted, but backend is not empty. ", Retry: true})
	ChildIngressDeleteWithoutBackendOnly      = RegisterIngressErrorCode(IngressErrorCode{Code: "E4203", ReasonDetail: AnnotationErrorReason, Message: "Ingress in child cluster: %s is deleting, but not using 'ingress.cloud.tencent.com/backend-manage-only'", Retry: false})
	RuleNotExistedInLBWhenEnsuringTargets     = RegisterIngressErrorCode(IngressErrorCode{Code: "E4204", ReasonDetail: LoadbalancerErrorReason, Message: "Rule is not existed when ensuring targets.", Retry: true})
	ListenerNotExistedInLBWhenEnsuringTargets = RegisterIngressErrorCode(IngressErrorCode{Code: "E4205", ReasonDetail: LoadbalancerErrorReason, Message: "Listener is not existed when ensuring targets.", Retry: true})
	InvalidListenPortError                    = RegisterIngressErrorCode(IngressErrorCode{Code: "E4206", ReasonDetail: ConfigurationErrorReason, Retry: false})
	ConflictListenPortError                   = RegisterIngressErrorCode(IngressErrorCode{Code: "E4207", ReasonDetail: ConfigurationErrorReason, Retry: false})
	AutoRewritePortRequiredError              = RegisterIngressErrorCode(IngressErrorCode{Code: "E4208", ReasonDetail: ConfigurationErrorReason, Retry: false})
	ListenProtocolUnsupportedError            = RegisterIngressErrorCode(IngressErrorCode{Code: "E4209", ReasonDetail: ConfigurationErrorReason, Retry: false})

	CertificateAlgorithmTypeError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4210", ReasonDetail: CertErrorReason, Message: "Ingress: %s. The MultiCertInfo of the CertList algorithm types cannot be repeated. (User InvalidParameterValue)", Retry: false})

	PatchRSTagAnnontationError = RegisterIngressErrorCode(IngressErrorCode{Code: "E4110", ReasonDetail: LoadbalancerErrorReason,
		Message: "Ingress %s intents to patch RS tags but failed",
		Retry:   true})
)

func RegisterIngressErrorCode(ingressErrorCode IngressErrorCode) IngressErrorCode {
	IngressErrorCodeMap[ingressErrorCode.Code] = ingressErrorCode
	return ingressErrorCode
}

func IsRSLimitExceededErrOrWarn(err error) bool {
	ingErr, ok := lo.ErrorsAs[*IngressError](err)
	if !ok {
		return false
	}
	return ingErr.ErrorCode.Code == NodeRSLimitExceeded.Code || ingErr.ErrorCode.Code == RSLimitExceeded.Code
}

func ContainsRSLimitExceededErrOrWarn(errs []error) bool {
	_, exists := lo.Find(errs, IsRSLimitExceededErrOrWarn)
	return exists
}
