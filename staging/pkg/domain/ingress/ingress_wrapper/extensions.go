package ingress_wrapper

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	extensions "k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/ingress/annotation"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
)

type IngressWrapperExtensions struct {
	annotation.Interface
	extensionsIngress *extensions.Ingress
}

type IngressTLSWrapperExtensions struct {
	extensionsIngress *extensions.IngressTLS
}

func NewIngressWrapperExtensions(ingress *extensions.Ingress) types.Ingress {
	if ingress == nil {
		return &IngressWrapperExtensions{}
	}

	if ingress.Annotations == nil {
		ingress.Annotations = map[string]string{}
	}

	return &IngressWrapperExtensions{
		Interface:         annotation.New(&ingress.Annotations),
		extensionsIngress: ingress,
	}
}

func NewIngressWrapperExtensionsList(ingresses []*extensions.Ingress) []types.Ingress {
	ingressWrapperExtensionss := make([]types.Ingress, len(ingresses))
	for index, ingress := range ingresses {
		ingressWrapperExtensionss[index] = NewIngressWrapperExtensions(ingress)
	}
	return ingressWrapperExtensionss
}

func (w *IngressWrapperExtensions) Version() string {
	return extensions.SchemeGroupVersion.Version
}

func (w *IngressWrapperExtensions) ResourceVersion() string {
	return w.extensionsIngress.ResourceVersion
}

func (w *IngressWrapperExtensions) String() string {
	return fmt.Sprintf("%s/%s", w.extensionsIngress.Namespace, w.extensionsIngress.Name)
}

func (w *IngressWrapperExtensions) GetRuntimeObject() runtime.Object {
	if w.extensionsIngress == nil {
		return nil
	}

	return w.extensionsIngress
}

func (w *IngressWrapperExtensions) Kind() string {
	return string(w.Type())
}

func (w *IngressWrapperExtensions) Update(ctx context.Context) (types.Ingress, error) {
	ingress, err := cluster.Instance.KubeClient().ExtensionsV1beta1().Ingresses(w.Namespace()).Update(ctx, w.extensionsIngress, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	return NewIngressWrapperExtensions(ingress), nil
}

func (w *IngressWrapperExtensions) UpdateAnnotation(ctx context.Context, update map[string]string, deleteAnnotation []string) (types.Ingress, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	currentIngress, err := cli.ExtensionsV1beta1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return NewIngressWrapperExtensions(currentIngress), err
	}
	if currentIngress.Annotations == nil {
		currentIngress.Annotations = make(map[string]string, 0)
	}
	isUpdated := false
	if update != nil && len(update) != 0 {
		for key, value := range update {
			if oldvalue, exist := currentIngress.Annotations[key]; !exist || oldvalue != value {
				currentIngress.Annotations[key] = value
				isUpdated = true
			}
		}
	}
	if deleteAnnotation != nil && len(deleteAnnotation) != 0 {
		for _, key := range deleteAnnotation {
			if _, exist := currentIngress.Annotations[key]; exist {
				delete(currentIngress.Annotations, key)
				isUpdated = true
			}
		}
	}
	if isUpdated {
		if cluster.Instance.Enabled(featuregates.DryRunIngress) {
			return w, types.NewError(errcode.MockError, "", fmt.Sprintf("UpdateAnnotation update: %v, delete: %v", update, deleteAnnotation), fmt.Sprintf("%s/%s", w.Namespace(), w.Name()))
		}
		updatedIngress, err := cli.ExtensionsV1beta1().Ingresses(w.Namespace()).Update(ctx, currentIngress, metav1.UpdateOptions{})
		if err != nil {
			return NewIngressWrapperExtensions(currentIngress), err
		}
		return NewIngressWrapperExtensions(updatedIngress), nil
	}
	return NewIngressWrapperExtensions(currentIngress), nil
}

func (w *IngressWrapperExtensions) UpdateLoadBalancerStatus(ctx context.Context, loadBalancerStatus v1.LoadBalancerStatus) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return types.NewError(errcode.MockError, "", "UpdateLoadBalancerStatus", fmt.Sprintf("%s/%s", w.Namespace(), w.Name()))
	}
	currentIngress, err := cli.ExtensionsV1beta1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	currentIngress.Status = extensions.IngressStatus{
		LoadBalancer: types.FromLoadBalancerStatus[extensions.IngressLoadBalancerStatus](loadBalancerStatus),
	}
	if _, err := cli.ExtensionsV1beta1().Ingresses(w.Namespace()).UpdateStatus(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func (w *IngressWrapperExtensions) HasFinalizer(ctx context.Context, finalizer string) (bool, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	currentIngress, err := cli.ExtensionsV1beta1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return false, err
	}
	for _, f := range currentIngress.Finalizers {
		if f == finalizer {
			return true, nil
		}
	}
	return false, nil
}

func (w *IngressWrapperExtensions) AddFinalizer(ctx context.Context, finalizer string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return nil
	}
	currentIngress, err := cli.ExtensionsV1beta1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	hasFinalizer, err := w.HasFinalizer(ctx, finalizer)
	if err != nil {
		return err
	}
	if !hasFinalizer {
		currentIngress.Finalizers = append(currentIngress.Finalizers, finalizer)
		if _, err = cli.ExtensionsV1beta1().Ingresses(w.Namespace()).Update(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (w *IngressWrapperExtensions) RemoveFinalizer(ctx context.Context, finalizer string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return nil
	}
	currentIngress, err := cli.ExtensionsV1beta1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	needsUpdate := false
	for i := 0; i < len(currentIngress.Finalizers); i++ {
		if currentIngress.Finalizers[i] == finalizer {
			currentIngress.Finalizers = append(currentIngress.Finalizers[:i], currentIngress.Finalizers[i+1:]...)
			i--
			needsUpdate = true
		}
	}
	if needsUpdate {
		if _, err = cli.ExtensionsV1beta1().Ingresses(w.Namespace()).Update(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (w *IngressWrapperExtensions) GetObjectMeta() metav1.Object {
	return w.extensionsIngress
}

func (w *IngressWrapperExtensions) RuntimeObject() runtime.Object {
	if w.extensionsIngress == nil {
		return nil
	}
	return w.extensionsIngress
}

func (w *IngressWrapperExtensions) ObjectReference() v1.ObjectReference {
	return v1.ObjectReference{
		Kind:            "Ingress",
		APIVersion:      extensions.SchemeGroupVersion.String(),
		Namespace:       w.extensionsIngress.Namespace,
		Name:            w.extensionsIngress.Name,
		ResourceVersion: w.extensionsIngress.ResourceVersion,
		UID:             w.extensionsIngress.UID,
	}
}

func (w *IngressWrapperExtensions) Type() types.IngressType {
	return types.CoreIngress
}

func (w *IngressWrapperExtensions) Namespace() string {
	return w.extensionsIngress.Namespace
}

func (w *IngressWrapperExtensions) Name() string {
	return w.extensionsIngress.Name
}

func (w *IngressWrapperExtensions) Annotations() map[string]string {
	return w.extensionsIngress.Annotations
}

func (w *IngressWrapperExtensions) IngressClassName() *string {
	return w.extensionsIngress.Spec.IngressClassName
}

func (w *IngressWrapperExtensions) TLS() []types.IngressTLS {
	ingressTLSWrapperExtensions := make([]types.IngressTLS, len(w.extensionsIngress.Spec.TLS))
	for index, tls := range w.extensionsIngress.Spec.TLS {
		ingressTLSWrapperExtensions[index] = types.IngressTLS{
			Hosts:      tls.Hosts,
			SecretName: tls.SecretName,
		}
	}
	return ingressTLSWrapperExtensions
}

func (w *IngressTLSWrapperExtensions) Hosts() []string {
	return w.extensionsIngress.Hosts
}

func (w *IngressTLSWrapperExtensions) SecretName() string {
	return w.extensionsIngress.SecretName
}

func (w *IngressWrapperExtensions) Rules() []types.IngressRule {
	ingressRuleWrapperExtensionss := make([]types.IngressRule, len(w.extensionsIngress.Spec.Rules))
	for index, r := range w.extensionsIngress.Spec.Rules {
		rule := types.IngressRule{
			Host: r.Host,
		}
		if r.HTTP != nil {
			rule.HTTPPaths = lo.Map(r.HTTP.Paths, func(p extensions.HTTPIngressPath, _ int) types.HTTPIngressPath {
				pp := types.HTTPIngressPath{
					Path:     p.Path,
					PathType: string(extensions.PathTypeImplementationSpecific),
					Backend: types.IngressBackend{
						ServiceName: p.Backend.ServiceName,
						ServicePort: p.Backend.ServicePort,
					},
				}
				if p.PathType != nil {
					pp.PathType = string(*p.PathType)
				}
				return pp
			})
		}
		ingressRuleWrapperExtensionss[index] = rule
	}
	return ingressRuleWrapperExtensionss
}

func (w *IngressWrapperExtensions) StatusLoadBalancer() v1.LoadBalancerStatus {
	return types.ToLoadBalancerStatus(w.extensionsIngress.Status.LoadBalancer)
}

func (w *IngressWrapperExtensions) UID() string {
	return string(w.extensionsIngress.UID)
}

func (w *IngressWrapperExtensions) DeletionTimestamp() *metav1.Time {
	return w.extensionsIngress.GetDeletionTimestamp()
}

func (w *IngressWrapperExtensions) DeepCopy() types.Ingress {
	return NewIngressWrapperExtensions(w.extensionsIngress.DeepCopy())
}

func (w *IngressWrapperExtensions) IsDryRun() bool {
	return false
}

func (w *IngressWrapperExtensions) SaveDryRunError(err error) {
}
