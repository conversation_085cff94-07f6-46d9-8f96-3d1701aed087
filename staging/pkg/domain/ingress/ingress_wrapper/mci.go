package ingress_wrapper

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"

	multiClusteringress "git.woa.com/kateway/multi-cluster-ingress-api/apis/multiclusteringress/v1alpha1"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/ingress/annotation"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
)

type IngressWrapperMultiCluster struct {
	annotation.Interface

	multiClusterIngress *multiClusteringress.MultiClusterIngress
}

type IngressTLSWrapperMultiCluster struct {
	multiClusterIngress *multiClusteringress.IngressTLS
}

func NewIngressWrapperMultiCluster(ingress *multiClusteringress.MultiClusterIngress) types.Ingress {
	if ingress == nil {
		return &IngressWrapperMultiCluster{}
	}

	if ingress.Annotations == nil {
		ingress.Annotations = map[string]string{}
	}

	return &IngressWrapperMultiCluster{
		Interface:           annotation.New(&ingress.Annotations),
		multiClusterIngress: ingress,
	}
}

func (w *IngressWrapperMultiCluster) Version() string {
	return multiClusteringress.SchemeGroupVersion.Version
}

func (w *IngressWrapperMultiCluster) ResourceVersion() string {
	return w.multiClusterIngress.ResourceVersion
}

func (w *IngressWrapperMultiCluster) String() string {
	return fmt.Sprintf("%s/%s", w.multiClusterIngress.Namespace, w.multiClusterIngress.Name)
}

func (w *IngressWrapperMultiCluster) GetRuntimeObject() runtime.Object {
	return w.multiClusterIngress
}

func (w *IngressWrapperMultiCluster) Kind() string {
	return string(w.Type())
}

func NewIngressWrapperMultiClusterList(ingresses []*multiClusteringress.MultiClusterIngress) []types.Ingress {
	ingressWrapperMultiClusters := make([]types.Ingress, len(ingresses))
	for index, ingress := range ingresses {
		ingressWrapperMultiClusters[index] = NewIngressWrapperMultiCluster(ingress)
	}
	return ingressWrapperMultiClusters
}

func (w *IngressWrapperMultiCluster) GetObjectMeta() metav1.Object {
	return w.multiClusterIngress
}

func (w *IngressWrapperMultiCluster) RuntimeObject() runtime.Object {
	if w.multiClusterIngress == nil {
		return nil
	}

	return w.multiClusterIngress
}

func (w *IngressWrapperMultiCluster) ObjectReference() v1.ObjectReference {
	return v1.ObjectReference{
		Kind:            "MultiClusterIngress",
		APIVersion:      multiClusteringress.SchemeGroupVersion.String(),
		Namespace:       w.multiClusterIngress.Namespace,
		Name:            w.multiClusterIngress.Name,
		ResourceVersion: w.multiClusterIngress.ResourceVersion,
		UID:             w.multiClusterIngress.UID,
	}
}

func (w *IngressWrapperMultiCluster) Type() types.IngressType {
	return types.MultiClusterIngress
}

func (w *IngressWrapperMultiCluster) Namespace() string {
	return w.multiClusterIngress.Namespace
}

func (w *IngressWrapperMultiCluster) Name() string {
	return w.multiClusterIngress.Name
}

func (w *IngressWrapperMultiCluster) Annotations() map[string]string {
	return w.multiClusterIngress.Annotations
}

func (w *IngressWrapperMultiCluster) IngressClassName() *string {
	return w.multiClusterIngress.Spec.IngressClassName
}

func (w *IngressWrapperMultiCluster) TLS() []types.IngressTLS {
	ingressTLSWrapperMultiCluster := make([]types.IngressTLS, len(w.multiClusterIngress.Spec.TLS))
	for index, tls := range w.multiClusterIngress.Spec.TLS {
		ingressTLSWrapperMultiCluster[index] = types.IngressTLS{
			Hosts:      tls.Hosts,
			SecretName: tls.SecretName,
		}
	}
	return ingressTLSWrapperMultiCluster
}

func (w *IngressWrapperMultiCluster) StatusLoadBalancer() v1.LoadBalancerStatus {
	return w.multiClusterIngress.Status.LoadBalancer
}

func (w *IngressWrapperMultiCluster) UID() string {
	return string(w.multiClusterIngress.UID)
}

func (w *IngressWrapperMultiCluster) Update(ctx context.Context) (types.Ingress, error) {
	ingress, err := cluster.Instance.MultiClusterIngressClient().CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Update(ctx, w.multiClusterIngress, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	return NewIngressWrapperMultiCluster(ingress), nil
}

func (w *IngressWrapperMultiCluster) UpdateAnnotation(ctx context.Context, update map[string]string, deleteAnnotation []string) (types.Ingress, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.MultiClusterIngressClient()
	currIng, err := cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return NewIngressWrapperMultiCluster(currIng), err
	}
	if currIng.Annotations == nil {
		currIng.Annotations = make(map[string]string, 0)
	}
	isUpdated := false
	if len(update) != 0 {
		for key, value := range update {
			if oldvalue, exist := currIng.Annotations[key]; !exist || oldvalue != value {
				currIng.Annotations[key] = value
				isUpdated = true
			}
		}
	}
	if len(deleteAnnotation) != 0 {
		for _, key := range deleteAnnotation {
			if _, exist := currIng.Annotations[key]; exist {
				delete(currIng.Annotations, key)
				isUpdated = true
			}
		}
	}
	if isUpdated {
		if cluster.Instance.Enabled(featuregates.DryRunIngress) {
			return w, types.NewError(errcode.MockError, "", fmt.Sprintf("UpdateAnnotation update: %v, delete: %v", update, deleteAnnotation), fmt.Sprintf("%s/%s", w.Namespace(), w.Name()))
		}
		updatedIngress, err := cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Update(ctx, currIng, metav1.UpdateOptions{})
		if err != nil {
			return NewIngressWrapperMultiCluster(currIng), err
		}
		return NewIngressWrapperMultiCluster(updatedIngress), err
	}
	return NewIngressWrapperMultiCluster(currIng), nil
}

func (w *IngressWrapperMultiCluster) UpdateLoadBalancerStatus(ctx context.Context, loadBalancerStatus v1.LoadBalancerStatus) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.MultiClusterIngressClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return types.NewError(errcode.MockError, "", "UpdateLoadBalancerStatus", fmt.Sprintf("%s/%s", w.Namespace(), w.Name()))
	}
	currentIngress, err := cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	currentIngress.Status = multiClusteringress.MultiClusterIngressStatus{
		LoadBalancer: loadBalancerStatus,
	}
	if _, err := cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).UpdateStatus(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func (w *IngressWrapperMultiCluster) HasFinalizer(ctx context.Context, finalizer string) (bool, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.MultiClusterIngressClient()
	currentIngress, err := cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return false, err
	}
	for _, f := range currentIngress.Finalizers {
		if f == finalizer {
			return true, nil
		}
	}
	return false, nil
}

func (w *IngressWrapperMultiCluster) AddFinalizer(ctx context.Context, finalizer string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.MultiClusterIngressClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return nil
	}
	currentIngress, err := cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	hasFinalizer, err := w.HasFinalizer(ctx, finalizer)
	if err != nil {
		return err
	}
	if !hasFinalizer {
		currentIngress.Finalizers = append(currentIngress.Finalizers, finalizer)
		if _, err = cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Update(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (w *IngressWrapperMultiCluster) RemoveFinalizer(ctx context.Context, finalizer string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.MultiClusterIngressClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return nil
	}
	currentIngress, err := cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	needsUpdate := false
	for i := 0; i < len(currentIngress.Finalizers); i++ {
		if currentIngress.Finalizers[i] == finalizer {
			currentIngress.Finalizers = append(currentIngress.Finalizers[:i], currentIngress.Finalizers[i+1:]...)
			i--
			needsUpdate = true
		}
	}
	if needsUpdate {
		if _, err = cli.CloudV1alpha1().MultiClusterIngresses(w.Namespace()).Update(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (w *IngressTLSWrapperMultiCluster) Hosts() []string {
	return w.multiClusterIngress.Hosts
}

func (w *IngressTLSWrapperMultiCluster) SecretName() string {
	return w.multiClusterIngress.SecretName
}

func (w *IngressWrapperMultiCluster) Rules() []types.IngressRule {
	ingressRuleWrapperMultiClusters := make([]types.IngressRule, len(w.multiClusterIngress.Spec.Rules))
	for index, r := range w.multiClusterIngress.Spec.Rules {
		rule := types.IngressRule{
			Host: r.Host,
		}
		if r.HTTP != nil {
			rule.HTTPPaths = lo.Map(r.HTTP.Paths, func(p multiClusteringress.HTTPIngressPath, _ int) types.HTTPIngressPath {
				pp := types.HTTPIngressPath{
					Path:     p.Path,
					PathType: string(lo.FromPtr(p.PathType)),
				}
				bsvc := p.Backend.Service
				if bsvc != nil {
					pp.Backend.ServiceName = bsvc.Name
					pp.Backend.ServicePort = lo.Ternary(bsvc.Port.Number != 0,
						intstr.FromInt(int(bsvc.Port.Number)), intstr.FromString(bsvc.Port.Name))
				}
				return pp
			})
		}
		ingressRuleWrapperMultiClusters[index] = rule
	}
	return ingressRuleWrapperMultiClusters
}

func (w *IngressWrapperMultiCluster) DeletionTimestamp() *metav1.Time {
	return w.multiClusterIngress.GetDeletionTimestamp()
}

func (w *IngressWrapperMultiCluster) DeepCopy() types.Ingress {
	return NewIngressWrapperMultiCluster(w.multiClusterIngress.DeepCopy())
}

func (w *IngressWrapperMultiCluster) IsDryRun() bool {
	return false
}

func (w *IngressWrapperMultiCluster) SaveDryRunError(err error) {
}

func SetLoadBalancerConditions(ctx context.Context, namespace, name string, conditions []metav1.Condition) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.MultiClusterIngressClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return types.NewError(errcode.MockError, "", "SetLoadBalancerConditions", fmt.Sprintf("%s/%s", namespace, name))
	}
	currentIngress, err := cli.CloudV1alpha1().MultiClusterIngresses(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return err
	}
	currentIngress.Status.Conditions = conditions
	if _, err := cli.CloudV1alpha1().MultiClusterIngresses(namespace).UpdateStatus(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func GetLoadBalancerConditions(ctx context.Context, namespace, name string) (conditions []metav1.Condition, err error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.MultiClusterIngressClient()
	currentIngress, err := cli.CloudV1alpha1().MultiClusterIngresses(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	return currentIngress.Status.Conditions, nil
}
