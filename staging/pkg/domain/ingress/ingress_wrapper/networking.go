package ingress_wrapper

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	networking "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/ingress/annotation"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
)

type IngressWrapperNetworking struct {
	annotation.Interface

	networkingIngress *networking.Ingress
}

type IngressTLSWrapperNetworking struct {
	networkingIngress *networking.IngressTLS
}

func NewIngressWrapperNetworking(ingress *networking.Ingress) types.Ingress {
	if ingress == nil {
		return &IngressWrapperNetworking{}
	}
	if ingress.Annotations == nil {
		ingress.Annotations = map[string]string{}
	}

	return &IngressWrapperNetworking{
		Interface:         annotation.New(&ingress.Annotations),
		networkingIngress: ingress,
	}
}

func (w *IngressWrapperNetworking) Version() string {
	return networking.SchemeGroupVersion.Version
}

func (w *IngressWrapperNetworking) ResourceVersion() string {
	return w.networkingIngress.ResourceVersion
}

func (w *IngressWrapperNetworking) String() string {
	return fmt.Sprintf("%s/%s", w.networkingIngress.Namespace, w.networkingIngress.Name)
}

func (w *IngressWrapperNetworking) GetRuntimeObject() runtime.Object {
	if w.networkingIngress == nil {
		return nil
	}

	return w.networkingIngress
}

func NewIngressWrapperNetworkingList(ingresses []*networking.Ingress) []types.Ingress {
	ingressWrapperNetworkings := make([]types.Ingress, len(ingresses))
	for index, ingress := range ingresses {
		ingressWrapperNetworkings[index] = NewIngressWrapperNetworking(ingress)
	}
	return ingressWrapperNetworkings
}

func (w *IngressWrapperNetworking) Kind() string {
	return string(w.Type())
}

func (w *IngressWrapperNetworking) GetObjectMeta() metav1.Object {
	return w.networkingIngress
}

func (w *IngressWrapperNetworking) RuntimeObject() runtime.Object {
	if w.networkingIngress == nil {
		return nil
	}
	return w.networkingIngress
}

func (w *IngressWrapperNetworking) ObjectReference() v1.ObjectReference {
	return v1.ObjectReference{
		Kind:            "Ingress",
		APIVersion:      networking.SchemeGroupVersion.String(),
		Namespace:       w.networkingIngress.Namespace,
		Name:            w.networkingIngress.Name,
		ResourceVersion: w.networkingIngress.ResourceVersion,
		UID:             w.networkingIngress.UID,
	}
}

func (w *IngressWrapperNetworking) Type() types.IngressType {
	return types.CoreIngress
}

func (w *IngressWrapperNetworking) Namespace() string {
	return w.networkingIngress.Namespace
}

func (w *IngressWrapperNetworking) Name() string {
	return w.networkingIngress.Name
}

func (w *IngressWrapperNetworking) Annotations() map[string]string {
	return w.networkingIngress.Annotations
}

func (w *IngressWrapperNetworking) IngressClassName() *string {
	return w.networkingIngress.Spec.IngressClassName
}

func (w *IngressWrapperNetworking) TLS() []types.IngressTLS {
	ingressTLSWrapperNetworking := make([]types.IngressTLS, len(w.networkingIngress.Spec.TLS))
	for index, tls := range w.networkingIngress.Spec.TLS {
		ingressTLSWrapperNetworking[index] = types.IngressTLS{
			Hosts:      tls.Hosts,
			SecretName: tls.SecretName,
		}
	}
	return ingressTLSWrapperNetworking
}

func (w *IngressWrapperNetworking) StatusLoadBalancer() v1.LoadBalancerStatus {
	return types.ToLoadBalancerStatus(w.networkingIngress.Status.LoadBalancer)
}

func (w *IngressWrapperNetworking) UID() string {
	return string(w.networkingIngress.UID)
}

func (w *IngressWrapperNetworking) Update(ctx context.Context) (types.Ingress, error) {
	ingress, err := cluster.Instance.KubeClient().NetworkingV1().Ingresses(w.Namespace()).Update(ctx, w.networkingIngress, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	return NewIngressWrapperNetworking(ingress), nil
}

func (w *IngressWrapperNetworking) UpdateAnnotation(ctx context.Context, update map[string]string, deleteAnnotation []string) (types.Ingress, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	currIng, err := cli.NetworkingV1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return NewIngressWrapperNetworking(currIng), err
	}
	if currIng.Annotations == nil {
		currIng.Annotations = make(map[string]string, 0)
	}
	isUpdated := false
	if update != nil && len(update) != 0 {
		for key, value := range update {
			if oldvalue, exist := currIng.Annotations[key]; !exist || oldvalue != value {
				currIng.Annotations[key] = value
				isUpdated = true
			}
		}
	}
	if deleteAnnotation != nil && len(deleteAnnotation) != 0 {
		for _, key := range deleteAnnotation {
			if _, exist := currIng.Annotations[key]; exist {
				delete(currIng.Annotations, key)
				isUpdated = true
			}
		}
	}
	if isUpdated {
		if cluster.Instance.Enabled(featuregates.DryRunIngress) {
			return w, types.NewError(errcode.MockError, "", fmt.Sprintf("UpdateAnnotation update: %v, delete: %v", update, deleteAnnotation), fmt.Sprintf("%s/%s", w.Namespace(), w.Name()))
		}
		updatedIngress, err := cli.NetworkingV1().Ingresses(w.Namespace()).Update(ctx, currIng, metav1.UpdateOptions{})
		if err != nil {
			return NewIngressWrapperNetworking(currIng), err
		}
		return NewIngressWrapperNetworking(updatedIngress), err
	}
	return NewIngressWrapperNetworking(currIng), nil
}

func (w *IngressWrapperNetworking) UpdateLoadBalancerStatus(ctx context.Context, loadBalancerStatus v1.LoadBalancerStatus) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return types.NewError(errcode.MockError, "", "UpdateLoadBalancerStatus", fmt.Sprintf("%s/%s", w.Namespace(), w.Name()))
	}
	currentIngress, err := cli.NetworkingV1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	currentIngress.Status = networking.IngressStatus{
		LoadBalancer: types.FromLoadBalancerStatus[networking.IngressLoadBalancerStatus](loadBalancerStatus),
	}
	if _, err := cli.NetworkingV1().Ingresses(w.Namespace()).UpdateStatus(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func (w *IngressWrapperNetworking) HasFinalizer(ctx context.Context, finalizer string) (bool, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	currentIngress, err := cli.NetworkingV1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return false, err
	}
	for _, f := range currentIngress.Finalizers {
		if f == finalizer {
			return true, nil
		}
	}
	return false, nil
}

func (w *IngressWrapperNetworking) AddFinalizer(ctx context.Context, finalizer string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return nil
	}
	currentIngress, err := cli.NetworkingV1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	hasFinalizer, err := w.HasFinalizer(ctx, finalizer)
	if err != nil {
		return err
	}
	if !hasFinalizer {
		currentIngress.Finalizers = append(currentIngress.Finalizers, finalizer)
		if _, err = cli.NetworkingV1().Ingresses(w.Namespace()).Update(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (w *IngressWrapperNetworking) RemoveFinalizer(ctx context.Context, finalizer string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	cli := cluster.Instance.KubeClient()
	if cluster.Instance.Enabled(featuregates.DryRunIngress) {
		return nil
	}
	currentIngress, err := cli.NetworkingV1().Ingresses(w.Namespace()).Get(ctx, w.Name(), metav1.GetOptions{})
	if err != nil {
		return err
	}
	needsUpdate := false
	for i := 0; i < len(currentIngress.Finalizers); i++ {
		if currentIngress.Finalizers[i] == finalizer {
			currentIngress.Finalizers = append(currentIngress.Finalizers[:i], currentIngress.Finalizers[i+1:]...)
			i--
			needsUpdate = true
		}
	}
	if needsUpdate {
		if _, err = cli.NetworkingV1().Ingresses(w.Namespace()).Update(ctx, currentIngress, metav1.UpdateOptions{}); err != nil {
			return err
		}
	}
	return nil
}

func (w *IngressTLSWrapperNetworking) Hosts() []string {
	return w.networkingIngress.Hosts
}

func (w *IngressTLSWrapperNetworking) SecretName() string {
	return w.networkingIngress.SecretName
}

func (w *IngressWrapperNetworking) Rules() []types.IngressRule {
	ingressRuleWrapperNetworkings := make([]types.IngressRule, len(w.networkingIngress.Spec.Rules))
	for index, r := range w.networkingIngress.Spec.Rules {
		rule := types.IngressRule{
			Host: r.Host,
		}
		if r.HTTP != nil {
			rule.HTTPPaths = lo.Map(r.HTTP.Paths, func(p networking.HTTPIngressPath, _ int) types.HTTPIngressPath {
				pp := types.HTTPIngressPath{
					Path:     p.Path,
					PathType: string(lo.FromPtr(p.PathType)),
				}
				bsvc := p.Backend.Service
				if bsvc != nil {
					pp.Backend.ServiceName = bsvc.Name
					pp.Backend.ServicePort = lo.Ternary(bsvc.Port.Number != 0, intstr.FromInt(int(bsvc.Port.Number)), intstr.FromString(bsvc.Port.Name))
				}
				return pp
			})
		}
		ingressRuleWrapperNetworkings[index] = rule
	}
	return ingressRuleWrapperNetworkings
}

func (w *IngressWrapperNetworking) DeletionTimestamp() *metav1.Time {
	return w.networkingIngress.GetDeletionTimestamp()
}

func (w *IngressWrapperNetworking) DeepCopy() types.Ingress {
	return NewIngressWrapperNetworking(w.networkingIngress.DeepCopy())
}

func (w *IngressWrapperNetworking) IsDryRun() bool {
	return false
}

func (w *IngressWrapperNetworking) SaveDryRunError(err error) {
}
