package annotation

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestIngressRegistry_SyncBeginTime(t *testing.T) {
	item := Ingress().SyncBeginTime()
	fmt.Println(item)
	assert.Equal(t, "ingress.cloud.tencent.com/sync-begin-time", item)
}

func TestIngressRegistry_ChaosErrorcode(t *testing.T) {
	item := Ingress().ChaosErrorcode()
	fmt.Println(item)
	assert.Equal(t, "ingress.cloud.tencent.com/chaos-errorcode", item)
}

func TestIngressRegistry_DeletionProtection(t *testing.T) {
	item := Ingress().DeletionProtection()
	fmt.Println(item)
	assert.Equal(t, "ingress.cloud.tencent.com/deletion-protection", item)
}

func TestIngressRegistry_ForceDelete(t *testing.T) {
	item := Ingress().ForceDelete()
	fmt.Println(item)
	assert.Equal(t, "ingress.cloud.tencent.com/force-delete", item)
}

func TestIngressAnnotations_GetChaosErrorcode(t *testing.T) {
	type fields struct {
		annotations map[string]string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			name: "nil value",
			fields: fields{
				annotations: nil,
			},
			want: "",
		},
		{
			name: "empty value",
			fields: fields{
				annotations: map[string]string{},
			},
			want: "",
		},
		{
			name: "parse success",
			fields: fields{
				annotations: map[string]string{
					Ingress().ChaosErrorcode(): "E5012",
				},
			},
			want: "E5012",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &IngressAnnotations{
				Items: &tt.fields.annotations,
			}
			got := a.GetChaosErrorcode()
			assert.Equalf(t, tt.want, got, "GetChaosErrorcode()")
		})
	}
}

func TestIngressAnnotations_GetSyncStartTime(t *testing.T) {
	type fields struct {
		annotations map[string]string
	}
	tests := []struct {
		name    string
		fields  fields
		want    time.Time
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "nil value",
			fields: fields{
				annotations: nil,
			},
			want:    time.Time{},
			wantErr: assert.Error,
		},
		{
			name: "empty value",
			fields: fields{
				annotations: map[string]string{},
			},
			want:    time.Time{},
			wantErr: assert.Error,
		},
		{
			name: "parse error",
			fields: fields{
				annotations: map[string]string{
					Ingress().SyncBeginTime(): "2020-01-01 12:00:00",
				},
			},
			want:    time.Time{},
			wantErr: assert.Error,
		},
		{
			name: "parse success",
			fields: fields{
				annotations: map[string]string{
					Ingress().SyncBeginTime(): "2020-01-01T12:00:00Z",
				},
			},
			want:    time.Date(2020, 1, 1, 12, 0, 0, 0, time.UTC),
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &IngressAnnotations{
				Items: &tt.fields.annotations,
			}
			got, err := a.GetSyncBeginTime()
			if !tt.wantErr(t, err, "GetSyncStartTime()") {
				return
			}
			assert.Equalf(t, tt.want, got, "GetSyncStartTime()")
		})
	}
}
