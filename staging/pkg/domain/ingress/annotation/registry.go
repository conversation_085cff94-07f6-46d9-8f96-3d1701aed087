package annotation

import "git.woa.com/kateway/pkg/k8s/k8sutil/annotation"

const (
	k8sPrefix       = "kubernetes.io"
	defaultPrefix   = "ingress.cloud.tencent.com"
	exampleTemplate = `
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
{{- range .}}
    {{. -}}
{{end}}
  name: example
spec:
  rules:
  - host: example.com
    http:
      paths:
      - backend:
          service:
            name: example
            port:
              number: 80
        path: /
        pathType: ImplementationSpecific
`
)

type Schema interface {
	List(options annotation.ListOptions) annotation.Items
	IsReadOnly(key string) bool

	StatusConditions() string
	SyncBeginTime() string
	SyncEndTime() string
	DeletionProtection() string
	ChaosErrorcode() string
	ForceDelete() string
}

var registry *IngressRegistry

type IngressRegistry struct {
	*annotation.Registry
}

func Ingress() Schema {
	return registry
}

func (s *IngressRegistry) Register(items annotation.Items) {
	s.Registry.Register(items)
}

func (s *IngressRegistry) List(options annotation.ListOptions) annotation.Items {
	return s.Registry.List(options)
}

func (s *IngressRegistry) StatusConditions() string {
	return s.GetByCaller().String()
}

func (s *IngressRegistry) IsReadOnly(key string) bool {
	return s.Registry.IsReadOnly(key)
}

func (s *IngressRegistry) SyncBeginTime() string {
	return s.GetByCaller().String()
}

func (s *IngressRegistry) SyncEndTime() string {
	return s.GetByCaller().String()
}

func (s *IngressRegistry) DeletionProtection() string {
	return s.GetByCaller().String()
}

func (s *IngressRegistry) ChaosErrorcode() string {
	return s.GetByCaller().String()
}

func (s *IngressRegistry) ForceDelete() string {
	return s.GetByCaller().String()
}
