package annotation

import (
	"fmt"
	"time"

	"github.com/spf13/cast"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Interface interface {
	// FilterReadOnly 过滤掉只读的注解
	FilterReadOnly() map[string]string
	SetStatusConditions(conditions []metav1.Condition)
	SetSyncBeginTime(t time.Time)
	GetSyncBeginTime() (time.Time, error)
	SetSyncEndTime(t time.Time)
	GetSyncEndTime() (time.Time, error)
	DeletionProtection() *bool
	GetChaosErrorcode() string
	ForceDelete() bool
}

type IngressAnnotations struct {
	Items *map[string]string
}

func New(annotations *map[string]string) *IngressAnnotations {
	if *annotations == nil {
		*annotations = map[string]string{}
	}
	return &IngressAnnotations{
		Items: annotations,
	}
}

func (a *IngressAnnotations) get(key string) (value string, ok bool) {
	if a.Items == nil {
		return "", false
	}

	if *a.Items == nil {
		return "", false
	}

	value, ok = (*a.Items)[key] // TODO: 实现根据key解码

	return
}

func (a *IngressAnnotations) set(key string, value any) {
	if a.Items == nil {
		a.Items = &map[string]string{}
	}

	(*a.Items)[key] = cast.ToString(value) // TODO: 实现根据key value编码
}

func (a *IngressAnnotations) parseTime(value string) (time.Time, error) {
	if value == "" {
		return time.Time{}, fmt.Errorf("empty time")
	}

	t, err := time.Parse(time.RFC3339, value)
	if err != nil {
		return time.Time{}, fmt.Errorf("parse %s error: %w", value, err)
	}

	return t, nil
}

func (a *IngressAnnotations) parseBool(value string) (bool, error) {
	switch value {
	case "true", "TRUE", "True":
		return true, nil
	case "false", "FALSE", "False":
		return false, nil
	}
	return false, fmt.Errorf("parse bool: %s error", value)
}

func (a *IngressAnnotations) FilterReadOnly() map[string]string {
	if a.Items == nil {
		return nil
	}

	annotations := map[string]string{}

	for k, v := range *a.Items {
		if !Ingress().IsReadOnly(k) {
			annotations[k] = v
		}
	}

	return annotations
}

func (a *IngressAnnotations) SetSyncBeginTime(t time.Time) {
	a.set(Ingress().SyncBeginTime(), t.Format(time.RFC3339))
}

func (a *IngressAnnotations) GetSyncBeginTime() (time.Time, error) {
	v, _ := a.get(Ingress().SyncBeginTime())
	return a.parseTime(v)
}

func (a *IngressAnnotations) SetSyncEndTime(t time.Time) {
	a.set(Ingress().SyncEndTime(), t.Format(time.RFC3339))
}

func (a *IngressAnnotations) GetSyncEndTime() (time.Time, error) {
	v, _ := a.get(Ingress().SyncEndTime())
	return a.parseTime(v)
}

func (a *IngressAnnotations) SetStatusConditions(conditions []metav1.Condition) {
	a.set(Ingress().StatusConditions(), conditions)
}

func (a *IngressAnnotations) DeletionProtection() *bool {
	v, exist := a.get(Ingress().DeletionProtection())
	if !exist {
		return nil
	}

	enabled, err := a.parseBool(v)
	if err != nil {
		return nil
	}

	return &enabled
}

func (a *IngressAnnotations) ForceDelete() bool {
	v, exist := a.get(Ingress().ForceDelete())
	if !exist {
		return false
	}

	enabled, err := a.parseBool(v)
	if err != nil {
		return false
	}

	return enabled
}

func (a *IngressAnnotations) GetChaosErrorcode() string {
	v, _ := a.get(Ingress().ChaosErrorcode())
	return v
}
