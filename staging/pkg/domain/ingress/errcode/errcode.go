package errcode

import (
	"github.com/samber/lo"

	"git.woa.com/kateway/pkg/domain/types"
)

var Registry = types.NewErrorRegistry()

func Register(errorCode types.ErrorCode) types.ErrorCode {
	return Registry.Register(errorCode)
}

var (
	SuccessReason = "Success"

	UnexpectedErrorReason    = "UnexpectedError"
	DependenceErrorReason    = "DependenceError"
	WhitelistErrorReason     = "WhitelistError"
	QuotaLimitErrorReason    = "QuotaLimitError"
	AnnotationErrorReason    = "AnnotationError"
	ConfigurationErrorReason = "ConfigurationError"
	LoadbalancerErrorReason  = "LoadbalancerError"
	ListenerErrorReason      = "ListenerError"
	BackendErrorReason       = "BackendError"
	CertErrorReason          = "CertError"
)

var (
	Success = Register(types.ErrorCode{Code: "S2000", ReasonDetail: types.SuccessReason, Message: "Success", Retry: false})

	NoAvalibaleNodeForService     = Register(types.ErrorCode{Code: "W1000", ReasonDetail: types.BackendErrorReason, Message: "Ingress: %s. The cluster has no available node for the service. cluster nodes is not ready or unscheduled.", Retry: false})
	MultyLoadbalancer             = Register(types.ErrorCode{Code: "W1001", ReasonDetail: types.UnexpectedErrorReason, Message: "Ingress: %s. Multy loadbalancer was related this ingress. %s", Retry: true})
	CleanDirtyLabel               = Register(types.ErrorCode{Code: "W1002", ReasonDetail: types.UnexpectedErrorReason, Message: "Ingress: %s. Clean label dirty data, The component will automatically retry later.", Retry: true})
	WeightZeroError               = Register(types.ErrorCode{Code: "W1003", ReasonDetail: types.BackendErrorReason, Message: "Ingress: %s. Host: %s. Path: %s. All dead triggers all alive.", Retry: false})
	SecurityGroupsNotSupportError = Register(types.ErrorCode{Code: "W1004", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/security-groups' only support with loadbalancer which created by tke. This is conflict with annotation `kubernetes.io/ingress.existLbId`.", Retry: false})
	PassToTargetNotSupportError   = Register(types.ErrorCode{Code: "W1005", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/pass-to-target' only support with loadbalancer which created by tke. This is conflict with annotation `kubernetes.io/ingress.existLbId`.", Retry: false})
	AddressIPVersionError         = Register(types.ErrorCode{Code: "W1006", ReasonDetail: types.BackendErrorReason, Message: "Ingress: %s. CLB AddressIPVersion Do not match the backend(Service: %s). CLB IPStack %s, Service IPStack %s", Retry: true})
	ReuseConcurrentOperationError = Register(types.ErrorCode{Code: "W1007", ReasonDetail: types.UnexpectedErrorReason, Message: "Ingress: %s. Reuse loadbalancer is syncing by other service. wait for syncing.", Retry: true})
	LoadBalancerNotExistError     = Register(types.ErrorCode{Code: "W1008", ReasonDetail: types.LoadbalancerErrorReason, Message: "ingress: %s. The loadbalancer has been deleted during synchronization.", Retry: true})
	EVMUpdatingError              = Register(types.ErrorCode{Code: "W1009", ReasonDetail: types.BackendErrorReason, Message: "ingress: %s. The evm pod is updating or pod eni instance is not normal status. wait for syncing again.", Retry: true})
	NodeRSLimitExceeded           = Register(types.ErrorCode{Code: "W1010", ReasonDetail: types.QuotaLimitErrorReason,
		Message: "The number of nodes expected to be registered for the rule %q exceeds the limit(expect total: %d, current quota: %d), some nodes are ignored.",
		Retry:   false,
	})
	ExplicitCLBResourceLeak = Register(types.ErrorCode{Code: "W1011", ReasonDetail: types.LoadbalancerErrorReason,
		Message: "The ingress %q has been removed, while the CLB instance %q auto-created by TKE is not deleted because the owner of the instance is set to user.",
		Retry:   false,
	})
	SingleNodeRisk = Register(types.ErrorCode{Code: "W1012", ReasonDetail: types.LoadbalancerErrorReason,
		Message: "Only one node in the backend, may lead to single point of failure.",
		Retry:   false,
	})

	EnvTkeRegionNotFound = Register(types.ErrorCode{Code: "E6001", ReasonDetail: types.UnexpectedErrorReason, Message: "%s RequestLimitExceeded.", Retry: true})
	EnvTkeVPCIdNotFound  = Register(types.ErrorCode{Code: "E6002", ReasonDetail: types.UnexpectedErrorReason, Message: "%s RequestLimitExceeded.", Retry: true})
	UnexpectedError      = Register(types.ErrorCode{Code: "E6009", ReasonDetail: types.UnexpectedErrorReason, Message: "UnexpectedError.", Retry: true})
	ClientTimeoutError   = Register(types.ErrorCode{Code: "E6006", ReasonDetail: types.UnexpectedErrorReason, Message: "ClientError.TimeoutError %s request: %s.", Retry: true})
	ClientNetworkError   = Register(types.ErrorCode{Code: "E6008", ReasonDetail: types.UnexpectedErrorReason, Message: "ClientError.NetworkError %s request: %s.", Retry: true})
	MockError            = Register(types.ErrorCode{Code: "E6000", ReasonDetail: types.SuccessReason, Message: "%s\t%s", Retry: false})

	CLBInternalError  = Register(types.ErrorCode{Code: "E5003", ReasonDetail: types.DependenceErrorReason, Message: "CLB InternalError. %s request: %s.", Retry: true})
	CVMInternalError  = Register(types.ErrorCode{Code: "E5004", ReasonDetail: types.DependenceErrorReason, Message: "CVM InternalError. %s request: %s.", Retry: true})
	TagInternalError  = Register(types.ErrorCode{Code: "E5005", ReasonDetail: types.DependenceErrorReason, Message: "Tag InternalError. %s request: %s.", Retry: true})
	NormInternalError = Register(types.ErrorCode{Code: "E5007", ReasonDetail: types.DependenceErrorReason, Message: "Norm InternalError. %s request: %s.", Retry: true})
	TKEInternalError  = Register(types.ErrorCode{Code: "E5008", ReasonDetail: types.DependenceErrorReason, Message: "TKE InternalError. %s request: %s.", Retry: true})
	VPCInternalError  = Register(types.ErrorCode{Code: "E5009", ReasonDetail: types.DependenceErrorReason, Message: "VPC InternalError. %s request: %s.", Retry: true})
	CLBUnknownError   = Register(types.ErrorCode{Code: "E5010", ReasonDetail: types.DependenceErrorReason, Message: "CLB UnknownError. %s request: %s.", Retry: true})
	SSLInternalError  = Register(types.ErrorCode{Code: "E5011", ReasonDetail: types.DependenceErrorReason, Message: "SSL InternalError. %s request: %s.", Retry: true})
	AdmitError        = Register(types.ErrorCode{Code: "E5012", ReasonDetail: types.DependenceErrorReason, Message: "Admit Service Prevent. %s", Retry: true})

	RequestLimitExceeded       = Register(types.ErrorCode{Code: "E4000", ReasonDetail: types.QuotaLimitErrorReason, Message: "%s RequestLimitExceeded.", Retry: true})
	LoadBalancerLimitExceeded  = Register(types.ErrorCode{Code: "E4003", ReasonDetail: types.QuotaLimitErrorReason, Message: "User LoadBalancer LimitExceeded.", Retry: true})
	ListenerLimitExceeded      = Register(types.ErrorCode{Code: "E4004", ReasonDetail: types.QuotaLimitErrorReason, Message: "User LoadBalancerListener LimitExceeded.", Retry: true})
	RuleLimitExceeded          = Register(types.ErrorCode{Code: "E4005", ReasonDetail: types.QuotaLimitErrorReason, Message: "User LoadBalancerRule LimitExceeded.", Retry: true})
	ListenerRedirectionError   = Register(types.ErrorCode{Code: "E4006", ReasonDetail: types.ListenerErrorReason, Message: "User LoadBalancerListener Can not be deleted. Redirection config on the listener.", Retry: true})
	NormIpMasqAgentConfigError = Register(types.ErrorCode{Code: "E4007", ReasonDetail: types.DependenceErrorReason, Message: "The ip-masq-agent-config may be changed. can not take the TKE credential.", Retry: true})
	NormTKEQCSRoleError        = Register(types.ErrorCode{Code: "E4008", ReasonDetail: types.DependenceErrorReason, Message: "CAM authorization role(TKE_QCSRole) has been revoked or changed.", Retry: true})
	SecretEmptyError           = Register(types.ErrorCode{Code: "E4009", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Secret name is empty. For https rule cert secret is required.", Retry: false})

	SecretNotFoundError                    = Register(types.ErrorCode{Code: "E4010", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Secret %s not found", Retry: false})
	SecretContentError                     = Register(types.ErrorCode{Code: "E4011", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Secret %s has no data of qcloud_cert_id.", Retry: false})
	CertificateStatusError                 = Register(types.ErrorCode{Code: "E4012", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Certificate Status Error. (User InvalidParameterValue)", Retry: false})
	RuleHttpEmptyError                     = Register(types.ErrorCode{Code: "E4013", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. Ingress rules invalid. 'spec.rules.http' is empty.", Retry: false})
	LoadBalancerLocatedError               = Register(types.ErrorCode{Code: "E4017", ReasonDetail: types.UnexpectedErrorReason, Message: "Ingress: %s. LoadBalance tag has been tampered. Cannot locate resources used by Ingress.", Retry: true})
	ReuseExistLoadBalancerNotExistError    = Register(types.ErrorCode{Code: "E4018", ReasonDetail: types.LoadbalancerErrorReason, Message: "ingress (%s) wants to use existed lbId (%s), but not exist.", Retry: false})
	ReuseExistLoadBalancerConflictTKEError = Register(types.ErrorCode{Code: "E4019", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. Can not use lb: %s created by TKE for ingress.", Retry: true})

	ReuseExistLoadBalancerConflictIngressError = Register(types.ErrorCode{Code: "E4020", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. Can not use lb: %s used by another ingress %s for ingress.", Retry: true})
	// ReuseExistLoadBalancerNotEmptyError        = Register(types.ErrorCode{Code: "E4021", Message: "Ingress: %s. Can not lb: %s listener not empty, remove all listeners and retry.", Retry: true})
	RuleHttpAnnotationError               = Register(types.ErrorCode{Code: "E4022", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'kubernetes.io/ingress.http-rules' is invalid.", Retry: false})
	RuleHttpsAnnotationError              = Register(types.ErrorCode{Code: "E4023", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'kubernetes.io/ingress.https-rules' is invalid.", Retry: false})
	LoadBalancerNameFormateError          = Register(types.ErrorCode{Code: "E4024", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer name was too long to conflict. LoadBalancerName: %s", Retry: true})
	LoadBalancerResourceInsufficientError = Register(types.ErrorCode{Code: "E4025", ReasonDetail: types.QuotaLimitErrorReason, Message: "Ingress: %s. User LoadBalancer ResourceInsufficient.", Retry: true})
	LoadBalancerExtensiveParamError       = Register(types.ErrorCode{Code: "E4026", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s.Ingress extensive parameters invalid. 'annotation: kubernetes.io/ingress.extensiveParameters' is invalid.", Retry: false})
	InsufficientAccountBalanceError       = Register(types.ErrorCode{Code: "E4027", ReasonDetail: types.QuotaLimitErrorReason, Message: "Insufficient Account Balance.", Retry: true})

	RuleProtocolError                       = Register(types.ErrorCode{Code: "E4030", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Listener type was L4 (TCP/UDP). It should be (L7) HTTP/HTTPS Listener.", Retry: true})
	RuleFormatError                         = Register(types.ErrorCode{Code: "E4031", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Ingress rule invalid. Invalid path.", Retry: false})
	LoadBalancerParamAddressIPVersinError   = Register(types.ErrorCode{Code: "E4032", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer AddressIPVersion must be IPV4、IPV6 or IPv6FullChain. AddressIPVersion: %s", Retry: false})
	LoadBalancerAddressIPVersinSupportError = Register(types.ErrorCode{Code: "E4033", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer AddressIPVersion do not support in this region.", Retry: false})
	RuleHostEmptyError                      = Register(types.ErrorCode{Code: "E4034", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Ingress rules invalid. 'spec.rules.host' is empty. LoadBalance(IPv6) do not have IP for default domain.", Retry: false})
	CertificateFormatError                  = Register(types.ErrorCode{Code: "E4035", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. The length of CertificateId Error. (User InvalidParameterValue)", Retry: false})
	CertificateNotFoundError                = Register(types.ErrorCode{Code: "E4036", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. CertificateId Not Found. (User InvalidParameterValue)", Retry: false})
	DirectAccessAnnotationError             = Register(types.ErrorCode{Code: "E4037", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/direct-access' should be true or false.", Retry: false})
	CertificateTypeError                    = Register(types.ErrorCode{Code: "E4038", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Certificate not SVR type Error. (User InvalidParameterValue)", Retry: false})
	CertificateOutOfDateError               = Register(types.ErrorCode{Code: "E4039", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Certificate out of date Error. (User InvalidParameterValue)", Retry: false})

	SecretNotMatchError                    = Register(types.ErrorCode{Code: "E4040", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Do not have any secret match this ingress resource. (host: %s)", Retry: false})
	ServiceNotFoundError                   = Register(types.ErrorCode{Code: "E4041", ReasonDetail: types.BackendErrorReason, Message: "Ingress: %s. Service(%s) is not found.", Retry: false})
	ServicePortNotFoundError               = Register(types.ErrorCode{Code: "E4042", ReasonDetail: types.BackendErrorReason, Message: "Ingress: %s. Service(%s) do not have port %v.", Retry: false})
	TkeServiceConfigNotFoundError          = Register(types.ErrorCode{Code: "E4043", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. TkeServiceConfig(%s) is not found.", Retry: false})
	RuleMixedAnnotationError               = Register(types.ErrorCode{Code: "E4044", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'kubernetes.io/ingress.rule-mix' is invalid. should be true or false.", Retry: false})
	InternetChargeTypeAnnotationError      = Register(types.ErrorCode{Code: "E4045", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. Ingress annotation invalid. annotation: 'kubernetes.io/ingress.internetChargeType' should be 'TRAFFIC_POSTPAID_BY_HOUR' or 'BANDWIDTH_POSTPAID_BY_HOUR' or 'BANDWIDTH_PACKAGE'.", Retry: false})
	InternetMaxBandwidthOutAnnotationError = Register(types.ErrorCode{Code: "E4046", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. Ingress annotation invalid. annotation: 'kubernetes.io/ingress.internetMaxBandwidthOut' should be a number. and in the range [0-2048].", Retry: false})
	ServiceTypeInvalidError                = Register(types.ErrorCode{Code: "E4047", ReasonDetail: types.BackendErrorReason, Message: "Ingress: %s. Service(%s) is no able to be the backend of ingress. NodePort service or LoadBalancer service is support for ingress.", Retry: false})
	SecretConflictError                    = Register(types.ErrorCode{Code: "E4048", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Milty default secret is conflict.", Retry: false})
	SecretSpecifyConflictError             = Register(types.ErrorCode{Code: "E4049", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. Milty specify secret is conflict with host %s.", Retry: false})

	TkeServiceConfigAutoAnnontationError  = Register(types.ErrorCode{Code: "E4050", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/tke-service-config-auto' should be true or false.", Retry: false})
	TkeServiceConfigConflictError         = Register(types.ErrorCode{Code: "E4051", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/tke-service-config' must not suffix with '-auto-ingress-config' or '-auto-service-config'", Retry: false})
	RuleHostFormatError                   = Register(types.ErrorCode{Code: "E4052", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Ingress rules invalid. 'spec.rules.host' is invalid. host(%s) do not match the regex '(\\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)+'", Retry: false})
	LoadBalancerSubnetIPInsufficientError = Register(types.ErrorCode{Code: "E4053", ReasonDetail: types.QuotaLimitErrorReason, Message: "Ingress: %s. User LoadBalancer SubnetIP ResourceInsufficient. Subnet: %s", Retry: true})
	BackendOverLimitError                 = Register(types.ErrorCode{Code: "E4054", ReasonDetail: types.QuotaLimitErrorReason, Message: "Ingress: %s. lbId %s. The number of backend reached the upper limit.", Retry: true})
	LoadBalancerParamSubnetNotExistError  = Register(types.ErrorCode{Code: "E4055", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer Subnet was not exist. Subnet: %s", Retry: true})
	PublicGraceShutdownAnnotationError    = Register(types.ErrorCode{Code: "E4057", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/enable-grace-shutdown' should be true or false.", Retry: false})
	TKExGraceShutdownAnnotationError      = Register(types.ErrorCode{Code: "E4058", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/enable-grace-shutdown-tkex' should be true or false.", Retry: false})
	CrossRegionInvalidReginError          = Register(types.ErrorCode{Code: "E4059", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. Cross region config error. Invalid regionId in annotation `service.cloud.tencent.com/cross-region-id` %s", Retry: true})

	SnatProNotSupportError            = Register(types.ErrorCode{Code: "E4060", ReasonDetail: types.WhitelistErrorReason, Message: "Ingress: %s. The user account do not support the SnatPro.", Retry: true})
	LoadBalancerParamTypeInvalidError = Register(types.ErrorCode{Code: "E4061", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. Annotation 'kubernetes.io/ingress.loadbalancerType' is invalid. it should be OPEN or INTERNAL.", Retry: false})
	AutoRewriteAnnotationError        = Register(types.ErrorCode{Code: "E4062", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'ingress.cloud.tencent.com/auto-rewrite' is invalid. should be true or false.", Retry: false})
	RewriteSupportAnnotationError     = Register(types.ErrorCode{Code: "E4063", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress rules invalid. annotation: 'ingress.cloud.tencent.com/rewrite-support' is invalid. should be true or false.", Retry: false})
	RuleInvalidError                  = Register(types.ErrorCode{Code: "E4064", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Ingress rules invalid. both Rewrite and Backend is empty. see the document support of \"ingress mixed rule\" and \"ingress rewrite rule\"", Retry: false})
	LoadBalancerSubnetInvalidError    = Register(types.ErrorCode{Code: "E4065", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. User LoadBalancer Subnet was not exist.", Retry: false})
	TkeServiceConfigCRDCreateError    = Register(types.ErrorCode{Code: "E4066", ReasonDetail: types.UnexpectedErrorReason, Message: "TkeServtruiceConfigCRD CRD Create Error.", Retry: false})
	BackendsLabelAnnotationError      = Register(types.ErrorCode{Code: "E4067", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. Annotation 'service.kubernetes.io/qcloud-loadbalancer-backends-label' is not invalid label selector.", Retry: false})
	RewriteRuleConflictError          = Register(types.ErrorCode{Code: "E4068", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Rewrite rule is conflict with other rules %s.", Retry: false})
	CustomizedWeightAnnotationError   = Register(types.ErrorCode{Code: "E4069", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Annotation 'ingress.cloud.tencent.com/lb-rs-weight' Unmarshal error.", Retry: false})

	CrossRegionConfigError           = Register(types.ErrorCode{Code: "E4070", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Cross region config error. use annotation `ingress.cloud.tencent.com/cross-region-id` with `ingress.cloud.tencent.com/cross-vpc-id` or `ingress.kubernetes.io/tke-existed-lbid`.", Retry: false})
	CrossRegionCNNConfigError        = Register(types.ErrorCode{Code: "E4071", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. Cross region config error. config must meet the following conditions simultaneously: a. The VPC where the load balancer is located must be in the same CCN as the VPC where the cluster is located. b. The VPC must be in the same APPID as the TKE cluster (cross APPID VPCs are not supported).", Retry: false})
	LoadBalancerDoNotSupportCNNError = Register(types.ErrorCode{Code: "E4072", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. Cross region config error. loadbalancer do not has vpc attributes, can not link to the CCN.", Retry: false})
	NetworkNotRegisterToCNNError     = Register(types.ErrorCode{Code: "E4073", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. The container network is not registered to the CCN.", Retry: false})
	NodeInsufficient                 = Register(types.ErrorCode{Code: "E4074", ReasonDetail: types.BackendErrorReason, Message: "Ingress: %s. Some node in cluster is locked or insufficient balance, please remove or unscheduled these node.", Retry: false})
	RegexPathIllegal                 = Register(types.ErrorCode{Code: "E4075", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Regex path is illegal, path can only use the letter、number and character -/.\\%%?=:#&^*[]$. and it should be a legal regular expression. invalid path: %s", Retry: false})
	PathIllegal                      = Register(types.ErrorCode{Code: "E4076", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Exact path is illegal, path can only use the letter、number and character -/.\\%%?=:#&. or use it with NonAbsolutePath. invalid path: %s", Retry: false})
	PathLengthIllegal                = Register(types.ErrorCode{Code: "E4077", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Path length cannot exceed 200.", Retry: false})
	ConflictPathType                 = Register(types.ErrorCode{Code: "E4078", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. annotation: 'kubernetes.io/ingress.http-rules' or 'kubernetes.io/ingress.http-rules' is invalid. annotation has conflict pathType. pathType: %s.", Retry: false})
	UnsupportedPathType              = Register(types.ErrorCode{Code: "E4079", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. annotation: 'kubernetes.io/ingress.http-rules' or 'kubernetes.io/ingress.http-rules' is invalid. unsupported pathType. pathType: %s.", Retry: false})

	RewriteInvalidPathType                     = Register(types.ErrorCode{Code: "E4080", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. annotation: 'kubernetes.io/ingress.http-rules' or 'kubernetes.io/ingress.https-rules' is invalid. rewrite can not use with regex path or exact path.", Retry: false})
	ConflictRule                               = Register(types.ErrorCode{Code: "E4081", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. annotation: 'kubernetes.io/ingress.http-rules' or 'kubernetes.io/ingress.https-rules' has conflict rule. all of the rule in annotation, (host, path, backend) must unique.", Retry: false})
	NoSelectorService                          = Register(types.ErrorCode{Code: "E4082", ReasonDetail: types.BackendErrorReason, Message: "Ingress: %s. LoadBalancer Service(%s) without label selector won't have any endpoint, even if you set Endpoints.", Retry: false})
	LabelStoredError                           = Register(types.ErrorCode{Code: "E4084", ReasonDetail: types.DependenceErrorReason, Message: "Ingress: %s. The label data failed to be stored within one minute. The component will automatically retry later.", Retry: true})
	SnatProNotSupportForLoadbalancerError      = Register(types.ErrorCode{Code: "E4085", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. The Loadbalancer do not support the SnatPro.", Retry: true})
	ModificationProtectionAnnontationError     = Register(types.ErrorCode{Code: "E4086", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s Annotation 'ingress.cloud.tencent.com/modification-protection' should be true or false.", Retry: false})
	ReuseNotSupportModificationProtectionError = Register(types.ErrorCode{Code: "E4087", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s Annotation 'ingress.cloud.tencent.com/modification-protection' do not support the service with reuse loadbalancer.", Retry: false})
	ModificationProtectionConflictError        = Register(types.ErrorCode{Code: "E4088", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s Loadbalancer was protected by user or another service.", Retry: true})
	ModificationProtectionDoNotSupportError    = Register(types.ErrorCode{Code: "E4089", ReasonDetail: types.WhitelistErrorReason, Message: "Ingress: %s Please check the white list about the CLB. Function modification protection may not be open.", Retry: true})

	HybridCloudWithoutSnatProSetting = Register(types.ErrorCode{Code: "E4090", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/hybrid-type', but not setting the snatIp from annotation 'ingress.cloud.tencent.com/snat-pro-info', please read the document for usage help.", Retry: false})
	CrossRegionWithoutSnatProSetting = Register(types.ErrorCode{Code: "E4091", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/cross-type', but not setting the snatIp from annotation 'ingress.cloud.tencent.com/snat-pro-info', please read the document for usage help.", Retry: false})
	SnatIPLimitExceeded              = Register(types.ErrorCode{Code: "E4092", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/snat-pro-info', snat ip can't exceed 10.", Retry: false})
	PassToTargetAnnotationError      = Register(types.ErrorCode{Code: "E4093", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/pass-to-target' is invalid. should be true or false.", Retry: false})
	SecurityGroupsAnnotationError    = Register(types.ErrorCode{Code: "E4094", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/security-groups' is invalid. security group id is over limit. all of the security group id should join by ','", Retry: false})
	MixIpTargetError                 = Register(types.ErrorCode{Code: "E4095", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. open MixIpTarget error, FullChain IPv6 CLB should open MixIpTarget before open SNATPro.", Retry: true})
	SecurityGroupsFormatError        = Register(types.ErrorCode{Code: "E4096", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/security-groups' is invalid. any of security group id format error.", Retry: false})
	SecurityGroupsNotExistError      = Register(types.ErrorCode{Code: "E4097", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/security-groups' is invalid. any of security group id is not exist.", Retry: false})
	ReuseFormatInvalidError          = Register(types.ErrorCode{Code: "E4098", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. The loadbalancer id in annotation `ingress.kubernetes.io/tke-existed-lbid` is format invalid.", Retry: false})
	ConflictListenerError            = Register(types.ErrorCode{Code: "E4099", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s The loadbalancer has port conflict in the listener %s.", Retry: true})

	ReuseBelongToAnotherClusterError = Register(types.ErrorCode{Code: "E4100", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. The loadbalancer in annotation `kubernetes.io/ingress.existLbId` is used by other cluster.", Retry: true})
	ReuseConflictListenerError       = Register(types.ErrorCode{Code: "E4101", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. The loadbalancer in annotation `kubernetes.io/ingress.existLbId` has port conflict with other resource. conflict listener info(%s)", Retry: true})
	HybridCNNConfigErrorError        = Register(types.ErrorCode{Code: "E4102", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. annotation: 'ingress.cloud.tencent.com/hybrid-type:CCN'. VPC do not join the CCN.", Retry: true})
	SnatIPAnnotationFormatError      = Register(types.ErrorCode{Code: "E4103", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/snat-pro-info', annotation format error.", Retry: false})
	PassToTargetSettingError         = Register(types.ErrorCode{Code: "E4104", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. Ingress use the 'ingress.cloud.tencent.com/pass-to-target', but ModifyLoadBalancerAttributes api error.", Retry: false})
	ServiceCloseNodePortsError       = Register(types.ErrorCode{Code: "E4105", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. Rules(%s) Service(%s) allocateLoadBalancerNodePorts is false.", Retry: false})

	LoadBalancerClientTokenHasExpiredError = Register(types.ErrorCode{Code: "E4106", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s. The ClientToken in annotation `ingress.cloud.tencent.com/client-token` has expired.", Retry: true})
	InvalidHealthCheckDomainError          = Register(types.ErrorCode{Code: "E4107", ReasonDetail: types.ConfigurationErrorReason, Message: "Ingress: %s. The health check domain of the regex or wildcard domains must be an exact domain.", Retry: false})
	RSLimitExceeded                        = Register(types.ErrorCode{Code: "E4108", ReasonDetail: types.QuotaLimitErrorReason,
		Message: "The number of rs expected to be register for the rule %q exceeds the limit(expect total: %d, current quota: %d), some rs are ignored.",
		Retry:   false,
	})

	TkeServiceConfigInvalidError = Register(types.ErrorCode{Code: "E4109", ReasonDetail: types.UnexpectedErrorReason, Message: "TkeServiceConfig of Ingress: %s Invalid Error", Retry: false})

	NotBelongToSpecifiedClusterError          = Register(types.ErrorCode{Code: "E4200", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s is used 'ingress.cloud.tencent.com/from-other-cluster' specified from cluster, but clb is not belong the target master cluster(%s).", Retry: true})
	BackendManageOnlyMustUseExistLBError      = Register(types.ErrorCode{Code: "E4201", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s use 'ingress.cloud.tencent.com/backend-manage-only' must use existed CLB.", Retry: false})
	MultiClusterDeleteWithBackend             = Register(types.ErrorCode{Code: "E4202", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s MultiClusterIngress is deleted, but backend is not empty. ", Retry: true})
	ChildIngressDeleteWithoutBackendOnly      = Register(types.ErrorCode{Code: "E4203", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress in child cluster: %s is deleting, but not using 'ingress.cloud.tencent.com/backend-manage-only'", Retry: false})
	RuleNotExistedInLBWhenEnsuringTargets     = Register(types.ErrorCode{Code: "E4204", ReasonDetail: types.LoadbalancerErrorReason, Message: "Rule is not existed when ensuring targets.", Retry: true})
	ListenerNotExistedInLBWhenEnsuringTargets = Register(types.ErrorCode{Code: "E4205", ReasonDetail: types.LoadbalancerErrorReason, Message: "Listener is not existed when ensuring targets.", Retry: true})
	InvalidListenPortError                    = Register(types.ErrorCode{Code: "E4206", ReasonDetail: types.ConfigurationErrorReason, Retry: false})
	ConflictListenPortError                   = Register(types.ErrorCode{Code: "E4207", ReasonDetail: types.ConfigurationErrorReason, Retry: false})
	AutoRewritePortRequiredError              = Register(types.ErrorCode{Code: "E4208", ReasonDetail: types.ConfigurationErrorReason, Retry: false})
	ListenProtocolUnsupportedError            = Register(types.ErrorCode{Code: "E4209", ReasonDetail: types.ConfigurationErrorReason, Retry: false})

	CertificateAlgorithmTypeError = Register(types.ErrorCode{Code: "E4210", ReasonDetail: types.CertErrorReason, Message: "Ingress: %s. The MultiCertInfo of the CertList algorithm types cannot be repeated. (User InvalidParameterValue)", Retry: false})
	CertificateContentError       = Register(types.ErrorCode{Code: "E4211", ReasonDetail: types.CertErrorReason, Message: "TLS Secret %s contains invalid certificate information", Retry: false})

	PatchRSTagAnnontationError = Register(types.ErrorCode{Code: "E4110", ReasonDetail: types.LoadbalancerErrorReason,
		Message: "Ingress %s intents to patch RS tags but failed",
		Retry:   true})

	ServiceEndpointNotFoundError = Register(types.ErrorCode{Code: "E4111", ReasonDetail: types.ConfigurationErrorReason,
		Message: "Ingress: %s. Service %s does not have any endpoint",
		Retry:   false,
	})

	DeletionProtectionError = Register(types.ErrorCode{Code: "E5015", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s Annotation 'ingress.cloud.tencent.com/deletion-protection' denied to delete clb when enabling deletion protect.", Retry: true})
	ForbiddenDeletionError  = Register(types.ErrorCode{Code: "E5016", ReasonDetail: types.LoadbalancerErrorReason, Message: "Ingress: %s cannot be deleted by clb policies", Retry: true})
)

func IsRSLimitExceededErrOrWarn(err error) bool {
	ingErr, ok := lo.ErrorsAs[*types.Error](err)
	if !ok {
		return false
	}
	return ingErr.ErrorCode.Code == NodeRSLimitExceeded.Code || ingErr.ErrorCode.Code == RSLimitExceeded.Code
}

func ContainsRSLimitExceededErrOrWarn(errs []error) bool {
	_, exists := lo.Find(errs, IsRSLimitExceededErrOrWarn)
	return exists
}
