package ingress

import (
	"fmt"
	"strings"
)

type IngressError struct {
	ErrorCode   IngressErrorCode
	Detail      string
	OriginError string
}

func NewIngressError(code IngressErrorCode, originError string, args ...interface{}) *IngressError {
	return &IngressError{
		ErrorCode:   code,
		Detail:      fmt.Sprintf(code.Message, args...),
		OriginError: originError,
	}
}

func (ingressError *IngressError) Error() string {
	if ingressError.IsSuccess() {
		return fmt.Sprintf("Ingress Sync Success. ReturnCode: %s", ingressError.ErrorCode.Code)
	}

	var originError string
	if ingressError.OriginError != "" { // OriginError 可能为空，如果为空则不输出
		originError = fmt.Sprintf(" OriginError: %s", ingressError.OriginError)
	}

	if ingressError.IsWarning() {
		return fmt.Sprintf("Ingress Sync Warning. WarningCode: %s Details: %s", ingressError.ErrorCode.Code, ingressError.Detail) + originError
	} else if ingressError.IsClientError() {
		return fmt.Sprintf("Ingress Sync ClientError. ErrorCode: %s Details: %s", ingressError.ErrorCode.Code, ingressError.Detail) + originError
	} else if ingressError.IsDependencyError() {
		return fmt.Sprintf("Ingress Sync DependencyError. ErrorCode: %s Details: %s", ingressError.ErrorCode.Code, ingressError.Detail) + originError
	}

	return fmt.Sprintf("IngressError. ErrorCode: %s Details: %s", ingressError.ErrorCode.Code, ingressError.Detail) + originError
}

func (ingressError *IngressError) IsSuccess() bool {
	return strings.HasPrefix(ingressError.ErrorCode.Code, "S")
}

func (ingressError *IngressError) IsWarning() bool {
	return strings.HasPrefix(ingressError.ErrorCode.Code, "W")
}

func (ingressError *IngressError) IsClientError() bool {
	return strings.HasPrefix(ingressError.ErrorCode.Code, "E4")
}

func (ingressError *IngressError) IsDependencyError() bool {
	return strings.HasPrefix(ingressError.ErrorCode.Code, "E5")
}
