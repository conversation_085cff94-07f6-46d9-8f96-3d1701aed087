package tencentapi

import (
	"context"

	clbv2 "github.com/howardshaw/qcloudapi-sdk-go/clb"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"

	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/tencentapi/dryrun"
	"git.woa.com/kateway/pkg/domain/types"
)

type readonlyAPIService struct {
	defaultService
}

func (api *readonlyAPIService) ModifyResourceTags(ctx context.Context, request *tag.ModifyResourceTagsRequest) (response *tag.ModifyResourceTagsResponse, err error) {
	// 2.3.5版本增加了防误删的相关逻辑，需要为lb实例增加lifecycleowner的标签，在预检的时候需要过滤掉这种修改情况
	request.ReplaceTags = lo.Filter(request.ReplaceTags, func(t *tag.Tag, _ int) bool {
		return lo.FromPtr(t.TagKey) != types.TagKeyLifecycleOwner.String()
	})
	if len(request.ReplaceTags) == 0 && len(request.DeleteTags) == 0 {
		return nil, nil
	}

	dryrun.SaveRecord(ctx, request.GetAction(), request)
	return nil, nil
}

func (api *readonlyAPIService) RegisterTargetsWithClassicalLB(ctx context.Context, request *clb.RegisterTargetsWithClassicalLBRequest) (response *clb.RegisterTargetsWithClassicalLBResponse, err error) {
	dryrun.SaveRecord(ctx, request.GetAction(), request)
	return nil, nil
}

func (api *readonlyAPIService) DeregisterTargetsFromClassicalLB(ctx context.Context, request *clb.DeregisterTargetsFromClassicalLBRequest) (response *clb.DeregisterTargetsFromClassicalLBResponse, err error) {
	dryrun.SaveRecord(ctx, request.GetAction(), request)
	return nil, nil
}

func (api *readonlyAPIService) CreateLoadBalancerV2(ctx context.Context, request *clbv2.CreateLoadBalancerArgs) (response *clbv2.CreateLoadBalancerResponse, err error) {
	dryrun.SaveRecord(ctx, CreateLoadBalancerV2.Name, request)
	return nil, types.NewError(errcode.MockError, "", CreateLoadBalancerV2.Name, JsonWrapperForAPI(request))
}

func (api *readonlyAPIService) CreateLoadBalancerListenersV2(ctx context.Context, request *clbv2.CreateLoadBalancerListenersArgs) (response *clbv2.CreateLoadBalancerListenersResponse, err error) {
	dryrun.SaveRecord(ctx, CreateLoadBalancerListenersV2.Name, request)
	return nil, types.NewError(errcode.MockError, "", CreateLoadBalancerListenersV2.Name, JsonWrapperForAPI(request))
}

func (api *readonlyAPIService) ModifyLoadBalancerListenerV2(ctx context.Context, request *clbv2.ModifyLoadBalancerListenerArgs) (response *clbv2.ModifyLoadBalancerListenerResponse, err error) {
	dryrun.SaveRecord(ctx, ModifyLoadBalancerListenerV2.Name, request)
	return nil, nil
}

func (api *readonlyAPIService) DeleteLoadBalancerListenersV2(ctx context.Context, request *clbv2.DeleteLoadBalancerListenersArgs) (response *clbv2.DeleteLoadBalancerListenersResponse, err error) {
	dryrun.SaveRecord(ctx, DeleteLoadBalancerListenersV2.Name, request)
	return nil, nil
}
