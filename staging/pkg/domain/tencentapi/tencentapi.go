package tencentapi

import (
	"context"
	"errors"
	"fmt"
	"net"
	"time"

	clbv2 "github.com/howardshaw/qcloudapi-sdk-go/clb"
	"github.com/howardshaw/qcloudapi-sdk-go/common"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	tencentErrors "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	ssl "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ssl/v20191205"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	glog "k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/metrics"
	serviceError "git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/format"
	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloud/clientset"
	"git.woa.com/kateway/pkg/tencent/cloud/tkeinternal"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	"git.woa.com/kateway/pkg/tencent/norm"
)

type defaultService struct {
	clientset         clientset.Interface
	RetryDefault      int
	TaskCheckInterval time.Duration
}

func (api *defaultService) handleNewClientErr(err error) error {
	if errors.Is(err, norm.ErrTKERoleNotFound) {
		return types.NewError(serviceError.NormTKEQCSRoleNotExistError, err.Error())
	} else if errors.Is(err, norm.ErrIPMasqAgentConfigChanged) {
		return types.NewError(serviceError.NormIpMasqAgentConfigChangedError, err.Error())
	} else if errors.Is(err, norm.ErrInternal) {
		return types.NewError(serviceError.ServiceNormError, err.Error())
	}
	return err
}

func (api *defaultService) CLBv2(region string, action string) (*clbv2.Client, error) {
	cli, err := api.clientset.CLBv2(region, action)
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) CLBv3(ctx context.Context) (*clb.Client, error) {
	cli, err := api.clientset.CLB(cloudctx.Region(ctx), cloudctx.Action(ctx))
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) CLB(region string, action string) (*clb.Client, error) {
	cli, err := api.clientset.CLB(region, action)
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) CLBInternal(region string, action string) (*clbinternal.Client, error) {
	cli, err := api.clientset.CLBInternal(region, action)
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) CVM(region string, action string) (*cvm.Client, error) {
	cli, err := api.clientset.CVM(region, action)
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) TKE(region string, action string) (*tke.Client, error) {
	cli, err := api.clientset.TKE(region, action)
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) TKEInternal(region string, action string) (*tkeinternal.Client, error) {
	cli, err := api.clientset.TKEInternal(region, action)
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) Tag(region string, action string) (*tag.Client, error) {
	cli, err := api.clientset.Tag(region, action)
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) VPC(region string, action string) (*vpc.Client, error) {
	cli, err := api.clientset.VPC(region, action)
	return cli, api.handleNewClientErr(err)
}

func (api *defaultService) SSL(region string, action string) (*ssl.Client, error) {
	cli, err := api.clientset.SSL(region, action)
	return cli, api.handleNewClientErr(err)
}

const (
	STATUS_TASK_SUCC = 0
	STATUS_TASK_FAIL = 1
)

// //////////////////////////////////////////////////////////////
// CVM API
// //////////////////////////////////////////////////////////////

func (api *defaultService) DescribeInstances(ctx context.Context, request *cvm.DescribeInstancesRequest) (response *cvm.DescribeInstancesResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeInstances
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CVM(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeInstancesWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

// //////////////////////////////////////////////////////////////
// SSL API
// //////////////////////////////////////////////////////////////
func (api *defaultService) DescribeCertificates(ctx context.Context, request *ssl.DescribeCertificatesRequest) (response *ssl.DescribeCertificatesResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeCertificates
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.SSL(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeCertificatesWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) UploadCertificate(ctx context.Context, request *ssl.UploadCertificateRequest) (response *ssl.UploadCertificateResponse, err error) {
	request.SetContext(ctx)
	apiAction := UploadCertificate
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.SSL(cloudctx.Region(ctx), apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.UploadCertificateWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); !retry || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		// 同步API，isTask == false
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeCertificateDetail(ctx context.Context, request *ssl.DescribeCertificateDetailRequest) (response *ssl.DescribeCertificateDetailResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeCertificateDetail
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.SSL(cloudctx.Region(ctx), apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeCertificateDetailWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); !retry || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

// //////////////////////////////////////////////////////////////
// TKE API
// //////////////////////////////////////////////////////////////

func (api *defaultService) DescribeClusters(ctx context.Context, request *tke.DescribeClustersRequest) (response *tke.DescribeClustersResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeClusters
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.TKE(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeClustersWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeEKSClusters(ctx context.Context, request *tke.DescribeEKSClustersRequest) (response *tke.DescribeEKSClustersResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeEKSClusters
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.TKE(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeEKSClustersWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	return response, nil
}

// //////////////////////////////////////////////////////////////
// TKE Inner API
// //////////////////////////////////////////////////////////////

func (api *defaultService) ForwardRequest(ctx context.Context, request *tkeinternal.ForwardRequestRequest) (response *tkeinternal.ForwardRequestResponse, err error) {
	request.SetContext(ctx)
	apiAction := ForwardRequest
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.TKEInternal(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ForwardRequestWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

// 该接口需要做特殊处理
func (api *defaultService) AdmitResourceOperation(ctx context.Context, request *tkeinternal.AdmitResourceOperationRequest) (response *tkeinternal.AdmitResourceOperationResponse, err error) {
	request.SetContext(ctx)
	apiAction := AdmitResourceOperation
	region := cloudctx.Region(ctx)
	startTime := time.Now()
	client, err := api.TKEInternal(region, apiAction.Name)
	if err != nil {
		return nil, err
	}
	if response, err = client.AdmitResourceOperationWithContext(ctx, request); err != nil {
		requestJson := JsonWrapperForAPI(request)
		_, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false)
		APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
		return nil, err
	}
	updateMetricSuccess(apiAction, startTime, false)
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

// //////////////////////////////////////////////////////////////
// VPC API
// //////////////////////////////////////////////////////////////
func (api *defaultService) DescribeSubnets(ctx context.Context, request *vpc.DescribeSubnetsRequest) (response *vpc.DescribeSubnetsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeSubnets
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.VPC(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeSubnetsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeCcnAttachedInstances(ctx context.Context, request *vpc.DescribeCcnAttachedInstancesRequest) (response *vpc.DescribeCcnAttachedInstancesResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeCcnAttachedInstances
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.VPC(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeCcnAttachedInstancesWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeSecurityGroups(ctx context.Context, request *vpc.DescribeSecurityGroupsRequest) (response *vpc.DescribeSecurityGroupsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeSecurityGroups
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.VPC(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeSecurityGroupsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

// //////////////////////////////////////////////////////////////
// TAG API
// //////////////////////////////////////////////////////////////

func (api *defaultService) DescribeTags(ctx context.Context, request *tag.DescribeTagsRequest) (response *tag.DescribeTagsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeTags
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.Tag(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeTagsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) ModifyResourceTags(ctx context.Context, request *tag.ModifyResourceTagsRequest) (response *tag.ModifyResourceTagsResponse, err error) {
	request.SetContext(ctx)
	apiAction := ModifyResourceTags
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.Tag(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyResourceTagsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DeleteTag(ctx context.Context, request *tag.DeleteTagRequest) (response *tag.DeleteTagResponse, err error) {
	request.SetContext(ctx)
	apiAction := DeleteTag
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.Tag(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeleteTagWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeResourcesByTags(ctx context.Context, request *tag.DescribeResourcesByTagsRequest) (response *tag.DescribeResourcesByTagsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeResourcesByTags
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.Tag(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeResourcesByTagsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeResourceTagsByResourceIds(ctx context.Context, request *tag.DescribeResourceTagsByResourceIdsRequest) (response *tag.DescribeResourceTagsByResourceIdsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeResourceTagsByResourceIds
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.Tag(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeResourceTagsByResourceIdsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

// //////////////////////////////////////////////////////////////
// CLB V3 API
// //////////////////////////////////////////////////////////////
func (api *defaultService) DescribeRegions(ctx context.Context, request *clbinternal.DescribeRegionsRequest) (response *clbinternal.DescribeRegionsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeRegions
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLBInternal(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeRegions(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) ExpandCreateLoadBalancer(ctx context.Context, request *ExpandCreateLoadBalancerRequest) (response *clb.CreateLoadBalancerResponse, err error) {
	request.SetContext(ctx)
	apiAction := CreateLoadBalancer
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		CLB, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = ExpandCreateLoadBalancer(CLB, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) CreateLoadBalancer(ctx context.Context, request *clb.CreateLoadBalancerRequest) (response *clb.CreateLoadBalancerResponse, err error) {
	request.SetContext(ctx)
	apiAction := CreateLoadBalancer
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.CreateLoadBalancerWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DescribeLoadBalancers(ctx context.Context, request *clb.DescribeLoadBalancersRequest) (response *clb.DescribeLoadBalancersResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeLoadBalancers
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeLoadBalancersWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DeleteLoadBalancer(ctx context.Context, request *clb.DeleteLoadBalancerRequest) (response *clb.DeleteLoadBalancerResponse, err error) {
	request.SetContext(ctx)
	apiAction := DeleteLoadBalancer
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeleteLoadBalancerWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) CreateListener(ctx context.Context, request *clbinternal.CreateListenerRequest) (response *clbinternal.CreateListenerResponse, err error) {
	request.SetContext(ctx)
	apiAction := CreateListener
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLBInternal(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.CreateListener(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DescribeListeners(ctx context.Context, request *clb.DescribeListenersRequest) (response *clb.DescribeListenersResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeListeners
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeListenersWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) ModifyListener(ctx context.Context, request *clbinternal.ModifyListenerRequest) (response *clbinternal.ModifyListenerResponse, err error) {
	request.SetContext(ctx)
	apiAction := ModifyListener
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLBInternal(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyListener(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DeleteListener(ctx context.Context, request *clb.DeleteListenerRequest) (response *clb.DeleteListenerResponse, err error) {
	request.SetContext(ctx)
	apiAction := DeleteListener
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeleteListenerWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) CreateRule(ctx context.Context, request *clb.CreateRuleRequest) (response *clb.CreateRuleResponse, err error) {
	request.SetContext(ctx)
	apiAction := CreateRule
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.CreateRuleWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) ModifyRule(ctx context.Context, request *clb.ModifyRuleRequest) (response *clb.ModifyRuleResponse, err error) {
	request.SetContext(ctx)
	apiAction := ModifyRule
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyRuleWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DescribeRewrite(ctx context.Context, request *clb.DescribeRewriteRequest) (response *clb.DescribeRewriteResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeRewrite
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeRewriteWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) AutoRewrite(ctx context.Context, request *clb.AutoRewriteRequest) (response *clb.AutoRewriteResponse, err error) {
	request.SetContext(ctx)
	apiAction := AutoRewrite
	region := cloudctx.Region(ctx)
	var startTime time.Time
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.AutoRewriteWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) ManualRewrite(ctx context.Context, request *clb.ManualRewriteRequest) (response *clb.ManualRewriteResponse, err error) {
	request.SetContext(ctx)
	apiAction := ManualRewrite
	region := cloudctx.Region(ctx)
	var startTime time.Time
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ManualRewriteWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DeleteRewrite(ctx context.Context, request *clb.DeleteRewriteRequest) (response *clb.DeleteRewriteResponse, err error) {
	request.SetContext(ctx)
	apiAction := DeleteRewrite
	region := cloudctx.Region(ctx)
	var startTime time.Time
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeleteRewriteWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) ModifyDomainAttributes(ctx context.Context, request *clb.ModifyDomainAttributesRequest) (response *clb.ModifyDomainAttributesResponse, err error) {
	request.SetContext(ctx)
	apiAction := ModifyDomainAttributes
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyDomainAttributesWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DeleteRule(ctx context.Context, request *clb.DeleteRuleRequest) (response *clb.DeleteRuleResponse, err error) {
	request.SetContext(ctx)
	apiAction := DeleteRule
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeleteRuleWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) BatchRegisterTargets(ctx context.Context, request *clb.BatchRegisterTargetsRequest) (response *clb.BatchRegisterTargetsResponse, err error) {
	request.SetContext(ctx)
	apiAction := BatchRegisterTargets
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.BatchRegisterTargetsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) BatchDeregisterTargets(ctx context.Context, request *clb.BatchDeregisterTargetsRequest) (response *clb.BatchDeregisterTargetsResponse, err error) {
	request.SetContext(ctx)
	apiAction := BatchDeregisterTargets
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.BatchDeregisterTargetsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) BatchModifyTargetWeight(ctx context.Context, request *clb.BatchModifyTargetWeightRequest) (response *clb.BatchModifyTargetWeightResponse, err error) {
	request.SetContext(ctx)
	apiAction := BatchModifyTargetWeight
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.BatchModifyTargetWeightWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) RegisterTargets(ctx context.Context, request *clb.RegisterTargetsRequest) (response *clb.RegisterTargetsResponse, err error) {
	request.SetContext(ctx)
	apiAction := RegisterTargets
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.RegisterTargetsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DeregisterTargets(ctx context.Context, request *clb.DeregisterTargetsRequest) (response *clb.DeregisterTargetsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DeregisterTargets
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeregisterTargetsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) ModifyTargetWeight(ctx context.Context, request *clb.ModifyTargetWeightRequest) (response *clb.ModifyTargetWeightResponse, err error) {
	request.SetContext(ctx)
	apiAction := ModifyTargetWeight
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyTargetWeightWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DescribeTargets(ctx context.Context, request *clb.DescribeTargetsRequest) (response *clb.DescribeTargetsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeTargets
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeTargetsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeTargetHealth(ctx context.Context, request *clb.DescribeTargetHealthRequest) (response *clb.DescribeTargetHealthResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeTargetHealth
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeTargetHealthWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) CreateLoadBalancerSnatIps(ctx context.Context, request *clb.CreateLoadBalancerSnatIpsRequest) (response *clb.CreateLoadBalancerSnatIpsResponse, err error) {
	request.SetContext(ctx)
	apiAction := CreateLoadBalancerSnatIps
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.CreateLoadBalancerSnatIpsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DeleteLoadBalancerSnatIps(ctx context.Context, request *clb.DeleteLoadBalancerSnatIpsRequest) (response *clb.DeleteLoadBalancerSnatIpsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DeleteLoadBalancerSnatIps
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeleteLoadBalancerSnatIpsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) SetLoadBalancerSecurityGroups(ctx context.Context, request *clb.SetLoadBalancerSecurityGroupsRequest) (response *clb.SetLoadBalancerSecurityGroupsResponse, err error) {
	request.SetContext(ctx)
	apiAction := SetLoadBalancerSecurityGroups
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.SetLoadBalancerSecurityGroupsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) ModifyLoadBalancerMixIpTarget(ctx context.Context, request *clb.ModifyLoadBalancerMixIpTargetRequest) (response *clb.ModifyLoadBalancerMixIpTargetResponse, err error) {
	request.SetContext(ctx)
	apiAction := ModifyLoadBalancerMixIpTarget
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyLoadBalancerMixIpTargetWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) BatchModifyTargetTag(ctx context.Context, request *clb.BatchModifyTargetTagRequest) (response *clb.BatchModifyTargetTagResponse, err error) {
	request.SetContext(ctx)
	apiAction := BatchModifyTargetTag
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.BatchModifyTargetTagWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

// 传统型接口

func (api *defaultService) DescribeClassicalLBListeners(ctx context.Context, request *clb.DescribeClassicalLBListenersRequest) (response *clb.DescribeClassicalLBListenersResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeClassicalLBListeners
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeClassicalLBListenersWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeClassicalLBTargets(ctx context.Context, request *clb.DescribeClassicalLBTargetsRequest) (response *clb.DescribeClassicalLBTargetsResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeClassicalLBTargets
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeClassicalLBTargetsWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) RegisterTargetsWithClassicalLB(ctx context.Context, request *clb.RegisterTargetsWithClassicalLBRequest) (response *clb.RegisterTargetsWithClassicalLBResponse, err error) {
	request.SetContext(ctx)
	apiAction := RegisterTargetsWithClassicalLB
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.RegisterTargetsWithClassicalLBWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DeregisterTargetsFromClassicalLB(ctx context.Context, request *clb.DeregisterTargetsFromClassicalLBRequest) (response *clb.DeregisterTargetsFromClassicalLBResponse, err error) {
	request.SetContext(ctx)
	apiAction := DeregisterTargetsFromClassicalLB
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeregisterTargetsFromClassicalLBWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

// //////////////////////////////////////////////////////////////
// CLB V2 API
// //////////////////////////////////////////////////////////////

func (api *defaultService) CreateLoadBalancerV2(ctx context.Context, request *clbv2.CreateLoadBalancerArgs) (response *clbv2.CreateLoadBalancerResponse, err error) {
	apiAction := CreateLoadBalancerV2
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLBv2(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.CreateLoadBalancer(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.waitUntiTaskDoneV2(ctx, response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DescribeQuota(ctx context.Context, request *clbinternal.DescribeQuotaRequest) (response *clbinternal.DescribeQuotaResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeQuota
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLBInternal(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeQuota(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) DescribeLBListeners(ctx context.Context, request *clb.DescribeLBListenersRequest) (response *clb.DescribeLBListenersResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeLBListeners
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeLBListenersWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) ModifyLoadBalancerAttributes(ctx context.Context, request *clbinternal.ModifyLoadBalancerAttributesRequest) (response *clbinternal.ModifyLoadBalancerAttributesResponse, err error) {
	request.SetContext(ctx)
	apiAction := ModifyLoadBalancerAttributes
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLBInternal(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyLoadBalancerAttributesWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, true)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.WaitUntilTaskDone(ctx, response.Response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DescribeLBOperateProtect(ctx context.Context, request *clbinternal.DescribeLBOperateProtectRequest) (response *clbinternal.DescribeLBOperateProtectResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeLBOperateProtect
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLBInternal(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeLBOperateProtect(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) ModifyLBOperateProtect(ctx context.Context, request *clbinternal.ModifyLBOperateProtectRequest) (response *clbinternal.ModifyLBOperateProtectResponse, err error) {
	request.SetContext(ctx)
	apiAction := ModifyLBOperateProtect
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLBInternal(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyLBOperateProtect(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) CreateLoadBalancerListenersV2(ctx context.Context, request *clbv2.CreateLoadBalancerListenersArgs) (response *clbv2.CreateLoadBalancerListenersResponse, err error) {
	apiAction := CreateLoadBalancerListenersV2
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLBv2(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.CreateLoadBalancerListeners(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err = api.waitUntiTaskDoneV2(ctx, response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) ModifyLoadBalancerListenerV2(ctx context.Context, request *clbv2.ModifyLoadBalancerListenerArgs) (response *clbv2.ModifyLoadBalancerListenerResponse, err error) {
	apiAction := ModifyLoadBalancerListenerV2
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLBv2(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.ModifyLoadBalancerListener(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err := api.waitUntiTaskDoneV2(ctx, response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

func (api *defaultService) DeleteLoadBalancerListenersV2(ctx context.Context, request *clbv2.DeleteLoadBalancerListenersArgs) (response *clbv2.DeleteLoadBalancerListenersResponse, err error) {
	apiAction := DeleteLoadBalancerListenersV2
	var startTime time.Time
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime = time.Now()
		client, err := api.CLBv2(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DeleteLoadBalancerListeners(request.LoadBalancerId, request.ListenerIds); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))

	if err := api.waitUntiTaskDoneV2(ctx, response.RequestId, apiAction, startTime); err != nil {
		return nil, err
	}
	return response, nil
}

// //////////////////////////////////////////////////////////////
// CLB Async DescribeTaskStatus
// //////////////////////////////////////////////////////////////

// WaitUntilTaskDone wait until lb task done
func (api *defaultService) WaitUntilTaskDone(ctx context.Context, taskID *string, task Action, taskStartTime time.Time) error {
	ctx, cancel := context.WithTimeout(ctx, 180*time.Second)
	defer cancel()

	// 该标识在domain/tencentapi/dryrun/dryrun.go:70
	if *taskID == "dryrun" {
		return nil
	}

	ticker := time.NewTicker(api.TaskCheckInterval)
	defer ticker.Stop()
	request := clb.NewDescribeTaskStatusRequest()
	request.TaskId = taskID
	for {
		select {
		case <-ctx.Done():
			updateMetricError(task, "Error.TaskTimeout", taskStartTime, true)
			glog.Errorf("Task timeout, action: %s/%s/%s taskId: %s", task.Service.Name, task.Name, task.Version, *taskID)
			return ctx.Err()
		case <-ticker.C:
			response, err := api.DescribeTaskStatus(ctx, request)
			if err != nil {
				return err
			}

			if *response.Response.Status == 0 {
				updateMetricSuccess(task, taskStartTime, true)
				return nil
			}
			if *response.Response.Status == 1 {
				updateMetricError(task, "Error.TaskError", taskStartTime, true)
				glog.Errorf("Task failed, action: %s/%s/%s taskId: %s", task.Service.Name, task.Name, task.Version, *taskID)
				return types.NewError(serviceError.ServiceCLBError, fmt.Sprintf("taskId: %s", *taskID))
			}
		}
	}
}

func (api *defaultService) DescribeTaskStatus(ctx context.Context, request *clb.DescribeTaskStatusRequest) (response *clb.DescribeTaskStatusResponse, err error) {
	request.SetContext(ctx)
	apiAction := DescribeTaskStatus
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLB(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeTaskStatusWithContext(ctx, request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if _, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); times == api.RetryDefault { // 这个接口的错误必定进行重试
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

func (api *defaultService) waitUntiTaskDoneV2(ctx context.Context, taskId int, task Action, taskStartTime time.Time) error {
	ctx, cancel := context.WithTimeout(ctx, 180*time.Second)
	defer cancel()
	ticker := time.NewTicker(api.TaskCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			updateMetricError(task, "Error.TaskTimeout", taskStartTime, true)
			return ctx.Err()
		case <-ticker.C:
			response, err := api.DescribeLoadBalancersTaskResultV2(ctx, taskId)
			if err != nil {
				return err
			}

			if response.Data.Status == STATUS_TASK_SUCC {
				updateMetricSuccess(task, taskStartTime, true)
				return nil
			}
			if response.Data.Status == STATUS_TASK_FAIL {
				updateMetricError(task, "Error.TaskError", taskStartTime, true)
				glog.Errorf("Task failed, action: %s/%s/%s taskID: %d", task.Service.Name, task.Name, task.Version, taskId)
				return types.NewError(serviceError.ServiceCLBError, fmt.Sprintf("taskID: %d", taskId))
			}
		}
	}
}

func (api *defaultService) DescribeLoadBalancersTaskResultV2(ctx context.Context, request int) (response *clbv2.DescribeLoadBalancersTaskResultResponse, err error) {
	apiAction := DescribeLoadBalancersTaskResultV2
	region := cloudctx.Region(ctx)
	for times := 1; times <= api.RetryDefault; times++ {
		startTime := time.Now()
		client, err := api.CLBv2(region, apiAction.Name)
		if err != nil {
			return nil, err
		}
		if response, err = client.DescribeLoadBalancersTaskResult(request); err != nil {
			requestJson := JsonWrapperForAPI(request)
			if retry, wrapperError := dealWithError(apiAction, requestJson, startTime, err, false); retry == false || times == api.RetryDefault {
				APIErrorRecord(ctx, apiAction, requestJson, JsonWrapperForAPI(response), wrapperError)
				return nil, wrapperError
			}
			continue
		}
		updateMetricSuccess(apiAction, startTime, false)
		break
	}
	APIRecord(ctx, apiAction, JsonWrapperForAPI(request), JsonWrapperForAPI(response))
	return response, nil
}

// //////////////////////////////////////////////////////////////
// API Error Report
// //////////////////////////////////////////////////////////////

func dealWithError(action Action, request string, startTime time.Time, err error, isTask bool) (bool, error) {
	glog.Infof("dealWithError %s request: %s, error: %T %s.", action.Name, request, err, err.Error())
	if sdkError, ok := err.(*tencentErrors.TencentCloudSDKError); ok {
		updateMetricSDKError(action, sdkError, startTime, isTask)
		if sdkError.Code == "RequestLimitExceeded" { // TODO misakazhou Not action.Service.ErrorCode
			return true, types.NewError(serviceError.RequestLimitExceeded, sdkError.Error(), action.Service.Name)
		} else if sdkError.Code == "InternalError" || sdkError.Code == "ClientError.HttpStatusCodeError" {
			return false, types.NewError(action.Service.ErrorCode, sdkError.Error())
		} else if sdkError.Code == "ClientError.NetworkError" {
			return false, types.NewError(serviceError.NetworkError, sdkError.Error())
		} else if sdkError.Code == "AuthFailure.UnauthorizedOperation" || sdkError.Code == "UnauthorizedOperation.CamNoAuth" {
			return false, types.NewError(serviceError.NormTKEQCSRoleNotExistError, sdkError.Error())
		}
		return false, err
	}

	if legacyError, ok := err.(common.LegacyAPIError); ok {
		updateMetricLegacyAPIError(action, legacyError, startTime, isTask)
		if legacyError.Code == 4400 { // TODO misakazhou Not action.Service.ErrorCode
			return true, types.NewError(action.Service.ErrorCode, legacyError.Error())
		} else if legacyError.Code == 6000 || legacyError.Code == 6100 || legacyError.Code == 6200 {
			return false, types.NewError(action.Service.ErrorCode, legacyError.Error())
		}
		return false, err
	} else if clientError, ok := err.(common.ClientError); ok {
		updateMetricClientError(action, clientError, startTime, isTask)
		return false, types.NewError(action.Service.ErrorCode, clientError.Error())
	}

	if netError, ok := err.(net.Error); ok {
		updateMetricNetError(action, netError, startTime, isTask)
		return true, dealWithNetError(action.Name, request, netError)
	}

	updateMetricError(action, serviceError.QcloudApiUnexpectedErrorCode, startTime, isTask)
	return false, err
}

func updateMetricSuccess(action Action, startTime time.Time, isTask bool) {
	r := action.GetAPIRecord(serviceError.ServiceSuccessMsg)
	if isTask {
		updateTask(r, startTime)
	} else {
		updateMetric(r, startTime)
	}
}

func updateMetricError(a Action, returnCode string, startTime time.Time, isTask bool) {
	r := a.GetAPIRecord(returnCode)
	if isTask {
		updateTask(r, startTime)
	} else {
		updateMetric(r, startTime)
	}
}

func updateMetricSDKError(a Action, err *tencentErrors.TencentCloudSDKError, startTime time.Time, isTask bool) {
	r := a.GetAPIRecord(err.Code)
	if isTask {
		updateTask(r, startTime)
	} else {
		updateMetric(r, startTime)
	}
}

func updateMetricNetError(a Action, err net.Error, startTime time.Time, isTask bool) {
	r := a.GetAPIRecord(getNetErrorErrorCode(err))
	if isTask {
		updateTask(r, startTime)
	} else {
		updateMetric(r, startTime)
	}
}

// func updateMetricDashboardError(labels prometheus.Labels, err *norm.DashboardError, startTime time.Time, isTask bool) {
//	labels["returnCode"] = fmt.Sprintf("%d", err.Code)
//	labels["service"] = "Norm"
//	//labels["request"] = err.Msg
//	if isTask {
//		updateTask(labels, startTime)
//	} else {
//		updateMetric(labels, startTime)
//	}
// }

// func updateMetricRequestResultError(labels prometheus.Labels, err *norm.RequestResultError, startTime time.Time, isTask bool) {
//	labels["returnCode"] = fmt.Sprintf("%d", err.Code)
//	labels["service"] = "Norm"
//	labels["request"] = err.InterfaceName
//	if isTask {
//		updateTask(labels, startTime)
//	} else {
//		updateMetric(labels, startTime)
//	}
// }

func getNetErrorErrorCode(err net.Error) string {
	if isTimeout := err.Timeout(); isTimeout {
		return serviceError.ServiceNetTimeoutErrorMsg
	}
	return serviceError.ServiceNetOtherErrorMsg
}

func updateMetric(r metrics.TencentAPIRecord, startTime time.Time) {
	if metrics.Instance != nil {
		metrics.Instance.UpdateQcloudAPIDelayTime(r, time.Since(startTime))
		metrics.Instance.IncAPIRequestCount(r)
	}
}

func updateTask(r metrics.TencentAPIRecord, startTime time.Time) {
	if metrics.Instance != nil {
		metrics.Instance.UpdateQcloudTaskDelayTime(r, time.Since(startTime))
		metrics.Instance.IncTaskRequestCount(r)
	}
}

// func updateMetricLogicalDelay(labels prometheus.Labels, startTime time.Time) {
//	metrics.MetricsCollectorServiceInstance.UpdateQcloudTaskDelayTime(labels, time.Since(startTime))
// }

func dealWithNetError(method string, request string, netError net.Error) *types.Error {
	if netError.Timeout() {
		return types.NewError(serviceError.NetworkTimeoutError, netError.Error())
	} else {
		return types.NewError(serviceError.NetworkError, netError.Error())
	}
}

func updateMetricLegacyAPIError(a Action, err common.LegacyAPIError, startTime time.Time, isTask bool) {
	r := a.GetAPIRecord("")
	if errMsg, exist := serviceError.LegacyErrorCodeToMsgMap[err.Code]; exist {
		r.ReturnCode = errMsg
	} else {
		r.ReturnCode = serviceError.QcloudApiUnexpectedLegacyErrorCode
	}
	if isTask {
		updateTask(r, startTime)
	} else {
		updateMetric(r, startTime)
	}
}

func updateMetricClientError(a Action, err common.ClientError, startTime time.Time, isTask bool) {
	r := a.GetAPIRecord(err.Message)
	if isTask {
		updateTask(r, startTime)
	} else {
		updateMetric(r, startTime)
	}
}

func APIErrorRecord(ctx context.Context, apiAction Action, request string, response string, err error) {
	var kvs []any
	object, ok := cloudctx.GetObject(ctx)
	if ok {
		kvs = append(kvs, "Kind", object.Kind(), "Name", object.Name(), "Namespace", object.Namespace())
	}
	kvs = append(kvs, "Region", cloudctx.Region(ctx), "API", apiAction.Service.Name, "Action", apiAction.Name, "Request", request, "Response", response, "Error", err)
	glog.Infof("APIError %s", format.KV(kvs...))
}

func APIRecord(ctx context.Context, apiAction Action, request string, response string) {
	if apiAction.ReadOnly {
		return
	}

	var kvs []any
	object, ok := cloudctx.GetObject(ctx)
	if ok {
		kvs = append(kvs, "Kind", object.Kind(), "Name", object.Name(), "Namespace", object.Namespace())
	}
	kvs = append(kvs, "Region", cloudctx.Region(ctx), "API", apiAction.Service.Name, "Action", apiAction.Name, "Request", request, "Response", response)
	glog.Infof("APIRecord %s", format.KV(kvs...))
}
