package tencentapi

import (
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
)

// https://tcloud-dev.oa.com/document/product/214/34691?!preview&!document=1
type ExpandCreateLoadBalancerRequest struct {
	*clb.CreateLoadBalancerRequest

	//此参数对外不可见。是否支持直通（仅供自研用户使用）
	ZhiTong bool `json:"ZhiTong,omitempty" name:"<PERSON><PERSON><PERSON><PERSON>"`

	// 此参数对外不可见。Tgw独占集群的名称
	// 1. 限速CAP申请，入参： 不传VipIsp，  TgwGroupName: ziyan
	// 2. 不限速CAP申请，入参：不传VipIsp，TgwGroupName: ziyan_normal_bgp
	// 3. 三网资源，入参：VipIsp: CMCC, TgwGroupName: ziyan
	//   (VipIsp可以是CTCC，CMCC，CUCC表示电信，移动，联通)
	// 4. 免流资源，入参：VipIsp: CMCC, TgwGroupName: ziyan_mianliu
	//
	// 总的来说，VipIsp不传就是BGP/限速CAP，传的话指定是三网具体运营商
	// TgwGroupName是独占集群标识，国内自研集群有ziyan、ziyan_normal_bgp、ziyan_mianliu
	//
	// 大业务独占时，各自传相应的TgwGroupName即可
	TgwGroupName *string `json:"TgwGroupName,omitempty" name:"TgwGroupName"`

	// 计费转移到其他产品。已知产品 TCB
	BusinessBelong *string `json:"BusinessBelong,omitempty" name:"BusinessBelong"`

	// 此参数对外不可见。开启IPv6FullChain负载均衡7层监听器支持混绑IPv4/IPv6目标功能。
	MixIpTarget *bool `json:"MixIpTarget,omitempty" name:"MixIpTarget"`

	// 内网独占集群
	// 注意：此字段可能返回 null，表示取不到有效值。
	ExclusiveCluster *clb.ExclusiveCluster `json:"ExclusiveCluster,omitempty" name:"ExclusiveCluster"`

	// 公网独占集群ID。
	ClusterIds []*string `json:"ClusterIds,omitempty" name:"ClusterIds"`

	// 此参数仅产品内部展示。仅适用于私有网络内网负载均衡。内网就近接入时，选择可用区下发。
	Zones []*string `json:"Zones,omitempty" name:"Zones"`
}

func NewCreateLoadBalancerRequest() (request *ExpandCreateLoadBalancerRequest) {
	return &ExpandCreateLoadBalancerRequest{
		CreateLoadBalancerRequest: clb.NewCreateLoadBalancerRequest(),
	}
}

// 本接口(CreateLoadBalancer)用来创建负载均衡实例（本接口只支持购买按量计费的负载均衡，包年包月的负载均衡请通过控制台购买）。为了使用负载均衡服务，您必须购买一个或多个负载均衡实例。成功调用该接口后，会返回负载均衡实例的唯一 ID。负载均衡实例的类型分为：公网、内网。详情可参考产品说明中的产品类型。
// 注意：(1)指定可用区申请负载均衡、跨zone容灾(仅香港支持)【如果您需要体验该功能，请通过 [工单申请](https://console.cloud.tencent.com/workorder/category)】；(2)目前只有北京、上海、广州支持IPv6；(3)一个账号在每个地域的默认购买配额为：公网100个，内网100个。
// 本接口为异步接口，接口成功返回后，可使用 DescribeLoadBalancers 接口查询负载均衡实例的状态（如创建中、正常），以确定是否创建成功。
func ExpandCreateLoadBalancer(c *clb.Client, request *ExpandCreateLoadBalancerRequest) (response *clb.CreateLoadBalancerResponse, err error) {
	if request == nil {
		request = NewCreateLoadBalancerRequest()
	}
	response = clb.NewCreateLoadBalancerResponse()
	err = c.Send(request, response)
	return
}
