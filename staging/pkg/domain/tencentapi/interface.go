package tencentapi

import (
	"context"

	clbv2 "github.com/howardshaw/qcloudapi-sdk-go/clb"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	ssl "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ssl/v20191205"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"

	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloud/tkeinternal"
)

type TencentAPIService interface {
	// CVM
	DescribeInstances(ctx context.Context, request *cvm.DescribeInstancesRequest) (response *cvm.DescribeInstancesResponse, err error)

	// VPC
	DescribeSubnets(ctx context.Context, request *vpc.DescribeSubnetsRequest) (response *vpc.DescribeSubnetsResponse, err error)
	DescribeCcnAttachedInstances(ctx context.Context, request *vpc.DescribeCcnAttachedInstancesRequest) (response *vpc.DescribeCcnAttachedInstancesResponse, err error)
	DescribeSecurityGroups(ctx context.Context, request *vpc.DescribeSecurityGroupsRequest) (response *vpc.DescribeSecurityGroupsResponse, err error)

	// SSL
	DescribeCertificates(ctx context.Context, request *ssl.DescribeCertificatesRequest) (response *ssl.DescribeCertificatesResponse, err error)
	UploadCertificate(ctx context.Context, request *ssl.UploadCertificateRequest) (response *ssl.UploadCertificateResponse, err error)
	DescribeCertificateDetail(ctx context.Context, request *ssl.DescribeCertificateDetailRequest) (response *ssl.DescribeCertificateDetailResponse, err error)

	// TKE
	DescribeClusters(ctx context.Context, request *tke.DescribeClustersRequest) (response *tke.DescribeClustersResponse, err error)
	DescribeEKSClusters(ctx context.Context, request *tke.DescribeEKSClustersRequest) (response *tke.DescribeEKSClustersResponse, err error)

	// TKE Inner
	ForwardRequest(ctx context.Context, request *tkeinternal.ForwardRequestRequest) (response *tkeinternal.ForwardRequestResponse, err error)
	AdmitResourceOperation(ctx context.Context, request *tkeinternal.AdmitResourceOperationRequest) (response *tkeinternal.AdmitResourceOperationResponse, err error)

	// TAG
	DescribeTags(ctx context.Context, request *tag.DescribeTagsRequest) (response *tag.DescribeTagsResponse, err error)
	ModifyResourceTags(ctx context.Context, request *tag.ModifyResourceTagsRequest) (response *tag.ModifyResourceTagsResponse, err error)
	DeleteTag(ctx context.Context, request *tag.DeleteTagRequest) (response *tag.DeleteTagResponse, err error)
	DescribeResourcesByTags(ctx context.Context, request *tag.DescribeResourcesByTagsRequest) (response *tag.DescribeResourcesByTagsResponse, err error) // kateway: 通过标签查询资源列表 https://cloud.tencent.com/document/api/651/38320
	DescribeResourceTagsByResourceIds(ctx context.Context, request *tag.DescribeResourceTagsByResourceIdsRequest) (response *tag.DescribeResourceTagsByResourceIdsResponse, err error)

	// CLB
	DescribeRegions(ctx context.Context, request *clbinternal.DescribeRegionsRequest) (response *clbinternal.DescribeRegionsResponse, err error)
	ExpandCreateLoadBalancer(ctx context.Context, request *ExpandCreateLoadBalancerRequest) (response *clb.CreateLoadBalancerResponse, err error)
	CreateLoadBalancer(ctx context.Context, request *clb.CreateLoadBalancerRequest) (response *clb.CreateLoadBalancerResponse, err error)
	DeleteLoadBalancer(ctx context.Context, request *clb.DeleteLoadBalancerRequest) (response *clb.DeleteLoadBalancerResponse, err error)
	DeleteListener(ctx context.Context, request *clb.DeleteListenerRequest) (response *clb.DeleteListenerResponse, err error)
	DeleteRule(ctx context.Context, request *clb.DeleteRuleRequest) (response *clb.DeleteRuleResponse, err error)
	DescribeRewrite(ctx context.Context, request *clb.DescribeRewriteRequest) (response *clb.DescribeRewriteResponse, err error)
	AutoRewrite(ctx context.Context, request *clb.AutoRewriteRequest) (response *clb.AutoRewriteResponse, err error)
	ManualRewrite(ctx context.Context, request *clb.ManualRewriteRequest) (response *clb.ManualRewriteResponse, err error)
	DeleteRewrite(ctx context.Context, request *clb.DeleteRewriteRequest) (response *clb.DeleteRewriteResponse, err error)
	ModifyDomainAttributes(ctx context.Context, request *clb.ModifyDomainAttributesRequest) (response *clb.ModifyDomainAttributesResponse, err error)
	BatchRegisterTargets(ctx context.Context, request *clb.BatchRegisterTargetsRequest) (response *clb.BatchRegisterTargetsResponse, err error)
	BatchDeregisterTargets(ctx context.Context, request *clb.BatchDeregisterTargetsRequest) (response *clb.BatchDeregisterTargetsResponse, err error)
	BatchModifyTargetWeight(ctx context.Context, request *clb.BatchModifyTargetWeightRequest) (response *clb.BatchModifyTargetWeightResponse, err error)
	RegisterTargets(ctx context.Context, request *clb.RegisterTargetsRequest) (response *clb.RegisterTargetsResponse, err error)
	DeregisterTargets(ctx context.Context, request *clb.DeregisterTargetsRequest) (response *clb.DeregisterTargetsResponse, err error)
	ModifyTargetWeight(ctx context.Context, request *clb.ModifyTargetWeightRequest) (response *clb.ModifyTargetWeightResponse, err error)
	DescribeTargets(ctx context.Context, request *clb.DescribeTargetsRequest) (response *clb.DescribeTargetsResponse, err error)
	DescribeTargetHealth(ctx context.Context, request *clb.DescribeTargetHealthRequest) (response *clb.DescribeTargetHealthResponse, err error)
	DescribeClassicalLBListeners(ctx context.Context, request *clb.DescribeClassicalLBListenersRequest) (response *clb.DescribeClassicalLBListenersResponse, err error)
	DescribeClassicalLBTargets(ctx context.Context, request *clb.DescribeClassicalLBTargetsRequest) (response *clb.DescribeClassicalLBTargetsResponse, err error)
	RegisterTargetsWithClassicalLB(ctx context.Context, request *clb.RegisterTargetsWithClassicalLBRequest) (response *clb.RegisterTargetsWithClassicalLBResponse, err error)
	DeregisterTargetsFromClassicalLB(ctx context.Context, request *clb.DeregisterTargetsFromClassicalLBRequest) (response *clb.DeregisterTargetsFromClassicalLBResponse, err error)
	DescribeTaskStatus(ctx context.Context, request *clb.DescribeTaskStatusRequest) (response *clb.DescribeTaskStatusResponse, err error)
	DescribeQuota(ctx context.Context, request *clbinternal.DescribeQuotaRequest) (response *clbinternal.DescribeQuotaResponse, err error)
	ModifyLoadBalancerAttributes(ctx context.Context, request *clbinternal.ModifyLoadBalancerAttributesRequest) (response *clbinternal.ModifyLoadBalancerAttributesResponse, err error)
	CreateLoadBalancerSnatIps(ctx context.Context, request *clb.CreateLoadBalancerSnatIpsRequest) (response *clb.CreateLoadBalancerSnatIpsResponse, err error)
	DeleteLoadBalancerSnatIps(ctx context.Context, request *clb.DeleteLoadBalancerSnatIpsRequest) (response *clb.DeleteLoadBalancerSnatIpsResponse, err error)
	ModifyLoadBalancerMixIpTarget(ctx context.Context, request *clb.ModifyLoadBalancerMixIpTargetRequest) (response *clb.ModifyLoadBalancerMixIpTargetResponse, err error)
	DescribeListeners(ctx context.Context, request *clb.DescribeListenersRequest) (response *clb.DescribeListenersResponse, err error)
	ModifyRule(ctx context.Context, request *clb.ModifyRuleRequest) (response *clb.ModifyRuleResponse, err error)
	CreateRule(ctx context.Context, request *clb.CreateRuleRequest) (response *clb.CreateRuleResponse, err error)

	ModifyListener(ctx context.Context, request *clbinternal.ModifyListenerRequest) (response *clbinternal.ModifyListenerResponse, err error)
	CreateListener(ctx context.Context, request *clbinternal.CreateListenerRequest) (response *clbinternal.CreateListenerResponse, err error)
	DescribeLoadBalancers(ctx context.Context, request *clb.DescribeLoadBalancersRequest) (response *clb.DescribeLoadBalancersResponse, err error)
	DescribeLBOperateProtect(ctx context.Context, request *clbinternal.DescribeLBOperateProtectRequest) (response *clbinternal.DescribeLBOperateProtectResponse, err error)
	ModifyLBOperateProtect(ctx context.Context, request *clbinternal.ModifyLBOperateProtectRequest) (response *clbinternal.ModifyLBOperateProtectResponse, err error)
	SetLoadBalancerSecurityGroups(ctx context.Context, request *clb.SetLoadBalancerSecurityGroupsRequest) (response *clb.SetLoadBalancerSecurityGroupsResponse, err error)
	DescribeLBListeners(ctx context.Context, request *clb.DescribeLBListenersRequest) (response *clb.DescribeLBListenersResponse, err error)

	BatchModifyTargetTag(ctx context.Context, request *clb.BatchModifyTargetTagRequest) (response *clb.BatchModifyTargetTagResponse, err error)

	// CLBv2
	CreateLoadBalancerV2(ctx context.Context, request *clbv2.CreateLoadBalancerArgs) (response *clbv2.CreateLoadBalancerResponse, err error)
	CreateLoadBalancerListenersV2(ctx context.Context, request *clbv2.CreateLoadBalancerListenersArgs) (response *clbv2.CreateLoadBalancerListenersResponse, err error)
	ModifyLoadBalancerListenerV2(ctx context.Context, request *clbv2.ModifyLoadBalancerListenerArgs) (response *clbv2.ModifyLoadBalancerListenerResponse, err error)
	DeleteLoadBalancerListenersV2(ctx context.Context, request *clbv2.DeleteLoadBalancerListenersArgs) (response *clbv2.DeleteLoadBalancerListenersResponse, err error)
	DescribeLoadBalancersTaskResultV2(ctx context.Context, request int) (response *clbv2.DescribeLoadBalancersTaskResultResponse, err error)
}
