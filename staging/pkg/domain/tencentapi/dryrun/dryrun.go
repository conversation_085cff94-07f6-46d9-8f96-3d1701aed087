package dryrun

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/samber/lo"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/json"
	"k8s.io/klog/v2"

	serviceError "git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
)

var Records []Record

var lock sync.Mutex

type Record struct {
	Name      string
	Namespace string
	Action    string
	Request   any
}

func SaveRecord(ctx context.Context, action string, request any) {
	lock.Lock()
	defer lock.Unlock()

	object := cloudctx.Object(ctx)
	record := Record{
		Name:      object.Name(),
		Namespace: object.Namespace(),
		Action:    action,
		Request:   request,
	}
	Records = append(Records, record)

	// 如果开启了 DryRun 模式，则只把错误记录到对象上，而不输出到标准输出，避免影响到预检升级流程
	if object.IsDryRun() {
		object.SaveDryRunError(fmt.Errorf("%s:%s", record.Action, lo.Must(json.MarshalIndent(record.Request, "", " "))))
	} else {
		fmt.Printf("MockError\t%s/%s/%s\t%s\t%s\t%s\n", object.Kind(), record.Namespace, record.Name, record.Action, lo.Must(json.Marshal(record.Request)), "No")
	}
}

func WithSend(sender common.Sender) common.Sender {
	return func(req tchttp.Request, rsp tchttp.Response) (err error) {
		klog.V(4).Infof("Request: %s %s\n", req.GetAction(), req.GetUrl())

		dryRun := cluster.Instance.DryRun()
		// 如果全局没有开启dryRun，再看对应的对象有没有开启dryRun
		if dryRun == false {
			// 云API调用不一定都是service或ingress触发的，全局信息获取也可能会调用，所以需要判定再使用
			object, ok := cloudctx.GetObject(req.GetContext())
			if ok {
				dryRun = object.IsDryRun()
			}
		}

		if dryRun {
			action := req.GetAction()
			if !strings.HasPrefix(action, "Describe") {
				SaveRecord(req.GetContext(), req.GetAction(), req)
				if strings.HasPrefix(action, "Create") {
					return types.NewError(serviceError.MockError, "", action, lo.Must(json.Marshal(req)))
				} else if strings.Contains(action, "Modify") || strings.HasPrefix(action, "Register") ||
					strings.HasPrefix(action, "Deregister") {
					// 模拟生成RequestID，避免上层读取失败，用在WaitUntilTaskDone中
					json.Unmarshal([]byte(`{"Response":{"RequestId":"dryrun"}}`), rsp)
				}

				return nil
			}
		}

		return sender(req, rsp)
	}
}
