package tencentapi

import (
	"context"
	"time"

	"git.woa.com/kateway/pkg/domain/tencentapi/dryrun"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloud/clientset"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	clbv2 "github.com/howardshaw/qcloudapi-sdk-go/clb"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
)

var (
	Instance TencentAPIService = nil
)

type Config struct {
	Admit    bool // 是否调用策略中心审计逻辑，测试场景下不需要
	ReadOnly bool // 是否只读调用，预检时使用

	Region string
	clientset.Config
}

func Init(config *Config) error {
	common.Init(&common.Config{
		WithSend: dryrun.WithSend,
	})

	defaultTencentRegionClientSetServiceInstance := clientset.New(config.Config)
	if _, err := defaultTencentRegionClientSetServiceInstance.GetForRegion(config.Region); err != nil {
		return err
	}

	tencentAPIService := &defaultService{
		clientset:         defaultTencentRegionClientSetServiceInstance,
		RetryDefault:      3,
		TaskCheckInterval: 3 * time.Second,
	}

	Instance = &api{
		config:         config,
		defaultService: tencentAPIService,
		readonlyAPI:    &readonlyAPIService{*tencentAPIService},
		admitAPI:       &admitAPIService{*tencentAPIService},
	}

	return nil
}

type api struct {
	config *Config

	*defaultService
	readonlyAPI *readonlyAPIService
	admitAPI    *admitAPIService
}

func (api *api) isReadOnly(object types.Object) bool {
	return api.config.ReadOnly || object.IsDryRun()
}

func (api *api) ModifyResourceTags(ctx context.Context, request *tag.ModifyResourceTagsRequest) (response *tag.ModifyResourceTagsResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyResourceTags(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyResourceTags(ctx, request)
	}

	return api.defaultService.ModifyResourceTags(ctx, request)
}

func (api *api) DeleteTag(ctx context.Context, request *tag.DeleteTagRequest) (response *tag.DeleteTagResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.DeleteTag(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.DeleteTag(ctx, request)
	}

	return api.defaultService.DeleteTag(ctx, request)
}

func (api *api) ExpandCreateLoadBalancer(ctx context.Context, request *ExpandCreateLoadBalancerRequest) (response *clb.CreateLoadBalancerResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ExpandCreateLoadBalancer(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ExpandCreateLoadBalancer(ctx, request)
	}

	return api.defaultService.ExpandCreateLoadBalancer(ctx, request)
}

func (api *api) CreateLoadBalancer(ctx context.Context, request *clb.CreateLoadBalancerRequest) (response *clb.CreateLoadBalancerResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.CreateLoadBalancer(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.CreateLoadBalancer(ctx, request)
	}

	return api.defaultService.CreateLoadBalancer(ctx, request)
}

func (api *api) DeleteLoadBalancer(ctx context.Context, request *clb.DeleteLoadBalancerRequest) (response *clb.DeleteLoadBalancerResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.DeleteLoadBalancer(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.DeleteLoadBalancer(ctx, request)
	}

	return api.defaultService.DeleteLoadBalancer(ctx, request)
}

func (api *api) CreateListener(ctx context.Context, request *clbinternal.CreateListenerRequest) (response *clbinternal.CreateListenerResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.CreateListener(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.CreateListener(ctx, request)
	}

	return api.defaultService.CreateListener(ctx, request)
}

func (api *api) ModifyListener(ctx context.Context, request *clbinternal.ModifyListenerRequest) (response *clbinternal.ModifyListenerResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyListener(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyListener(ctx, request)
	}

	return api.defaultService.ModifyListener(ctx, request)
}

func (api *api) DeleteListener(ctx context.Context, request *clb.DeleteListenerRequest) (response *clb.DeleteListenerResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.DeleteListener(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.DeleteListener(ctx, request)
	}

	return api.defaultService.DeleteListener(ctx, request)
}

func (api *api) RegisterTargets(ctx context.Context, request *clb.RegisterTargetsRequest) (response *clb.RegisterTargetsResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.RegisterTargets(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.RegisterTargets(ctx, request)
	}

	return api.defaultService.RegisterTargets(ctx, request)
}

func (api *api) DeregisterTargets(ctx context.Context, request *clb.DeregisterTargetsRequest) (response *clb.DeregisterTargetsResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.DeregisterTargets(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.DeregisterTargets(ctx, request)
	}

	return api.defaultService.DeregisterTargets(ctx, request)
}

func (api *api) ModifyTargetWeight(ctx context.Context, request *clb.ModifyTargetWeightRequest) (response *clb.ModifyTargetWeightResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyTargetWeight(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyTargetWeight(ctx, request)
	}

	return api.defaultService.ModifyTargetWeight(ctx, request)
}

func (api *api) BatchRegisterTargets(ctx context.Context, request *clb.BatchRegisterTargetsRequest) (response *clb.BatchRegisterTargetsResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.BatchRegisterTargets(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.BatchRegisterTargets(ctx, request)
	}

	return api.defaultService.BatchRegisterTargets(ctx, request)
}

func (api *api) BatchDeregisterTargets(ctx context.Context, request *clb.BatchDeregisterTargetsRequest) (response *clb.BatchDeregisterTargetsResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.BatchDeregisterTargets(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.BatchDeregisterTargets(ctx, request)
	}

	return api.defaultService.BatchDeregisterTargets(ctx, request)
}

func (api *api) BatchModifyTargetWeight(ctx context.Context, request *clb.BatchModifyTargetWeightRequest) (response *clb.BatchModifyTargetWeightResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.BatchModifyTargetWeight(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.BatchModifyTargetWeight(ctx, request)
	}

	return api.defaultService.BatchModifyTargetWeight(ctx, request)
}

func (api *api) RegisterTargetsWithClassicalLB(ctx context.Context, request *clb.RegisterTargetsWithClassicalLBRequest) (response *clb.RegisterTargetsWithClassicalLBResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.RegisterTargetsWithClassicalLB(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.RegisterTargetsWithClassicalLB(ctx, request)
	}

	return api.defaultService.RegisterTargetsWithClassicalLB(ctx, request)
}

func (api *api) DeregisterTargetsFromClassicalLB(ctx context.Context, request *clb.DeregisterTargetsFromClassicalLBRequest) (response *clb.DeregisterTargetsFromClassicalLBResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.DeregisterTargetsFromClassicalLB(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.DeregisterTargetsFromClassicalLB(ctx, request)
	}

	return api.defaultService.DeregisterTargetsFromClassicalLB(ctx, request)
}

func (api *api) ModifyLoadBalancerAttributes(ctx context.Context, request *clbinternal.ModifyLoadBalancerAttributesRequest) (response *clbinternal.ModifyLoadBalancerAttributesResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyLoadBalancerAttributes(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyLoadBalancerAttributes(ctx, request)
	}

	return api.defaultService.ModifyLoadBalancerAttributes(ctx, request)
}

func (api *api) SetLoadBalancerSecurityGroups(ctx context.Context, request *clb.SetLoadBalancerSecurityGroupsRequest) (response *clb.SetLoadBalancerSecurityGroupsResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.SetLoadBalancerSecurityGroups(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.SetLoadBalancerSecurityGroups(ctx, request)
	}

	return api.defaultService.SetLoadBalancerSecurityGroups(ctx, request)
}

func (api *api) CreateRule(ctx context.Context, request *clb.CreateRuleRequest) (response *clb.CreateRuleResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.CreateRule(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.CreateRule(ctx, request)
	}

	return api.defaultService.CreateRule(ctx, request)
}

func (api *api) ModifyRule(ctx context.Context, request *clb.ModifyRuleRequest) (response *clb.ModifyRuleResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyRule(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyRule(ctx, request)
	}

	return api.defaultService.ModifyRule(ctx, request)
}

func (api *api) ModifyDomainAttributes(ctx context.Context, request *clb.ModifyDomainAttributesRequest) (response *clb.ModifyDomainAttributesResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyDomainAttributes(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyDomainAttributes(ctx, request)
	}

	return api.defaultService.ModifyDomainAttributes(ctx, request)
}

func (api *api) DeleteRule(ctx context.Context, request *clb.DeleteRuleRequest) (response *clb.DeleteRuleResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.DeleteRule(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.DeleteRule(ctx, request)
	}

	return api.defaultService.DeleteRule(ctx, request)
}

func (api *api) ModifyLBOperateProtect(ctx context.Context, request *clbinternal.ModifyLBOperateProtectRequest) (response *clbinternal.ModifyLBOperateProtectResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyLBOperateProtect(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyLBOperateProtect(ctx, request)
	}

	return api.defaultService.ModifyLBOperateProtect(ctx, request)
}

func (api *api) BatchModifyTargetTag(ctx context.Context, request *clb.BatchModifyTargetTagRequest) (response *clb.BatchModifyTargetTagResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.BatchModifyTargetTag(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.BatchModifyTargetTag(ctx, request)
	}

	return api.defaultService.BatchModifyTargetTag(ctx, request)
}

func (api *api) CreateLoadBalancerSnatIps(ctx context.Context, request *clb.CreateLoadBalancerSnatIpsRequest) (response *clb.CreateLoadBalancerSnatIpsResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.CreateLoadBalancerSnatIps(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.CreateLoadBalancerSnatIps(ctx, request)
	}

	return api.defaultService.CreateLoadBalancerSnatIps(ctx, request)
}

func (api *api) DeleteLoadBalancerSnatIps(ctx context.Context, request *clb.DeleteLoadBalancerSnatIpsRequest) (response *clb.DeleteLoadBalancerSnatIpsResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.DeleteLoadBalancerSnatIps(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.DeleteLoadBalancerSnatIps(ctx, request)
	}

	return api.defaultService.DeleteLoadBalancerSnatIps(ctx, request)
}

func (api *api) ModifyLoadBalancerMixIpTarget(ctx context.Context, request *clb.ModifyLoadBalancerMixIpTargetRequest) (response *clb.ModifyLoadBalancerMixIpTargetResponse, err error) {
	request.SetContext(ctx)
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyLoadBalancerMixIpTarget(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyLoadBalancerMixIpTarget(ctx, request)
	}

	return api.defaultService.ModifyLoadBalancerMixIpTarget(ctx, request)
}

func (api *api) CreateLoadBalancerV2(ctx context.Context, request *clbv2.CreateLoadBalancerArgs) (response *clbv2.CreateLoadBalancerResponse, err error) {
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.CreateLoadBalancerV2(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.CreateLoadBalancerV2(ctx, request)
	}

	return api.defaultService.CreateLoadBalancerV2(ctx, request)
}

func (api *api) CreateLoadBalancerListenersV2(ctx context.Context, request *clbv2.CreateLoadBalancerListenersArgs) (response *clbv2.CreateLoadBalancerListenersResponse, err error) {
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.CreateLoadBalancerListenersV2(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.CreateLoadBalancerListenersV2(ctx, request)
	}

	return api.defaultService.CreateLoadBalancerListenersV2(ctx, request)
}

func (api *api) ModifyLoadBalancerListenerV2(ctx context.Context, request *clbv2.ModifyLoadBalancerListenerArgs) (response *clbv2.ModifyLoadBalancerListenerResponse, err error) {
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.ModifyLoadBalancerListenerV2(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.ModifyLoadBalancerListenerV2(ctx, request)
	}

	return api.defaultService.ModifyLoadBalancerListenerV2(ctx, request)
}

func (api *api) DeleteLoadBalancerListenersV2(ctx context.Context, request *clbv2.DeleteLoadBalancerListenersArgs) (response *clbv2.DeleteLoadBalancerListenersResponse, err error) {
	if api.isReadOnly(cloudctx.Object(ctx)) {
		return api.readonlyAPI.DeleteLoadBalancerListenersV2(ctx, request)
	}

	if api.config.Admit {
		return api.admitAPI.DeleteLoadBalancerListenersV2(ctx, request)
	}

	return api.defaultService.DeleteLoadBalancerListenersV2(ctx, request)
}
