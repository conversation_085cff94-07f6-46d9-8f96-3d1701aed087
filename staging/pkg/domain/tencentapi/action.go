package tencentapi

import (
	"fmt"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/samber/lo"

	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	serviceError "git.woa.com/kateway/pkg/domain/types"
)

type Service struct {
	Name      string                 `json:"name"`
	ErrorCode serviceError.ErrorCode `json:"errorCode"`
}

type Action struct {
	Service  Service `json:"service"`
	Name     string  `json:"name"`
	Version  string  `json:"version"`
	ReadOnly bool    `json:"readOnly"`
}

func (a Action) GetAPIRecord(returnCode string) metrics.TencentAPIRecord {
	return metrics.TencentAPIRecord{
		Module:     a.Service.Name,
		Action:     a.Name,
		Version:    a.Version,
		Type:       lo.Ternary(a.ReadOnly, "r", "w"),
		ReturnCode: returnCode,
	}
}

func (action *Action) GetLabels() prometheus.Labels {
	if action.ReadOnly {
		return prometheus.Labels{"service": action.Service.Name, "request": action.Name, "version": action.Version, "type": "r"}
	} else {
		return prometheus.Labels{"service": action.Service.Name, "request": action.Name, "version": action.Version, "type": "w"}
	}
}

func (action *Action) GetMethod() string {
	return fmt.Sprintf("%s%sV%s", action.Service.Name, action.Name, action.Version)
}

var (
	CLB  = Service{Name: "CLB", ErrorCode: errcode.ServiceCLBError}
	CVM  = Service{Name: "CVM", ErrorCode: errcode.ServiceCVMError}
	TKE  = Service{Name: "TKE", ErrorCode: errcode.ServiceTKEError}
	Tag  = Service{Name: "Tag", ErrorCode: errcode.ServiceTagError}
	VPC  = Service{Name: "VPC", ErrorCode: errcode.ServiceVPCError}
	SSL  = Service{Name: "SSL", ErrorCode: errcode.SSLInternalError}
	Norm = Service{Name: "Norm", ErrorCode: errcode.ServiceNormError}
)

var (
	/* CLB */
	DescribeRegions                  = Action{Service: CLB, Name: "DescribeRegions", Version: "3", ReadOnly: true}
	CreateLoadBalancer               = Action{Service: CLB, Name: "CreateLoadBalancer", Version: "3", ReadOnly: false}
	DeleteLoadBalancer               = Action{Service: CLB, Name: "DeleteLoadBalancer", Version: "3", ReadOnly: false}
	DescribeLoadBalancers            = Action{Service: CLB, Name: "DescribeLoadBalancers", Version: "3", ReadOnly: true}
	DescribeTaskStatus               = Action{Service: CLB, Name: "DescribeTaskStatus", Version: "3", ReadOnly: true}
	CreateRule                       = Action{Service: CLB, Name: "CreateRule", Version: "3", ReadOnly: false}
	DeleteRule                       = Action{Service: CLB, Name: "DeleteRule", Version: "3", ReadOnly: false}
	ModifyRule                       = Action{Service: CLB, Name: "ModifyRule", Version: "3", ReadOnly: false}
	ModifyDomainAttributes           = Action{Service: CLB, Name: "ModifyDomainAttributes", Version: "3", ReadOnly: false}
	DescribeListeners                = Action{Service: CLB, Name: "DescribeListeners", Version: "3", ReadOnly: true}
	CreateListener                   = Action{Service: CLB, Name: "CreateListener", Version: "3", ReadOnly: false}
	DeleteListener                   = Action{Service: CLB, Name: "DeleteListener", Version: "3", ReadOnly: false}
	ModifyListener                   = Action{Service: CLB, Name: "ModifyListener", Version: "3", ReadOnly: false}
	DescribeRewrite                  = Action{Service: CLB, Name: "DescribeRewrite", Version: "3", ReadOnly: true}
	AutoRewrite                      = Action{Service: CLB, Name: "AutoRewrite", ReadOnly: false}
	ManualRewrite                    = Action{Service: CLB, Name: "ManualRewrite", ReadOnly: false}
	DeleteRewrite                    = Action{Service: CLB, Name: "DeleteRewrite", ReadOnly: false}
	DescribeTargets                  = Action{Service: CLB, Name: "DescribeTargets", Version: "3", ReadOnly: true}
	RegisterTargets                  = Action{Service: CLB, Name: "RegisterTargets", Version: "3", ReadOnly: false}
	DeregisterTargets                = Action{Service: CLB, Name: "DeregisterTargets", Version: "3", ReadOnly: false}
	ModifyTargetWeight               = Action{Service: CLB, Name: "ModifyTargetWeight", Version: "3", ReadOnly: false}
	DescribeClassicalLBTargets       = Action{Service: CLB, Name: "DescribeClassicalLBTargets", Version: "3", ReadOnly: true}
	RegisterTargetsWithClassicalLB   = Action{Service: CLB, Name: "RegisterTargetsWithClassicalLB", Version: "3", ReadOnly: false}
	DeregisterTargetsFromClassicalLB = Action{Service: CLB, Name: "DeregisterTargetsFromClassicalLB", Version: "3", ReadOnly: false}
	DescribeClassicalLBListeners     = Action{Service: CLB, Name: "DescribeClassicalLBListeners", Version: "3", ReadOnly: true}
	BatchRegisterTargets             = Action{Service: CLB, Name: "BatchRegisterTargets", Version: "3", ReadOnly: false}
	BatchDeregisterTargets           = Action{Service: CLB, Name: "BatchDeregisterTargets", Version: "3", ReadOnly: false}
	BatchModifyTargetWeight          = Action{Service: CLB, Name: "BatchModifyTargetWeight", Version: "3", ReadOnly: false}
	DescribeTargetHealth             = Action{Service: CLB, Name: "DescribeTargetHealth", Version: "3", ReadOnly: true}
	DescribeQuota                    = Action{Service: CLB, Name: "DescribeQuota", ReadOnly: true}
	ModifyLoadBalancerAttributes     = Action{Service: CLB, Name: "ModifyLoadBalancerAttributes", ReadOnly: false}
	SetLoadBalancerSecurityGroups    = Action{Service: CLB, Name: "SetLoadBalancerSecurityGroups", ReadOnly: false}
	DescribeLBOperateProtect         = Action{Service: CLB, Name: "DescribeLBOperateProtect", ReadOnly: true}
	ModifyLBOperateProtect           = Action{Service: CLB, Name: "ModifyLBOperateProtect", ReadOnly: false}
	CreateLoadBalancerSnatIps        = Action{Service: CLB, Name: "CreateLoadBalancerSnatIps", ReadOnly: false}
	DeleteLoadBalancerSnatIps        = Action{Service: CLB, Name: "DeleteLoadBalancerSnatIps", ReadOnly: false}
	ModifyLoadBalancerMixIpTarget    = Action{Service: CLB, Name: "ModifyLoadBalancerMixIpTarget", ReadOnly: false}
	DescribeLBListeners              = Action{Service: CLB, Name: "DescribeLBListeners", ReadOnly: true}
	BatchModifyTargetTag             = Action{Service: CLB, Name: "BatchModifyTargetTag", ReadOnly: false}

	CreateLoadBalancerV2              = Action{Service: CLB, Name: "CreateLoadBalancerV2", Version: "2", ReadOnly: false}
	CreateLoadBalancerListenersV2     = Action{Service: CLB, Name: "CreateLoadBalancerListenersV2", Version: "2", ReadOnly: false}
	ModifyLoadBalancerListenerV2      = Action{Service: CLB, Name: "ModifyLoadBalancerListenerV2", Version: "2", ReadOnly: false}
	DeleteLoadBalancerListenersV2     = Action{Service: CLB, Name: "DeleteLoadBalancerListenersV2", Version: "2", ReadOnly: false}
	DescribeLoadBalancersTaskResultV2 = Action{Service: CLB, Name: "DescribeLoadBalancersTaskResultV2", Version: "2", ReadOnly: true}

	/* CVM */
	DescribeInstances = Action{Service: CVM, Name: "DescribeInstances", Version: "3", ReadOnly: true}

	/* SSL */
	DescribeCertificates      = Action{Service: SSL, Name: "DescribeCertificates", ReadOnly: true}
	UploadCertificate         = Action{Service: SSL, Name: "UploadCertificate", ReadOnly: false}
	DescribeCertificateDetail = Action{Service: SSL, Name: "DescribeCertificateDetail", ReadOnly: true}

	/* TKE */
	DescribeClusters    = Action{Service: TKE, Name: "DescribeClusters", Version: "3", ReadOnly: true}
	DescribeEKSClusters = Action{Service: TKE, Name: "DescribeEKSClusters", Version: "3", ReadOnly: true}

	/* TKE Inner */
	ForwardRequest         = Action{Service: TKE, Name: "ForwardRequest", Version: "3", ReadOnly: true}
	AdmitResourceOperation = Action{Service: TKE, Name: "AdmitResourceOperation", Version: "3", ReadOnly: true}

	/* Tag */
	DescribeResourcesByTags           = Action{Service: Tag, Name: "DescribeResourcesByTags", Version: "3", ReadOnly: true}
	ModifyResourceTags                = Action{Service: Tag, Name: "ModifyResourceTags", Version: "3", ReadOnly: false}
	DescribeResourceTagsByResourceIds = Action{Service: Tag, Name: "DescribeResourceTagsByResourceIds", Version: "3", ReadOnly: true}
	DeleteTag                         = Action{Service: Tag, Name: "DeleteTag", Version: "3", ReadOnly: false}
	DescribeTags                      = Action{Service: Tag, Name: "DescribeTags", Version: "3", ReadOnly: true}

	/* VPC */
	DescribeSubnets              = Action{Service: VPC, Name: "DescribeSubnets", Version: "3", ReadOnly: true}
	DescribeCcnAttachedInstances = Action{Service: VPC, Name: "DescribeCcnAttachedInstances", Version: "3", ReadOnly: true}
	DescribeSecurityGroups       = Action{Service: VPC, Name: "DescribeSecurityGroups", Version: "3", ReadOnly: true}

	/* Norm */
	NormGetClusterLBInfo = Action{Service: Norm, Name: "NormGetClusterLBInfo", Version: "2", ReadOnly: true}
)
