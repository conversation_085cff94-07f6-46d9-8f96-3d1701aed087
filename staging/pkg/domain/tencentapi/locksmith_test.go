package tencentapi

import (
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	locksmithHttp "git.woa.com/tke-library/locksmith/client-go/http/client/credential_exchange"
	"git.woa.com/tke-library/locksmith/client-go/provider"
	"git.woa.com/tke-library/locksmith/client-go/serviceaccount/file"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.opentelemetry.io/otel"
	"go.uber.org/zap"
)

func Test_locksmith(t *testing.T) {
	zapLogger, err := zap.NewProduction()
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	httpClient := &http.Client{
		// Add OpenTelemetry HTTP client transport.
		// You can initialize OpenTelemetry tracer provider globally via otel.SetTracerProvider and
		// let OpenTelemetry HTTP transport use global tracer provider without setting otelhttp.WithTracerProvider
		// option, or
		// you may pass a customized tracer provider to otelhttp.NewTransport via otelhttp.WithTracerProvider option.
		//
		// Tracer created by this tracer provider can carry OpenTelemetry trace ID to Locksmith server
		// to help debugging.
		// Locksmith server will use this trace ID in all spans when processing the request.
		Transport: otelhttp.NewTransport(
			http.DefaultTransport,
			// For demonstration purpose only, this line can be removed.
			// It is the same as the default behavior of otelhttp.NewTransport.
			otelhttp.WithTracerProvider(otel.GetTracerProvider()),
		),
	}

	client, err := locksmithHttp.NewLocksmithCredentialExchangeServiceClient(
		locksmithHttp.WithHTTPClient(httpClient),
		locksmithHttp.WithLogger(zapLogger),
	)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	credProvider, err := provider.NewTKEServiceRoleCredentialProvider(
		client,
		provider.WithLogger(zapLogger),
		provider.WithClusterID("cls-********"), // TKE cluster ID must be set.
		provider.WithServiceAccountTokenProvider(file.NewTokenProvider("./sa_token")),
		provider.WithSessionName("test-session"),
		provider.WithCredentialLifetime(3600), // 1 hour.
		provider.WithCredentialTokenTags(map[string]string{"key1": "value1"}),
		// Refresh token if it will expire in less than 20 seconds.
		provider.WithTimeRefreshCredentialInAdvance(time.Second*20),
		// Set OpenTelemetry tracer provider for TKEServiceRoleCredentialProvider.
		// You can initialize OpenTelemetry tracer provider globally via otel.SetTracerProvider and
		// let TKEServiceRoleCredentialProvider use global tracer provider without setting provider.WithTracerProvider
		// option, or
		// you may set a customized tracer provider via provider.WithTracerProvider option.
		//
		// Tracer created by this tracer provider can help you trace time costs of various steps
		// of getting Tencent Cloud temporary credential when GetTKEServiceRoleCredential is called.
		//
		// For demonstration purpose only, this line can be removed.
		// It is the same as the default behavior of NewTKEServiceRoleCredentialProvider.
		provider.WithTracerProvider(otel.GetTracerProvider()),
	)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	// #===============================================================================================================#
	// #                Use the provider as the credential provider when creating Tencent Cloud common client.         #
	// #===============================================================================================================#
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.RootDomain = "internal.tencentcloudapi.com"

	tencentcloudClient, err := common.NewClientWithProviders(regions.Guangzhou, credProvider)
	if err != nil {
		fmt.Printf("%s", err.Error())
		os.Exit(1)
	}

	tcCred := tencentcloudClient.GetCredential()
	fmt.Println(tcCred.GetToken())
}
