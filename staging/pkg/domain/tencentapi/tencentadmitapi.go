package tencentapi

import (
	"context"

	clbv2 "github.com/howardshaw/qcloudapi-sdk-go/clb"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"

	serviceError "git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	tkeInner "git.woa.com/kateway/pkg/tencent/cloud/tkeinternal"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
)

type admitAPIService struct {
	defaultService
}

const (
	OPERATIONDENIED_POLICYNOTALLOWED = "OperationDenied.PolicyNotAllowed"
)

// //////////////////////////////////////////////////////////////
// CVM API
// //////////////////////////////////////////////////////////////

// //////////////////////////////////////////////////////////////
// TKE Inner API
// //////////////////////////////////////////////////////////////

// //////////////////////////////////////////////////////////////
// TAG API
// //////////////////////////////////////////////////////////////

// //////////////////////////////////////////////////////////////
// CLB V3 API
// //////////////////////////////////////////////////////////////

func (api *admitAPIService) DeleteLoadBalancer(ctx context.Context, request *clb.DeleteLoadBalancerRequest) (response *clb.DeleteLoadBalancerResponse, err error) {
	kubernetesResource := ConvertKubernetesResource(cloudctx.Object(ctx))
	admitResourceOperationRequest := tkeInner.NewAdmitResourceOperationRequest()
	admitResourceOperationRequest.ClusterID = lo.ToPtr(cluster.Instance.Name())
	admitResourceOperationRequest.Origin = lo.ToPtr(cluster.Instance.ControllerName())
	admitResourceOperationRequest.Operation = lo.ToPtr("delete")
	admitResourceOperationRequest.TargetAction = lo.ToPtr("DeleteLoadBalancer")
	admitResourceOperationRequest.ResourceSet = make([]*tkeInner.Resource, len(request.LoadBalancerIds))
	for index, loadBalancerId := range request.LoadBalancerIds {
		admitResourceOperationRequest.ResourceSet[index] = &tkeInner.Resource{
			Cloud: &tkeInner.CloudResource{
				ResourceType: lo.ToPtr("clb"),
				ResourceID:   loadBalancerId,
			},
			Kubernetes: kubernetesResource,
		}
	}
	if _, err := api.defaultService.AdmitResourceOperation(ctx, admitResourceOperationRequest); err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == OPERATIONDENIED_POLICYNOTALLOWED {
				return nil, types.NewError(serviceError.AdmitError, sdkError.Error())
			}
		}
	}
	return api.defaultService.DeleteLoadBalancer(ctx, request)
}

func (api *admitAPIService) DeleteListener(ctx context.Context, request *clb.DeleteListenerRequest) (response *clb.DeleteListenerResponse, err error) {
	kubernetesResource := ConvertKubernetesResource(cloudctx.Object(ctx))
	admitResourceOperationRequest := tkeInner.NewAdmitResourceOperationRequest()
	admitResourceOperationRequest.ClusterID = lo.ToPtr(cluster.Instance.Name())
	admitResourceOperationRequest.Origin = lo.ToPtr(cluster.Instance.ControllerName())
	admitResourceOperationRequest.Operation = lo.ToPtr("delete")
	admitResourceOperationRequest.TargetAction = lo.ToPtr("DeleteListener")
	admitResourceOperationRequest.ResourceSet = []*tkeInner.Resource{
		{
			Cloud: &tkeInner.CloudResource{
				ResourceType: lo.ToPtr("clb.listener"),
				ResourceID:   lo.ToPtr(*request.ListenerId),
			},
			Kubernetes: kubernetesResource,
		},
	}
	if _, err := api.defaultService.AdmitResourceOperation(ctx, admitResourceOperationRequest); err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == OPERATIONDENIED_POLICYNOTALLOWED {
				return nil, types.NewError(serviceError.AdmitError, sdkError.Error())
			}
		}
	}
	return api.defaultService.DeleteListener(ctx, request)
}

func (api *admitAPIService) DeleteRule(ctx context.Context, request *clb.DeleteRuleRequest) (response *clb.DeleteRuleResponse, err error) {
	kubernetesResource := ConvertKubernetesResource(cloudctx.Object(ctx))
	admitResourceOperationRequest := tkeInner.NewAdmitResourceOperationRequest()
	admitResourceOperationRequest.ClusterID = lo.ToPtr(cluster.Instance.Name())
	admitResourceOperationRequest.Origin = lo.ToPtr(cluster.Instance.ControllerName())
	admitResourceOperationRequest.Operation = lo.ToPtr("delete")
	admitResourceOperationRequest.TargetAction = lo.ToPtr("DeleteRule")
	admitResourceOperationRequest.ResourceSet = make([]*tkeInner.Resource, len(request.LocationIds))
	for index, locationId := range request.LocationIds {
		admitResourceOperationRequest.ResourceSet[index] = &tkeInner.Resource{
			Cloud: &tkeInner.CloudResource{
				ResourceType: lo.ToPtr("clb.location"),
				ResourceID:   lo.ToPtr(*locationId),
			},
			Kubernetes: kubernetesResource,
		}
	}
	if _, err := api.defaultService.AdmitResourceOperation(ctx, admitResourceOperationRequest); err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == OPERATIONDENIED_POLICYNOTALLOWED {
				return nil, types.NewError(serviceError.AdmitError, sdkError.Error())
			}
		}
	}
	return api.defaultService.DeleteRule(ctx, request)
}

func (api *admitAPIService) BatchDeregisterTargets(ctx context.Context, request *clb.BatchDeregisterTargetsRequest) (response *clb.BatchDeregisterTargetsResponse, err error) {
	kubernetesResource := ConvertKubernetesResource(cloudctx.Object(ctx))
	admitResourceOperationRequest := tkeInner.NewAdmitResourceOperationRequest()
	admitResourceOperationRequest.ClusterID = lo.ToPtr(cluster.Instance.Name())
	admitResourceOperationRequest.Origin = lo.ToPtr(cluster.Instance.ControllerName())
	admitResourceOperationRequest.Operation = lo.ToPtr("delete")
	admitResourceOperationRequest.TargetAction = lo.ToPtr("BatchDeregisterTargets")
	admitResourceOperationRequest.ResourceSet = make([]*tkeInner.Resource, len(request.Targets))
	for index, target := range request.Targets {
		admitResourceOperationRequest.ResourceSet[index] = &tkeInner.Resource{
			Cloud: &tkeInner.CloudResource{
				ResourceType: lo.ToPtr("clb.backend"),
				ResourceID:   getTarget(target),
			},
			Kubernetes: kubernetesResource,
		}
	}
	if _, err := api.defaultService.AdmitResourceOperation(ctx, admitResourceOperationRequest); err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == OPERATIONDENIED_POLICYNOTALLOWED {
				return nil, types.NewError(serviceError.AdmitError, sdkError.Error())
			}
		}
	}
	return api.defaultService.BatchDeregisterTargets(ctx, request)
}

func getTarget(target *clb.BatchTarget) *string {
	if target.InstanceId != nil && *target.InstanceId != "" {
		return lo.ToPtr(*target.InstanceId)
	} else {
		return lo.ToPtr(*target.EniIp)
	}
}

func (api *admitAPIService) DeleteLoadBalancerListenersV2(ctx context.Context, request *clbv2.DeleteLoadBalancerListenersArgs) (response *clbv2.DeleteLoadBalancerListenersResponse, err error) {
	kubernetesResource := ConvertKubernetesResource(cloudctx.Object(ctx))
	admitResourceOperationRequest := tkeInner.NewAdmitResourceOperationRequest()
	admitResourceOperationRequest.ClusterID = lo.ToPtr(cluster.Instance.Name())
	admitResourceOperationRequest.Origin = lo.ToPtr(cluster.Instance.ControllerName())
	admitResourceOperationRequest.Operation = lo.ToPtr("delete")
	admitResourceOperationRequest.TargetAction = lo.ToPtr("DeleteLoadBalancerListenersV2")
	admitResourceOperationRequest.ResourceSet = make([]*tkeInner.Resource, len(request.ListenerIds))
	for index, listenerId := range request.ListenerIds {
		admitResourceOperationRequest.ResourceSet[index] = &tkeInner.Resource{
			Cloud: &tkeInner.CloudResource{
				ResourceType: lo.ToPtr("clb.listener"),
				ResourceID:   lo.ToPtr(listenerId),
			},
			Kubernetes: kubernetesResource,
		}
	}
	if _, err := api.defaultService.AdmitResourceOperation(ctx, admitResourceOperationRequest); err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == OPERATIONDENIED_POLICYNOTALLOWED {
				return nil, types.NewError(serviceError.AdmitError, sdkError.Error())
			}
		}
	}
	return api.defaultService.DeleteLoadBalancerListenersV2(ctx, request)
}

func ConvertKubernetesResource(service types.Object) *tkeInner.KubernetesResource {
	return &tkeInner.KubernetesResource{
		Kind:      lo.ToPtr(service.Kind()),
		Namespace: lo.ToPtr(service.Namespace()),
		Name:      lo.ToPtr(service.Name()),
		UUID:      lo.ToPtr(service.UID()),
	}
}
