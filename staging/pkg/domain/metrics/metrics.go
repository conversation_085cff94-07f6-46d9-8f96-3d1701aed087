package metrics

import (
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"k8s.io/utils/clock"

	"git.woa.com/kateway/pkg/domain/types"
)

var (
	Instance Collector
)

type ControllerCollector interface {
	IncSyncCount(obj types.Object, returnCode string)
	UpdateSyncTime(obj types.Object, returnCode string, syncTime time.Duration)
	RemoveObjectMetrics(types.Object)
	IncNodeChangeCount(nodename string)
	SetOperationalCount(objKind string, operationalMap map[string]int)

	IncWebhookRequestCount(prometheus.Labels)
	UpdateWebhookDelayTime(l prometheus.Labels, delayTime time.Duration)
}

type TencentAPIRecord struct {
	// 被调service的名称
	Module  string
	Action  string
	Version string
	// 读写操作类型
	Type       string
	ReturnCode string
}

type TencentAPICollector interface {
	IncAPIRequestCount(TencentAPIRecord)
	UpdateQcloudAPIDelayTime(r TencentAPIRecord, delayTime time.Duration)
	IncTaskRequestCount(TencentAPIRecord)
	UpdateQcloudTaskDelayTime(r TencentAPIRecord, delayTime time.Duration)
}

type Collector interface {
	ControllerCollector
	TencentAPICollector

	GetRegistry() *prometheus.Registry
	Start()
	Stop()
}

// This file provides abstractions for setting the provider (e.g., prometheus)
// of metrics.

type QueueMetric interface {
	Add(any)
	Get(any)
	Done(any)
	UpdateUnfinishedWork()
}

// GaugeMetric represents a single numerical value that can arbitrarily go up
// and down.
type GaugeMetric interface {
	Inc()
	Dec()
}

// SettableGaugeMetric represents a single numerical value that can arbitrarily go up
// and down. (Separate from GaugeMetric to preserve backwards compatibility.)
type SettableGaugeMetric interface {
	Set(float64)
}

// CounterMetric represents a single numerical value that only ever
// goes up.
type CounterMetric interface {
	Inc()
}

// SummaryMetric captures individual observations.
type SummaryMetric interface {
	Observe(float64)
}

// HistogramMetric counts individual observations.
type HistogramMetric interface {
	Observe(float64)
}

type noopMetric struct{}

func (noopMetric) Inc() {}

func (noopMetric) Dec() {}

func (noopMetric) Set(float64) {}

func (noopMetric) Observe(float64) {}

// defaultQueueMetrics expects the caller to lock before setting any metrics.
type defaultQueueMetrics struct {
	clock clock.Clock

	// current depth of a workqueue
	depth GaugeMetric
	// total number of adds handled by a workqueue
	adds CounterMetric
	// how long an item stays in a workqueue
	latency HistogramMetric
	// how long processing an item from a workqueue takes
	workDuration         HistogramMetric
	addTimes             map[any]time.Time
	processingStartTimes map[any]time.Time

	// how long have current threads been working?
	unfinishedWorkSeconds   SettableGaugeMetric
	longestRunningProcessor SettableGaugeMetric
}

func (m *defaultQueueMetrics) Add(item any) {
	if m == nil {
		return
	}

	m.adds.Inc()
	m.depth.Inc()
	if _, exists := m.addTimes[item]; !exists {
		m.addTimes[item] = m.clock.Now()
	}
}

func (m *defaultQueueMetrics) Get(item any) {
	if m == nil {
		return
	}

	m.depth.Dec()
	m.processingStartTimes[item] = m.clock.Now()
	if startTime, exists := m.addTimes[item]; exists {
		m.latency.Observe(m.sinceInSeconds(startTime))
		delete(m.addTimes, item)
	}
}

func (m *defaultQueueMetrics) Done(item any) {
	if m == nil {
		return
	}

	if startTime, exists := m.processingStartTimes[item]; exists {
		m.workDuration.Observe(m.sinceInSeconds(startTime))
		delete(m.processingStartTimes, item)
	}
}

func (m *defaultQueueMetrics) UpdateUnfinishedWork() {
	// Note that a summary metric would be better for this, but prometheus
	// doesn't seem to have non-hacky ways to reset the summary metrics.
	var total float64
	var oldest float64
	for _, t := range m.processingStartTimes {
		age := m.sinceInSeconds(t)
		total += age
		if age > oldest {
			oldest = age
		}
	}
	m.unfinishedWorkSeconds.Set(total)
	m.longestRunningProcessor.Set(oldest)
}

type noMetrics struct{}

func (noMetrics) Add(any) {}

func (noMetrics) Get(any) {}

func (noMetrics) Done(any) {}

func (noMetrics) UpdateUnfinishedWork() {}

// Gets the time since the specified start in seconds.
func (m *defaultQueueMetrics) sinceInSeconds(start time.Time) float64 {
	return m.clock.Since(start).Seconds()
}

type RetryMetric interface {
	Retry()
}

type defaultRetryMetrics struct {
	retries CounterMetric
}

func (m *defaultRetryMetrics) Retry() {
	if m == nil {
		return
	}

	m.retries.Inc()
}

// QueueMetricsProvider generates various metrics used by the queue.
type QueueMetricsProvider interface {
	NewDepthMetric(name string) GaugeMetric
	NewAddsMetric(name string) CounterMetric
	NewLatencyMetric(name string) HistogramMetric
	NewWorkDurationMetric(name string) HistogramMetric
	NewUnfinishedWorkSecondsMetric(name string) SettableGaugeMetric
	NewLongestRunningProcessorSecondsMetric(name string) SettableGaugeMetric
	NewRetriesMetric(name string) CounterMetric
}

type noopMetricsProvider struct{}

func (noopMetricsProvider) NewDepthMetric(string) GaugeMetric {
	return noopMetric{}
}

func (noopMetricsProvider) NewAddsMetric(string) CounterMetric {
	return noopMetric{}
}

func (noopMetricsProvider) NewLatencyMetric(string) HistogramMetric {
	return noopMetric{}
}

func (noopMetricsProvider) NewWorkDurationMetric(string) HistogramMetric {
	return noopMetric{}
}

func (noopMetricsProvider) NewUnfinishedWorkSecondsMetric(string) SettableGaugeMetric {
	return noopMetric{}
}

func (noopMetricsProvider) NewLongestRunningProcessorSecondsMetric(string) SettableGaugeMetric {
	return noopMetric{}
}

func (noopMetricsProvider) NewRetriesMetric(string) CounterMetric {
	return noopMetric{}
}

var globalMetricsFactory = QueueMetricsFactory{
	metricsProvider: noopMetricsProvider{},
}

type QueueMetricsFactory struct {
	metricsProvider QueueMetricsProvider

	onlyOnce sync.Once
}

func (f *QueueMetricsFactory) setProvider(mp QueueMetricsProvider) {
	f.onlyOnce.Do(func() {
		f.metricsProvider = mp
	})
}

func (f *QueueMetricsFactory) NewQueueMetric(name string, clock clock.Clock) QueueMetric {
	mp := f.metricsProvider
	if len(name) == 0 || mp == (noopMetricsProvider{}) {
		return noMetrics{}
	}
	return &defaultQueueMetrics{
		clock:                   clock,
		depth:                   mp.NewDepthMetric(name),
		adds:                    mp.NewAddsMetric(name),
		latency:                 mp.NewLatencyMetric(name),
		workDuration:            mp.NewWorkDurationMetric(name),
		unfinishedWorkSeconds:   mp.NewUnfinishedWorkSecondsMetric(name),
		longestRunningProcessor: mp.NewLongestRunningProcessorSecondsMetric(name),
		addTimes:                map[any]time.Time{},
		processingStartTimes:    map[any]time.Time{},
	}
}

func NewRetryMetric(name string) RetryMetric {
	var ret *defaultRetryMetrics
	if len(name) == 0 {
		return ret
	}
	return &defaultRetryMetrics{
		retries: globalMetricsFactory.metricsProvider.NewRetriesMetric(name),
	}
}

func GetQueueMetricsFactory() *QueueMetricsFactory {
	return &globalMetricsFactory
}
