package metrics

import (
	"fmt"
	"sync"

	"git.woa.com/kateway/pkg/domain/types"
)

type SyncCodeMaps struct {
	rwLock           sync.RWMutex
	codeMapsByObject map[string]map[string]struct{}
}

func NewSyncCodeMaps() *SyncCodeMaps {
	return &SyncCodeMaps{
		codeMapsByObject: map[string]map[string]struct{}{},
	}
}

func (m *SyncCodeMaps) IsCodeExists(obj types.Object, code string) bool {
	m.rwLock.RLock()
	defer m.rwLock.RUnlock()

	key := fmt.Sprintf("%s/%s", obj.Kind(), obj.String())
	if cm, exist := m.codeMapsByObject[key]; exist {
		if _, exist := cm[code]; exist {
			return true
		}
	}
	return false
}

func (m *SyncCodeMaps) AddCode(obj types.Object, code string) {
	m.rwLock.Lock()
	defer m.rwLock.Unlock()

	key := fmt.Sprintf("%s/%s", obj.Kind(), obj.String())
	if _, exist := m.codeMapsByObject[key]; !exist {
		m.codeMapsByObject[key] = make(map[string]struct{})
	}
	m.codeMapsByObject[key][code] = struct{}{}
}

func (m *SyncCodeMaps) RemoveCodeMap(obj types.Object) {
	m.rwLock.Lock()
	defer m.rwLock.Unlock()

	delete(m.codeMapsByObject, fmt.Sprintf("%s/%s", obj.Kind(), obj.String()))
}

func (m *SyncCodeMaps) GetCodeMap(obj types.Object) map[string]struct{} {
	m.rwLock.RLock()
	defer m.rwLock.RUnlock()

	res := make(map[string]struct{})

	key := fmt.Sprintf("%s/%s", obj.Kind(), obj.String())
	if cm, exist := m.codeMapsByObject[key]; exist {
		for k, v := range cm {
			res[k] = v
		}
	}
	return res
}
