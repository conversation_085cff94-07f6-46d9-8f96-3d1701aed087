package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

// Package prometheus sets the workqueue DefaultMetricsFactory to produce
// prometheus metrics. To use this package, you just have to import it.

// Metrics subsystem and keys used by the taskqueue.
const (
	WorkQueueSubsystem         = "workqueue"
	DepthKey                   = "depth"
	AddsKey                    = "adds_total"
	QueueLatencyKey            = "queue_duration_seconds"
	WorkDurationKey            = "work_duration_seconds"
	UnfinishedWorkKey          = "unfinished_work_seconds"
	LongestRunningProcessorKey = "longest_running_processor_seconds"
	RetriesKey                 = "retries_total"
)

type QueueCollector struct {
	prometheus.Collector

	PrometheusMetricsProvider *PrometheusMetricsProvider
}

func NewQueueCollector() *QueueCollector {
	workqueueController := &QueueCollector{
		PrometheusMetricsProvider: &PrometheusMetricsProvider{
			depth: prometheus.NewGaugeVec(
				prometheus.GaugeOpts{
					Subsystem: WorkQueueSubsystem,
					Name:      Depth<PERSON><PERSON>,
					Help:      "Current depth of workqueue",
				},
				[]string{"name"},
			),
			adds: prometheus.NewCounterVec(
				prometheus.CounterOpts{
					Subsystem: WorkQueueSubsystem,
					Name:      AddsKey,
					Help:      "Total number of adds handled by workqueue",
				},
				[]string{"name"},
			),
			latency: prometheus.NewHistogramVec(
				prometheus.HistogramOpts{
					Subsystem: WorkQueueSubsystem,
					Name:      QueueLatencyKey,
					Help:      "How long in seconds an item stays in workqueue before being requested.",
					Buckets:   prometheus.ExponentialBuckets(10e-9, 10, 10),
				},
				[]string{"name"},
			),
			workDuration: prometheus.NewHistogramVec(
				prometheus.HistogramOpts{
					Subsystem: WorkQueueSubsystem,
					Name:      WorkDurationKey,
					Help:      "How long in seconds processing an item from workqueue takes.",
					Buckets:   prometheus.ExponentialBuckets(10e-9, 10, 10),
				},
				[]string{"name"},
			),
			unfinished: prometheus.NewGaugeVec(
				prometheus.GaugeOpts{
					Subsystem: WorkQueueSubsystem,
					Name:      UnfinishedWorkKey,
					Help: "How many seconds of work has done that " +
						"is in progress and hasn't been observed by work_duration. Large " +
						"values indicate stuck threads. One can deduce the number of stuck " +
						"threads by observing the rate at which this increases.",
				},
				[]string{"name"},
			),
			longestRunningProcessor: prometheus.NewGaugeVec(
				prometheus.GaugeOpts{
					Subsystem: WorkQueueSubsystem,
					Name:      LongestRunningProcessorKey,
					Help: "How many seconds has the longest running " +
						"processor for workqueue been running.",
				},
				[]string{"name"},
			),
			retries: prometheus.NewCounterVec(
				prometheus.CounterOpts{
					Subsystem: WorkQueueSubsystem,
					Name:      RetriesKey,
					Help:      "Total number of retries handled by workqueue",
				},
				[]string{"name"},
			),
		},
	}
	globalMetricsFactory.setProvider(workqueueController.PrometheusMetricsProvider)
	return workqueueController
}

// Collect implements the prometheus.Collector interface.
func (c *QueueCollector) Collect(ch chan<- prometheus.Metric) {
	c.PrometheusMetricsProvider.depth.Collect(ch)
	c.PrometheusMetricsProvider.adds.Collect(ch)
	c.PrometheusMetricsProvider.latency.Collect(ch)
	c.PrometheusMetricsProvider.workDuration.Collect(ch)
	c.PrometheusMetricsProvider.unfinished.Collect(ch)
	c.PrometheusMetricsProvider.longestRunningProcessor.Collect(ch)
	c.PrometheusMetricsProvider.retries.Collect(ch)
}

func (c *QueueCollector) Describe(ch chan<- *prometheus.Desc) {
	c.PrometheusMetricsProvider.depth.Describe(ch)
	c.PrometheusMetricsProvider.adds.Describe(ch)
	c.PrometheusMetricsProvider.latency.Describe(ch)
	c.PrometheusMetricsProvider.workDuration.Describe(ch)
	c.PrometheusMetricsProvider.unfinished.Describe(ch)
	c.PrometheusMetricsProvider.longestRunningProcessor.Describe(ch)
	c.PrometheusMetricsProvider.retries.Describe(ch)
}

type PrometheusMetricsProvider struct {
	depth                   *prometheus.GaugeVec
	adds                    *prometheus.CounterVec
	latency                 *prometheus.HistogramVec
	workDuration            *prometheus.HistogramVec
	unfinished              *prometheus.GaugeVec
	longestRunningProcessor *prometheus.GaugeVec
	retries                 *prometheus.CounterVec
}

func (p PrometheusMetricsProvider) NewDepthMetric(name string) GaugeMetric {
	return p.depth.WithLabelValues(name)
}

func (p PrometheusMetricsProvider) NewAddsMetric(name string) CounterMetric {
	return p.adds.WithLabelValues(name)
}

func (p PrometheusMetricsProvider) NewLatencyMetric(name string) HistogramMetric {
	return p.latency.WithLabelValues(name)
}

func (p PrometheusMetricsProvider) NewWorkDurationMetric(name string) HistogramMetric {
	return p.workDuration.WithLabelValues(name)
}

func (p PrometheusMetricsProvider) NewUnfinishedWorkSecondsMetric(name string) SettableGaugeMetric {
	return p.unfinished.WithLabelValues(name)
}

func (p PrometheusMetricsProvider) NewLongestRunningProcessorSecondsMetric(name string) SettableGaugeMetric {
	return p.longestRunningProcessor.WithLabelValues(name)
}

func (p PrometheusMetricsProvider) NewRetriesMetric(name string) CounterMetric {
	return p.retries.WithLabelValues(name)
}
