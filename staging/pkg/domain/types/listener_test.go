package types

import (
	"testing"

	"github.com/samber/lo"

	"git.woa.com/kateway/pkg/sets"
)

func TestDomain_Equals(t *testing.T) {
	testCases := []struct {
		name     string
		expect   Domain
		current  Domain
		expected bool
	}{
		{
			name: "相等的情况",
			expect: Domain{
				Host:         "test",
				Default:      true,
				HTTP2Enabled: lo.ToPtr(true),
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			current: Domain{
				Host:         "test",
				Default:      true,
				HTTP2Enabled: lo.ToPtr(true),
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			expected: true,
		},
		{
			name: "不相等的情况",
			expect: Domain{
				Host:         "test1",
				Default:      true,
				HTTP2Enabled: lo.ToPtr(true),
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			current: Domain{
				Host:         "test2",
				Default:      false,
				HTTP2Enabled: lo.ToPtr(false),
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert2"}...),
					SSLMode:        SSLModeMutual,
				},
			},
			expected: false,
		},
		{
			name: "预期不是默认域名，但实际是默认域名，认为相等",
			expect: Domain{
				Host: "abc",
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			current: Domain{
				Host:    "abc",
				Default: true,
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			expected: true,
		},
		{
			name: "预期没有证书信息，但实际有证书信息，认为相等",
			expect: Domain{
				Host:         "test",
				HTTP2Enabled: lo.ToPtr(true),
			},
			current: Domain{
				Host:         "test",
				HTTP2Enabled: lo.ToPtr(true),
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			expected: true,
		},
		{
			name: "预期是单向认证，忽略配置的ca证书",
			expect: Domain{
				Host:         "test",
				HTTP2Enabled: lo.ToPtr(true),
				TLSConfig: &TLSConfig{
					CertificateIDs:  sets.New([]string{"cert1"}...),
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("hello"),
				},
			},
			current: Domain{
				Host:         "test",
				HTTP2Enabled: lo.ToPtr(true),
				TLSConfig: &TLSConfig{
					CertificateIDs:  sets.New([]string{"cert1"}...),
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("test"),
				},
			},
			expected: true,
		},
		{
			name: "预期的HTTP2字段未设置，其他字段相同，认为是相同",
			expect: Domain{
				Host: "test",
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			current: Domain{
				Host:         "test",
				HTTP2Enabled: lo.ToPtr(true),
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			expected: true,
		},
		{
			name: "不同的Host值",
			expect: Domain{
				Host: "host1",
			},
			current: Domain{
				Host: "host2",
			},
			expected: false,
		},
		{
			name: "不同的Default值",
			expect: Domain{
				Host:    "test",
				Default: true,
			},
			current: Domain{
				Host:    "test",
				Default: false,
			},
			expected: false,
		},
		{
			name: "HTTP2Enabled为nil",
			expect: Domain{
				Host:         "test",
				HTTP2Enabled: nil,
			},
			current: Domain{
				Host:         "test",
				HTTP2Enabled: lo.ToPtr(true),
			},
			expected: true,
		},
		{
			name: "TLSConfig为空",
			expect: Domain{
				Host:      "test",
				TLSConfig: nil,
			},
			current: Domain{
				Host: "test",
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			expected: true,
		},
		{
			name: "不同的CertificateIDs",
			expect: Domain{
				Host: "test",
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			current: Domain{
				Host: "test",
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert2"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			expected: false,
		},
		{
			name: "不同的SSLMode",
			expect: Domain{
				Host: "test",
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeUnidirectional,
				},
			},
			current: Domain{
				Host: "test",
				TLSConfig: &TLSConfig{
					CertificateIDs: sets.New([]string{"cert1"}...),
					SSLMode:        SSLModeMutual,
				},
			},
			expected: false,
		},
		{
			name: "TLSConfig中CACertificateID为nil",
			expect: Domain{
				Host: "test",
				TLSConfig: &TLSConfig{
					CertificateIDs:  sets.New([]string{"cert1"}...),
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: nil,
				},
			},
			current: Domain{
				Host: "test",
				TLSConfig: &TLSConfig{
					CertificateIDs:  sets.New([]string{"cert1"}...),
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("test"),
				},
			},
			expected: true, // 因为 Equals 方法忽略了单向认证中的 CA 证书比较
		},
		{
			name: "预期和当前域名都没有TLSConfig，认为相等",
			expect: Domain{
				Host: "test",
			},
			current: Domain{
				Host: "test",
			},
			expected: true,
		},
		{
			name: "完全相同的域名配置",
			expect: Domain{
				Host:         "example.com",
				Default:      true,
				HTTP2Enabled: lo.ToPtr(true),
			},
			current: Domain{
				Host:         "example.com",
				Default:      true,
				HTTP2Enabled: lo.ToPtr(true),
			},
			expected: true,
		},
		{
			name: "当前为默认域名而期望非默认域名时应该相等",
			expect: Domain{
				Host:    "example.com",
				Default: false,
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
			},
			expected: true,
		},
		{
			name: "期望为默认域名而当前非默认域名时应该不相等",
			expect: Domain{
				Host:    "example.com",
				Default: true,
			},
			current: Domain{
				Host:    "example.com",
				Default: false,
			},
			expected: false,
		},
		{
			name: "HTTP2配置不同时应该不相等",
			expect: Domain{
				Host:         "example.com",
				Default:      true,
				HTTP2Enabled: lo.ToPtr(true),
			},
			current: Domain{
				Host:         "example.com",
				Default:      true,
				HTTP2Enabled: lo.ToPtr(false),
			},
			expected: false,
		},
		{
			name: "期望未配置HTTP2时应忽略当前HTTP2配置",
			expect: Domain{
				Host:    "example.com",
				Default: true,
			},
			current: Domain{
				Host:         "example.com",
				Default:      true,
				HTTP2Enabled: lo.ToPtr(true),
			},
			expected: true,
		},
		{
			name: "单向认证时忽略CA证书配置",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("ca-1"),
					CertificateIDs:  sets.New([]string{"cert-1"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("ca-2"),
					CertificateIDs:  sets.New([]string{"cert-1"}...),
				},
			},
			expected: true,
		},
		{
			name: "单向认证时忽略CA证书配置2",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("ca-2"),
					CertificateIDs:  sets.New([]string{"cert-2"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("ca-1"),
					CertificateIDs:  sets.New([]string{"cert-1"}...),
				},
			},
			expected: false,
		},
		{
			name: "单向认证时忽略CA证书配置3",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("ca-1"),
					CertificateIDs:  sets.New([]string{"cert-1", "cert-2"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("ca-2"),
					CertificateIDs:  sets.New([]string{"cert-2", "cert-1"}...),
				},
			},
			expected: true,
		},
		{
			name: "单向认证时忽略CA证书配置4",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("ca-1"),
					CertificateIDs:  sets.New([]string{"cert-1", "cert-2"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeUnidirectional,
					CACertificateID: lo.ToPtr("ca-2"),
					CertificateIDs:  sets.New([]string{"cert-2", "cert-3"}...),
				},
			},
			expected: false,
		},
		{
			name: "期望未配置TLS时应忽略当前TLS配置",
			expect: Domain{
				Host:    "example.com",
				Default: true,
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"cert-1"}...),
				},
			},
			expected: true,
		},
		{
			name: "证书ID不同时应该不相等",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"cert-1", "cert-2"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"cert-2", "cert-3"}...),
				},
			},
			expected: false,
		},
		{
			name: "证书ID不同时应该不相等",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"cert-1", "cert-2"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"cert-2"}...),
				},
			},
			expected: false,
		},
		{
			name: "证书ID不同时应该不相等",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"cert-1"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"cert-2"}...),
				},
			},
			expected: false,
		},
		{
			name: "服务器证书ID相同但是顺序不同时应该相等",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"aaa", "bbb"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"bbb", "aaa"}...),
				},
			},
			expected: true,
		},
		{
			name: "当前无TLS配置但期望有TLS配置时应该不相等",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:        SSLModeUnidirectional,
					CertificateIDs: sets.New([]string{"cert-1"}...),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
			},
			expected: false,
		},
		{
			name: "双向认证",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeMutual,
					CertificateIDs:  sets.New([]string{"cert-1"}...),
					CACertificateID: lo.ToPtr("ca-1"),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeMutual,
					CertificateIDs:  sets.New([]string{"cert-1"}...),
					CACertificateID: lo.ToPtr("ca-1"),
				},
			},
			expected: true,
		},
		{
			name: "双向认证,CA证书ID不同时应该不相等",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeMutual,
					CertificateIDs:  sets.New([]string{"cert-1"}...),
					CACertificateID: lo.ToPtr("ca-1"),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeMutual,
					CertificateIDs:  sets.New([]string{"cert-1"}...),
					CACertificateID: lo.ToPtr("ca-2"),
				},
			},
			expected: false,
		},
		{
			name: "双向认证,证书ID顺序不同时应该相等",
			expect: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeMutual,
					CertificateIDs:  sets.New([]string{"cert-1", "cert-2"}...),
					CACertificateID: lo.ToPtr("ca-1"),
				},
			},
			current: Domain{
				Host:    "example.com",
				Default: true,
				TLSConfig: &TLSConfig{
					SSLMode:         SSLModeMutual,
					CertificateIDs:  sets.New([]string{"cert-2", "cert-1"}...),
					CACertificateID: lo.ToPtr("ca-1"),
				},
			},
			expected: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.expect.Equals(tc.current)
			if result != tc.expected {
				t.Errorf("期望结果为 %v，但实际结果为 %v", tc.expected, result)
			}
		})
	}
}
