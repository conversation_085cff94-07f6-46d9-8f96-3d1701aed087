package types

import (
	"reflect"
	"strings"

	gclone "github.com/huandu/go-clone/generic"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"

	tscapi "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/net"
	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
)

type L7RuleConfig tscapi.L7RuleConfig

// nolint var-naming
func Convert_Tencent_CLB_HealthCheck_To_CLBInternal_HealthCheck(in clb.HealthCheck, out *clbinternal.HealthCheck) {
	if err := copier.CopyWithOption(out, in, copier.Option{IgnoreEmpty: true}); err != nil {
		panic(err)
	}
}

// nolint var-naming
func Convert_CLBInternal_HealthCheck_To_Tencent_CLB_HealthCheck(in clbinternal.HealthCheck, out *clb.HealthCheck) {
	if err := copier.CopyWithOption(out, in, copier.Option{IgnoreEmpty: true}); err != nil {
		panic(err)
	}
}

// nolint var-naming
func Convert_TKEService_L4HeathCheck_To_Tencent_CLB_HealthCheck(in tscapi.L4HealthCheck, out *clb.HealthCheck) {
	if err := copier.CopyWithOption(out, in, copier.Option{IgnoreEmpty: true}); err != nil {
		panic(err)
	}
	out.HealthSwitch = lo.Ternary(in.Enable, lo.ToPtr(int64(1)), lo.ToPtr(int64(0)))
	if out.HttpCheckMethod != nil {
		out.HttpCheckMethod = lo.ToPtr(strings.ToLower(*out.HttpCheckMethod))
	}
}

// nolint var-naming
func Convert_TKEService_L7HealthCheck_To_Tencent_CLB_HealthCheck(in tscapi.L7HealthCheck, out *clb.HealthCheck) {
	if err := copier.CopyWithOption(out, in, copier.Option{IgnoreEmpty: true}); err != nil {
		panic(err)
	}
	out.HealthSwitch = lo.Ternary(in.Enable, lo.ToPtr(int64(1)), lo.ToPtr(int64(0)))
	if out.HttpCheckMethod != nil {
		out.HttpCheckMethod = lo.ToPtr(strings.ToLower(*out.HttpCheckMethod))
	}
}

// nolint var-naming
func Convert_Tencent_CLB_HealthCheck_To_TKEService_L7HealthCheck(in clb.HealthCheck, out *tscapi.L7HealthCheck) {
	if err := copier.CopyWithOption(out, in, copier.Option{IgnoreEmpty: true}); err != nil {
		panic(err)
	}
	out.Enable = lo.Ternary(in.HealthSwitch == nil || *in.HealthSwitch == 0, false, true)
	if out.HttpCheckMethod != nil {
		out.HttpCheckMethod = lo.ToPtr(strings.ToUpper(*out.HttpCheckMethod))
	}
}

func (c L7RuleConfig) buildAndCompareHealthCheck(current *clb.RuleOutput, defaultDomain *string) (*clb.HealthCheck, bool) {
	removeHTTPFieldsForTCPCheck := func(hc clb.HealthCheck) clb.HealthCheck {
		if lo.FromPtr(hc.CheckType) == net.ProtocolTCP {
			hc.HttpCheckDomain = nil
			hc.HttpCheckPath = nil
			hc.HttpCheckMethod = nil
			hc.HttpCode = nil
		}
		return hc
	}

	semanticEqual := func(a, b clb.HealthCheck) bool {
		if a.HealthSwitch == nil || b.HealthSwitch == nil {
			return false
		}
		if *a.HealthSwitch == 0 && *b.HealthSwitch == 0 {
			return true
		}
		if a.CheckType == nil || b.CheckType == nil {
			return false
		}
		return reflect.DeepEqual(a, b)
	}

	hc := gclone.Clone(current.HealthCheck)
	if c.HealthCheck != nil {
		Convert_TKEService_L7HealthCheck_To_Tencent_CLB_HealthCheck(*c.HealthCheck, hc)
		if c.HealthCheck.HttpCheckDomain != nil && *c.HealthCheck.HttpCheckDomain == "" && defaultDomain != nil {
			hc.HttpCheckDomain = lo.ToPtr(*defaultDomain)
		}
	}

	expectHC := removeHTTPFieldsForTCPCheck(*hc)
	currentHC := removeHTTPFieldsForTCPCheck(*current.HealthCheck)

	return &expectHC, !semanticEqual(expectHC, currentHC)
}

func (c L7RuleConfig) Apply(req *clb.ModifyRuleRequest, current *clb.RuleOutput,
	defaultDomain *string) (*clb.ModifyRuleRequest, bool) {
	var needUpdate bool
	req.ForwardType = current.ForwardType
	req.HealthCheck, needUpdate = c.buildAndCompareHealthCheck(current, defaultDomain)

	if c.Session != nil {
		var expectExpire int64
		if c.Session.Enable {
			expectExpire = lo.TernaryF(c.Session.SessionExpireTime == nil,
				func() int64 { return 30 },
				func() int64 { return int64(*c.Session.SessionExpireTime) })
		}
		req.SessionExpireTime = &expectExpire
		needUpdate = needUpdate || current.SessionExpireTime == nil || expectExpire != *current.SessionExpireTime
	}
	if c.Scheduler != nil {
		req.Scheduler = lo.ToPtr(*c.Scheduler)
		needUpdate = needUpdate || current.Scheduler == nil || *c.Scheduler != *current.Scheduler
	}
	if c.ForwardType != nil {
		req.ForwardType = lo.ToPtr(*c.ForwardType)
		needUpdate = needUpdate || current.ForwardType == nil || *c.ForwardType != *current.ForwardType
	}

	return req, needUpdate
}
