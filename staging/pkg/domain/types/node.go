package types

import (
	"fmt"
	"strings"

	v1 "k8s.io/api/core/v1"
)

const (
	InstanceTypeEKS = "EKLET"
	InstanceTypeIDC = "EXTERNAL"
)

type Node struct {
	*v1.Node
}

func NewNode(node *v1.Node) *Node {
	return &Node{node}
}

// CanBind 能否作为后端绑定到LB上
func (n *Node) CanBind() bool {
	if n.IsEKS() {
		return false
	}

	if n.IsIDC() {
		return false
	}

	if n.HasInternalIP() {
		return true
	}

	// kateway(wallaceqian) 什么情况下会到达这个条件？如果没有内部IP，后端节点是什么？

	return true
}

func (n *Node) InstanceID() string {
	if !n.IsCVM() {
		panic(fmt.Errorf("Node(%s %s) is not CVM", n.Name, n.Spec.ProviderID))
	}

	_, id := n.providerInfo()

	return id
}

// IsIDC tdcc混合云场景，集群中可能存在idc节点
func (n *Node) IsIDC() bool {
	t, ok := n.instanceType()
	if ok {
		return t == InstanceTypeIDC
	}

	return false
}

// IsEKS EKS集群或TKE弹EKS节点
func (n *Node) IsEKS() bool {
	t, ok := n.instanceType()
	if ok {
		return t == InstanceTypeEKS
	}

	return false
}

// IsCXM TKE原生节点
func (n *Node) IsCXM() bool {
	name, id := n.providerInfo()
	return name == "tencentcloud" && strings.HasPrefix(id, "kn-")
}

func (n *Node) IsCVM() bool {
	name, id := n.providerInfo()
	// qcloud => 腾讯云普通CVM节点
	// tecentcloud => ?
	return (name == "qcloud" || name == "tencentcloud") && strings.HasPrefix(id, "ins-")
}

func (n *Node) instanceType() (string, bool) {
	value, ok := n.Labels[v1.LabelInstanceTypeStable]
	return strings.ToUpper(value), ok
}

func (n *Node) providerInfo() (name string, id string) {
	i := strings.Index(n.Spec.ProviderID, "://")
	if i == -1 {
		return "", ""
	}
	j := strings.LastIndex(n.Spec.ProviderID, "/")
	if j == -1 {
		return "", ""
	}
	return n.Spec.ProviderID[:i], n.Spec.ProviderID[j+1:]
}

func (n *Node) HasInternalIP() bool {
	_, ok := n.InternalIP()
	return ok
}

func (n *Node) InternalIP() (string, bool) {
	return n.Address(v1.NodeInternalIP)
}

func (n *Node) Address(t v1.NodeAddressType) (string, bool) {
	for _, address := range n.Status.Addresses {
		if address.Type == t {
			return address.Address, true
		}
	}

	return "", false
}

func (n *Node) Zone() (string, bool) {
	// 优先使用camp标签
	for key, value := range n.Labels {
		if strings.HasPrefix(key, "zone.topology.camp.io/") && value == "true" {
			return strings.TrimPrefix(key, "zone.topology.camp.io/"), true
		}
	}

	// 其次使用cbs标签
	zone, ok := n.Labels["topology.com.tencent.cloud.csi.cbs/zone"]

	return zone, ok
}

func (n *Node) HasWaitFinalizer() bool {
	nodeFinalizers := n.GetFinalizers()
	for _, finalizer := range nodeFinalizers {
		if finalizer == NodeWaitFinalizer {
			return true
		}
	}
	return false
}

func (n *Node) HasTargetFinalizer(target string) bool {
	nodeFinalizers := n.GetFinalizers()
	for _, finalizer := range nodeFinalizers {
		if finalizer == target {
			return true
		}
	}
	return false
}

func (n *Node) HasTargetLabel(target string) bool {
	nodeLabels := n.GetLabels()
	if value, exist := nodeLabels[target]; exist && value == "true" {
		return true
	}
	return false
}
