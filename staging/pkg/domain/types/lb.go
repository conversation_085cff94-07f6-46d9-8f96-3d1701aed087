package types

import (
	"strings"

	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
)

type LB struct {
	*clb.LoadBalancer
	autoCreated bool
}

func NewLB(lb *clb.LoadBalancer, autoCreatedByTKE bool) *LB {
	return &LB{
		LoadBalancer: lb,
		autoCreated:  autoCreatedByTKE,
	}
}

// IsClassicType 传统型负载均衡
func (lb *LB) IsClassicType() bool {
	return *lb.Forward == 0
}

func (lb *LB) BackendType() string {
	addressIPVersion := strings.ToLower(*lb.AddressIPVersion)
	if addressIPVersion == "ipv6" { // IPv6
		if *lb.IPv6Mode == "IPv6FullChain" { // IPv6FullChain
			if *lb.MixIpTarget {
				return "mixed"
			}

			return "ipv6"
		}

		return "ipv4" // IPv6Nat64
	}

	return "ipv4"
}

func (lb *LB) IsAutoCreatedByTKE() bool {
	return lb.autoCreated
}

func (lb *LB) IsDeletable() bool {
	// 自动创建的CLB可以删除
	deletable := lb.IsAutoCreatedByTKE()
	v := lb.GetLifecycleOwnerValue()
	if v != nil {
		// 如果云标签存在，且云标签值不是tke，则不能删除
		if *v != TagValueLifecycleOwnedByTKE {
			deletable = false
		}
	} else if lb.IsDeletionProtectionEnabled() {
		// 如果云标签不存在，那说明该CLB实例还没同步，如果删除保护开启了，说明是用户自己开启的，不能删除
		deletable = false
	}
	return deletable
}

func (lb LB) IsDeletionProtectionEnabled() bool {
	_, exists := lo.Find(lb.AttributeFlags, func(f *string) bool {
		return *f == "DeleteProtect"
	})
	return exists
}

func (lb LB) GetLifecycleOwnerValue() *string {
	t, exists := lo.Find(lb.Tags, func(t *clb.TagInfo) bool { return *t.TagKey == TagKeyLifecycleOwner.String() })
	if !exists {
		return nil
	}
	return t.TagValue
}
