package types

import (
	"encoding/json"
	"errors"
	"fmt"

	"git.woa.com/kateway/pkg/net"
	"git.woa.com/kateway/pkg/sets"
)

const (
	CloudGroup   = "cloud.tencent.com/"
	ServiceGroup = "service." + CloudGroup
	IngressGroup = "ingress." + CloudGroup
	SecretGroup  = "secret." + CloudGroup
)

const (
	ServiceAnnotationMode = ServiceGroup + "mode" // service-controller 运行模式
	ModeSkip              = "skip"
	ModeDryRun            = "dry-run"

	ServiceAnnotationSessionAffinityFallback = ServiceGroup + "session-affinity-fallback"
	ServiceAnnotationCrossRegionID           = ServiceGroup + "cross-region-id" // kateway 本地 controller 管理另一地域的 clb, todo 待确认
	ServiceAnnotationCrossVPCID              = ServiceGroup + "cross-vpc-id"    // kateway
	ServiceAnnotationClientToken             = ServiceGroup + "client-token"
	ServiceAnnotationManagedByCLBMigration   = ServiceGroup + "managed-by-clbmigration"
	ServiceAnnotationBackendManageOnly       = ServiceGroup + "backend-manage-only" // kateway 开启该注解之后，负载均衡的转发规则将不会由子集群处理
	ServiceAnnotationBackendManagementMode   = ServiceGroup + "backend-management-mode"
	ServiceAnnotationFromOtherCluster        = ServiceGroup + "from-other-cluster" // kateway 子集群允许通过使用已有的方式，使用TDCC集群的负载均衡
	ServiceAnnotationExtensiveParams         = "service.kubernetes.io/service.extensiveParameters"
	ServiceAnnotationInternalSubnetID        = "service.kubernetes.io/qcloud-loadbalancer-internal-subnetid"
	ServiceAnnotationExistingLoadBalancerID  = "service.kubernetes.io/tke-existed-lbid"
	ServiceAnnotationLoadBalancerType        = "service.kubernetes.io/loadbalance-type"
	ServiceAnnotationLoadBalancerID          = "service.kubernetes.io/loadbalance-id"

	AnnoBackendsListLabel  = "service.kubernetes.io/qcloud-loadbalancer-backends-label"
	AnnoListenerParameters = "service.kubernetes.io/service.listenerParameters"

	// kateway(wallaceqian) 待讨论使用哪种方式
	ServiceDirectAccessAnnotation      = ServiceGroup + "direct-access"
	ServiceEnableCustomizedWeight      = "service.cloud.tencent.com/lb-rs-weight"
	ServicePodCustomWeight             = "service.cloud.tencent.com/custom-weight"
	PassToTargetAnnotation             = "service.cloud.tencent.com/pass-to-target"
	SecurityGroupsAnnotation           = "service.cloud.tencent.com/security-groups"
	TkeServiceConfigAnnotation         = "service.cloud.tencent.com/tke-service-config"
	TkeServiceConfigAutoAnnotation     = "service.cloud.tencent.com/tke-service-config-auto"
	SpecifyProtocolAnnotation          = "service.cloud.tencent.com/specify-protocol"
	TargetCrossRegionIDAnnotation      = "service.cloud.tencent.com/target-cross-region-id" // kateway todo 这是给 master 用的？用于告诉 clb target 在哪个地域（和master 不在一个地域）
	TargetCrossVPCIdAnnotation         = "service.cloud.tencent.com/target-cross-vpc-id"
	HybridTypeAnnotation               = "service.cloud.tencent.com/hybrid-type"
	SnatProInfoAnnotation              = "service.cloud.tencent.com/snat-pro-info"
	ModificationProtectionAnnotation   = "service.cloud.tencent.com/modification-protection" // kateway todo 没有产品文档，不确定是不是用的不多 https://iwiki.woa.com/p/4007639346#%E9%85%8D%E7%BD%AE%E4%BF%AE%E6%94%B9%E4%BF%9D%E6%8A%A4
	PreventLoopbackAnnotation          = "service.cloud.tencent.com/prevent-loopback"
	TkeManagementAnnotation            = "service.cloud.tencent.com/tke-management"
	ServiceReadinessGateSkipAnnotation = "service.cloud.tencent.com/readiness-gate-skip"
	CrossTypeAnnotation                = "service.cloud.tencent.com/cross-type" // kateway 跨域类型

	ServiceAnnotationSourceEndpoints = ServiceGroup + "loadbalancer-source-endpoints"

	FORWARDTYPE             = 1
	CLASSICTYPE             = 0
	ForwardLoadbalanceType  = "yunapiv3_forward_clb"
	ClassicLoadbalanceType  = "yunapi_clb"
	ClassicLoadbalanceType2 = "classic"

	TypeClusterManaged            = "MANAGED_CLUSTER"
	ClusterTypeClusterIndependent = "INDEPENDENT_CLUSTER"

	CrossType0_0 = "ManagerOnly"
	CrossType1_0 = "CrossTarget"
	CrossType2_0 = "CCN"
	CrossType1_1 = "PVGW"
	CrossType1_2 = "PVGW-PRO"

	HybridTypeNone = "NONE" // 未使用混合云
	HybridTypePvgw = "PVGW"
	HybridTypeCcn  = "CCN"
)

const (
	IngressAnnotationExistingLoadBalancerID = "kubernetes.io/ingress.existLbId"
	IngressAnnotationLoadBalancerID         = "kubernetes.io/ingress.qcloud-loadbalance-id"
	IngressAnnotationLoadBalancerNetType    = "kubernetes.io/ingress.loadbalancerType"
	IngressAnnotationInternalSubnetID       = "kubernetes.io/ingress.subnetId"
	IngressAnnotationExtensiveParams        = "kubernetes.io/ingress.extensiveParameters"
	IngressAnnotationHTTPRules              = "kubernetes.io/ingress.http-rules"
	IngressAnnotationHTTPSRules             = "kubernetes.io/ingress.https-rules"
	IngressAnnotationRuleMix                = "kubernetes.io/ingress.rule-mix"
	IngressAnnotationCrossVPCID             = IngressGroup + "cross-vpc-id"
	IngressAnnotationCrossRegionID          = IngressGroup + "cross-region-id"
	IngressAnnotationDirectAccess           = IngressGroup + "direct-access"
	IngressAnnotationClientToken            = IngressGroup + "client-token"
	IngressAnnotationManagedByCLBMigration  = IngressGroup + "managed-by-clbmigration"
	IngressAnnotationBackendManageOnly      = IngressGroup + "backend-manage-only"
	IngressAnnotationBackendManagementMode  = IngressGroup + "backend-management-mode"
	IngressAnnotationFromOtherCluster       = IngressGroup + "from-other-cluster"
	IngressAnnotationListenerPorts          = IngressGroup + "listen-ports"
	IngressAnnotationRewriteSupport         = IngressGroup + "rewrite-support"
	IngressAnnotationAutoRewrite            = IngressGroup + "auto-rewrite"
	IngressAnnotationTKEServiceConfigAuto   = IngressGroup + "tke-service-config-auto"
	IngressAnnotationTKEServiceConfig       = IngressGroup + "tke-service-config"
)

// add for tls-secret-service
const (
	AnnotationEnableTLSSecret        = CloudGroup + "enable-tls-secret" // [开关注解]ingress支持tls类型secret
	AnnotationCertID                 = CloudGroup + "qcloud-cert-id"    // 服务端证书ID
	AnnotationCaCertID               = CloudGroup + "qcloud-ca-cert-id" // 客户端双向验证的证书ID
	SecretAnnotationStatusConditions = CloudGroup + "status.conditions"
)

type BackendManagementModeType string

const (
	BackendManagementModeAll         BackendManagementModeType = "all"
	BackendManagementModeTag         BackendManagementModeType = "tag" // kateway 多集群模式下， 子集群 svc controller 将rs添加集群tag,，各自管理本集群后端
	BackendManagementModeTargetGroup BackendManagementModeType = "target-group"
)

const (
	DirectAccessConditionType     = "cloud.tencent.com/load-balancer-backendgroup-ready"
	DirectAccessConditionReady    = "LoadBalancerNetworkGroupReady"
	DirectAccessConditionNotReady = "LoadBalancerNetworkGroupNotReady"
)

const (
	IngressClassKey    = "kubernetes.io/ingress.class"
	QcloudIngressClass = "qcloud"
)

const (
	PodNetworkAnnotation = "tke.cloud.tencent.com/networks"

	DelegatesKubenet      = "kubenet"
	DelegatesTKEBridge    = "tke-bridge"     // GR 默认方式
	DelegatesTKERouteENI  = "tke-route-eni"  // 共享网卡
	DelegatesTKEDirectENI = "tke-direct-eni" // 独占网卡
	DelegatesTKESubENI    = "tke-sub-eni"    // eni trunking https://iwiki.woa.com/p/4008949351
)

const (
	ServiceFinalizer        = "service.k8s.tencent/resources"
	ServiceTypeCore         = "Service"
	ServiceTypeMultiCluster = "MultiClusterService"
)

const (
	AnnotationLBOriginallyCreatedByTKE = CloudGroup + "lb-originally-created-by-tke"
	AnnotationSkipValidating           = CloudGroup + "skip-validating"
	AnnotationPatchRSTags              = CloudGroup + "patch-rs-tags"
)

const (
	AnnotationEnableIngressControllerAwareness = CloudGroup + "enable-ingress-controller-awareness"
)

type SpecifyProtocolAnnotationMap map[int32]SpecifyProtocol

type SpecifyProtocol struct {
	Protocol []string                        `json:"protocol,omitempty"`
	TLS      *string                         `json:"tls,omitempty"`
	Hosts    map[string]*SpecifyProtocolHost `json:"hosts,omitempty"`
}

type SpecifyProtocolHost struct {
	TLS *string `json:"tls,omitempty"`
}

var (
	ErrIncompatibleProtocols = errors.New("incompatible protocols for the same port, TCP,TCP_SSL,HTTP,HTTPS can not create listener in the same port. UDP,QUIC can not create listener in the same port")
	ErrRepeatProtocol        = errors.New("repeat protocol")
	ErrInvalidFormat         = errors.New("invalid format")
)

// validateProtocols 检查特定端口的协议切片是否合法
func ValidateProtocols(protocols []string) error {
	tcpFamilyProtocols := sets.New[string](net.TCPFamilyProtocols()...)
	udpFamilyProtocols := sets.New[string](net.UDPFamilyProtocols()...)
	usedTCPProtocols := sets.New[string]()
	usedUDPProtocols := sets.New[string]()
	for _, protocol := range protocols {
		if tcpFamilyProtocols.Has(protocol) {
			if usedTCPProtocols.Has(protocol) {
				return fmt.Errorf("%w: %s", ErrRepeatProtocol, protocol)
			}
			usedTCPProtocols.Insert(protocol)
		} else if udpFamilyProtocols.Has(protocol) {
			if usedUDPProtocols.Has(protocol) {
				return fmt.Errorf("%w: %s", ErrRepeatProtocol, protocol)
			}
			usedUDPProtocols.Insert(protocol)
		} else {
			// 拒绝不支持的协议类型（区分大小写）
			return fmt.Errorf("do not support the protocol(%s). ValidProtocol:TCP,UDP,TCP_SSL,QUIC,HTTP,HTTPS", protocol)
		}
	}
	if usedTCPProtocols.Len() > 1 || usedUDPProtocols.Len() > 1 {
		return ErrIncompatibleProtocols
	}
	return nil
}

// validateJSON 校验 JSON 字符串
func ValidateSpecifyProtoJSON(jsonStr string) error {
	specifyProto := SpecifyProtocolAnnotationMap{} // 可以根据需要定义更具体的结构
	// 格式校验
	if err := json.Unmarshal([]byte(jsonStr), &specifyProto); err != nil {
		return ErrInvalidFormat
	}

	// 1.协议校验
	for port, protocols := range specifyProto {
		if err := ValidateProtocols(protocols.Protocol); err != nil {
			return fmt.Errorf("%w on port %d", err, port)
		}
	}
	// TODO: 增加更多的校验逻辑
	return nil
}
