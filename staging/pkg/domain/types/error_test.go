package types

import (
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestReadyConditionNeedUpdate(t *testing.T) {
	type args struct {
		old v1.Condition
		new v1.Condition
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Reason不同",
			args: args{
				old: v1.Condition{
					Reason: "Synchronizing",
				},
				new: v1.Condition{
					Status: "Success",
				},
			},
			want: true,
		},
		{
			name: "Status不同",
			args: args{
				old: v1.Condition{
					Status: v1.ConditionFalse,
				},
				new: v1.Condition{
					Status: v1.ConditionTrue,
				},
			},
			want: true,
		},
		{
			name: "Message不同，但错误码相同",
			args: args{
				old: v1.Condition{
					Message: `a W1000`,
				},
				new: v1.Condition{
					Message: `b W1000`,
				},
			},
			want: false,
		},
		{
			name: "Message不同，错误码不相同",
			args: args{
				old: v1.Condition{
					Message: `a W1000`,
				},
				new: v1.Condition{
					Message: `b E1000`,
				},
			},
			want: true,
		},
		{
			name: "错误码顺序不同",
			args: args{
				old: v1.Condition{
					Message: `W1000 E1000`,
				},
				new: v1.Condition{
					Message: `E1000 W1000`,
				},
			},
			want: true,
		},
		{
			name: "重复错误码去重",
			args: args{
				old: v1.Condition{
					Message: `W1000 a W1000`,
				},
				new: v1.Condition{
					Message: `b W1000`,
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, ReadyConditionNeedUpdate(tt.args.old, tt.args.new), "ReadyConditionNeedUpdate(%v, %v)", tt.args.old, tt.args.new)
		})
	}
}
