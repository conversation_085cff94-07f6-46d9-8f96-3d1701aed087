package types

import "git.woa.com/kateway/pkg/runtime/conv"

func init() {
	conv.Register(Convert_CLBInternal_HealthCheck_To_Tencent_CLB_HealthCheck)
	conv.Register(Convert_Tencent_CLB_HealthCheck_To_CLBInternal_HealthCheck)

	conv.Register(Convert_TKEService_L4HeathCheck_To_Tencent_CLB_HealthCheck)

	conv.Register(Convert_TKEService_L7HealthCheck_To_Tencent_CLB_HealthCheck)
	conv.Register(Convert_Tencent_CLB_HealthCheck_To_TKEService_L7HealthCheck)

	conv.Register(Convert_TLSConfig_To_CLB_CertInput)
	conv.Register(Convert_TLSConfig_To_CLB_MultiCertInfo) // 多证书
}
