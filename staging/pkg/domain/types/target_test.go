package types

import (
	"testing"

	"github.com/samber/lo"
)

func TestBuildTargetTag(t *testing.T) {
	tests := []struct {
		name      string
		clusterId string
		expected  string
	}{
		{
			name:      "正常集群ID",
			clusterId: "test-cluster",
			expected:  "clusterId:test-cluster",
		},
		{
			name:      "空集群ID",
			clusterId: "",
			expected:  "clusterId:",
		},
		{
			name:      "带特殊字符的集群ID",
			clusterId: "test-cluster-123!@#",
			expected:  "clusterId:test-cluster-123!@#",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := BuildTargetTag(tt.clusterId); got != tt.expected {
				t.<PERSON>("BuildTargetTag() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestManagedTarget(t *testing.T) {
	testClusterId := "test-cluster"

	tests := []struct {
		name     string
		tag      *string
		expected bool
	}{
		{
			name:     "nil标签",
			tag:      nil,
			expected: false,
		},
		{
			name:     "空标签",
			tag:      lo.ToPtr(""),
			expected: false,
		},
		{
			name:     "错误集群ID值",
			tag:      lo.ToPtr("clusterId:wrong-cluster"),
			expected: false,
		},
		{
			name:     "错误键名",
			tag:      lo.ToPtr("wrongKey:test-cluster"),
			expected: false,
		},
		{
			name:     "无效标签格式",
			tag:      lo.ToPtr("clusterId=test-cluster"),
			expected: false,
		},
		{
			name:     "部分匹配但不正确",
			tag:      lo.ToPtr("clusterId:test-cluster-wrong"),
			expected: false,
		},
		{
			name:     "正确集群ID标签",
			tag:      lo.ToPtr("clusterId:test-cluster"),
			expected: true,
		},
		{
			name:     "多标签组合-正确集群ID",
			tag:      lo.ToPtr("key1:value1;clusterId:test-cluster;key2:value2"),
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ManagedTarget(tt.tag, testClusterId); got != tt.expected {
				t.Errorf("ManagedTarget() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestNeedSKipTarget(t *testing.T) {
	tests := []struct {
		name     string
		tag      *string
		expected bool
	}{
		{
			name:     "nil标签",
			tag:      nil,
			expected: false,
		},
		{
			name:     "空标签",
			tag:      lo.ToPtr(""),
			expected: false,
		},
		{
			name:     "错误skip值",
			tag:      lo.ToPtr("sync-rs-weight:no-skip"),
			expected: false,
		},
		{
			name:     "错误键名",
			tag:      lo.ToPtr("wrongKey:skip"),
			expected: false,
		},
		{
			name:     "无效标签格式",
			tag:      lo.ToPtr("sync-rs-weight=skip"),
			expected: false,
		},
		{
			name:     "部分匹配但不正确",
			tag:      lo.ToPtr("sync-rs-weight:skip-this"),
			expected: false,
		},
		{
			name:     "正确skip标签",
			tag:      lo.ToPtr("sync-rs-weight:skip"),
			expected: true,
		},
		{
			name:     "多标签组合-正确skip标签",
			tag:      lo.ToPtr("key1:value1;sync-rs-weight:skip;key2:value2"),
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NeedSKipTarget(tt.tag); got != tt.expected {
				t.Errorf("NeedSKipTarget() = %v, want %v", got, tt.expected)
			}
		})
	}
}
