package types

import (
	"encoding/json"

	corev1 "k8s.io/api/core/v1"
)

func FromLoadBalancerStatus[T any](in corev1.LoadBalancerStatus) T {
	out := new(T)
	data, _ := json.Marshal(in)
	_ = json.Unmarshal(data, &out)

	return *out
}

func ToLoadBalancerStatus[T any](in T) corev1.LoadBalancerStatus {
	out := new(corev1.LoadBalancerStatus)
	data, _ := json.Marshal(in)
	_ = json.Unmarshal(data, &out)

	return *out
}
