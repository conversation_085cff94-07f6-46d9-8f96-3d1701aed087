package types

import (
	"reflect"

	gclone "github.com/huandu/go-clone/generic"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"

	tscv1alpha1 "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/sets"
)

type CLBListener struct {
	*clb.Listener
}

func NewCLBListener(listener *clb.Listener) *CLBListener {
	return &CLBListener{
		Listener: listener,
	}
}

func (l CLBListener) SNIEnabled() bool {
	return lo.FromPtr(l.SniSwitch) == 1
}

const (
	// 双向认证
	SSLModeMutual string = "MUTUAL"
	// 单向认证
	SSLModeUnidirectional string = "UNIDIRECTIONAL"
)

type TLSConfig struct {
	SSLMode         string
	CACertificateID *string
	CertificateIDs  sets.Set[string] // 多证书变量，存放额外的服务端证书
}

// nolint var-naming
func Convert_TLSConfig_To_CLB_CertInput(in TLSConfig, out *clb.CertificateInput) {
	out.SSLMode = &in.SSLMode
	if in.SSLMode == SSLModeMutual {
		out.CertCaId = in.CACertificateID
	}
	// 兼容旧的单证书逻辑：没有证书id的情况下，给out.CertId赋值一个空字符串指针
	if len(in.CertificateIDs) == 0 {
		emptyID := ""
		out.CertId = &emptyID
		return
	}
	out.CertId = &in.CertificateIDs.UnsortedList()[0]
}

// nolint var-naming
func Convert_TLSConfig_To_CLB_MultiCertInfo(in TLSConfig, out *clb.MultiCertInfo) {
	// 设置 SSLMode
	out.SSLMode = &in.SSLMode

	// 初始化 CertList 列表
	out.CertList = []*clb.CertInfo{}

	// 将多证书 CertificateIDs 添加到 CertList 中
	for certID := range in.CertificateIDs {
		certIDCopy := certID // 创建一个副本以避免指针引用问题
		out.CertList = append(out.CertList, &clb.CertInfo{
			CertId: &certIDCopy,
		})
	}

	// 如果是双向认证，添加 CA 证书
	if in.SSLMode == SSLModeMutual && in.CACertificateID != nil {
		out.CertList = append(out.CertList, &clb.CertInfo{
			CertId: in.CACertificateID,
		})
	}
}

type Domain struct {
	Host         string
	Default      bool
	HTTP2Enabled *bool
	TLSConfig    *TLSConfig
}

func (expect Domain) Equals(current Domain) bool {
	// 比较默认域名以及HTTP2开关状态是否一致，这里有个特殊情况，即预期不是默认域名，而当前clb侧配置为默认域名，这种情况认为是相等，这里和老代码保持兼容
	e := (expect.Default == current.Default || !expect.Default && current.Default) && expect.Host == current.Host
	// 如果期望的HTTP2字段不为空，则进行比较，否则忽略（防止覆盖用户手动在clb侧的配置）
	if expect.HTTP2Enabled != nil {
		e = e && *expect.HTTP2Enabled == lo.FromPtr(current.HTTP2Enabled)
	}
	// 如果预期没有配置证书，则不去进行证书信息的比较，这里clb侧用户有可能手动配置了证书，不去进行覆盖
	if expect.TLSConfig != nil {
		expect.TLSConfig = gclone.Clone(expect.TLSConfig)
		if current.TLSConfig != nil {
			current.TLSConfig = gclone.Clone(current.TLSConfig)
		}
		// 当预期的认证是单向认证时，不考虑配置的ca证书，同时也忽略clb侧的ca证书
		if expect.TLSConfig.SSLMode == SSLModeUnidirectional {
			expect.TLSConfig.CACertificateID = nil
			if current.TLSConfig != nil {
				current.TLSConfig.CACertificateID = nil
			}
		}
		e = e && reflect.DeepEqual(expect.TLSConfig, current.TLSConfig)
	}
	return e
}

func (l CLBListener) GetDomain(host string) *Domain {
	rule, exists := lo.Find(l.Rules, func(item *clb.RuleOutput) bool {
		return *item.Domain == host
	})
	if !exists {
		return nil
	}
	d := &Domain{
		Host:         host,
		Default:      lo.FromPtr(rule.DefaultServer),
		HTTP2Enabled: rule.Http2,
	}
	if certs := rule.Certificate; *l.Protocol == "HTTPS" && certs != nil {
		slices := []string{*certs.CertId}
		if certs.ExtCertIds != nil {
			for _, id := range certs.ExtCertIds {
				slices = append(slices, *id)
			}
		}
		d.TLSConfig = &TLSConfig{
			CertificateIDs:  sets.New(slices...),
			SSLMode:         *certs.SSLMode,
			CACertificateID: certs.CertCaId,
		}
	}
	return d
}

type ListenerMeta struct {
	Port     int
	Protocol string
}

type Listener struct {
	ListenerMeta
	// 根据Host进行分组
	Rules          map[string][]ListenerRule
	TLSConfig      *TLSConfig
	Forward        *ForwardActionConfig
	ListenerConfig *tscv1alpha1.L7ListenerConfig
}

func (lis *Listener) AddRules(rules ...ListenerRule) {
	for _, r := range rules {
		lis.Rules[r.Host] = append(lis.Rules[r.Host], r)
	}
}

func (lis *Listener) SetRules(rules []ListenerRule) {
	lis.Rules = lo.GroupBy(rules, func(r ListenerRule) string { return r.Host })
}

func (lis Listener) GetRewriteRules() []ListenerRule {
	return lo.Filter(lis.FlattenRules(), func(r ListenerRule, _ int) bool {
		return r.Action.Rewrite != nil
	})
}

func (lis Listener) GetForwardRules() []ListenerRule {
	return lo.Filter(lis.FlattenRules(), func(r ListenerRule, _ int) bool {
		return r.Action.Forward != nil
	})
}

func (lis Listener) FlattenRules() []ListenerRule {
	return lo.Flatten(lo.Values(lis.Rules))
}

func (lis Listener) FindRules(fn func(r ListenerRule) bool) []*ListenerRule {
	rs := []*ListenerRule{}
	for _, rules := range lis.Rules {
		for i, r := range rules {
			if fn(r) {
				rs = append(rs, &rules[i])
			}
		}
	}
	return rs
}

func (lis Listener) FindRulesByMeta(meta ListenerRuleMeta) []*ListenerRule {
	return lis.FindRules(func(r ListenerRule) bool {
		return r.GetMeta() == meta
	})
}

func NewListener(port int, protocol string, rules []ListenerRule) Listener {
	return Listener{
		ListenerMeta: ListenerMeta{
			Port:     port,
			Protocol: protocol,
		},
		Rules: lo.GroupBy(rules, func(r ListenerRule) string { return r.Host }),
	}
}
