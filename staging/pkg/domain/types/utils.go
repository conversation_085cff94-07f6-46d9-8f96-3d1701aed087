package types

import (
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"

	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/k8s/k8sutil/annotation"
)

func IsIngressCascadedDirectAccess(ing Ingress, svc *Service) bool {
	if env.IsInEKSCluster() {
		return true
	}
	if v, exists := ing.Annotations()[IngressAnnotationDirectAccess]; exists {
		enabled, _ := ParseBool(v)
		return enabled
	}
	return svc.IsDirectAccess()
}

func IsIngressBackendOnly(ing Ingress) bool {
	backendOnlyPtr, err := annotation.Parse[bool](ing.Annotations(), IngressAnnotationBackendManageOnly, annotation.WithCustomParseFunc(ParseBool))
	if err != nil {
		return false
	}
	return lo.FromPtr(backendOnlyPtr)
}

func ParseBool(s string) (bool, error) {
	switch s {
	case "true", "TRUE", "True":
		return true, nil
	case "false", "FALSE", "False":
		return false, nil
	}
	return false, errors.New("invalid boolean value")
}

func GetListenerKey(port int32, protocol string) string {
	return fmt.Sprintf("%d_%s", port, strings.ToUpper(protocol))
}

// [qingyangwu] 工具函数 SplitStrings 处理以指定分隔符分隔的字符串，且可以指定是否去重
//
// 参数:
//
//	input: 要拆分的输入字符串。
//	delimiter: 用于拆分字符串的分隔符。
//	shouldUniq: 如果为 true，则返回的切片将去重；如果为 false，则返回的切片可能包含重复元素。
//
// 返回值:
//
//	返回一个字符串切片，包含拆分后的有效字符串部分，去除前后空格和空字符串。
func SplitStrings(input string, delimiter string, shouldUniq bool) []string {
	var result []string
	parts := strings.Split(input, delimiter)
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if len(part) == 0 {
			continue
		}
		result = append(result, part)
	}
	if shouldUniq {
		result = lo.Uniq(result)
	}
	return result
}

// [qingyangwu] 切割k8s字符串
func SplitKeyString(key string) (namespace, name string, err error) {
	return cache.SplitMetaNamespaceKey(key)
}

// [qingyangwu] JoinKeyStrings 方法用于拼接不定数量的字符串
func JoinKeyStrings(separator string, parts ...string) string {
	return strings.Join(parts, separator)
}

func ToConditionStatus(b bool) metav1.ConditionStatus {
	if b {
		return metav1.ConditionTrue
	}

	return metav1.ConditionFalse
}
