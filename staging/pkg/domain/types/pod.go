package types

import (
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	podutil "git.woa.com/kateway/pkg/k8s/k8sutil/pod"
)

type Pod struct {
	*v1.Pod
}

func NewPod(pod *v1.Pod) *Pod {
	return &Pod{pod}
}

func (p *Pod) Port(port intstr.IntOrString) (int32, error) {
	if port.Type == intstr.Int {
		return port.IntVal, nil
	}

	for _, container := range p.Spec.Containers {
		for _, podPort := range container.Ports {
			if podPort.Name == port.StrVal {
				return podPort.ContainerPort, nil
			}
		}
	}

	return 0, errors.Errorf("unable to find port %s on pod %s", port.String(), p.Name)
}

func (p *Pod) IPs() []string {
	result := make([]string, 0)
	if len(p.Status.PodIPs) != 0 {
		for _, podIP := range p.Status.PodIPs {
			result = append(result, podIP.IP)
		}
		return result
	}
	if p.Status.PodIP != "" {
		result = append(result, p.Status.PodIP)
	}
	return result
}

func (p *Pod) IsGalaxyHostPortMapping() bool {
	_, ok := p.galaxyHostPortMapping()
	return ok
}

func (p *Pod) galaxyHostPortMapping() (string, bool) {
	s, ok := p.Annotations["tkestack.io/portmapping"]
	return s, ok
}

// GetGalaxyHostPort 获取pod galaxy映射的目标主机端口
func (p *Pod) GetGalaxyHostPort(targetPort intstr.IntOrString) (int32, error) {
	port, err := p.Port(targetPort)
	if err != nil {
		return 0, err
	}

	return p.getGalaxyPortMapping().hostPort(port)
}

func (p *Pod) getGalaxyPortMapping() (result galaxyPortMapping) {
	portMapping, _ := p.galaxyHostPortMapping()
	err := json.Unmarshal([]byte(portMapping), &result)
	if err != nil {
		panic(err)
	}
	return
}

type galaxyPortMapping []galaxyPortMappingItem

type galaxyPortMappingItem struct {
	HostPort      int32
	ContainerPort int32
	Protocol      string
	PodName       string
	PodIP         string
}

// hostPort 根据容器端口找到对应的主机端口
func (m galaxyPortMapping) hostPort(port int32) (int32, error) {
	item, ok := lo.Find(m, func(item galaxyPortMappingItem) bool {
		return item.ContainerPort == port
	})
	if !ok {
		return 0, fmt.Errorf("can't find galaxy port mapping item for %v", port)
	}

	return item.HostPort, nil
}

func (p *Pod) customWeight(kind string) *int {
	key := fmt.Sprintf("%s.cloud.tencent.com/custom-weight", kind)
	weight, exist := p.Annotations[key]
	if !exist {
		return nil
	}

	return lo.ToPtr(cast.ToInt(weight))
}

type PodAttributes struct {
	ServiceAttributes
	IngressAttributes
}

type ServiceAttributes struct {
	CustomWeight *int
}

type IngressAttributes struct {
	CustomWeight *int
}

func (p *Pod) Attributes() *PodAttributes {
	return &PodAttributes{
		ServiceAttributes: ServiceAttributes{
			CustomWeight: p.customWeight("service"),
		},
		IngressAttributes: IngressAttributes{
			CustomWeight: p.customWeight("ingress"),
		},
	}
}

func (p *Pod) IsContainersReadyConditionTrue() bool {
	if p == nil {
		return false
	}
	return podutil.IsContainersReadyConditionTrue(p.Status)
}

func (p *Pod) IsDirectAccessReadyConditionTrue() bool {
	if p == nil {
		return false
	}

	_, condition := podutil.GetPodCondition(&p.Status, DirectAccessConditionType)
	return condition != nil && condition.Status == v1.ConditionTrue
}
