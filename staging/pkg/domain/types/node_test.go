package types

import (
	"fmt"
	"testing"

	v1 "k8s.io/api/core/v1"
)

func TestNode_ProviderInfo(t *testing.T) {
	type fields struct {
		Node *v1.Node
	}
	tests := []struct {
		name     string
		fields   fields
		wantName string
		wantId   string
	}{
		{
			"qcloud",
			fields{
				&v1.Node{Spec: v1.NodeSpec{ProviderID: "qcloud:///200007/ins-gxh9v3f3"}},
			},
			"qcloud",
			"ins-gxh9v3f3",
		},
		{
			"unknown",
			fields{
				&v1.Node{},
			},
			"",
			"",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			n := &Node{
				Node: tt.fields.Node,
			}
			gotName, gotId := n.providerInfo()
			fmt.Println(gotName, gotId)
			if gotName != tt.wantName {
				t.<PERSON><PERSON>rf("ProviderInfo() gotName = %v, want %v", gotName, tt.wantName)
			}
			if gotId != tt.wantId {
				t.<PERSON><PERSON>("ProviderInfo() gotId = %v, want %v", gotId, tt.wantId)
			}
		})
	}
}
