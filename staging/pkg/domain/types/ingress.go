package types

import (
	"context"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"

	"git.woa.com/kateway/pkg/domain/ingress/annotation"
)

type IngressType string

const (
	CoreIngress         IngressType = "Ingress"
	MultiClusterIngress IngressType = "MultiClusterIngress"
)

const (
	IngressPathTypeExact                  string = "Exact"
	IngressPathTypePrefix                 string = "Prefix"
	IngressPathTypeImplementationSpecific string = "ImplementationSpecific"
)

type Ingress interface {
	annotation.Interface

	Object
	Type() IngressType
	IngressClassName() *string
	Annotations() map[string]string
	GetObjectMeta() metav1.Object
	RuntimeObject() runtime.Object
	ObjectReference() v1.ObjectReference
	TLS() []IngressTLS
	Rules() []IngressRule
	StatusLoadBalancer() v1.LoadBalancerStatus
	DeletionTimestamp() *metav1.Time

	Update(ctx context.Context) (Ingress, error)
	UpdateAnnotation(ctx context.Context, toUpdate map[string]string, toDelete []string) (Ingress, error)
	UpdateLoadBalancerStatus(ctx context.Context, loadBalancerStatus v1.LoadBalancerStatus) error
	AddFinalizer(ctx context.Context, finalizer string) error
	RemoveFinalizer(ctx context.Context, finalizer string) error

	DeepCopy() Ingress
}

type IngressTLS struct {
	Hosts      []string
	SecretName string
}

type IngressRule struct {
	Host      string
	HTTPPaths []HTTPIngressPath
}

type HTTPIngressPath struct {
	Path     string
	PathType string
	Backend  IngressBackend
}

type IngressBackend struct {
	ServiceName string
	ServicePort intstr.IntOrString
}
