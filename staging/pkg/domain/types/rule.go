package types

import (
	"k8s.io/apimachinery/pkg/util/intstr"

	tscv1alpha1 "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
)

const (
	DefaultRulePath = "/"
)

type ListenerRuleMeta struct {
	Host string
	Path string
}

func NewListenerRuleMeta(host, path string) ListenerRuleMeta {
	return ListenerRuleMeta{
		Host: host,
		Path: path,
	}
}

type ListenerRule struct {
	Domain

	Path         string
	CLBPathTypes []CLBPathType
	// 调用CLB API创建rule时实际传入的Path参数
	CLBPath    string
	Action     ListenerRuleAction
	RuleConfig *tscv1alpha1.L7RuleConfig
}

func NewListenerRule(host, path, clbPath string, clbPathTypes []CLBPathType, action ListenerRuleAction) ListenerRule {
	return ListenerRule{
		Domain:       Domain{Host: host},
		Path:         path,
		CLBPathTypes: clbPathTypes,
		CLBPath:      clbPath,
		Action:       action,
	}
}

func (r ListenerRule) GetMeta() ListenerRuleMeta {
	return ListenerRuleMeta{
		Host: r.Host,
		Path: r.Path,
	}
}

type ListenerRuleAction struct {
	Forward *ForwardActionConfig
	Rewrite *RewriteActionConfig
}

type ForwardBackendMeta struct {
	ServiceName string
	ServicePort intstr.IntOrString
}

type ForwardActionConfig struct {
	ForwardBackendMeta

	TargetPort *intstr.IntOrString
}

type RewriteActionConfig struct {
	Host    string
	Port    int
	Path    string
	CLBPath string
	Auto    bool
}

// 参考：https://cloud.tencent.com/document/product/214/9032#.E8.BD.AC.E5.8F.91.E5.9F.9F.E5.90.8D.E9.85.8D.E7.BD.AE.E8.A7.84.E5.88.99
type CLBPathType string

func (t CLBPathType) GetPrefix() (p string) {
	switch t {
	case CLBPathTypeExact:
		p = "="
	case CLBPathTypeRegex:
		p = "~"
	case CLBPathTypeRegexIgnoreCase:
		p = "~*"
	default:
	}
	return
}

const (
	// Prefix是负载均衡默认路径格式，已经是前缀匹配的含义，无需支持。

	// =  开头表示精确匹配。
	CLBPathTypeExact CLBPathType = "Exact"
	// ~  开头表示区分大小写的正则匹配。
	CLBPathTypeRegex CLBPathType = "Regex"
	// ~* 开头表示不区分大小写的正则匹配。
	CLBPathTypeRegexIgnoreCase CLBPathType = "RegexIgnoreCase"
	// 转发路径不以绝对路径开头
	CLBPathTypeNonAbsolute CLBPathType = "NonAbsolutePath"
)

var (
	MainCLBPathTypes  = []CLBPathType{CLBPathTypeExact, CLBPathTypeRegex, CLBPathTypeRegexIgnoreCase}
	SubCLBPathTypes   = []CLBPathType{CLBPathTypeNonAbsolute}
	ValidCLBPathTypes = append(append([]CLBPathType{}, MainCLBPathTypes...), SubCLBPathTypes...)
)

type AnnotationRuleKey struct {
	ListenerRuleMeta
	BackendServiceName string
	BackendServicePort string
}

type IngressAnnotationRule struct {
	Host         string        `json:"host,omitempty"`
	Path         string        `json:"path,omitempty"`
	CLBPathTypes []CLBPathType `json:"pathType,omitempty"`
	Backend      *struct {
		Name string             `json:"serviceName,omitempty"`
		Port intstr.IntOrString `json:"servicePort,omitempty"`
	} `json:"backend,omitempty"`
	Rewrite *struct {
		Port int32  `json:"port,omitempty"`
		Host string `json:"host,omitempty"`
		Path string `json:"path,omitempty"`
	} `json:"rewrite,omitempty"`
}

func (r IngressAnnotationRule) IsForward() bool {
	return r.Backend != nil
}

func (r IngressAnnotationRule) IsRewrite() bool {
	return r.Rewrite != nil
}

func (r IngressAnnotationRule) GetRuleMeta() ListenerRuleMeta {
	return ListenerRuleMeta{
		Host: r.Host,
		Path: r.Path,
	}
}

func (r IngressAnnotationRule) GetRuleKey() AnnotationRuleKey {
	return AnnotationRuleKey{
		ListenerRuleMeta:   r.GetRuleMeta(),
		BackendServiceName: r.Backend.Name,
		BackendServicePort: r.Backend.Port.String(),
	}
}
