package types

import (
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"

	tscapi "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/net"
)

func TestL7RuleConfig_buildAndCompareHealthCheck(t *testing.T) {
	defaultDomain := "test.domain.com"

	tests := []struct {
		name          string
		current       *clb.RuleOutput
		config        L7RuleConfig
		defaultDomain *string
		want          *clb.HealthCheck
		shouldUpdate  bool
	}{
		{
			name: "用户提供的配置为nil，无需更新",
			current: &clb.RuleOutput{
				HealthCheck: &clb.HealthCheck{
					HealthSwitch:    lo.ToPtr(int64(1)),
					CheckType:       lo.ToPtr(net.ProtocolHTTP),
					HttpCheckDomain: lo.ToPtr("abc.com"),
					HttpCheckPath:   lo.ToPtr("/"),
				},
			},
			config:        L7RuleConfig{},
			defaultDomain: nil,
			want: &clb.HealthCheck{
				HealthSwitch:    lo.ToPtr(int64(1)),
				CheckType:       lo.ToPtr(net.ProtocolHTTP),
				HttpCheckDomain: lo.ToPtr("abc.com"),
				HttpCheckPath:   lo.ToPtr("/"),
			},
			shouldUpdate: false,
		},
		{
			name: "http类型健康检查，用户配置不为nil，需要更新",
			current: &clb.RuleOutput{
				HealthCheck: &clb.HealthCheck{
					HealthSwitch:    lo.ToPtr(int64(1)),
					CheckType:       lo.ToPtr(net.ProtocolHTTP),
					HttpCheckDomain: lo.ToPtr("abc.com"),
					HttpCheckPath:   lo.ToPtr("/"),
					HttpCheckMethod: lo.ToPtr("get"),
					HttpCode:        lo.ToPtr(int64(100)),
				},
			},
			config: L7RuleConfig{
				HealthCheck: &tscapi.L7HealthCheck{
					Enable:          true,
					CheckType:       lo.ToPtr(net.ProtocolHTTP),
					HttpCheckDomain: lo.ToPtr("domain.com"),
					HttpCheckPath:   lo.ToPtr("/health"),
					HttpCheckMethod: lo.ToPtr("HEAD"),
					HttpCode:        lo.ToPtr(int64(200)),
				},
			},
			defaultDomain: nil,
			want: &clb.HealthCheck{
				HealthSwitch:    lo.ToPtr(int64(1)),
				CheckType:       lo.ToPtr(net.ProtocolHTTP),
				HttpCheckDomain: lo.ToPtr("domain.com"),
				HttpCheckPath:   lo.ToPtr("/health"),
				HttpCheckMethod: lo.ToPtr("head"),
				HttpCode:        lo.ToPtr(int64(200)),
			},
			shouldUpdate: true,
		},
		{
			name: "用户配置了tcp类型健康检查，且填写了http相关字段，需要去掉http字段，但无需更新",
			current: &clb.RuleOutput{
				HealthCheck: &clb.HealthCheck{
					HealthSwitch: lo.ToPtr(int64(1)),
					CheckType:    lo.ToPtr(net.ProtocolTCP),
				},
			},
			config: L7RuleConfig{
				HealthCheck: &tscapi.L7HealthCheck{
					Enable:          true,
					CheckType:       lo.ToPtr(net.ProtocolTCP),
					HttpCheckDomain: lo.ToPtr("domain.com"),
					HttpCheckPath:   lo.ToPtr("/health"),
					HttpCheckMethod: lo.ToPtr("GET"),
					HttpCode:        lo.ToPtr(int64(200)),
				},
			},
			defaultDomain: nil,
			want: &clb.HealthCheck{
				HealthSwitch: lo.ToPtr(int64(1)),
				CheckType:    lo.ToPtr(net.ProtocolTCP),
			},
			shouldUpdate: false,
		},
		{
			name: "clb侧和用户侧都配置了不同的http字段，但检查类型为tcp，无需更新",
			current: &clb.RuleOutput{
				HealthCheck: &clb.HealthCheck{
					HealthSwitch:    lo.ToPtr(int64(1)),
					CheckType:       lo.ToPtr(net.ProtocolTCP),
					HttpCheckDomain: lo.ToPtr("domain.com"),
					HttpCheckPath:   lo.ToPtr("/health"),
					HttpCheckMethod: lo.ToPtr("get"),
					HttpCode:        lo.ToPtr(int64(200)),
				},
			},
			config: L7RuleConfig{
				HealthCheck: &tscapi.L7HealthCheck{
					Enable:          true,
					CheckType:       lo.ToPtr(net.ProtocolTCP),
					HttpCheckDomain: lo.ToPtr("a.com"),
					HttpCheckPath:   lo.ToPtr("/"),
					HttpCheckMethod: lo.ToPtr("HEAD"),
					HttpCode:        lo.ToPtr(int64(200)),
				},
			},
			defaultDomain: nil,
			want: &clb.HealthCheck{
				HealthSwitch: lo.ToPtr(int64(1)),
				CheckType:    lo.ToPtr(net.ProtocolTCP),
			},
			shouldUpdate: false,
		},
		{
			name: "用户将检查类型从tcp调整为http，原有的clb侧http字段被更新，需要更新",
			current: &clb.RuleOutput{
				HealthCheck: &clb.HealthCheck{
					HealthSwitch:    lo.ToPtr(int64(1)),
					CheckType:       lo.ToPtr(net.ProtocolTCP),
					HttpCheckDomain: lo.ToPtr("domain.com"),
					HttpCheckPath:   lo.ToPtr("/health"),
					HttpCheckMethod: lo.ToPtr("get"),
					HttpCode:        lo.ToPtr(int64(200)),
				},
			},
			config: L7RuleConfig{
				HealthCheck: &tscapi.L7HealthCheck{
					Enable:          true,
					CheckType:       lo.ToPtr(net.ProtocolHTTP),
					HttpCheckDomain: lo.ToPtr("test.com"),
				},
			},
			defaultDomain: nil,
			want: &clb.HealthCheck{
				HealthSwitch:    lo.ToPtr(int64(1)),
				CheckType:       lo.ToPtr(net.ProtocolHTTP),
				HttpCheckDomain: lo.ToPtr("test.com"),
				HttpCheckPath:   lo.ToPtr("/health"),
				HttpCheckMethod: lo.ToPtr("get"),
				HttpCode:        lo.ToPtr(int64(200)),
			},
			shouldUpdate: true,
		},
		{
			name: "用户将检查类型从tcp调整为http，原有的clb侧http字段保留，需要更新",
			current: &clb.RuleOutput{
				HealthCheck: &clb.HealthCheck{
					HealthSwitch:    lo.ToPtr(int64(1)),
					CheckType:       lo.ToPtr(net.ProtocolTCP),
					HttpCheckDomain: lo.ToPtr("domain.com"),
					HttpCheckPath:   lo.ToPtr("/health"),
					HttpCheckMethod: lo.ToPtr("get"),
					HttpCode:        lo.ToPtr(int64(200)),
				},
			},
			config: L7RuleConfig{
				HealthCheck: &tscapi.L7HealthCheck{
					Enable:    true,
					CheckType: lo.ToPtr(net.ProtocolHTTP),
				},
			},
			defaultDomain: nil,
			want: &clb.HealthCheck{
				HealthSwitch:    lo.ToPtr(int64(1)),
				CheckType:       lo.ToPtr(net.ProtocolHTTP),
				HttpCheckDomain: lo.ToPtr("domain.com"),
				HttpCheckPath:   lo.ToPtr("/health"),
				HttpCheckMethod: lo.ToPtr("get"),
				HttpCode:        lo.ToPtr(int64(200)),
			},
			shouldUpdate: true,
		},
		{
			name: "用户将检查类型从http调整为tcp，http相关字段需要去除，需要更新",
			current: &clb.RuleOutput{
				HealthCheck: &clb.HealthCheck{
					HealthSwitch:    lo.ToPtr(int64(1)),
					CheckType:       lo.ToPtr(net.ProtocolHTTP),
					HttpCheckDomain: lo.ToPtr("domain.com"),
					HttpCheckPath:   lo.ToPtr("/health"),
					HttpCheckMethod: lo.ToPtr("get"),
					HttpCode:        lo.ToPtr(int64(200)),
				},
			},
			config: L7RuleConfig{
				HealthCheck: &tscapi.L7HealthCheck{
					Enable:          true,
					CheckType:       lo.ToPtr(net.ProtocolTCP),
					HttpCheckDomain: lo.ToPtr("domain.com"),
					HttpCheckPath:   lo.ToPtr("/health"),
					HttpCheckMethod: lo.ToPtr("GET"),
					HttpCode:        lo.ToPtr(int64(200)),
				},
			},
			defaultDomain: nil,
			want: &clb.HealthCheck{
				HealthSwitch: lo.ToPtr(int64(1)),
				CheckType:    lo.ToPtr(net.ProtocolTCP),
			},
			shouldUpdate: true,
		},
		{
			name: "用户配置的domain为空字符串，使用默认域名",
			current: &clb.RuleOutput{
				HealthCheck: &clb.HealthCheck{
					HealthSwitch:    lo.ToPtr(int64(1)),
					CheckType:       lo.ToPtr(net.ProtocolHTTP),
					HttpCheckDomain: lo.ToPtr("old.domain.com"),
				},
			},
			config: L7RuleConfig{
				HealthCheck: &tscapi.L7HealthCheck{
					Enable:          true,
					CheckType:       lo.ToPtr(net.ProtocolHTTP),
					HttpCheckDomain: lo.ToPtr(""),
				},
			},
			defaultDomain: &defaultDomain,
			want: &clb.HealthCheck{
				HealthSwitch:    lo.ToPtr(int64(1)),
				CheckType:       lo.ToPtr(net.ProtocolHTTP),
				HttpCheckDomain: lo.ToPtr("test.domain.com"),
			},
			shouldUpdate: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, diff := tt.config.buildAndCompareHealthCheck(tt.current, tt.defaultDomain)
			assert.Equal(t, tt.want, got)
			assert.Equal(t, tt.shouldUpdate, diff)
		})
	}
}
