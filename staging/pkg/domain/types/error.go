package types

import (
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"sort"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type ErrorRegistry struct {
	errorCodes map[string]ErrorCode
}

func NewErrorRegistry() *ErrorRegistry {
	return &ErrorRegistry{
		errorCodes: make(map[string]ErrorCode),
	}
}

func (e *ErrorRegistry) Register(errorCode ErrorCode) ErrorCode {
	e.errorCodes[errorCode.Code] = errorCode
	return errorCode
}

func (e *ErrorRegistry) Get(code string) (ErrorCode, bool) {
	errorCode, exist := e.errorCodes[code]
	return errorCode, exist
}

func (e *ErrorRegistry) List() []ErrorCode {
	items := make([]ErrorCode, 0, len(e.errorCodes))
	for _, item := range e.errorCodes {
		items = append(items, item)
	}
	sort.Slice(items, func(i, j int) bool {
		return items[i].Code < items[j].Code
	})
	return items
}

var (
	SuccessReason = "Success"

	UnexpectedErrorReason    = "UnexpectedError"
	DependenceErrorReason    = "DependenceError"
	WhitelistErrorReason     = "WhitelistError"
	QuotaLimitErrorReason    = "QuotaLimitError"
	AnnotationErrorReason    = "AnnotationError"
	ConfigurationErrorReason = "ConfigurationError"
	LoadbalancerErrorReason  = "LoadbalancerError"
	ListenerErrorReason      = "ListenerError"
	BackendErrorReason       = "BackendError"
	CertErrorReason          = "CertError"
	DryRunErrorReason        = "DryRunError"
)

func NewError(code ErrorCode, originError string, args ...interface{}) *Error {
	return &Error{
		ErrorCode:   code,
		Detail:      fmt.Sprintf(code.Message, args...),
		OriginError: originError,
	}
}

type Error struct {
	ErrorCode   ErrorCode
	Detail      string
	OriginError string
}

type ErrorCode struct {
	Code           string `json:"code"`
	ReasonDetail   string `json:"reasonDetail"` // 错误类别，用来归类和输出condition使用
	Message        string `json:"message"`
	Retry          bool   `json:"retry"`
	RetryImmediate bool   `json:"retryImmediate"`
}

func (e *Error) Error() string {
	if e.IsSuccess() {
		return fmt.Sprintf("Sync Success. ReturnCode: %s", e.ErrorCode.Code)
	}

	var originError string
	if e.OriginError != "" { // OriginError 可能为空，如果为空则不输出
		originError = fmt.Sprintf("OriginError: %s\n", e.OriginError)
	}

	if e.IsWarning() {
		return fmt.Sprintf("Sync Warning\nWarningCode: %s\nDetails: %s\n", e.ErrorCode.Code, e.Detail) + originError
	} else if e.IsClientError() {
		return fmt.Sprintf("Sync ClientError\nErrorCode: %s\nDetails: %s\n", e.ErrorCode.Code, e.Detail) + originError
	} else if e.IsDependencyError() {
		return fmt.Sprintf("Sync DependencyError\nErrorCode: %s\nDetails: %s\n", e.ErrorCode.Code, e.Detail) + originError
	}

	return fmt.Sprintf("Sync Error\nErrorCode: %s\nDetails: %s\n", e.ErrorCode.Code, e.Detail) + originError
}

func (e *Error) NeedRetry() bool {
	return e.ErrorCode.Retry
}

func (e *Error) NeedRetryImmediate() bool {
	return e.ErrorCode.RetryImmediate
}

func (e *Error) IsSuccess() bool {
	return strings.HasPrefix(e.ErrorCode.Code, "S")
}

func (e *Error) IsWarning() bool {
	return strings.HasPrefix(e.ErrorCode.Code, "W")
}

func (e *Error) IsClientError() bool {
	return strings.HasPrefix(e.ErrorCode.Code, "E4")
}

func (e *Error) IsDependencyError() bool {
	return strings.HasPrefix(e.ErrorCode.Code, "E5")
}

func ToString(errs []error) string {
	data := make(map[string]bool)

	result := make([]string, 0)
	for _, err := range errs {
		if svcError := new(Error); errors.As(err, &svcError) {
			// 每个 errorcode 取第一个错误信息
			if _, exist := data[svcError.ErrorCode.Code]; exist {
				continue
			}
			data[svcError.ErrorCode.Code] = true
		}
		result = append(result, err.Error())
	}
	return strings.Join(result, "\n")
}

func getErrorCodes(message string) []string {
	// 匹配错误码，注意这里表达式应尽可能满足现状，不应关于宽松，避免误判
	re := regexp.MustCompile(`[EW]\d{4}`)
	codes := re.FindAllString(message, -1)
	return codes
}

func ReadyConditionNeedUpdate(old, new metav1.Condition) bool {
	if old.Reason != new.Reason || old.Status != new.Status {
		return true
	}

	return !reflect.DeepEqual(getErrorCodes(old.Message), getErrorCodes(new.Message))
}
