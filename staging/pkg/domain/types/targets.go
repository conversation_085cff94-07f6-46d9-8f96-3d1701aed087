package types

import (
	"context"
	"math"
	"sort"

	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
)

type SortedBatchTargets []*clb.BatchTarget

func (s SortedBatchTargets) Len() int {
	return len(s)
}

func (s SortedBatchTargets) Less(i, j int) bool {
	a := lo.Ternary(s[i].InstanceId != nil, s[i].InstanceId, s[i].EniIp)
	b := lo.Ternary(s[j].InstanceId != nil, s[j].InstanceId, s[j].EniIp)
	return *a < *b
}

func (s SortedBatchTargets) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

type ProcessedTargetsToAdd struct {
	BeforeDeletion, AfterDeletion []*clb.BatchTarget
}

type BatchTargetsRegist struct {
	TargetsToRegistBeforeFirstDeletion, TargetsToRegistBetweenDeletion, TargetsToRegistAfterSecondDeletion []*clb.BatchTarget
}

func (p BatchTargetsRegist) Len() int {
	return len(p.TargetsToRegistBeforeFirstDeletion) + len(p.TargetsToRegistBetweenDeletion) + len(p.TargetsToRegistAfterSecondDeletion)
}

func (p ProcessedTargetsToAdd) Len() int {
	return len(p.BeforeDeletion) + len(p.AfterDeletion)
}

type BatchTargetsDeRegist struct {
	FirstDeletion, SecondDeletion []*clb.BatchTarget
}

func (p BatchTargetsDeRegist) Len() int {
	return len(p.FirstDeletion) + len(p.SecondDeletion)
}

type ProcessedTargetsBundle struct {
	TargetsToAdd    ProcessedTargetsToAdd
	TargetsToUpdate []*clb.RsWeightRule
	TargetsToDelete []*clb.BatchTarget
}

type ListenerTargetsProcessor struct {
	quota int
}

func NewListenerTargetsProcessor(quota int) *ListenerTargetsProcessor {
	return &ListenerTargetsProcessor{quota: quota}
}

// ProcessAddition根据当前监听器已绑定的RS数量，待删除的RS数量，在Quota限制下，对待增加的RS进行处理，最终目标是在不超过配额限制的情况下，保证尽可能多的RS被关联
// 具体方案见：https://iwiki.woa.com/p/4010754350
func (p *ListenerTargetsProcessor) ProcessAddition(_ context.Context, currentTargetsTotal, targetsToDeleteTotal int,
	targetsToAdd []*clb.BatchTarget) ProcessedTargetsToAdd {
	ptt := ProcessedTargetsToAdd{}
	currentGap := p.quota - currentTargetsTotal
	// 删除rs之后，可以增加多少rs
	gapAfterDeletion := currentGap + targetsToDeleteTotal
	if gapAfterDeletion > 0 {
		// 如果删除rs之后可以增加rs
		truncateLen := min(gapAfterDeletion, len(targetsToAdd))
		// 对待增加的所有rs进行排序
		sort.Sort(SortedBatchTargets(targetsToAdd))
		// 当前rs数量已经超过了quota数量，因此currentGap可能小于0，需要特殊处理
		currentGap = max(currentGap, 0)
		if truncateLen > currentGap {
			// 如果待增加的rs数量比删除rs之前的余量要大，则有一部分rs需要在删除之后添加
			ptt.BeforeDeletion = append(ptt.BeforeDeletion, targetsToAdd[:currentGap]...)
			ptt.AfterDeletion = append(ptt.AfterDeletion, targetsToAdd[currentGap:truncateLen]...)
		} else {
			ptt.BeforeDeletion = append(ptt.BeforeDeletion, targetsToAdd[:truncateLen]...)
		}
	}
	return ptt
}

func (p *ListenerTargetsProcessor) ProcessTargets(_ context.Context, currentTargetsTotal int, targetsToDelete, targetsToAdd []*clb.BatchTarget) (BatchTargetsRegist, BatchTargetsDeRegist) {
	ptt := BatchTargetsRegist{}
	ptd := BatchTargetsDeRegist{}

	// 1. 获取算法计算结果
	res := p.RSAlgorithm(context.Background(), currentTargetsTotal, len(targetsToDelete), len(targetsToAdd))

	// 2. 对待添加目标排序（保持与原有逻辑一致）
	sort.Sort(SortedBatchTargets(targetsToAdd))

	// 3. 切分添加目标切片
	addStart := 0
	if res.AddBeforeFirstDeletionCount > 0 {
		end := min(res.AddBeforeFirstDeletionCount, len(targetsToAdd))
		ptt.TargetsToRegistBeforeFirstDeletion = append(ptt.TargetsToRegistBeforeFirstDeletion, targetsToAdd[addStart:end]...)
		addStart = end
	}

	if res.AddBetweenDeletionCount > 0 {
		end := min(addStart+res.AddBetweenDeletionCount, len(targetsToAdd))
		ptt.TargetsToRegistBetweenDeletion = append(ptt.TargetsToRegistBetweenDeletion, targetsToAdd[addStart:end]...)
		addStart = end
	}

	if res.AddAfterSecondDeletionCount > 0 {
		end := min(addStart+res.AddAfterSecondDeletionCount, len(targetsToAdd))
		ptt.TargetsToRegistAfterSecondDeletion = append(ptt.TargetsToRegistAfterSecondDeletion, targetsToAdd[addStart:end]...)
	}

	sort.Sort(SortedBatchTargets(targetsToDelete))
	// 4. 切分删除目标切片
	delStart := 0
	if res.FirstDeletionCount > 0 {
		end := min(res.FirstDeletionCount, len(targetsToDelete))
		ptd.FirstDeletion = append(ptd.FirstDeletion, targetsToDelete[delStart:end]...)
		delStart = end
	}

	if res.SecondDeletionCount > 0 {
		end := min(delStart+res.SecondDeletionCount, len(targetsToDelete))
		ptd.SecondDeletion = append(ptd.SecondDeletion, targetsToDelete[delStart:end]...)
	}

	return ptt, ptd
}

// 数量处理结构体
type QuantityResult struct {
	AddBeforeFirstDeletionCount int // 首批添加数量
	AddBetweenDeletionCount     int // 中间添加数量
	AddAfterSecondDeletionCount int // 最后添加数量
	FirstDeletionCount          int // 首批删除数量
	SecondDeletionCount         int // 二次删除数量
}

// 方案iwiki: https://iwiki.woa.com/p/4010754350
func (p *ListenerTargetsProcessor) RSAlgorithm(_ context.Context, currentTargetsTotal, targetsToDelCount, targetsToAddCount int) QuantityResult {
	result := QuantityResult{}
	availableSpace := p.quota - currentTargetsTotal // 当前可用配额
	remainingAdds := targetsToAddCount              // 剩余待添加数量

	// 阶段1：处理立即可以添加的数量
	if availableSpace > 0 {
		directAdd := min(targetsToAddCount, availableSpace)
		result.AddBeforeFirstDeletionCount = directAdd
		remainingAdds = targetsToAddCount - directAdd
	}

	// 计算最大可添加量（考虑删除后的空间）
	maxAdd := min(
		max(p.quota-(currentTargetsTotal-targetsToDelCount), 0),
		targetsToAddCount,
	)

	// 如果没有需要后续处理的添加操作
	if remainingAdds == 0 {
		result.FirstDeletionCount = targetsToDelCount
		return result
	}

	// 阶段2：处理超限场景
	if currentTargetsTotal > p.quota {
		overLimit := currentTargetsTotal - p.quota
		if overLimit >= targetsToDelCount {
			result.FirstDeletionCount = targetsToDelCount
			return result
		}

		// 计算需要额外删除的数量
		remainingDel := targetsToDelCount - overLimit
		extraDel := min(
			int(math.Ceil(float64(remainingDel)/2)),
			remainingAdds,
		)

		// 设置删除和添加数量
		result.FirstDeletionCount = overLimit + extraDel
		result.AddBetweenDeletionCount = extraDel
		result.SecondDeletionCount = targetsToDelCount - result.FirstDeletionCount
		result.AddAfterSecondDeletionCount = maxAdd - result.AddBeforeFirstDeletionCount - result.AddBetweenDeletionCount

		return result
	}

	// 阶段3：处理正常场景
	baseDel := min(
		int(math.Ceil(float64(currentTargetsTotal)/2)),
		targetsToDelCount,
	)
	effectiveDel := min(baseDel, remainingAdds)

	result.FirstDeletionCount = effectiveDel
	result.AddBetweenDeletionCount = effectiveDel
	result.SecondDeletionCount = targetsToDelCount - effectiveDel
	result.AddAfterSecondDeletionCount = maxAdd - result.AddBeforeFirstDeletionCount - result.AddBetweenDeletionCount

	return result
}

func CVMTargetsOnly(ts []*clb.BatchTarget) bool {
	_, otherTypeExists := lo.Find(ts, func(t *clb.BatchTarget) bool { return t.EniIp != nil })
	return !otherTypeExists
}
