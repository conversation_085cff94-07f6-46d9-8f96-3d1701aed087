package types

import (
	"strings"

	v1 "k8s.io/api/core/v1"
)

const (
	ServiceEnableGraceDeletion         = "service.cloud.tencent.com/enable-grace-deletion"       // Pod 优雅删除：先将 CLB rs 权重降为0，再删除 pod
	PodEnableDeleteProtectionByService = "tke.cloud.tencent.com/protected-by-service-controller" // Pod 删除保护
	PodProtectedByServices             = "service.cloud.tencent.com/protected-by-services"       // Pod 由哪些 services 开启的优雅删除

	IngressEnableGraceDeletion         = "ingress.cloud.tencent.com/enable-grace-deletion"       // Pod 优雅删除：先将 CLB rs 权重降为0，再删除 pod
	PodEnableDeleteProtectionByIngress = "tke.cloud.tencent.com/protected-by-ingress-controller" // Pod 删除保护
	PodProtectedByIngresses            = "ingress.cloud.tencent.com/protected-by-ingresses"      // Pod 由哪些 ingresses 开启的优雅删除
)

func IsIngressEnableGraceDeletion(ingress Ingress) bool {
	if graceDeletion, exist := ingress.Annotations()[IngressEnableGraceDeletion]; exist {
		return strings.ToLower(graceDeletion) == "true"
	}
	return false
}

func IsPodEnableDeleteProtectionOfService(pod *v1.Pod) bool {
	value, exist := pod.Annotations[PodEnableDeleteProtectionByService]
	return exist && strings.ToLower(value) == "true"
}

func IsPodEnableDeleteProtectionOfIngress(pod *v1.Pod) bool {
	value, exist := pod.Annotations[PodEnableDeleteProtectionByIngress]
	return exist && strings.ToLower(value) == "true"
}
