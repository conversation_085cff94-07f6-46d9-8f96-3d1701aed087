package types

import (
	"fmt"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/domain/service/annotation"
)

type Service struct {
	*corev1.Service
	annotation.Interface
}

func NewService(service *corev1.Service) *Service {
	// 兼容service被删除的情况
	if service == nil {
		return nil
	}
	if service.Annotations == nil {
		service.Annotations = make(map[string]string)
	}

	return &Service{
		Service:   service,
		Interface: annotation.New(&service.Annotations),
	}
}

func (s *Service) IsIndirectAccess() bool {
	return !s.IsDirectAccess()
}

func (s *Service) IsDirectAccess() bool {
	// EKS集群只支持直连
	if env.IsInEKSCluster() {
		return true
	}

	// 普通集群根据service直连注解判定
	value, ok := s.Annotation(ServiceDirectAccessAnnotation)
	if ok {
		return cast.ToBool(value)
	}

	// 默认非直连
	return false
}

func (s *Service) ExternalTrafficPolicyIsLocal() bool {
	return s.Spec.ExternalTrafficPolicy == corev1.ServiceExternalTrafficPolicyTypeLocal
}

func (s *Service) Annotation(key string) (string, bool) {
	value, ok := s.GetAnnotations()[key]

	return value, ok
}

func (s *Service) IsNoSelector() bool {
	return len(s.Spec.Selector) == 0
}

func (s *Service) String() string {
	return fmt.Sprintf("%s/%s", s.Namespace, s.Name)
}

func (s *Service) FindPort(p intstr.IntOrString) *intstr.IntOrString {
	svcPort, exists := lo.Find(s.Spec.Ports, func(sp corev1.ServicePort) bool {
		if p.Type == intstr.Int {
			return sp.Port == p.IntVal
		}
		return sp.Name == p.StrVal
	})
	if exists {
		return &svcPort.TargetPort
	}
	return nil
}

func (s *Service) SkipReadinessGate() bool {
	if readinessGateSkip, exist := s.Annotation(ServiceReadinessGateSkipAnnotation); exist {
		if isSkip, err := ParseBool(readinessGateSkip); err == nil { // 填错的场景，忽略注解的作用
			return isSkip
		}
	}
	return false
}

func (s *Service) GetBackendQuota(lbId string) int {
	return config.Global.GetBackendQuota(lbId, s.IsDirectAccess())
}
