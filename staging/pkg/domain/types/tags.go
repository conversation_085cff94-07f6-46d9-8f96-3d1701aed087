package types

import (
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
)

const (
	TagKeyMultiClusterServiceUUID TagKey = "tke-lb-multi-cluster-service-uuid"
	TagKeyMultiClusterIngressUUID TagKey = "tke-lb-multi-cluster-ingress-uuid"
	TagKeyServiceUUIDOld          TagKey = "tke-lb-serviceuuid"
	TagKeyServiceUUID             TagKey = "tke-lb-serviceId"
	TagKeyIngressUUID             TagKey = "tke-lb-ingress-uuid"
	TagKeyAutoCreated             TagKey = "tke-createdBy-flag"
	TagKeyClusterID               TagKey = "tke-clusterId"
	TagKeyLifecycleOwner          TagKey = "tke-lifecycle-owner"
	// used in eks env
	TagKeyAutoCreatedInEKS TagKey = "tke-created"
	TagKeyObjectKind       TagKey = "tke-kind"
	TagKeyObjectName       TagKey = "tke-name"

	TagKeyClusterIDPrefix = string(TagKeyClusterID) + ": "

	TagValueAutoCreated          = "yes"
	TagValueLifecycleOwnedByTKE  = "tke"
	TagValueLifecycleOwnedByUser = "user"
)

type TagKey string

func (tk TagKey) String() string {
	return string(tk)
}

func (tk TagKey) ToCLBTagInfo(v string) *clb.TagInfo {
	return &clb.TagInfo{
		TagKey:   (*string)(&tk),
		TagValue: &v,
	}
}

func (tk TagKey) ToTagResource(v string) *tag.TagResource {
	return &tag.TagResource{
		TagKey:   (*string)(&tk),
		TagValue: &v,
	}
}

func (tk TagKey) ToTagFilter(vs ...string) *tag.TagFilter {
	return &tag.TagFilter{
		TagKey:   (*string)(&tk),
		TagValue: lo.ToSlicePtr(vs),
	}
}
