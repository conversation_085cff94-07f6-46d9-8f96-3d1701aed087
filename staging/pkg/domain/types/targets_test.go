package types

import (
	"context"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
)

func TestProcessingTargets(t *testing.T) {
	type Case struct {
		name                string
		quota               int
		currentTargetsTotal int
		targetsToDelTotal   int
		targetsToAdd        []*clb.BatchTarget
		expect              ProcessedTargetsToAdd
	}
	cases := []Case{
		{
			name:                "配额达到上限，无法加入新实例",
			quota:               100,
			currentTargetsTotal: 100,
			targetsToDelTotal:   0,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{},
		},
		{
			name:                "已有实例数量超过配额，无法加入新实例",
			quota:               100,
			currentTargetsTotal: 103,
			targetsToDelTotal:   1,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{},
		},
		{
			name:                "已有实例数量超过配额，但删除实例之后可以增加全部实例",
			quota:               100,
			currentTargetsTotal: 103,
			targetsToDelTotal:   5,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{
				AfterDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
			},
		},
		{
			name:                "已有实例数量超过配额，但删除实例之后可以增加部分实例",
			quota:               100,
			currentTargetsTotal: 103,
			targetsToDelTotal:   4,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{
				AfterDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
				},
			},
		},
		{
			name:                "未达到配额上限，可以全量加入，加入后到达上限",
			quota:               100,
			currentTargetsTotal: 98,
			targetsToDelTotal:   0,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{
				BeforeDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
			},
		},
		{
			name:                "配额量小于当前期望加入实例，可以部分加入",
			quota:               100,
			currentTargetsTotal: 98,
			targetsToDelTotal:   0,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
				{
					InstanceId: lo.ToPtr("t-0"),
				},
			},
			expect: ProcessedTargetsToAdd{
				BeforeDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
			},
		},
		{
			name:                "删除实例前后可以增加部分实例，删除之前增加2实例，删除时候增加1实例",
			quota:               100,
			currentTargetsTotal: 98,
			targetsToDelTotal:   1,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
			},
			expect: ProcessedTargetsToAdd{
				BeforeDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
				AfterDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-2"),
					},
				},
			},
		},
		{
			name:                "删除实例前后均可增加实例，最终达到配额上限",
			quota:               100,
			currentTargetsTotal: 98,
			targetsToDelTotal:   2,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{
				BeforeDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
				AfterDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
		},
		{
			name:                "删除实例前后均可增加实例，增加后未到达上限",
			quota:               100,
			currentTargetsTotal: 98,
			targetsToDelTotal:   3,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{
				BeforeDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
				AfterDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
		},
		{
			name:                "删除之后才可增加所有实例，增加之后到达上限",
			quota:               100,
			currentTargetsTotal: 100,
			targetsToDelTotal:   4,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{
				AfterDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
		},
		{
			name:                "删除之后才可增加部分实例",
			quota:               100,
			currentTargetsTotal: 100,
			targetsToDelTotal:   3,
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			expect: ProcessedTargetsToAdd{
				AfterDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
					{
						InstanceId: lo.ToPtr("t-2"),
					},
				},
			},
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			targetsToAdd := NewListenerTargetsProcessor(c.quota).ProcessAddition(context.TODO(), c.currentTargetsTotal, c.targetsToDelTotal, c.targetsToAdd)
			assert.Equal(t, c.expect, targetsToAdd)
		})
	}
}

func TestProcessAdd(t *testing.T) {
	type Case struct {
		name                string
		quota               int
		currentTargetsTotal int
		targetsToDelTotal   []*clb.BatchTarget
		targetsToAdd        []*clb.BatchTarget
		expectAdd           BatchTargetsRegist
		expectDel           BatchTargetsDeRegist
	}
	cases := []Case{
		{
			name:                "非直连 -> 直连，存量已满配额",
			quota:               3,
			currentTargetsTotal: 3,
			targetsToDelTotal: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
				{
					InstanceId: lo.ToPtr("t-2"),
				},
			},
			targetsToAdd: []*clb.BatchTarget{
				{
					EniIp: lo.ToPtr("*******"),
				},
				{

					EniIp: lo.ToPtr("*******"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBetweenDeletion: []*clb.BatchTarget{
					{
						EniIp: lo.ToPtr("*******"),
					},
					{
						EniIp: lo.ToPtr("*******"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{
				FirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
				SecondDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-2"),
					},
				},
			},
		},
		{
			name:                "非直连 -> 直连，存量已超出配额",
			quota:               3,
			currentTargetsTotal: 4,
			targetsToDelTotal: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
			},
			targetsToAdd: []*clb.BatchTarget{
				{
					EniIp: lo.ToPtr("*******"),
				},
				{

					EniIp: lo.ToPtr("*******"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBetweenDeletion: []*clb.BatchTarget{
					{
						EniIp: lo.ToPtr("*******"),
					},
					{
						EniIp: lo.ToPtr("*******"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{
				FirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
					{
						InstanceId: lo.ToPtr("t-2"),
					},
				},
				SecondDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
		},
		{
			name:                "严重超限，部分新增RS无法绑定",
			quota:               3,
			currentTargetsTotal: 7,
			targetsToDelTotal: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
				{
					InstanceId: lo.ToPtr("t-4"),
				},
			},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-5"),
				},
				{
					InstanceId: lo.ToPtr("t-6"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBetweenDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-5"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{
				FirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
					{
						InstanceId: lo.ToPtr("t-4"),
					},
				},
			},
		},
		{
			name:                "严重超限，全部新增RS无法绑定",
			quota:               3,
			currentTargetsTotal: 7,
			targetsToDelTotal: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
			},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-4"),
				},
				{
					InstanceId: lo.ToPtr("t-5"),
				},
			},
			expectAdd: BatchTargetsRegist{},
			expectDel: BatchTargetsDeRegist{
				FirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
					{
						InstanceId: lo.ToPtr("t-1"),
					},
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
		},
		{
			name:                "部分新增RS需要第一次解绑后才能添加",
			quota:               3,
			currentTargetsTotal: 2,
			targetsToDelTotal: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-4"),
				},
				{
					InstanceId: lo.ToPtr("t-5"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBeforeFirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-4"),
					},
				},
				TargetsToRegistBetweenDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-5"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{
				FirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
				},
				SecondDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
			},
		},
		{
			name:                "部分新增RS需要第二次解绑后才能绑定",
			quota:               3,
			currentTargetsTotal: 2,
			targetsToDelTotal: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-4"),
				},
				{
					InstanceId: lo.ToPtr("t-5"),
				},
				{
					InstanceId: lo.ToPtr("t-6"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBeforeFirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-4"),
					},
				},
				TargetsToRegistBetweenDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-5"),
					},
				},
				TargetsToRegistAfterSecondDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-6"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{
				FirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-0"),
					},
				},
				SecondDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-1"),
					},
				},
			},
		},
		{
			name:                "满配额",
			quota:               100,
			currentTargetsTotal: 100,
			targetsToDelTotal: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-0"),
				},
				{
					InstanceId: lo.ToPtr("t-1"),
				},
			},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBetweenDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{
				FirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-1"),
					},
					{
						InstanceId: lo.ToPtr("t-2"),
					},
				},
			},
		},
		{
			name:                "只新增无解绑",
			quota:               100,
			currentTargetsTotal: 4,
			targetsToDelTotal:   []*clb.BatchTarget{},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBeforeFirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{},
		},
		{
			name:                "只解绑无新增",
			quota:               100,
			currentTargetsTotal: 4,
			targetsToDelTotal:   []*clb.BatchTarget{},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBeforeFirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{},
		},
		{
			name:                "只解绑无新增",
			quota:               100,
			currentTargetsTotal: 4,
			targetsToDelTotal:   []*clb.BatchTarget{},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-2"),
				},
				{
					InstanceId: lo.ToPtr("t-3"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBeforeFirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-2"),
					},
					{
						InstanceId: lo.ToPtr("t-3"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{},
		},
		{
			name:                "配额充足，有删除有新增",
			quota:               100,
			currentTargetsTotal: 4,
			targetsToDelTotal: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-1"),
				},
				{
					InstanceId: lo.ToPtr("t-2"),
				},
			},
			targetsToAdd: []*clb.BatchTarget{
				{
					InstanceId: lo.ToPtr("t-3"),
				},
				{
					InstanceId: lo.ToPtr("t-4"),
				},
			},
			expectAdd: BatchTargetsRegist{
				TargetsToRegistBeforeFirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-3"),
					},
					{
						InstanceId: lo.ToPtr("t-4"),
					},
				},
			},
			expectDel: BatchTargetsDeRegist{
				FirstDeletion: []*clb.BatchTarget{
					{
						InstanceId: lo.ToPtr("t-1"),
					},
					{
						InstanceId: lo.ToPtr("t-2"),
					},
				},
			},
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			targetsToAdd, targetsToDel := NewListenerTargetsProcessor(c.quota).ProcessTargets(context.TODO(), c.currentTargetsTotal, c.targetsToDelTotal, c.targetsToAdd)

			// 验证添加目标结构
			assert.Equalf(t, len(c.expectAdd.TargetsToRegistBeforeFirstDeletion), len(targetsToAdd.TargetsToRegistBeforeFirstDeletion),
				"BeforeDeletion数量不匹配 期望:%d 实际:%d", len(c.expectAdd.TargetsToRegistBeforeFirstDeletion), len(targetsToAdd.TargetsToRegistBeforeFirstDeletion))
			assert.Equalf(t, len(c.expectAdd.TargetsToRegistBetweenDeletion), len(targetsToAdd.TargetsToRegistBetweenDeletion),
				"BetweenDeletion数量不匹配 期望:%d 实际:%d", len(c.expectAdd.TargetsToRegistBetweenDeletion), len(targetsToAdd.TargetsToRegistBetweenDeletion))
			assert.Equalf(t, len(c.expectAdd.TargetsToRegistAfterSecondDeletion), len(targetsToAdd.TargetsToRegistAfterSecondDeletion),
				"AfterDeletion数量不匹配 期望:%d 实际:%d", len(c.expectAdd.TargetsToRegistAfterSecondDeletion), len(targetsToAdd.TargetsToRegistAfterSecondDeletion))

			// 验证删除目标结构
			assert.Equalf(t, len(c.expectDel.FirstDeletion), len(targetsToDel.FirstDeletion),
				"FirstDeletion数量不匹配 期望:%d 实际:%d", len(c.expectDel.FirstDeletion), len(targetsToDel.FirstDeletion))
			assert.Equalf(t, len(c.expectDel.SecondDeletion), len(targetsToDel.SecondDeletion),
				"SecondDeletion数量不匹配 期望:%d 实际:%d", len(c.expectDel.SecondDeletion), len(targetsToDel.SecondDeletion))

			// 验证排序逻辑（InstanceId升序）
			verifySorted := func(targets []*clb.BatchTarget) {
				for i := 1; i < len(targets); i++ {
					prev := lo.Ternary(targets[i-1].InstanceId != nil, targets[i-1].InstanceId, targets[i-1].EniIp)
					current := lo.Ternary(targets[i].InstanceId != nil, targets[i].InstanceId, targets[i].EniIp)
					assert.True(t, *prev < *current, "目标排序错误: %v 应该在 %v 之前", *prev, *current)
				}
			}

			// 验证添加目标的排序
			allAdded := append(append(targetsToAdd.TargetsToRegistBeforeFirstDeletion, targetsToAdd.TargetsToRegistBetweenDeletion...), targetsToAdd.TargetsToRegistAfterSecondDeletion...)
			verifySorted(allAdded)

			// 验证删除目标的排序
			allDeleted := append(targetsToDel.FirstDeletion, targetsToDel.SecondDeletion...)
			verifySorted(allDeleted)

			// 验证总数守恒
			assert.Equal(t, len(c.targetsToDelTotal), len(allDeleted), "删除目标总数不匹配")
		})
	}
}

func TestRSAlgorithm(t *testing.T) {
	tests := []struct {
		name          string
		quota         int
		current       int
		toDelCount    int
		toAddCount    int
		wantBefore    int
		wantBetween   int
		wantAfter     int
		wantFirstDel  int
		wantSecondDel int
	}{
		// 正常场景测试
		{
			name:         "配额充足_直接添加",
			quota:        500,
			current:      300,
			toDelCount:   0,
			toAddCount:   200,
			wantBefore:   200,
			wantFirstDel: 0,
		},
		// 边界测试
		{
			name:         "刚好达到配额",
			quota:        500,
			current:      499,
			toDelCount:   499,
			toAddCount:   1,
			wantBefore:   1,
			wantBetween:  0,
			wantFirstDel: 499,
		},
		{
			name:         "需要部分删除和添加",
			quota:        500,
			current:      400,
			toDelCount:   200,
			toAddCount:   300,
			wantBefore:   100,
			wantFirstDel: 200,
			wantBetween:  200,
		},

		// 超限场景测试
		{
			name:          "轻度超限",
			quota:         500,
			current:       499,
			toDelCount:    499,
			toAddCount:    2,
			wantBefore:    1,
			wantFirstDel:  1,
			wantBetween:   1,
			wantSecondDel: 498,
			wantAfter:     0,
		},
		{
			name:         "严重超限",
			quota:        500,
			current:      900,
			toDelCount:   400,
			toAddCount:   400,
			wantFirstDel: 400,
			wantBetween:  0,
			wantAfter:    0,
		},
		{
			name:         "零配额场景",
			quota:        0,
			current:      10,
			toDelCount:   10,
			toAddCount:   5,
			wantFirstDel: 10,
		},
		{
			name:          "超限2",
			quota:         500,
			current:       700,
			toDelCount:    400,
			toAddCount:    400,
			wantBefore:    0,
			wantFirstDel:  300,
			wantBetween:   100,
			wantSecondDel: 100,
			wantAfter:     100,
		},
		{
			name:          "非直连切直连，新增少量RS",
			quota:         500,
			current:       500,
			toDelCount:    500,
			toAddCount:    2,
			wantBefore:    0,
			wantFirstDel:  2,
			wantBetween:   2,
			wantSecondDel: 498,
			wantAfter:     0,
		},
		{
			name:          "非直连切直连，超限",
			quota:         500,
			current:       600,
			toDelCount:    600,
			toAddCount:    2,
			wantBefore:    0,
			wantFirstDel:  102,
			wantBetween:   2,
			wantSecondDel: 498,
			wantAfter:     0,
		},
		{
			name:          "只解绑无新增绑定",
			quota:         500,
			current:       400,
			toDelCount:    400,
			toAddCount:    0,
			wantBefore:    0,
			wantFirstDel:  400,
			wantBetween:   0,
			wantSecondDel: 0,
			wantAfter:     0,
		},
		{
			name:          "只新增无解绑",
			quota:         500,
			current:       400,
			toDelCount:    0,
			toAddCount:    10,
			wantBefore:    10,
			wantFirstDel:  0,
			wantBetween:   0,
			wantSecondDel: 0,
			wantAfter:     0,
		},
		{
			name:          "只新增无解绑，新增超限",
			quota:         500,
			current:       400,
			toDelCount:    0,
			toAddCount:    120,
			wantBefore:    100,
			wantFirstDel:  0,
			wantBetween:   0,
			wantSecondDel: 0,
			wantAfter:     0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			processor := NewListenerTargetsProcessor(tt.quota)
			result := processor.RSAlgorithm(
				context.Background(),
				tt.current,
				tt.toDelCount,
				tt.toAddCount,
			)

			// 验证添加结果
			if result.AddBeforeFirstDeletionCount != tt.wantBefore {
				t.Errorf("AddBeforeFirstDeletionCount 错误: 期望 %d, 实际 %d", tt.wantBefore, result.AddBeforeFirstDeletionCount)
			}
			if result.AddBetweenDeletionCount != tt.wantBetween {
				t.Errorf("AddBetweenDeletionCount 错误: 期望 %d, 实际 %d", tt.wantBetween, result.AddBetweenDeletionCount)
			}
			if result.AddAfterSecondDeletionCount != tt.wantAfter {
				t.Errorf("AddAfterSecondDeletionCount 错误: 期望 %d, 实际 %d", tt.wantAfter, result.AddAfterSecondDeletionCount)
			}

			// 验证删除结果
			if result.FirstDeletionCount != tt.wantFirstDel {
				t.Errorf("FirstDeletionCount 错误: 期望 %d, 实际 %d", tt.wantFirstDel, result.FirstDeletionCount)
			}
			if result.SecondDeletionCount != tt.wantSecondDel {
				t.Errorf("SecondDeletionCount 错误: 期望 %d, 实际 %d", tt.wantSecondDel, result.SecondDeletionCount)
			}

			// 验证总数守恒
			totalAdded := result.AddBeforeFirstDeletionCount + result.AddBetweenDeletionCount + result.AddAfterSecondDeletionCount
			expectedAdded := tt.wantBefore + tt.wantBetween + tt.wantAfter
			if totalAdded != expectedAdded {
				t.Errorf("总添加量错误: 期望 %d, 实际 %d", expectedAdded, totalAdded)
			}

			totalDeleted := result.FirstDeletionCount + result.SecondDeletionCount
			expectedDeleted := tt.wantFirstDel + tt.wantSecondDel
			if totalDeleted != expectedDeleted {
				t.Errorf("总删除量错误: 期望 %d, 实际 %d", expectedDeleted, totalDeleted)
			}
		})
	}
}
