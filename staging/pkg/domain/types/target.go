package types

import (
	"fmt"
	"strings"

	v1 "k8s.io/api/core/v1"
)

// Target https://cloud.tencent.com/document/api/214/30694#BatchTarget

/*
 * https://cloud.tencent.com/document/api/214/30694#Backend 后端服务的类型
 */
type BackendType string

var (
	ENI             BackendType = "ENI"
	CVM             BackendType = "CVM"
	NAT             BackendType = "NAT"         // SNAT Pro，PVGW网络跨地域后端、PVGW网络混合云后端
	EVM             BackendType = "EVM"         // 弹性实例后端
	CCN             BackendType = "CCN"         // 云联网跨地域绑定类型后端
	GLOBALROUTE     BackendType = "GLOBALROUTE" // 容器网络直连类型后端
	PVGW            BackendType = "PVGW"
	MANAGED_SERVICE BackendType = "MANAGED_SERVICE" // 跨租户弹性网卡类型后端
)

func BackendIsIP(backendType string) bool {
	return backendType != string(CVM)
}

func BackendIsCVM(backendType string) bool {
	return backendType == string(CVM)
}

type TargetType string

type RuleTarget struct {
	Port            int64
	Protocol        string
	Targets         []*Target
	ExcludedTargets map[string]bool

	AllDown bool
}

func (r *RuleTarget) FakeAllDown() (bool, bool) {
	var byIsolating, byLocalUpgrading bool
	if r.AllDown {
		for _, target := range r.Targets {
			if target.Isolating() {
				byIsolating = true
			}
			if target.LocalUpgrading() {
				byLocalUpgrading = true
			}
		}
	}

	return byIsolating, byLocalUpgrading
}

type Target struct {
	Node        *v1.Node
	Pod         *v1.Pod
	Direct      bool
	BackendType BackendType
	Target      string // kateway Node instanceID
	Port        int32
	Weight      *int64
}

func (t *Target) Isolating() bool {
	if t.Pod != nil {
		for _, condition := range t.Pod.Status.Conditions {
			if condition.Type == "platform.tkex/debug-pod" && condition.Status == "False" {
				return true
			}
		}
	}
	return false
}

func (t *Target) LocalUpgrading() bool {
	if t.Pod != nil {
		for _, condition := range t.Pod.Status.Conditions {
			if condition.Type == "platform.tkex/InPlace-Update-Ready" && condition.Status == "False" {
				return true
			}
		}
	}
	return false
}

type Targets []*Target

func (s Targets) Len() int           { return len(s) }
func (s Targets) Less(i, j int) bool { return s[i].Target < s[j].Target }
func (s Targets) Swap(i, j int)      { s[i], s[j] = s[j], s[i] }

func BuildTargetTag(clusterId string) string {
	return fmt.Sprintf("%s:%s", "clusterId", clusterId)
}

func ManagedTarget(tag *string, clusterId string) bool {
	if tag == nil || *tag == "" {
		return false
	}
	splits := strings.Split(*tag, ";")
	for _, split := range splits {
		keyvalue := strings.Split(split, ":")
		if len(keyvalue) == 2 && keyvalue[0] == "clusterId" && keyvalue[1] == clusterId {
			return true
		}
	}
	return false
}

func NeedSKipTarget(tag *string) bool {
	if tag == nil || *tag == "" {
		return false
	}
	splits := strings.Split(*tag, ";")
	for _, split := range splits {
		keyvalue := strings.Split(split, ":")
		if len(keyvalue) == 2 && keyvalue[0] == "skip-sync-weight" && keyvalue[1] == "true" {
			return true
		}
	}
	return false
}
