package ingress

import (
	"encoding/json"
	"fmt"
	"os"

	fakeit "github.com/brianvoe/gofakeit/v7"
	"github.com/ghodss/yaml"
	"github.com/samber/lo"
	corev1 "k8s.io/api/core/v1"
	netv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	tscv1alpha1 "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/domain/types/fake/ingress/wraps"
	fakeingress "git.woa.com/kateway/pkg/k8s/fake/ingress"
	"git.woa.com/kateway/pkg/rand"
)

type Environment struct {
	Type              types.IngressType
	Ingress           netv1.Ingress
	Services          []corev1.Service
	Secrets           []corev1.Secret
	TKEServiceConfigs []tscv1alpha1.TkeServiceConfig
}

func LoadEnvironment(filePath string) (*Environment, error) {
	raw, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}
	ctx := &Environment{}
	if err := yaml.Unmarshal(raw, ctx); err != nil {
		return nil, err
	}
	return ctx, nil
}

func (env Environment) Dump(filePath string) error {
	f, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		return err
	}
	defer f.Close()
	raw, err := yaml.Marshal(env)
	if err != nil {
		return err
	}
	f.WriteString(string(raw))
	return nil
}

func (env Environment) String() string {
	return fmt.Sprintf("%s\n---\n%s\n---\n%s\n---\n%s",
		lo.Must(yaml.Marshal(env.Ingress)),
		lo.Must(yaml.Marshal(env.Services)),
		lo.Must(yaml.Marshal(env.Secrets)),
		lo.Must(yaml.Marshal(env.TKEServiceConfigs)),
	)
}

func (env Environment) GetWrappedIngress() *wraps.NetworkingIngress {
	return wraps.NewNetworking(env.Type, &env.Ingress)
}

func New() *Environment {
	penv := fakeingress.New()
	env := &Environment{
		Type:     rand.Ternary(0.7, types.CoreIngress, types.MultiClusterIngress),
		Ingress:  penv.Ingress,
		Services: penv.Services,
		Secrets:  penv.Secrets,
	}
	buildIngressAnnotations(env)
	buildServiceAnnotations(env)
	for i := range env.Secrets {
		initSecret(&env.Secrets[i])
	}
	return env
}

func initSecret(s *corev1.Secret) {
	s.Data = map[string][]byte{}
	rand.Invoke(.8, func() {
		s.Data["qcloud_cert_id"] = []byte(fakeit.LetterN(5))
	})
	rand.Invoke(.3, func() {
		s.Data["qcloud_ca_cert_id"] = []byte(fakeit.LetterN(5))
	})
}

func buildServiceAnnotations(env *Environment) {
	for i := range env.Services {
		svc := &env.Services[i]
		rand.Invoke(.5, func() {
			svc.Annotations = map[string]string{
				types.ServiceDirectAccessAnnotation: rand.Pick1Else(.9, []string{"true", "false", "True", "False"}, []string{"fault", "1", "0"}),
			}
		})
	}
}

func buildIngressAnnotations(env *Environment) {
	annotations := map[string]string{}
	env.Ingress.ObjectMeta.Annotations = annotations
	rand.Invoke(.5, func() {
		annotations[types.IngressAnnotationBackendManageOnly] = rand.Pick1Else(0.9, []string{"true", "false", "TRUE", "False"}, []string{"invalid", "", "1", "0"})
	})
	rand.Invoke(.5, func() {
		annotations[types.IngressAnnotationDirectAccess] = rand.Pick1Else(0.9, []string{"true", "false", "True", "FALSE"}, []string{"invalid", "1", "0", ""})
	})
	rand.Invoke(.7, func() {
		annotations[types.IngressAnnotationRuleMix] = rand.Pick1Else(0.9, []string{"TRUE", "False", "true", "false"}, []string{"invalid", "1", "0", ""})
	})
	rand.Invoke(.7, func() {
		annotations[types.IngressAnnotationAutoRewrite] = rand.Pick1Else(0.9, []string{"true", "false", "TRUE", "False"}, []string{"invalid", "1", "0", ""})
	})
	rand.Invoke(.7, func() {
		rules := string(lo.Must(json.Marshal(newIngressAnnotationRules(env.Ingress))))
		annotations[types.IngressAnnotationHTTPRules] = rand.Pick1Else(.9, []string{rules}, []string{"", "{}", "nil"})
	})
	rand.Invoke(.7, func() {
		rules := string(lo.Must(json.Marshal(newIngressAnnotationRules(env.Ingress))))
		annotations[types.IngressAnnotationHTTPSRules] = rand.Pick1Else(.9, []string{rules}, []string{"", "{}", "nil"})
	})
	rand.Invoke(.7, func() {
		annotations[types.IngressAnnotationTKEServiceConfigAuto] = rand.Pick1Else(.9, []string{"true", "1", "false", "0"}, []string{"", "nil"})
		rand.Invoke(.8, func() { buildTKEServiceConfig(env, env.Ingress.Name+"-auto-ingress-config") })
	})
	rand.Invoke(.6, func() {
		name := rand.Pick1Else(0.8, []string{fakeit.LetterN(10)}, []string{env.Ingress.Name + "-auto-ingress-config"})
		annotations[types.IngressAnnotationTKEServiceConfig] = name
		rand.Invoke(.8, func() { buildTKEServiceConfig(env, name) })
	})
}

func buildTKEServiceConfig(env *Environment, name string) {
	_, exists := lo.Find(env.TKEServiceConfigs, func(cfg tscv1alpha1.TkeServiceConfig) bool { return cfg.Name == name })
	if exists {
		return
	}
	cfg := tscv1alpha1.TkeServiceConfig{
		TypeMeta: metav1.TypeMeta{
			Kind:       "TkeServiceConfig",
			APIVersion: tscv1alpha1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: env.Ingress.Namespace,
		},
	}
	hosts := lo.Map(env.Ingress.Spec.Rules, func(r netv1.IngressRule, _ int) string { return r.Host })
	hostsPtr := lo.ToSlicePtr(hosts)
	buildL7ListenerConfig := func(protocol string, port int32) *tscv1alpha1.L7ListenerConfig {
		return &tscv1alpha1.L7ListenerConfig{
			Protocol:      protocol,
			Port:          port,
			DefaultServer: rand.Pick1(append(hostsPtr, nil)...),
			Domains: lo.Map(rand.Subset(env.Ingress.Spec.Rules), func(r netv1.IngressRule, _ int) *tscv1alpha1.L7DomainConfig {
				return &tscv1alpha1.L7DomainConfig{
					Domain: r.Host,
					Http2:  lo.ToPtr(rand.Pick1(true, false)),
					Rules: lo.Map(rand.Subset(r.HTTP.Paths), func(p netv1.HTTPIngressPath, _ int) *tscv1alpha1.L7RuleConfig {
						return &tscv1alpha1.L7RuleConfig{
							Url:         p.Path,
							ForwardType: lo.ToPtr(rand.Pick1("HTTP", "HTTPS", "GRPC")),
							HealthCheck: &tscv1alpha1.L7HealthCheck{
								Enable: rand.Pick1(true, false),
							},
						}
					}),
				}
			}),
		}
	}
	cfg.Spec.LoadBalancer.L7Listeners = append(cfg.Spec.LoadBalancer.L7Listeners,
		buildL7ListenerConfig(
			rand.Pick1Else(.9, []string{"HTTP", "http"}, []string{"TLS"}),
			rand.Pick1Else(.9, []int32{80}, []int32{123, 345}),
		),
		buildL7ListenerConfig(
			rand.Pick1Else(.9, []string{"HTTPS", "https"}, []string{"QUIC"}),
			rand.Pick1Else(.9, []int32{443}, []int32{123, 345}),
		),
	)
	env.TKEServiceConfigs = append(env.TKEServiceConfigs, cfg)
}

func newIngressAnnotationRules(ing netv1.Ingress) []types.IngressAnnotationRule {
	rules := []types.IngressAnnotationRule{}
	for _, r := range ing.Spec.Rules {
		for _, p := range r.HTTP.Paths {
			bsvc := p.Backend.Service
			rand.Invoke(.6, func() {
				ar := types.IngressAnnotationRule{
					Host:         r.Host,
					Path:         p.Path,
					CLBPathTypes: rand.Subset([]types.CLBPathType{types.CLBPathTypeExact, types.CLBPathTypeRegex, types.CLBPathTypeRegexIgnoreCase, types.CLBPathTypeNonAbsolute, ""}),
				}
				ar.Backend = &struct {
					Name string             "json:\"serviceName,omitempty\""
					Port intstr.IntOrString "json:\"servicePort,omitempty\""
				}{
					Name: bsvc.Name,
				}
				if bsvc.Port.Name != "" {
					ar.Backend.Port = intstr.FromString(bsvc.Port.Name)
				} else {
					ar.Backend.Port = rand.Ternary(.7, intstr.FromInt(int(bsvc.Port.Number)), intstr.FromString(fmt.Sprint(bsvc.Port.Number)))
				}
				rules = append(rules, ar)
			})
		}
	}
	for i := range rules {
		rand.Invoke(0.4, func() {
			r := rand.Pick1(ing.Spec.Rules...)
			p := rand.Pick1(r.HTTP.Paths...)
			rules[i].Backend = nil
			rand.Invoke(.8, func() {
				rules[i].CLBPathTypes = nil
			})
			rules[i].Rewrite = &struct {
				Port int32  "json:\"port,omitempty\""
				Host string "json:\"host,omitempty\""
				Path string "json:\"path,omitempty\""
			}{
				Port: int32(rand.Pick1(80, 443)),
				Host: rand.Ternary(.9, r.Host, "hostdoesnotexist.com"),
				Path: rand.Ternary(.9, p.Path, "/pathdoesnotexist/does/not/exist"),
			}
		})
		rand.Invoke(0.1, func() {
			rules[i].Backend = nil
			rules[i].Rewrite = nil
		})
	}
	return rules
}
