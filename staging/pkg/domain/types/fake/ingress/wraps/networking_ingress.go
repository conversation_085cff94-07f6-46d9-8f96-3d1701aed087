package wraps

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	networking "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"

	"git.woa.com/kateway/pkg/domain/ingress/annotation"
	"git.woa.com/kateway/pkg/domain/types"
)

type NetworkingIngress struct {
	t types.IngressType
	*networking.Ingress
	annotation.Interface
}

func NewNetworking(ingType types.IngressType, ingress *networking.Ingress) *NetworkingIngress {
	if ingress.Annotations == nil {
		ingress.Annotations = map[string]string{}
	}
	return &NetworkingIngress{
		t:         ingType,
		Ingress:   ingress,
		Interface: annotation.New(&ingress.Annotations),
	}
}

func (ing NetworkingIngress) Type() types.IngressType {
	return ing.t
}

func (ing NetworkingIngress) RuntimeObject() runtime.Object {
	return ing.Ingress
}

func (ing NetworkingIngress) ObjectReference() v1.ObjectReference {
	return v1.ObjectReference{
		Kind:            "Ingress",
		APIVersion:      networking.SchemeGroupVersion.String(),
		Namespace:       ing.Ingress.Namespace,
		Name:            ing.Ingress.Name,
		ResourceVersion: ing.Ingress.ResourceVersion,
		UID:             ing.Ingress.UID,
	}
}

func (ing NetworkingIngress) Namespace() string {
	return ing.Ingress.GetNamespace()
}

func (ing NetworkingIngress) Name() string {
	return ing.Ingress.GetName()
}

func (ing NetworkingIngress) Annotations() map[string]string {
	return ing.Ingress.Annotations
}

func (ing NetworkingIngress) IngressClassName() *string {
	return ing.Spec.IngressClassName
}

func (ing NetworkingIngress) TLS() []types.IngressTLS {
	return lo.Map(ing.Spec.TLS, func(item networking.IngressTLS, _ int) types.IngressTLS {
		return types.IngressTLS{
			Hosts:      item.Hosts,
			SecretName: item.SecretName,
		}
	})
}

func (ing NetworkingIngress) StatusLoadBalancer() v1.LoadBalancerStatus {
	return types.ToLoadBalancerStatus(ing.Status.LoadBalancer)
}

func (ing NetworkingIngress) UID() string {
	return string(ing.Ingress.UID)
}

func (ing *NetworkingIngress) Update(ctx context.Context) (types.Ingress, error) {
	return ing, nil
}

func (ing *NetworkingIngress) UpdateAnnotation(_ context.Context, _ map[string]string, _ []string) (types.Ingress, error) {
	return ing, nil
}

func (ing *NetworkingIngress) UpdateLoadBalancerStatus(_ context.Context, _ v1.LoadBalancerStatus) error {
	return nil
}

func (ing NetworkingIngress) HasFinalizer(_ context.Context, _ string) (bool, error) {
	return false, nil
}

func (ing *NetworkingIngress) AddFinalizer(_ context.Context, _ string) error {
	return nil
}

func (ing *NetworkingIngress) RemoveFinalizer(_ context.Context, _ string) error {
	return nil
}

func (ing NetworkingIngress) DeletionTimestamp() *metav1.Time {
	return ing.Ingress.DeletionTimestamp
}

func (ing NetworkingIngress) Rules() []types.IngressRule {
	return lo.Map(ing.Spec.Rules, func(item networking.IngressRule, _ int) types.IngressRule {
		return types.IngressRule{
			Host: item.Host,
			HTTPPaths: lo.Map(item.HTTP.Paths, func(ps networking.HTTPIngressPath, _ int) types.HTTPIngressPath {
				return types.HTTPIngressPath{
					Path:     ps.Path,
					PathType: string(*ps.PathType),
					Backend: types.IngressBackend{
						ServiceName: ps.Backend.Service.Name,
						ServicePort: lo.Ternary(
							ps.Backend.Service.Port.Name == "",
							intstr.FromInt(int(ps.Backend.Service.Port.Number)),
							intstr.FromString(ps.Backend.Service.Port.Name),
						),
					},
				}
			}),
		}
	})
}

func (ing NetworkingIngress) DeepCopy() types.Ingress {
	return &NetworkingIngress{Ingress: ing.Ingress.DeepCopy()}
}

func (ing *NetworkingIngress) Version() string {
	return networking.SchemeGroupVersion.Version
}

func (ing *NetworkingIngress) ResourceVersion() string {
	return ing.Ingress.ResourceVersion
}

func (ing *NetworkingIngress) String() string {
	return fmt.Sprintf("%s/%s", ing.Ingress.Namespace, ing.Ingress.Name)
}

func (ing *NetworkingIngress) GetRuntimeObject() runtime.Object {
	return ing.Ingress
}

func (ing *NetworkingIngress) Kind() string {
	return string(ing.Type())
}

func (ing *NetworkingIngress) IsDryRun() bool {
	return false
}

func (ing *NetworkingIngress) SaveDryRunError(err error) {
}
