package types

import (
	multiclusterservice "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"

	"git.woa.com/kateway/pkg/domain/service/annotation"
)

type MCS struct {
	*multiclusterservice.MultiClusterService
	annotation.Interface
}

func NewMCS(mcs *multiclusterservice.MultiClusterService) *MCS {
	if mcs == nil {
		return nil
	}

	if mcs.Annotations == nil {
		mcs.Annotations = map[string]string{}
	}

	return &MCS{
		MultiClusterService: mcs,
		Interface:           annotation.New(&mcs.Annotations),
	}
}
