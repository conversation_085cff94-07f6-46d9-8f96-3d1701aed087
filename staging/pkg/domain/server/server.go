package server

import (
	"encoding/json"
	"fmt"
	"html/template"
	"net/http"
	"net/http/pprof"
	"os"
	"reflect"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gopkg.in/yaml.v2"

	"git.woa.com/kateway/pkg/app/version"
	"git.woa.com/kateway/pkg/domain/server/metrics"
	"git.woa.com/kateway/pkg/domain/types"
)

type Metrics struct {
	IngressClass                string
	StandaloneIngressController bool
	MetricsPort                 int
}

type Config struct {
	Metrics

	Port int
}

type Server struct {
	*Config
	engine  *gin.Engine
	api     []string
	tracing tracingStatus
}

type tracingStatus struct {
	Enable bool
}

var (
	server *Server
)

func Init(config *Config) {
	server = New(config.Port)
	go server.Run()

	metrics.New(config.MetricsPort, config.StandaloneIngressController, config.IngressClass).Run()
}

func New(port int) *Server {
	gin.SetMode(gin.ReleaseMode)
	engine := gin.Default()

	s := &Server{
		Config: &Config{
			Port: port,
		},
		engine: engine,
	}

	s.engine.NoRoute(s.index)

	s.Register("/health", s.Health)
	s.Register("/healthz", s.HealthZ)

	s.Register("/env", s.Env)
	s.Register("/version", s.Version)
	s.Register("/status", ListStatus)

	s.Register("/debug/pprof/", pprof.Index)
	s.Register("/debug/pprof/heap", pprof.Index)
	s.Register("/debug/pprof/mutex", pprof.Index)
	s.Register("/debug/pprof/goroutine", pprof.Index)
	s.Register("/debug/pprof/threadcreate", pprof.Index)
	s.Register("/debug/pprof/block", pprof.Index)
	s.Register("/debug/pprof/cmdline", pprof.Cmdline)
	s.Register("/debug/pprof/profile", pprof.Profile)
	s.Register("/debug/pprof/symbol", pprof.Symbol)
	s.Register("/debug/pprof/trace", pprof.Trace)

	return s
}

func (s *Server) Register(relativePath string, handler any) {
	handlerType := reflect.TypeOf(handler)
	if handlerType.ConvertibleTo(reflect.TypeOf(http.HandlerFunc(nil))) {
		s.engine.GET(relativePath, gin.WrapF(handler.(func(http.ResponseWriter, *http.Request))))
	} else {
		s.engine.GET(relativePath, handler.(func(*gin.Context)))
	}
	s.api = append(s.api, relativePath)
}

func Register(relativePath string, handler any) {
	if server == nil {
		return
	}

	server.Register(relativePath, handler)
}

func (s *Server) Run() {
	s.engine.Run(fmt.Sprintf(":%d", s.Port))
}

func (s *Server) write(c *gin.Context, obj interface{}) {
	data, _ := yaml.Marshal(obj)
	c.Data(http.StatusOK, gin.MIMEPlain, data)
}

func (s *Server) isFromBrowser(c *gin.Context) bool {
	return strings.Contains(c.Request.UserAgent(), "Chrome")
}

func (s *Server) index(c *gin.Context) {
	tmpl := `<!DOCTYPE html>
    <html>
    <head>
      <title>API 列表</title>
    </head>
    <body>
      <h1>API 列表</h1>
      <ul>
		{{range $index, $element := .}}
		  <li><a href="{{$element}}">{{$element}}</a></li>
		{{end}}
      </ul>
    </body>
    </html>`
	t, err := template.New("routes").Parse(tmpl)
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to parse template")
		return
	}

	err = t.Execute(c.Writer, s.api)
	if err != nil {
		c.String(http.StatusInternalServerError, "Failed to render template")
		return
	}
}

func (s *Server) Health(c *gin.Context) {
	c.Status(http.StatusOK)
}

func (s *Server) HealthZ(c *gin.Context) {
	c.Status(http.StatusOK)
}

func (s *Server) Env(c *gin.Context) {
	s.write(c, os.Environ())
}

func (s *Server) Version(c *gin.Context) {
	s.write(c, version.Get())
}

type ObjectMeta struct {
	Type      string
	Name      string
	Namespace string
}

type ObjectStatus struct {
	ObjectMeta

	Errors []string
}

var (
	statuses sync.Map // map[ObjectMeta]ObjectStatus
)

const (
	maxResponseSize = 1 * 1024 * 1024 // 1MB
	maxErrorSize    = 1000
)

func ListStatus(c *gin.Context) {
	data := []ObjectStatus{}
	statuses.Range(func(key, value interface{}) bool {
		data = append(data, value.(ObjectStatus))
		return true
	})
	for i := range data {
		s := &data[i]
		s.Errors = lo.Map(s.Errors, func(msg string, _ int) string {
			if len(msg) > maxErrorSize {
				return msg[:maxErrorSize] + "..."
			}
			return msg
		})
	}

	raw := lo.Must(json.Marshal(data))
	if len(raw) > maxResponseSize {
		raw = raw[:maxResponseSize]
	}
	c.String(http.StatusOK, string(raw))
}

func RecordErrors(obj any, errs ...error) {
	wrapper, ok := obj.(types.Object)
	if !ok {
		return
	}
	meta := ObjectMeta{
		Type:      wrapper.Kind(),
		Name:      wrapper.Name(),
		Namespace: wrapper.Namespace(),
	}
	if len(errs) == 0 {
		statuses.Delete(meta)
		return
	}
	status := ObjectStatus{
		ObjectMeta: meta,
		Errors:     lo.Map(errs, func(e error, _ int) string { return e.Error() }),
	}
	statuses.Store(meta, status)
}
