package metrics

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/collectors"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/samber/lo"
	"k8s.io/klog/v2"

	ingmetrics "git.woa.com/kateway/pkg/domain/ingress/metrics"
	"git.woa.com/kateway/pkg/domain/metrics"
	svcmetrics "git.woa.com/kateway/pkg/domain/service/metrics"
	"git.woa.com/kateway/pkg/domain/types"
)

func New(port int, standaloneIngress bool, ingressClass string) *Server {
	collector := &defaultCollector{
		serviceController: svcmetrics.NewControllerCollector(),
		ingressController: ingmetrics.NewControllerCollector(ingressClass, standaloneIngress),
		queue:             metrics.NewQueueCollector(),
		api:               svcmetrics.NewTencentAPICollector(),
		registry:          prometheus.NewRegistry(),
	}
	// ingress独立运行模式下云api相关指标项使用ingress原有的名称，同时将service controller collector置空，
	// 防止operational_total指标项重复注册导致报错
	if standaloneIngress {
		collector.api = ingmetrics.NewTencentAPICollector()
		collector.serviceController = nil
	}
	return &Server{
		port:              port,
		standaloneIngress: standaloneIngress,
		ingressClass:      ingressClass,
		collector:         collector,
	}
}

type Server struct {
	port              int
	standaloneIngress bool
	ingressClass      string

	collector metrics.Collector
}

func (s Server) Run() {
	metrics.Instance = s.collector

	s.collector.Start()

	registry := s.collector.GetRegistry()

	mux := http.NewServeMux()

	mux.Handle(
		"/metrics",
		promhttp.InstrumentMetricHandler(
			registry, promhttp.HandlerFor(registry, promhttp.HandlerOpts{Registry: registry}),
		),
	)

	registry.MustRegister(collectors.NewGoCollector())
	registry.MustRegister(collectors.NewProcessCollector(collectors.ProcessCollectorOpts{}))

	server := &http.Server{
		Addr:              fmt.Sprintf(":%v", s.port),
		Handler:           mux,
		ReadTimeout:       10 * time.Second,
		ReadHeaderTimeout: 10 * time.Second,
		WriteTimeout:      300 * time.Second,
		IdleTimeout:       120 * time.Second,
	}

	go func() {
		klog.Fatal(server.ListenAndServe())
	}()
}

type defaultCollector struct {
	serviceController *svcmetrics.ControllerCollector
	ingressController *ingmetrics.ControllerCollector

	queue *metrics.QueueCollector
	api   metrics.TencentAPICollector

	registry *prometheus.Registry
}

func (c defaultCollector) GetRegistry() *prometheus.Registry {
	return c.registry
}

func (c defaultCollector) Start() {
	apiCollector, _ := c.api.(prometheus.Collector)
	c.registry.MustRegister(
		apiCollector,
		c.queue,
	)
	if c.serviceController != nil {
		c.registry.MustRegister(c.serviceController)
	}
	if c.ingressController != nil {
		c.registry.MustRegister(c.ingressController)
	}
}

func (c defaultCollector) Stop() {
	apiCollector, _ := c.api.(prometheus.Collector)
	c.registry.Unregister(apiCollector)
	c.registry.Unregister(c.queue)
	if c.serviceController != nil {
		c.registry.Unregister(c.serviceController)
	}
	if c.ingressController != nil {
		c.registry.Unregister(c.ingressController)
	}
}

func (c defaultCollector) controllerCollector(kind string) metrics.ControllerCollector {
	return lo.Ternary[metrics.ControllerCollector](strings.Contains(kind, "Service"), c.serviceController, c.ingressController)
}

// controller collector interface
func (c defaultCollector) IncSyncCount(obj types.Object, returnCode string) {
	c.controllerCollector(obj.Kind()).IncSyncCount(obj, returnCode)
}

func (c defaultCollector) UpdateSyncTime(obj types.Object, returnCode string, syncTime time.Duration) {
	c.controllerCollector(obj.Kind()).UpdateSyncTime(obj, returnCode, syncTime)
}

func (c defaultCollector) RemoveObjectMetrics(obj types.Object) {
	c.controllerCollector(obj.Kind()).RemoveObjectMetrics(obj)
}

func (c defaultCollector) IncNodeChangeCount(nodename string) {
	c.serviceController.IncNodeChangeCount(nodename)
}

func (c defaultCollector) SetOperationalCount(objKind string, operationalMap map[string]int) {
	c.controllerCollector(objKind).SetOperationalCount(objKind, operationalMap)
}

func (c defaultCollector) IncWebhookRequestCount(l prometheus.Labels) {
	c.serviceController.IncWebhookRequestCount(l)
}

func (c defaultCollector) UpdateWebhookDelayTime(l prometheus.Labels, delayTime time.Duration) {
	c.serviceController.UpdateWebhookDelayTime(l, delayTime)
}

// cloudapi collector interface
func (c defaultCollector) IncAPIRequestCount(r metrics.TencentAPIRecord) {
	c.api.IncAPIRequestCount(r)
}

func (c defaultCollector) UpdateQcloudAPIDelayTime(r metrics.TencentAPIRecord, delayTime time.Duration) {
	c.api.UpdateQcloudAPIDelayTime(r, delayTime)
}

func (c defaultCollector) IncTaskRequestCount(r metrics.TencentAPIRecord) {
	c.api.IncTaskRequestCount(r)
}

func (c defaultCollector) UpdateQcloudTaskDelayTime(r metrics.TencentAPIRecord, delayTime time.Duration) {
	c.api.UpdateQcloudTaskDelayTime(r, delayTime)
}
