package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"

	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/types"
)

// ControllerCollector defines base metrics about the service controller
type ControllerCollector struct {
	prometheus.Collector

	syncOperation    *prometheus.CounterVec
	syncTime         *prometheus.HistogramVec
	nodeChange       *prometheus.CounterVec
	operational      *prometheus.GaugeVec
	webhookRequest   *prometheus.CounterVec
	webhookDelayTime *prometheus.HistogramVec

	codeMaps *metrics.SyncCodeMaps
}

// NewController creates a new prometheus collector for the
// Service controller operations
func NewControllerCollector() *ControllerCollector {
	return &ControllerCollector{
		syncOperation: prometheus.NewCounterVec( // 同步
			prometheus.CounterOpts{
				Name: "service_sync_total",
				Help: `Cumulative number of sync operations`,
			},
			[]string{"serviceType", "service", "returnCode"},
		),

		syncTime: prometheus.NewHistogramVec( // 同步时间
			prometheus.HistogramOpts{
				Name:    "service_sync_time_seconds",
				Help:    "Length of time per sync per controller",
				Buckets: prometheus.ExponentialBuckets(0.1, 2, 10),
			},
			[]string{"serviceType", "service", "returnCode"},
		),

		nodeChange: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "node_change_total",
				Help: `Total number of node change by the controller`,
			},
			[]string{"nodename"},
		),

		operational: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "operational_total",
				Help: `Statistics of feature usage by the controller`,
			},
			[]string{"serviceType", "operational"},
		),

		webhookRequest: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "service_webhook_task_requests_total",
				Help: `Cumulative number of task made to the webhook`,
			},
			[]string{"returnCode"},
		),

		webhookDelayTime: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "service_webhook_task_time_seconds",
				Help:    "Length of time per webhook call",
				Buckets: prometheus.ExponentialBuckets(0.01, 2, 10),
			},
			[]string{"returnCode"},
		),
		codeMaps: metrics.NewSyncCodeMaps(),
	}
}

// IncSyncCount increment the reconcile update counter
func (c *ControllerCollector) IncSyncCount(obj types.Object, returnCode string) {
	l := prometheus.Labels{
		"serviceType": obj.Kind(),
		"service":     obj.String(),
		"returnCode":  returnCode,
	}
	c.syncOperation.With(l).Inc()

	if exist := c.codeMaps.IsCodeExists(obj, returnCode); !exist {
		c.codeMaps.AddCode(obj, returnCode)
	}
}

// UpdateSyncTime updates prometheus metrics within the controller
func (c *ControllerCollector) UpdateSyncTime(obj types.Object, returnCode string, syncTime time.Duration) {
	l := prometheus.Labels{
		"serviceType": obj.Kind(),
		"service":     obj.String(),
		"returnCode":  returnCode,
	}
	c.syncTime.With(l).Observe(syncTime.Seconds())

	if exist := c.codeMaps.IsCodeExists(obj, returnCode); !exist {
		c.codeMaps.AddCode(obj, returnCode)
	}
}

func (c *ControllerCollector) RemoveObjectMetrics(obj types.Object) {
	returnCodeMap := c.codeMaps.GetCodeMap(obj)
	for returnCode := range returnCodeMap {
		l := prometheus.Labels{
			"serviceType": obj.Kind(),
			"service":     obj.String(),
			"returnCode":  returnCode,
		}
		c.syncOperation.Delete(l)
		c.syncTime.Delete(l)
	}
	c.codeMaps.RemoveCodeMap(obj)
}

func (c *ControllerCollector) SetOperationalCount(serviceType string, operationalMap map[string]int) {
	l := prometheus.Labels{}

	for operationalName, operationalCount := range operationalMap {
		count := float64(operationalCount)
		l["serviceType"] = serviceType
		l["operational"] = operationalName
		c.operational.With(l).Set(count)
	}
}

// IncSyncCount increment the reconcile update counter
func (c *ControllerCollector) IncNodeChangeCount(nodename string) {
	l := prometheus.Labels{
		"nodename": nodename,
	}
	c.nodeChange.With(l).Inc()
}

// RemoveMetrics removes metrics for service that have been removed
func (c *ControllerCollector) RemoveMetrics(obj types.Object) {
	l := prometheus.Labels{
		"serviceType": obj.Kind(),
		"service":     obj.String(),
	}
	c.syncOperation.Delete(l)
}

// IncWebhookRequestCount increment the task request counter
func (c *ControllerCollector) IncWebhookRequestCount(l prometheus.Labels) {
	c.webhookRequest.With(l).Inc()
}

// UpdateWebhookDelayTime update the task call delay
func (c *ControllerCollector) UpdateWebhookDelayTime(l prometheus.Labels, delayTime time.Duration) {
	c.webhookDelayTime.With(l).Observe(delayTime.Seconds())
}

// Collect implements the prometheus.Collector interface.
func (c *ControllerCollector) Collect(ch chan<- prometheus.Metric) {
	c.syncOperation.Collect(ch)
	c.syncTime.Collect(ch)
	c.nodeChange.Collect(ch)
	c.operational.Collect(ch)
	c.webhookRequest.Collect(ch)
	c.webhookDelayTime.Collect(ch)
}

func (c *ControllerCollector) Describe(ch chan<- *prometheus.Desc) {
	c.syncOperation.Describe(ch)
	c.syncTime.Describe(ch)
	c.nodeChange.Describe(ch)
	c.operational.Describe(ch)
	c.webhookRequest.Describe(ch)
	c.webhookDelayTime.Describe(ch)
}
