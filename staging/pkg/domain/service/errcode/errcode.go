package errcode

import (
	"errors"

	"github.com/samber/lo"

	"git.woa.com/kateway/pkg/domain/types"
)

var Registry = types.NewErrorRegistry()

func Register(errorCode types.ErrorCode) types.ErrorCode {
	return Registry.Register(errorCode)
}

var (
	HealthCheckNotReadyError          = errors.New("LoadBalancerHealth not ready")
	HealthCheckServiceRSNotFoundError = errors.New("LoadBalancerHealth service rs not found")
	HealthCheckIngressRSNotFoundError = errors.New("LoadBalancerHealth ingress rs not found")
)

const (
	ServiceSuccessCode = "S2000"

	ServiceNetTimeoutErrorCode = "E6006"
	ServiceNetOtherErrorCode   = "E6008"
	ServiceOtherErrorCode      = "E6009"

	ServiceCLBErrorCode      = "E5003"
	ServiceCVMErrorCode      = "E5004"
	ServiceTagErrorCode      = "E5005"
	ServiceVPCErrorCode      = "E5006"
	ServiceNormErrorCode     = "E5007"
	ServiceTKEErrorCode      = "E5008"
	NormDashboardErrorCode   = "E5009"
	NormUnkonwErrorCode      = "E5010"
	AuthErrorCode            = "E5011"
	InternalErrorCode        = "E5012"
	FailedOperationErrorCode = "E5013"

	QuotaLimitExceededErrorCode                   = "E4004"
	NormRequestResultErrorCode                    = "E4007" // record not exist,maybe ipmasq misconfig -8017
	NormAssumeTkeCredentialErrorCode              = "E4008" // data is nil,no tke ccs role -8002
	ResoucreInsufficientErrorCode                 = "E4009"
	RequestLimitExceededErrorCode                 = "E4010"
	InvalidActionErrorCode                        = "E4011"
	InvalidParameterErrorCode                     = "E4012"
	DryRunErrorCode                               = "E4013"
	OutOfMoneyErrorCode                           = "E4014"
	ConcurrentOperationErrorCode                  = "E4015"
	BackendConfigAnnontationFormateErrorErrorCode = "E4016"

	ServiceNetTimeoutErrorMsg = "Network.Timeout"
	ServiceNetOtherErrorMsg   = "Network.UnexpectedError"
	ServiceSuccessMsg         = "Success"

	QcloudApiUnexpectedLegacyErrorCode = "LegacyAPIError.UnexpectedError"
	QcloudApiUnexpectedErrorCode       = "Error.UnexpectedError"

	NormDataIsNilErrorCode      = -8002
	NormRecordNotExistErrorCode = -8017
)

var (
	LegacyErrorCodeToMsgMap = map[int]string{
		4000: "ErrParamInvalid",                  // 请求参数非法	缺少必要参数，或者参数值格式不正确，具体错误信息请查看错误描述 message 字段。
		4100: "ErrAuthFailure",                   // 身份认证失败	身份认证失败，一般是由于签名计算错误导致的。
		4101: "ErrUnauthorizedInterface",         // 未授权访问接口 子账号未被主账号授权访问该接口，请联系主帐号管理员开通接口权限。
		4102: "ErrUnauthorizedResource",          // 未授权访问资源 子账号未被主账号授权访问特定资源，请联系主帐号管理员开通资源权限。
		4103: "ErrUnauthorizedInterfaceResource", // 未授权访问当前接口所操作的资源 子账号没有被主账户授权访问该接口中所操作的特定资源，请联系主帐号管理员开通资源权限。
		4104: "ErrSecretIdNotFound",              // 密钥不存在 用于请求的密钥不存在，请确认后重试。
		4105: "ErrTokenFailure",                  // token 错误	token 错误。
		4106: "ErrMFAFailure",                    // MFA 校验失败	MFA 校验失败。
		4110: "ErrUnauthorizedOperation",         // 其他 CAM 鉴权失败	其他 CAM 鉴权失败。
		4200: "ErrRequestExpired",                // 请求过期
		4300: "ErrForbidden",                     // 拒绝访问	帐号被封禁，或者不在接口针对的用户范围内等。
		4400: "ErrRequestLimitExceeded",          // 超过配额	请求的次数超过了配额限制，请 提交工单 解决。
		4500: "ErrReplayAttack",                  // 重放攻击	请求的 Nonce 和 Timestamp 参数用于确保每次请求只会在服务器端被执行一次，所以本次的 Nonce 和上次的不能重复，Timestamp 与腾讯服务器相差不能超过 5 分钟。
		4600: "ErrUnsupportedProtocol",           // 协议不支持 协议不支持，当前 API 仅支持 HTTPS 协议，不支持 HTTP 协议。
		5000: "ErrResourceDoesNotExists",         // 资源不存在 资源标识对应的实例不存在，或者实例已经被退还，或者访问了其他用户的资源。
		5100: "ErrOperationFailure",              // 资源操作失败	对资源的操作失败，具体错误信息请查看错误描述 message 字段，稍后重试或者联系客服人员帮忙解决。
		5200: "ErrPurchaseFailure",               // 资源购买失败	购买资源失败，可能是不支持实例配置，资源不足等等。
		5300: "ErrOutOfMoney",                    // 余额不足	用户帐号余额不足，无法完成购买或升级。
		5400: "ErrPartialSuccess",                // 部分执行成功	批量操作部分执行成功，详情见方法返回值。
		5500: "ErrLackOfQualifications",          // 用户资质审核未通过	购买资源失败，用户资质审核未通过。
		6000: "ErrInternalServerError",           // 服务器内部错误 服务器内部出现错误，请稍后重试或者联系客服人员帮忙解决。
		6100: "ErrUnsupportedVersion",            // 版本暂不支持 本版本内不支持此接口或该接口处于维护状态等。注意：出现这个错误时，请先确定接口的域名是否正确，不同的模块，域名可能不一样。
		6200: "ErrEndpointUnavailable",           // 接口暂时无法访问 当前接口处于停服维护状态，请稍后重试。
	}
)

var (
	Success = Register(types.ErrorCode{Code: "S2000", ReasonDetail: types.SuccessReason, Message: "Success", Retry: false})

	DryRunError                     = Register(types.ErrorCode{Code: "W1000", ReasonDetail: types.DryRunErrorReason, Message: "Service: %s. has modify operations in dry-run mode.", Retry: false})
	BackendTypeEmpty                = Register(types.ErrorCode{Code: "W1001", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. The backend type is empty.", Retry: false})
	BackendTypeUnexpected           = Register(types.ErrorCode{Code: "W1002", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. The backend type(%s) is not support and unexpected.", Retry: false})
	NoAvalibaleNodeForService       = Register(types.ErrorCode{Code: "W1003", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. The cluster has no available node for the service. cluster nodes is not ready or unscheduled.", Retry: false})
	SecurityGroupsNotSupportError   = Register(types.ErrorCode{Code: "W1004", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. annotation: 'service.cloud.tencent.com/security-groups' only support with loadbalancer which created by tke. This is conflict with annotation `service.kubernetes.io/tke-existed-lbid`.", Retry: false})
	PassToTargetNotSupportError     = Register(types.ErrorCode{Code: "W1005", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. annotation: 'service.cloud.tencent.com/pass-to-target' only support with loadbalancer which created by tke. This is conflict with annotation `service.kubernetes.io/tke-existed-lbid`.", Retry: false})
	ReuseConcurrentOperationError   = Register(types.ErrorCode{Code: "W1004", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. Reuse loadbalancer is syncing by other service. wait for syncing.", Retry: true, RetryImmediate: true})
	ServiceWithMultiLoadbalancer    = Register(types.ErrorCode{Code: "W1005", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. Multi loadbalancer found for the service. %s", Retry: true})
	CleanDirtyLabel                 = Register(types.ErrorCode{Code: "W1006", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. Clean label dirty data, The component will automatically retry later.", Retry: true})
	WeightZeroError                 = Register(types.ErrorCode{Code: "W1007", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. All dead triggers all alive.", Retry: false})
	CustomizedWeightAnnotationError = Register(types.ErrorCode{Code: "W1008", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. Annotation 'service.cloud.tencent.com/lb-rs-weight' Unmarshal error.", Retry: false})
	AddressIPVersionError           = Register(types.ErrorCode{Code: "W1009", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. CLB AddressIPVersion Do not match the backend.", Retry: false})
	IntendToRecreateError           = Register(types.ErrorCode{Code: "W1010", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. %s. User intent to recreate CLB. You can recreate the CLB by switching the Service type or recreate the Service", Retry: false})
	EVMUpdatingError                = Register(types.ErrorCode{Code: "W1011", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. The evm pod is updating or pod eni instance is not normal status. wait for syncing again.", Retry: true})
	NodeRSLimitExceeded             = Register(types.ErrorCode{Code: "W1012", ReasonDetail: types.QuotaLimitErrorReason,
		Message: "The number of nodes expected to be registered for %s exceeds the limit(expect total: %d, current quota: %d), some nodes are ignored.",
		Retry:   false,
	})
	ExplicitCLBResourceLeak = Register(types.ErrorCode{Code: "W1013", ReasonDetail: types.LoadbalancerErrorReason,
		Message: "The service %q has been removed, while the CLB instance %q auto-created by TKE is not deleted because the owner of the instance is set to user.",
		Retry:   false,
	})
	NodeNotFoundError = Register(types.ErrorCode{Code: "W1014", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. Can't find node(%s) for pod(%s)", Retry: true})
	SingleNodeRisk    = Register(types.ErrorCode{Code: "W1015", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. Only one node in the backend, may lead to single point of failure.", Retry: false})

	NetworkTimeoutError  = Register(types.ErrorCode{Code: "E6006", ReasonDetail: types.UnexpectedErrorReason, Message: "Unexpected Network Timeout.", Retry: true})
	NetworkError         = Register(types.ErrorCode{Code: "E6008", ReasonDetail: types.UnexpectedErrorReason, Message: "Unexpected Network Error.", Retry: true})
	UnexpectedError      = Register(types.ErrorCode{Code: "E6009", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. UnexpectedError.", Retry: true})
	CLBResourceLeakError = Register(types.ErrorCode{Code: "E6010", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. CLB Resource Idempotent Error.", Retry: true})
	MockError            = Register(types.ErrorCode{Code: "E6000", ReasonDetail: types.SuccessReason, Message: "%s\t%s", Retry: false})

	ServiceCLBError           = Register(types.ErrorCode{Code: "E5003", ReasonDetail: types.DependenceErrorReason, Message: "CLB Service Internal Error. %s", Retry: true})
	ServiceCVMError           = Register(types.ErrorCode{Code: "E5004", ReasonDetail: types.DependenceErrorReason, Message: "CVM Service Internal Error. %s", Retry: true})
	ServiceTagError           = Register(types.ErrorCode{Code: "E5005", ReasonDetail: types.DependenceErrorReason, Message: "TAG Service Internal Error. %s", Retry: true})
	ServiceNormError          = Register(types.ErrorCode{Code: "E5007", ReasonDetail: types.DependenceErrorReason, Message: "NORM Service Internal Error. %s", Retry: true})
	ServiceTKEError           = Register(types.ErrorCode{Code: "E5008", ReasonDetail: types.DependenceErrorReason, Message: "TKE Service Internal Error. %s", Retry: true})
	ServiceVPCError           = Register(types.ErrorCode{Code: "E5009", ReasonDetail: types.DependenceErrorReason, Message: "VPC Service Internal Error. %s", Retry: true})
	ServiceCLBUnexpectedError = Register(types.ErrorCode{Code: "E5010", ReasonDetail: types.DependenceErrorReason, Message: "CLB Service Unexpected Error. %s", Retry: true})
	SSLInternalError          = Register(types.ErrorCode{Code: "E5011", ReasonDetail: types.DependenceErrorReason, Message: "SSL Service Internal Error. %s", Retry: true})
	CLBBackendError           = Register(types.ErrorCode{Code: "E5012", ReasonDetail: types.DependenceErrorReason, Message: "CLB Backend Register Or Deregister Error. RequestId: %s", Retry: true})
	AdmitError                = Register(types.ErrorCode{Code: "E5013", ReasonDetail: types.DependenceErrorReason, Message: "Admit Service Prevent. %s", Retry: true})

	RequestLimitExceeded                         = Register(types.ErrorCode{Code: "E4000", ReasonDetail: types.QuotaLimitErrorReason, Message: "Action: %s RequestLimitExceeded.", Retry: true})
	NormTKEQCSRoleNotExistError                  = Register(types.ErrorCode{Code: "E4001", ReasonDetail: types.DependenceErrorReason, Message: "CAM authorization role(TKE_QCSRole) has been revoked or changed.", Retry: true})
	NormIpMasqAgentConfigChangedError            = Register(types.ErrorCode{Code: "E4002", ReasonDetail: types.DependenceErrorReason, Message: "The ip-masq-agent-config may be changed. can not take the TKE credential.", Retry: true})
	LoadBalancerNotExistError                    = Register(types.ErrorCode{Code: "E4003", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The loadbalancer has been deleted during synchronization.", Retry: true})
	LoadBalancerLimitExceededError               = Register(types.ErrorCode{Code: "E4004", ReasonDetail: types.QuotaLimitErrorReason, Message: "Service: %s. The number of loadbalancer reached the upper limit.", Retry: true})
	LoadBalancerCreateParamFormatError           = Register(types.ErrorCode{Code: "E4005", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The params of loadbalancer format invalid. (CreateLoadBalancer)", Retry: true})
	LoadBalancerCreateParamAddressIPVersionError = Register(types.ErrorCode{Code: "E4006", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The value of parameter AddressIPVersion invalid. (CreateLoadBalancer)", Retry: false})
	DoNotSupportIPv6Error                        = Register(types.ErrorCode{Code: "E4007", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. This region do not support the IPv6. (CreateLoadBalancer)", Retry: false})
	LoadBalancerSubnetIPInsufficientError        = Register(types.ErrorCode{Code: "E4008", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The number of IP in subnet is not enough. (CreateLoadBalancer)", Retry: true})
	InsufficientAccountBalanceError              = Register(types.ErrorCode{Code: "E4009", ReasonDetail: types.QuotaLimitErrorReason, Message: "Service: %s. Insufficient account balance.", Retry: true})
	ReuseFormatInvalidError                      = Register(types.ErrorCode{Code: "E4010", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The loadbalancer id in annotation `service.kubernetes.io/tke-existed-lbid` is format invalid.", Retry: false})

	ReuseLBNotExistError        = Register(types.ErrorCode{Code: "E4011", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The loadbalancer in annotation `service.kubernetes.io/tke-existed-lbid` is not exist.", Retry: true})
	ReuseAlreadyInUsedError     = Register(types.ErrorCode{Code: "E4012", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The loadbalancer in annotation `service.kubernetes.io/tke-existed-lbid` is managed by other service or ingress.", Retry: true})
	BelongToAnotherClusterError = Register(types.ErrorCode{Code: "E4013", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The loadbalancer is used by other cluster.", Retry: true})
	ReuseConflictListenerError  = Register(types.ErrorCode{Code: "E4014", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The loadbalancer in annotation `service.kubernetes.io/tke-existed-lbid` has port conflict with other service. conflict listener info(%s)", Retry: true})
	//  ReuseConcurrentOperationError     = Register(types.ErrorCode{Code: "E4015", Message: "Service: %s. Reuse loadbalancer is syncing by other service. wait for syncing.", Retry: true})
	ReuseFeatureNotSupportError       = Register(types.ErrorCode{Code: "E4016", ReasonDetail: types.WhitelistErrorReason, Message: "Service: %s. The loadbalancer in annotation `service.kubernetes.io/tke-existed-lbid` can not reuse. reuse feature is turn off.", Retry: true})
	DirectAccessClassicalSupportError = Register(types.ErrorCode{Code: "E4017", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. Direct access do not support the classical loadbalancer.", Retry: false})
	// InternalLoadBalanceNotSupportIPv6Error  = Register(types.ErrorCode{Code: "E4018", Message: "Internal loadbalancer do not support the ipv6.", Retry: false})
	// ClassicalLoadBalanceNotSupportIPv6Error = Register(types.ErrorCode{Code: "E4019", Message: "classical loadbalancer do not support the ipv6.", Retry: false})
	LoadBalancerListenerLimitExceededError = Register(types.ErrorCode{Code: "E4020", ReasonDetail: types.QuotaLimitErrorReason, Message: "Service: %s. The number of loadbalancer listener reached the upper limit.", Retry: true})

	CreateLoadBalancerListenerCertFormatError   = Register(types.ErrorCode{Code: "E4021", ReasonDetail: types.CertErrorReason, Message: "Service: %s. The params of loadbalancer listener certId format invalid. (CreateLoadBalancerListener)", Retry: false})
	CreateLoadBalancerListenerCertNotExistError = Register(types.ErrorCode{Code: "E4022", ReasonDetail: types.CertErrorReason, Message: "Service: %s. The params of loadbalancer listener cert not exist. (CreateLoadBalancerListener)", Retry: true})
	CreateLoadBalancerListenerCertStatusError   = Register(types.ErrorCode{Code: "E4023", ReasonDetail: types.CertErrorReason, Message: "Service: %s. The params of loadbalancer listener cert status error. (CreateLoadBalancerListener)", Retry: true})
	ExtensiveParametersFormatError              = Register(types.ErrorCode{Code: "E4024", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. Annotation `service.kubernetes.io/service.extensiveParameters` format error. invalid json string.", Retry: false})
	ListenerParametersFormatError               = Register(types.ErrorCode{Code: "E4025", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. Annotation `service.kubernetes.io/service.listenerParameters` format error. invalid json string.", Retry: false})
	TkeServiceConfigNotFoundError               = Register(types.ErrorCode{Code: "E4026", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. TkeServiceConfig(%s) in Annotation `service.cloud.tencent.com/tke-service-config` resource not found error.", Retry: false})
	LoadBalancerNotReadyError                   = Register(types.ErrorCode{Code: "E4027", ReasonDetail: types.DependenceErrorReason, Message: "Service: %s. The loadbalancer status is not normal, wait for loadbalancer ready.", Retry: true})
	IngressRulesHostEmptyError                  = Register(types.ErrorCode{Code: "E4028", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress rule host empty error. IPv6 do not have default domain.", Retry: false})
	// IngressRulesHttpEmptyError                  = Register(types.ErrorCode{Code: "E4029", Message: "Ingress: %s. Ingress rule http empty error.", Retry: false})
	IngressAnnotationHttpRuleError = Register(types.ErrorCode{Code: "E4030", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress annotation invalid. 'annotation: kubernetes.io/ingress.http-rules' is invalid.", Retry: false})

	IngressAnnotationHttpsRuleError      = Register(types.ErrorCode{Code: "E4031", ReasonDetail: types.AnnotationErrorReason, Message: "Ingress: %s. Ingress annotation invalid. 'annotation: kubernetes.io/ingress.https-rules' is invalid.", Retry: false})
	TkeServiceConfigCRDCreateError       = Register(types.ErrorCode{Code: "E4032", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. TkeServiceConfigCRD CRD Create Error.", Retry: false})
	DirectAccessNoEndpointError          = Register(types.ErrorCode{Code: "E4033", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. Service used direct access but has no ENI pods.", Retry: false})
	ReadinessServerSecretError           = Register(types.ErrorCode{Code: "E4034", ReasonDetail: types.UnexpectedErrorReason, Message: "Readiness server secret not found. please restart the service to rebuild the secret.", Retry: false})
	BackendOverLimitError                = Register(types.ErrorCode{Code: "E4035", ReasonDetail: types.QuotaLimitErrorReason, Message: "Service: %s. The number of backend reached the upper limit.", Retry: false})
	BackendConflictError                 = Register(types.ErrorCode{Code: "E4036", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. The backend is conflict in a clb. the (vip\\protocol\\backendIP\\backendPort)  should be unique.", Retry: true})
	LoadBalancerParamSubnetNotExistError = Register(types.ErrorCode{Code: "E4037", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. User LoadBalancer Subnet was not this region or not exist.", Retry: false})
	RuleFormatError                      = Register(types.ErrorCode{Code: "E4038", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s Ingress rule invalid. Invalid path.", Retry: false})
	RuleProtocolError                    = Register(types.ErrorCode{Code: "E4039", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s Listener type was L4 (TCP/UDP). It should be (L7) HTTP/HTTPS Listener.", Retry: true})
	RuleLimitExceeded                    = Register(types.ErrorCode{Code: "E4040", ReasonDetail: types.QuotaLimitErrorReason, Message: "Service: %s User LoadBalancerRule LimitExceeded Listener %s", Retry: true})

	TkeServiceConfigAutoAnnontationError = Register(types.ErrorCode{Code: "E4041", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/tke-service-config-auto' should be true or false.", Retry: false})
	TkeServiceConfigConflictError        = Register(types.ErrorCode{Code: "E4042", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/tke-service-config' must not suffix with '-auto-ingress-config' or '-auto-service-config' or ''-auto-multiclusterservice-config''", Retry: false})
	TLSSecretEmptyError                  = Register(types.ErrorCode{Code: "E4043", ReasonDetail: types.CertErrorReason, Message: "Service: %s TCP_SSL、QUIC listener must specify a secret for cert.", Retry: false})
	SecretNotFoundError                  = Register(types.ErrorCode{Code: "E4044", ReasonDetail: types.CertErrorReason, Message: "Service: %s TCP_SSL、HTTPS or QUIC listener used secret(%s) is not found.", Retry: false})
	SecretContentError                   = Register(types.ErrorCode{Code: "E4045", ReasonDetail: types.CertErrorReason, Message: "Service: %s TCP_SSL、HTTPS or QUIC listener used secret(%s) has no data of 'qcloud_cert_id'.", Retry: false})
	InvalidProtocolError                 = Register(types.ErrorCode{Code: "E4046", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s Do not support the protocol(%s). ValidProtocol:TCP,UDP,TCP_SSL,QUIC,HTTP,HTTPS", Retry: false})
	// TLSSNISecretEmptyError               = Register(types.ErrorCode{Code: "E4047", Message: "Service: %s HTTPS listener must specify at least one domain.", Retry: false})
	DuplicateHostError     = Register(types.ErrorCode{Code: "E4048", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s HTTPS listener has declare duplicate host(%s).", Retry: false})
	RuleHostFormatError    = Register(types.ErrorCode{Code: "E4049", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s HTTPS listener host is invalid. host(%s) do not match the regex '(\\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)+'", Retry: false})
	DuplicateProtocolError = Register(types.ErrorCode{Code: "E4050", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s HTTPS listener has declare duplicate protocol(%s).", Retry: false})

	// TKExGraceShutdownAnnotationError      = Register(types.ErrorCode{Code: "E4051", Message: "Service: %s Annotation 'service.cloud.tencent.com/enable-grace-shutdown-tkex' should be true or false.", Retry: false})
	// TKEGraceShutdownAnnotationError       = Register(types.ErrorCode{Code: "E4052", Message: "Service: %s Annotation 'service.cloud.tencent.com/enable-grace-shutdown' should be true or false.", Retry: false})
	// GraceShutdownAnnotationError          = Register(types.ErrorCode{Code: "E4053", Message: "Service: %s Annotation 'service.cloud.tencent.com/enable-grace-shutdown' and 'service.cloud.tencent.com/enable-grace-shutdown-tkex' can't be both true.", Retry: false})
	ConflictProtocolError                 = Register(types.ErrorCode{Code: "E4054", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s HTTPS listener has conflict protocol. TCP,TCP_SSL,HTTP,HTTPS can not create listener in the same port. UDP,QUIC can not create listener in the same port.", Retry: false})
	ConflictListenerError                 = Register(types.ErrorCode{Code: "E4055", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s The loadbalancer has port conflict in the lb %s port %d.", Retry: true})
	SnatProNotSupportError                = Register(types.ErrorCode{Code: "E4056", ReasonDetail: types.WhitelistErrorReason, Message: "Service: %s The user account do not support the SNATPro.", Retry: true})
	LoadBalancerResourceInsufficientError = Register(types.ErrorCode{Code: "E4057", ReasonDetail: types.QuotaLimitErrorReason, Message: "Service: %s User LoadBalancer ResourceInsufficient.", Retry: true})
	LoadBalancerNameFormateError          = Register(types.ErrorCode{Code: "E4058", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s User LoadBalancer Name was too long to conflict.", Retry: true})
	ServiceSpecifyProtocolHostEmptyError  = Register(types.ErrorCode{Code: "E4059", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s. Service specify protocol rule host is empty. IPv6 do not have default domain.", Retry: false})

	TkeServiceConfigContentError                 = Register(types.ErrorCode{Code: "E4060", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. tke service config content error. %s", Retry: false})
	CreateLoadBalancerListenerCertTypeError      = Register(types.ErrorCode{Code: "E4061", ReasonDetail: types.CertErrorReason, Message: "Service: %s. Certificate type is Error.", Retry: true})
	CreateLoadBalancerListenerCertOutOfDateError = Register(types.ErrorCode{Code: "E4062", ReasonDetail: types.CertErrorReason, Message: "Service: %s. Certificate out of date Error.", Retry: true})
	BackendsLabelAnnotationError                 = Register(types.ErrorCode{Code: "E4063", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. Annotation 'service.kubernetes.io/qcloud-loadbalancer-backends-label' is not invalid label selector.", Retry: false})
	// CustomizedWeightAnnotationError              = Register(types.ErrorCode{Code: "E4064", Message: "Service: %s. Annotation 'service.cloud.tencent.com/lb-rs-weight' Unmarshal error."})
	// ReuseBelongToAnotherIngressError        = Register(types.ErrorCode{Code: "E4065", Message: "Service: %s. The loadbalancer in annotation `service.kubernetes.io/tke-existed-lbid` is used by other ingress.", Retry: true})
	ClassicLoadBalancerNotSupportedError = Register(types.ErrorCode{Code: "E4066", ReasonDetail: types.ConfigurationErrorReason, Message: "The loadbalancer in annotation `service.kubernetes.io/tke-existed-lbid` is a classic loadbalancer, and EKS\\MultiClusterService does NOT support reuse classic loadbalancers.", Retry: false})
	CrossRegionConfigError               = Register(types.ErrorCode{Code: "E4067", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. Cross region config error. use annotation `service.cloud.tencent.com/cross-region-id` with `service.cloud.tencent.com/cross-vpc-id` or `service.kubernetes.io/tke-existed-lbid`.", Retry: false})
	CrossRegionCNNConfigError            = Register(types.ErrorCode{Code: "E4068", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. Cross region config error. config must meet the following conditions simultaneously: a. The VPC where the load balancer is located must be in the same CCN as the VPC where the cluster is located. b. The VPC must be in the same APPID as the TKE cluster (cross APPID VPCs are not supported).", Retry: true})
	LoadBalancerDoNotSupportCNNError     = Register(types.ErrorCode{Code: "E4069", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. Cross region config error. loadbalancer do not has vpc attributes, can not link to the CCN.", Retry: false})

	NetworkNotRegisterToCNNError          = Register(types.ErrorCode{Code: "E4070", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. The container network is not registered to the CCN.", Retry: true})
	NodeInsufficient                      = Register(types.ErrorCode{Code: "E4071", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. Some node in cluster is locked or insufficient balance, please remove or unscheduled these node.", Retry: false})
	QUICNotSupportError                   = Register(types.ErrorCode{Code: "E4072", ReasonDetail: types.WhitelistErrorReason, Message: "Service: %s. The user account do not support the QUIC Protocol. whitelist: CLB_QUIC_Listener", Retry: false})
	QUICConflicWithHttpsQUIC              = Register(types.ErrorCode{Code: "E4073", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s. port conflict with HTTPS Listener. the HTTPS Listener maybe open the QUIC Support.", Retry: false})
	SnatProNotSupportForLoadbalancerError = Register(types.ErrorCode{Code: "E4074", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. The Loadbalancer do not support the SnatPro.", Retry: true})
	CrossRegionInvalidReginError          = Register(types.ErrorCode{Code: "E4075", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. Cross region config error. Invalid regionId in annotation `service.cloud.tencent.com/cross-region-id` %s", Retry: true})
	// CleanDirtyLabel   = Register(types.ErrorCode{Code: "E4076", Message: "Service: %s. Clean label dirty data, The component will automatically retry later.", Retry: true})
	// LabelStoredError  = Register(types.ErrorCode{Code: "E4077", Message: "Service: %s. The label data failed to be stored within one minute. The component will automatically retry later.", Retry: true})
	RegexPathIllegal  = Register(types.ErrorCode{Code: "E4078", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Regex path is illegal, path can only use the letter、number and character -/.\\%%?=:#&^*[]$. and it should be a legal regular expression. invalid path: %s", Retry: false})
	PathIllegal       = Register(types.ErrorCode{Code: "E4079", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Exact path is illegal, path can only use the letter、number and character -/.\\%%?=:#&. or use it with NonAbsolutePath. invalid path: %s", Retry: false})
	PathLengthIllegal = Register(types.ErrorCode{Code: "E4080", ReasonDetail: types.ListenerErrorReason, Message: "Ingress: %s. Path length cannot exceed 200.", Retry: false})

	PassToTargetAnnotationError                = Register(types.ErrorCode{Code: "E4072", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. annotation: 'service.cloud.tencent.com/pass-to-target' is invalid. should be true or false.", Retry: false})
	SecurityGroupsAnnotationError              = Register(types.ErrorCode{Code: "E4073", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. annotation: 'service.cloud.tencent.com/security-groups' is invalid. security group id is over limit. all of the security group id should join by ','", Retry: false})
	ModificationProtectionAnnontationError     = Register(types.ErrorCode{Code: "E4081", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/modification-protection' should be true or false.", Retry: false})
	ReuseNotSupportModificationProtectionError = Register(types.ErrorCode{Code: "E4082", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/modification-protection' do not support the service with reuse loadbalancer.", Retry: false})
	ModificationProtectionConflictError        = Register(types.ErrorCode{Code: "E4083", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s Loadbalancer was protected by user or another service.", Retry: true})
	ModificationProtectionDoNotSupportError    = Register(types.ErrorCode{Code: "E4084", ReasonDetail: types.WhitelistErrorReason, Message: "Service: %s Please check the white list about the CLB. Function modification protection may not be open.", Retry: true})
	PreventLoopbackAnnontationError            = Register(types.ErrorCode{Code: "E4085", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/prevent-loopback' should be true or false.", Retry: false})
	NoSelectorService                          = Register(types.ErrorCode{Code: "E4086", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. LoadBalancer Service without label selector won't have any endpoint, even if you set Endpoints.", Retry: false})
	HybridCloudWithoutSnatProSetting           = Register(types.ErrorCode{Code: "E4087", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. Service use the 'service.cloud.tencent.com/hybrid-type', but not setting the snatIp from annotation 'service.cloud.tencent.com/snat-pro-info', please read the document for usage help.", Retry: false})
	CrossRegionWithoutSnatProSetting           = Register(types.ErrorCode{Code: "E4088", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. Service use the 'service.cloud.tencent.com/cross-type', but not setting the snatIp from annotation 'service.cloud.tencent.com/snat-pro-info', please read the document for usage help.", Retry: false})
	SnatIPLimitExceeded                        = Register(types.ErrorCode{Code: "E4089", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. Service use the 'service.cloud.tencent.com/snat-pro-info', snat ip can't exceed 10.", Retry: false})

	MixIpTargetError                       = Register(types.ErrorCode{Code: "E4090", ReasonDetail: types.BackendErrorReason, Message: "Service: %s. open MixIpTarget error, FullChain IPv6 CLB should open MixIpTarget before open SNATPro.", Retry: true})
	SecurityGroupsFormatError              = Register(types.ErrorCode{Code: "E4091", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. annotation: 'service.cloud.tencent.com/security-groups' is invalid. any of security group id format error.", Retry: false})
	SecurityGroupsNotExistError            = Register(types.ErrorCode{Code: "E4092", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. annotation: 'service.cloud.tencent.com/security-groups' is invalid. any of security group id is not exist.", Retry: false})
	HybridCNNConfigErrorError              = Register(types.ErrorCode{Code: "E4093", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. annotation: 'service.cloud.tencent.com/hybrid-type:CCN'. VPC do not join the CCN.", Retry: true})
	ServiceCloseNodePortsError             = Register(types.ErrorCode{Code: "E4094", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. service allocateLoadBalancerNodePorts is false.", Retry: false})
	LoadBalancerClientTokenHasExpiredError = Register(types.ErrorCode{Code: "E4095", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s. The ClientToken in annotation `service.cloud.tencent.com/client-token` has expired.", Retry: true})
	ServiceSpecifyProtocolFormatError      = Register(types.ErrorCode{Code: "E4096", ReasonDetail: types.ListenerErrorReason, Message: "Service: %s. Service specify protocol is invalid json format.", Retry: false})
	BackendManageOnlyAnnontationError      = Register(types.ErrorCode{Code: "E4097", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/backend-manage-only' should be true or false.", Retry: false})
	BackendManageOnlyMustUseExistLBError   = Register(types.ErrorCode{Code: "E4098", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/backend-manage-only'. should use master cluster existed clb.", Retry: false})
	BackendManageOnlyListenerNotExistError = Register(types.ErrorCode{Code: "E4099", ReasonDetail: types.BackendErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/backend-manage-only'. listener(%s) not exist", Retry: true})
	MultiClusterDeleteWithBackend          = Register(types.ErrorCode{Code: "E4100", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s MultiClusterService is deleted, but backend is not empty, controller prevent to delete. ", Retry: true})
	NotBelongToSpecifiedClusterError       = Register(types.ErrorCode{Code: "E4101", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s is used 'service.cloud.tencent.com/from-other-cluster' specified from cluster, but clb is not belong the cluster(%s).", Retry: true})
	RSLimitExceeded                        = Register(types.ErrorCode{Code: "E4102", ReasonDetail: types.QuotaLimitErrorReason,
		Message: "The number of rs expected to be register for %s exceeds the limit(expect total: %d, current quota: %d), some rs are ignored.",
		Retry:   false,
	})
	InvalidHealthCheckDomainError = Register(types.ErrorCode{Code: "E4107", ReasonDetail: types.ConfigurationErrorReason, Message: "Service: %s. The health check domain of the regex or wildcard domains must be an exact domain.", Retry: false})

	PatchRSTagAnnontationError = Register(types.ErrorCode{Code: "E4103", ReasonDetail: types.LoadbalancerErrorReason,
		Message: "Service %s intents to patch RS tags but failed",
		Retry:   true})

	DeletionProtectionError  = Register(types.ErrorCode{Code: "E5015", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s Annotation 'service.cloud.tencent.com/deletion-protection' denied to delete clb when enabling deletion protect.", Retry: true})
	ForbiddenDeletionError   = Register(types.ErrorCode{Code: "E5016", ReasonDetail: types.LoadbalancerErrorReason, Message: "Service: %s cannot be deleted by clb policies.", Retry: true})
	EndpointsAnnotationError = Register(types.ErrorCode{Code: "E4104", ReasonDetail: types.AnnotationErrorReason, Message: "Service: %s. The annotation `service.cloud.tencent.com/loadbalancer-source-endpoints` is invalid.", Retry: false})
	EndpointsOperationError  = Register(types.ErrorCode{Code: "E4105", ReasonDetail: types.UnexpectedErrorReason, Message: "Service: %s. Operate endpoints failed.", Retry: true})
)

func IsRSLimitExceededErrOrWarn(err error) bool {
	ingErr, ok := lo.ErrorsAs[*types.Error](err)
	if !ok {
		return false
	}
	return ingErr.ErrorCode.Code == NodeRSLimitExceeded.Code || ingErr.ErrorCode.Code == RSLimitExceeded.Code
}

func IsLoadBalancerNotExistError(err error) bool {
	e, ok := lo.ErrorsAs[*types.Error](err)
	if !ok {
		return false
	}
	return e.ErrorCode.Code == LoadBalancerNotExistError.Code
}

func ContainsRSLimitExceededErrOrWarn(errs []error) bool {
	_, exists := lo.Find(errs, IsRSLimitExceededErrOrWarn)
	return exists
}
