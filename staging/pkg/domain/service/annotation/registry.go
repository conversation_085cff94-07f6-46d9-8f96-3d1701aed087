package annotation

import "git.woa.com/kateway/pkg/k8s/k8sutil/annotation"

const (
	k8sPrefix       = "service.kubernetes.io"
	defaultPrefix   = "service.cloud.tencent.com"
	exampleTemplate = `
apiVersion: v1
kind: Service
metadata:
  annotations:
{{- range .}}
    {{. -}}
{{end}}
  name: example
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: example
  type: LoadBalancer
`
)

type Schema interface {
	List(options annotation.ListOptions) annotation.Items
	IsReadOnly(key string) bool

	StatusConditions() string
	SyncBeginTime() string
	SyncEndTime() string
	Mode() string
	DeletionProtection() string
	ChaosErrorcode() string
	ForceDelete() string
	LoadbalancerSourceEndpoints() string
}

var service *ServiceRegistry

type ServiceRegistry struct {
	*annotation.Registry
}

func Service() Schema {
	return service
}

func (s *ServiceRegistry) Register(items annotation.Items) {
	s.Registry.Register(items)
}

func (s *ServiceRegistry) List(options annotation.ListOptions) annotation.Items {
	return s.Registry.List(options)
}

func (s *ServiceRegistry) StatusConditions() string {
	return s.Registry.Get("status.conditions").String()
}

func (s *ServiceRegistry) IsReadOnly(key string) bool {
	return s.Registry.IsReadOnly(key)
}

func (s *ServiceRegistry) SyncBeginTime() string {
	return s.GetByCaller().String()
}

func (s *ServiceRegistry) SyncEndTime() string {
	return s.GetByCaller().String()
}

func (s *ServiceRegistry) Mode() string {
	return s.GetByCaller().String()
}

func (s *ServiceRegistry) DeletionProtection() string {
	return s.GetByCaller().String()
}

func (s *ServiceRegistry) ChaosErrorcode() string {
	return s.GetByCaller().String()
}
func (s *ServiceRegistry) ForceDelete() string {
	return s.GetByCaller().String()
}
func (s *ServiceRegistry) LoadbalancerSourceEndpoints() string {
	return s.GetByCaller().String()
}
