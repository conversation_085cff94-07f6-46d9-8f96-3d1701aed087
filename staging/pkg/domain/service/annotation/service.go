package annotation

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Interface interface {
	// FilterReadOnly 过滤掉只读的注解，在触发更新回调时，用来判断是否需要更新。
	// 现在不相关annotation更新也会导致入队，未来考虑使用明确功能定义注解来决定是否更新。
	FilterReadOnly() map[string]string
	SetStatusConditions(conditions []metav1.Condition)
	SetSyncBeginTime(t time.Time)
	GetSyncBeginTime() (time.Time, error)
	SetSyncEndTime(t time.Time)
	GetSyncEndTime() (time.Time, error)
	Mode() string
	DeletionProtection() *bool
	GetChaosErrorcode() string
	ForceDelete() bool
	LoadbalancerSourceEndpoints() (*EndpointsBinding, error)
}

type ServiceAnnotations struct {
	Items *map[string]string
}

func New(annotations *map[string]string) *ServiceAnnotations {
	if *annotations == nil {
		*annotations = map[string]string{}
	}
	return &ServiceAnnotations{
		Items: annotations,
	}
}

func (a *ServiceAnnotations) get(key string) (value string, ok bool) {
	if a.Items == nil {
		return "", false
	}

	if *a.Items == nil {
		return "", false
	}

	value, ok = (*a.Items)[key] // TODO: 实现根据key解码

	return
}

func (a *ServiceAnnotations) set(key string, value any) {
	if a.Items == nil {
		a.Items = &map[string]string{}
	}

	(*a.Items)[key] = cast.ToString(value) // TODO: 实现根据key value编码
}

func (a *ServiceAnnotations) parseTime(value string) (time.Time, error) {
	if value == "" {
		return time.Time{}, fmt.Errorf("empty time")
	}

	t, err := time.Parse(time.RFC3339, value)
	if err != nil {
		return time.Time{}, fmt.Errorf("parse %s error: %w", value, err)
	}

	return t, nil
}

func (a *ServiceAnnotations) parseBool(value string) (bool, error) {
	switch value {
	case "true", "TRUE", "True":
		return true, nil
	case "false", "FALSE", "False":
		return false, nil
	}
	return false, fmt.Errorf("parse bool: %s error", value)
}

func parseJson[T any](val string) (*T, error) {
	var result T
	if err := json.Unmarshal([]byte(val), &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal %s: %w", val, err)
	}
	return &result, nil
}

func (a *ServiceAnnotations) FilterReadOnly() map[string]string {
	if a.Items == nil {
		return nil
	}

	annotations := map[string]string{}

	for k, v := range *a.Items {
		if !Service().IsReadOnly(k) {
			annotations[k] = v
		}
	}

	return annotations
}

func (a *ServiceAnnotations) SetSyncBeginTime(t time.Time) {
	a.set(Service().SyncBeginTime(), t.Format(time.RFC3339))
}

func (a *ServiceAnnotations) GetSyncBeginTime() (time.Time, error) {
	v, _ := a.get(Service().SyncBeginTime())
	return a.parseTime(v)
}

func (a *ServiceAnnotations) SetSyncEndTime(t time.Time) {
	a.set(Service().SyncEndTime(), t.Format(time.RFC3339))
}

func (a *ServiceAnnotations) GetSyncEndTime() (time.Time, error) {
	v, _ := a.get(Service().SyncEndTime())
	return a.parseTime(v)
}

func (a *ServiceAnnotations) SetStatusConditions(conditions []metav1.Condition) {
	a.set(Service().StatusConditions(), conditions)
}

func (a *ServiceAnnotations) Mode() string {
	v, _ := a.get(Service().Mode())
	return v
}

func (a *ServiceAnnotations) DeletionProtection() *bool {
	v, exist := a.get(Service().DeletionProtection())
	if !exist {
		return nil
	}

	enabled, err := a.parseBool(v)
	if err != nil {
		return nil
	}

	return &enabled
}

func (a *ServiceAnnotations) ForceDelete() bool {
	v, exist := a.get(Service().ForceDelete())
	if !exist {
		return false
	}

	enabled, err := a.parseBool(v)
	if err != nil {
		return false
	}

	return enabled
}

func (a *ServiceAnnotations) GetChaosErrorcode() string {
	v, _ := a.get(Service().ChaosErrorcode())
	return v
}

// EndpointsBinding 定义endpoints绑定配置
type EndpointsBinding struct {
	// Endpoints名称（必填）
	Name string `json:"name"`

	// 是否同步endpoints内容到同名service
	// 可选，默认false（当字段不存在或值为空时自动设为默认值）
	Sync *bool `json:"sync,omitempty"`
}

var ErrAnnoKeyNotFound = errors.New("annotation key not found")

func (a *ServiceAnnotations) LoadbalancerSourceEndpoints() (*EndpointsBinding, error) {
	v, exist := a.get(Service().LoadbalancerSourceEndpoints())
	if !exist {
		return nil, fmt.Errorf("load balancer source endpoints err: %w", ErrAnnoKeyNotFound)
	}
	binding, err := parseJson[EndpointsBinding](v)
	if err != nil {
		return nil, fmt.Errorf("parse load balancer source endpoints err: %w", err)
	}
	return binding, nil
}
