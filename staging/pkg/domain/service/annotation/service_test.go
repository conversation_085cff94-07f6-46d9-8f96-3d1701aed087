package annotation

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestServiceRegistry_SyncBeginTime(t *testing.T) {
	item := Service().SyncBeginTime()
	fmt.Println(item)
	assert.Equal(t, "service.cloud.tencent.com/sync-begin-time", item)
}

func TestServiceRegistry_ChaosErrorcode(t *testing.T) {
	item := Service().ChaosErrorcode()
	fmt.Println(item)
	assert.Equal(t, "service.cloud.tencent.com/chaos-errorcode", item)
}

func TestServiceRegistry_DeletionProtection(t *testing.T) {
	item := Service().DeletionProtection()
	fmt.Println(item)
	assert.Equal(t, "service.cloud.tencent.com/deletion-protection", item)
}

func TestServiceRegistry_ForceDelete(t *testing.T) {
	item := Service().ForceDelete()
	fmt.Println(item)
	assert.Equal(t, "service.cloud.tencent.com/force-delete", item)
}

func TestServiceAnnotations_GetSyncStartTime(t *testing.T) {
	type fields struct {
		annotations map[string]string
	}
	tests := []struct {
		name    string
		fields  fields
		want    time.Time
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "nil value",
			fields: fields{
				annotations: nil,
			},
			want:    time.Time{},
			wantErr: assert.Error,
		},
		{
			name: "empty value",
			fields: fields{
				annotations: map[string]string{},
			},
			want:    time.Time{},
			wantErr: assert.Error,
		},
		{
			name: "parse error",
			fields: fields{
				annotations: map[string]string{
					Service().SyncBeginTime(): "2020-01-01 12:00:00",
				},
			},
			want:    time.Time{},
			wantErr: assert.Error,
		},
		{
			name: "parse success",
			fields: fields{
				annotations: map[string]string{
					Service().SyncBeginTime(): "2020-01-01T12:00:00Z",
				},
			},
			want:    time.Date(2020, 1, 1, 12, 0, 0, 0, time.UTC),
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &ServiceAnnotations{
				Items: &tt.fields.annotations,
			}
			got, err := a.GetSyncBeginTime()
			if !tt.wantErr(t, err, "GetSyncStartTime()") {
				return
			}
			assert.Equalf(t, tt.want, got, "GetSyncStartTime()")
		})
	}
}
