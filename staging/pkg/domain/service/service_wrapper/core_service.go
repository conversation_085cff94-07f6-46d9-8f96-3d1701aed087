package service_wrapper

import (
	"context"
	"fmt"
	"strings"

	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	k8stypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/strategicpatch"
	"k8s.io/klog/v2"

	multiclusterservice "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"

	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
)

func NewService(service *v1.Service) ServiceWrapper {
	s := &ServiceWrapperCoreService{
		Service: types.NewService(service),
	}

	return &Service{
		serviceWrapper: s,
	}
}

type ServiceWrapperCoreService struct {
	*types.Service
}

func (s *ServiceWrapperCoreService) Version() string {
	return "v1"
}

func (s *ServiceWrapperCoreService) Raw() any {
	return s.Service.Service
}

func (s *ServiceWrapperCoreService) ServiceType() ServiceType {
	return CoreService
}

func (s *ServiceWrapperCoreService) IsService() bool {
	return true
}

func (s *ServiceWrapperCoreService) RawService() *v1.Service {
	return s.Service.Service
}

func (s *ServiceWrapperCoreService) IsMCS() bool {
	return false
}

func (s *ServiceWrapperCoreService) RawMCS() *multiclusterservice.MultiClusterService {
	return nil
}

func (s *ServiceWrapperCoreService) Type() v1.ServiceType {
	return s.Service.Spec.Type
}

func (s *ServiceWrapperCoreService) Selector() map[string]string {
	return s.Service.Spec.Selector
}

func (s *ServiceWrapperCoreService) ExternalTrafficPolicy() v1.ServiceExternalTrafficPolicyType {
	return s.Service.Spec.ExternalTrafficPolicy
}

func (s *ServiceWrapperCoreService) Protocols() map[int32]v1.Protocol {
	protocolMaps := map[int32]v1.Protocol{}
	for _, port := range s.Service.Spec.Ports {
		protocolMaps[port.Port] = port.Protocol
	}
	return protocolMaps
}

func (s *ServiceWrapperCoreService) GetRuntimeObject() runtime.Object {
	if s.Service == nil { // 必须加，不然会有问题 kateway svc 的删除事件，这里是nil
		return nil
	}
	return s.Service.Service
}

func (s *ServiceWrapperCoreService) GetObjectMeta() metav1.Object {
	return s.Service.GetObjectMeta()
}

func (s *ServiceWrapperCoreService) DeepCopy() ServiceWrapper {
	return NewService(s.Service.DeepCopy())
}

func (s *ServiceWrapperCoreService) GetStatusLoadBalancer() v1.LoadBalancerStatus {
	return s.Service.Status.LoadBalancer
}

func (s *ServiceWrapperCoreService) SetStatusLoadBalancer(loadBalancerStatus v1.LoadBalancerStatus) {
	s.Service.Status.LoadBalancer = loadBalancerStatus
}

func (s *ServiceWrapperCoreService) GetStatusConditions() []metav1.Condition {
	return s.Service.Status.Conditions
}

func (s *ServiceWrapperCoreService) SetStatusConditions(conditions []metav1.Condition) {
	if lo.Must(cluster.Instance.CheckVersion("< v1.20")) {
		s.Service.SetStatusConditions(conditions)
	} else {
		s.Service.Status.Conditions = conditions
	}
}

func (s *ServiceWrapperCoreService) GetLatest() (ServiceWrapper, error) {
	updated, err := cluster.Instance.KubeClient().CoreV1().Services(s.Service.Namespace).Get(context.Background(), s.Service.Name, metav1.GetOptions{})
	if err == nil {
		return NewService(updated), nil
	}
	return nil, err
}

func (s *ServiceWrapperCoreService) Update() (ServiceWrapper, error) {
	updated, err := cluster.Instance.KubeClient().CoreV1().Services(s.Service.Namespace).Update(context.Background(), s.Service.Service, metav1.UpdateOptions{})
	if err == nil {
		return NewService(updated), nil
	}
	return nil, err
}

func (s *ServiceWrapperCoreService) Patch(ctx context.Context, old ServiceWrapper, latest ServiceWrapper) (ServiceWrapper, error) {
	patch, err := strategicpatch.CreateTwoWayMergePatch(old.JSON(), latest.JSON(), s.Raw())
	if err != nil {
		return nil, fmt.Errorf("create patch error: %w", err)
	}
	updated, err := cluster.Instance.KubeClient().CoreV1().Services(s.Service.Namespace).Patch(ctx, s.Service.Name, k8stypes.StrategicMergePatchType, patch, metav1.PatchOptions{}, "status")
	if err == nil {
		klog.V(4).Infof("patch %s ok: %s", old.String(), string(patch))
		return NewService(updated), nil
	}
	klog.V(4).Infof("patch %s error(%s): %s", old.String(), err, string(patch))
	return nil, err
}

func (s *ServiceWrapperCoreService) GetServiceIPStack() string {
	originService := s.RawService()
	ipv4 := false
	ipv6 := false
	if len(originService.Spec.IPFamilies) != 0 {
		for _, ipFamilies := range originService.Spec.IPFamilies {
			if ipFamilies == v1.IPv4Protocol {
				ipv4 = true
			} else if ipFamilies == v1.IPv6Protocol {
				ipv6 = true
			}
		}
	}
	if len(originService.Spec.ClusterIPs) != 0 {
		for _, ipFamilies := range originService.Spec.ClusterIPs {
			if strings.Contains(ipFamilies, ".") {
				ipv4 = true
			} else if strings.Contains(ipFamilies, ":") {
				ipv6 = true
			}
		}
	}
	if originService.Spec.ClusterIP != "" && originService.Spec.ClusterIP != "None" {
		if strings.Contains(originService.Spec.ClusterIP, ".") {
			ipv4 = true
		} else if strings.Contains(originService.Spec.ClusterIP, ":") {
			ipv6 = true
		}
	}
	if ipv4 && ipv6 {
		return "mixed"
	} else if ipv6 {
		return "ipv6"
	}

	return "ipv4"
}
