package service_wrapper

import (
	"context"

	multiclusterservice "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"git.woa.com/kateway/pkg/domain/service/annotation"
	"git.woa.com/kateway/pkg/domain/types"
)

type ServiceType string

const (
	CoreService         ServiceType = "Service"
	MultiClusterService ServiceType = "MultiClusterService"
)

type ServiceWrapper interface {
	serviceWrapper
	types.Object

	MCS() *types.MCS
	Service() *types.Service

	JSON() []byte
	Finish(errors []error) error
	UpdateStatus() (ServiceWrapper, error)
	NewReadyCondition(errs []error) metav1.Condition
	GetReadyCondition() metav1.Condition
}

type serviceWrapper interface {
	annotation.Interface

	Version() string
	Raw() any
	IsService() bool
	// Deprecated: 使用Service()替代，如果没有需要的方法，请补充完善
	RawService() *v1.Service

	IsMCS() bool
	RawMCS() *multiclusterservice.MultiClusterService

	ServiceType() ServiceType
	Type() v1.ServiceType
	Selector() map[string]string
	ExternalTrafficPolicy() v1.ServiceExternalTrafficPolicyType
	Protocols() map[int32]v1.Protocol
	GetRuntimeObject() runtime.Object
	GetObjectMeta() metav1.Object
	DeepCopy() ServiceWrapper

	GetStatusLoadBalancer() v1.LoadBalancerStatus
	SetStatusLoadBalancer(loadBalancerStatus v1.LoadBalancerStatus)
	GetStatusConditions() []metav1.Condition
	SetStatusConditions(conditions []metav1.Condition)

	GetLatest() (ServiceWrapper, error)
	Update() (ServiceWrapper, error)
	Patch(ctx context.Context, old ServiceWrapper, latest ServiceWrapper) (ServiceWrapper, error)

	GetServiceIPStack() string
}
