package service_wrapper

import (
	"context"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog/v2"

	multiclusterservice "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"

	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
)

func NewMultiClusterService(service *multiclusterservice.MultiClusterService) ServiceWrapper {
	s := &ServiceWrapperMultiClusterService{
		MCS: types.NewMCS(service),
	}

	return &Service{serviceWrapper: s}
}

type ServiceWrapperMultiClusterService struct {
	*types.MCS
}

func (s *ServiceWrapperMultiClusterService) Version() string {
	return multiclusterservice.SchemeGroupVersion.Version
}

func (s *ServiceWrapperMultiClusterService) Raw() any {
	return s.MCS.MultiClusterService
}

func (s *ServiceWrapperMultiClusterService) ServiceType() ServiceType {
	return MultiClusterService
}

func (s *ServiceWrapperMultiClusterService) Type() v1.ServiceType {
	return s.MCS.Spec.Type
}

func (s *ServiceWrapperMultiClusterService) IsService() bool {
	return false
}

func (s *ServiceWrapperMultiClusterService) RawService() *v1.Service {
	return nil
}

func (s *ServiceWrapperMultiClusterService) IsMCS() bool {
	return true
}

func (s *ServiceWrapperMultiClusterService) RawMCS() *multiclusterservice.MultiClusterService {
	return s.MCS.MultiClusterService
}

func (s *ServiceWrapperMultiClusterService) Selector() map[string]string {
	return s.MCS.Spec.Selector
}

func (s *ServiceWrapperMultiClusterService) ExternalTrafficPolicy() v1.ServiceExternalTrafficPolicyType {
	return v1.ServiceExternalTrafficPolicyTypeCluster
}

func (s *ServiceWrapperMultiClusterService) Protocols() map[int32]v1.Protocol {
	protocolMaps := map[int32]v1.Protocol{}
	for _, port := range s.MCS.Spec.Ports {
		protocolMaps[port.Port] = v1.Protocol(port.Protocol)
	}
	return protocolMaps
}

func (s *ServiceWrapperMultiClusterService) GetRuntimeObject() runtime.Object {
	if s.MCS == nil { // 必须加，不然会有问题
		return nil
	}
	return s.MCS.MultiClusterService
}

func (s *ServiceWrapperMultiClusterService) GetObjectMeta() metav1.Object {
	return s.MCS.GetObjectMeta()
}

func (s *ServiceWrapperMultiClusterService) DeepCopy() ServiceWrapper {
	return NewMultiClusterService(s.MCS.DeepCopy())
}

func (s *ServiceWrapperMultiClusterService) GetStatusLoadBalancer() v1.LoadBalancerStatus {
	return s.MCS.Status.LoadBalancer
}

func (s *ServiceWrapperMultiClusterService) SetStatusLoadBalancer(loadBalancerStatus v1.LoadBalancerStatus) {
	s.MCS.Status.LoadBalancer = loadBalancerStatus
}

func (s *ServiceWrapperMultiClusterService) GetStatusConditions() []metav1.Condition {
	return s.MCS.Status.Conditions
}

func (s *ServiceWrapperMultiClusterService) SetStatusConditions(conditions []metav1.Condition) {
	s.MCS.Status.Conditions = conditions
}

func (s *ServiceWrapperMultiClusterService) GetLatest() (ServiceWrapper, error) {
	latest, err := cluster.Instance.MultiClusterServiceClient().CloudV1alpha1().MultiClusterServices(s.MCS.Namespace).Get(context.Background(), s.MCS.Name, metav1.GetOptions{})
	if err == nil {
		return NewMultiClusterService(latest), nil
	}
	return nil, err
}

func (s *ServiceWrapperMultiClusterService) Update() (ServiceWrapper, error) {
	klog.V(4).InfoS("update", "mcs", s.MCS.MultiClusterService)
	updated, err := cluster.Instance.MultiClusterServiceClient().CloudV1alpha1().MultiClusterServices(s.MCS.Namespace).Update(context.Background(), s.MCS.MultiClusterService, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}
	updated.Status = s.MCS.Status
	klog.V(4).InfoS("update status", "mcs", updated)
	updated, err = cluster.Instance.MultiClusterServiceClient().CloudV1alpha1().MultiClusterServices(s.MCS.Namespace).UpdateStatus(context.Background(), updated, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}
	klog.V(4).InfoS("update status ok", "mcs", updated)

	return NewMultiClusterService(updated), nil
}

func (s *ServiceWrapperMultiClusterService) Patch(_ context.Context, _ ServiceWrapper, latest ServiceWrapper) (ServiceWrapper, error) {
	return latest.Update()
}

func (s *ServiceWrapperMultiClusterService) GetServiceIPStack() string {
	return "mixed"
}
