// nolint:revive
package service_wrapper

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
)

const (
	readyConditionType = "Ready"
)

type Service struct {
	dryRunErrors []error
	serviceWrapper
}

func (service *Service) Kind() string {
	return string(service.ServiceType())
}

func (service *Service) UID() string {
	return string(service.GetObjectMeta().GetUID())
}

func (service *Service) Name() string {
	return service.GetObjectMeta().GetName()
}

func (service *Service) Namespace() string {
	return service.GetObjectMeta().GetNamespace()
}

func (service *Service) ResourceVersion() string {
	return service.GetObjectMeta().GetResourceVersion()
}

func (service *Service) String() string {
	return fmt.Sprintf("%s/%s", service.Namespace(), service.Name())
}

func (service *Service) MCS() *types.MCS {
	return types.NewMCS(service.RawMCS())
}

func (service *Service) Service() *types.Service {
	return types.NewService(service.RawService())
}

func (service *Service) NewReadyCondition(errs []error) metav1.Condition {
	reason := types.SuccessReason
	for _, err := range errs {
		// 使用第一个识别的接入层错误码类别作为Reason
		if codeError := new(types.Error); errors.As(err, &codeError) {
			reason = codeError.ErrorCode.ReasonDetail
			break
		}
	}

	return metav1.Condition{
		Type:               readyConditionType,
		Status:             types.ToConditionStatus(reason == types.SuccessReason),
		LastTransitionTime: metav1.Now(),
		Reason:             reason,
		Message:            types.ToString(errs),
	}
}

func (service *Service) GetReadyCondition() metav1.Condition {
	return lo.FindOrElse(service.GetStatusConditions(), metav1.Condition{}, func(item metav1.Condition) bool {
		return item.Type == readyConditionType
	})
}

func (service *Service) Finish(errors []error) error {
	if cluster.Instance.DryRun() {
		return nil
	}

	service.updateReadyCondition(errors)
	_, err := service.UpdateStatus()

	return err
}

func (service *Service) updateReadyCondition(errs []error) {
	needUpdate := false
	// 当存在预检错误时，保证始终更新状态信息以获得最新数据描述
	if len(service.dryRunErrors) > 0 {
		errs = append(errs, types.NewError(errcode.DryRunError, types.ToString(service.dryRunErrors), service))
		needUpdate = true
	}
	klog.V(4).Info("update ready condition", "service", service.String(), "errors", errs)

	condition := service.NewReadyCondition(errs)
	oldCondition := service.GetReadyCondition()
	// 暂时保持与存量逻辑一致，只有当错误码发生实际变更时才更新，避免类似云api响应返回RequestID时触发更新
	if needUpdate || types.ReadyConditionNeedUpdate(oldCondition, condition) {
		klog.V(4).InfoS("update ready condition", "old", oldCondition, "new", condition)
		service.serviceWrapper.SetStatusConditions([]metav1.Condition{condition})
		return
	}

	klog.V(4).InfoS("skip update ready condition since no change", "old", oldCondition, "new", condition)
}

func (service *Service) UpdateStatus() (ServiceWrapper, error) {
	// 更新同步结束时间
	service.SetSyncEndTime(time.Now())

	// 通过对比最新状态，获取有效更新补丁
	latest, err := service.GetLatest()
	if err != nil {
		return nil, err
	}
	if latest.ResourceVersion() != service.ResourceVersion() {
		return nil, fmt.Errorf("latest resource version not match: %s != %s", latest.ResourceVersion(), service.ResourceVersion())
	}
	old := latest.DeepCopy()

	// 对需要更新的字段进行更新
	latest.SetSyncBeginTime(lo.Must(service.GetSyncBeginTime()))
	latest.SetSyncEndTime(lo.Must(service.GetSyncEndTime()))
	latest.SetStatusLoadBalancer(service.GetStatusLoadBalancer())
	latest.SetStatusConditions(service.GetStatusConditions())

	updated, err := service.Patch(context.Background(), old, latest)
	if err != nil {
		return nil, err
	}

	return updated, nil
}

func (service *Service) JSON() []byte {
	return lo.Must(json.Marshal(service.Raw()))
}

func (service *Service) IsDryRun() bool {
	return service.serviceWrapper.Mode() == types.ModeDryRun
}

func (service *Service) SaveDryRunError(err error) {
	service.dryRunErrors = append(service.dryRunErrors, err)
}
