package errdef

import (
	"errors"

	"github.com/samber/lo"
)

type Fatal struct {
	err error
}

func NewFatal(err error) error {
	return &Fatal{err: err}
}

func (f Fatal) Error() string {
	return f.err.Error()
}

func (f Fatal) Unwrap() error {
	return f.err
}

func (f Fatal) Is(err error) bool {
	return errors.Is(f.err, err)
}

func IsFatal(err error) bool {
	_, ok := lo.ErrorsAs[*Fatal](err)
	return ok
}

// Retryable 类型表示可重试的错误
type Retryable struct {
	err error
}

// NewRetryable 创建一个新的 Retryable 错误
func NewRetryable(err error) error {
	return &Retryable{err: err}
}

// Error 实现 error 接口
func (r Retryable) Error() string {
	return r.err.Error()
}

// Unwrap 返回内部错误
func (r Retryable) Unwrap() error {
	return r.err
}

// Is 检查是否是特定错误
func (r Retryable) Is(err error) bool {
	return errors.Is(r.err, err)
}

// IsRetryable 检查错误是否是 Retryable 类型
func IsRetryable(err error) bool {
	_, ok := lo.ErrorsAs[*Retryable](err)
	return ok
}
