package errdef

import "strings"

func Unwrap(err error) []error {
	errs := []error{}
	var unwrap func(error)
	unwrap = func(err error) {
		if err == nil {
			return
		}
		joined, ok := err.(interface{ Unwrap() []error })
		if !ok {
			errs = append(errs, err)
			return
		}
		for _, e := range joined.Unwrap() {
			unwrap(e)
		}
	}
	unwrap(err)
	return errs
}

func Errors(errs []error) string {
	result := make([]string, len(errs))
	for index, err := range errs {
		result[index] = err.Error()
	}
	return strings.Join(result, "; ")
}
