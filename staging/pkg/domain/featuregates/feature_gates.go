package featuregates

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	"github.com/samber/lo"
	"github.com/spf13/pflag"
)

// 接入层特性文档：https://iwiki.woa.com/p/4013120069
var (
	// GR直连特性
	GlobalRouteDirectAccess = NewMutableFeature("GlobalRouteDirectAccess")
	// 开启融合版本的ingress-controller
	MergedIngressController = NewMutableFeature("MergedIngressController")
	// 节点优雅下线特性
	NodeGracefulDeletion = NewMutableFeature("NodeGracefulDeletion")
)

var (
	DryRunService = NewMutableFeature("DryRunService")
	DryRunIngress = NewMutableFeature("DryRunIngress")
	// 多集群MCS特性
	MultiClusterService = NewImmutableFeature("MultiClusterService")
	// 多集群MCI特性
	MultiClusterIngress = NewImmutableFeature("MultiClusterIngress")
	// 在EKS1.0中使用Loadbalancer类型service模拟ClusterIP类型Service
	ClusterIPSimulation = NewImmutableFeature("ClusterIPSimulation")
)

var KnownFeatures = []Feature{
	GlobalRouteDirectAccess,
	GlobalRouteDirectAccess,
	MergedIngressController,
	NodeGracefulDeletion,
	DryRunService,
	DryRunIngress,
	MultiClusterService,
	MultiClusterIngress,
	ClusterIPSimulation,
}

type Feature interface {
	Name() string
}

type MutableFeature struct {
	name string
}

func (f MutableFeature) Name() string {
	return f.name
}

type ImmutableFeature struct {
	name string
	_    struct{}
}

func (f ImmutableFeature) Name() string {
	return f.name
}

func NewMutableFeature(name string) MutableFeature {
	return MutableFeature{
		name: name,
	}
}

func NewImmutableFeature(name string) ImmutableFeature {
	return ImmutableFeature{
		name: name,
	}
}

type FeatureGates interface {
	Enabled(Feature) bool

	Enable(MutableFeature)
	Disable(MutableFeature)

	AddFlags(*pflag.FlagSet)
}

func NewFeatureGates(features map[Feature]bool) FeatureGates {
	return &defaultFeatureGates{
		features: lo.MapKeys(features, func(_ bool, f Feature) string { return f.Name() }),
	}
}

type defaultFeatureGates struct {
	features map[string]bool

	lock sync.RWMutex
}

func (g *defaultFeatureGates) String() string {
	return strings.Join(lo.MapToSlice(g.features,
		func(name string, enabled bool) string { return fmt.Sprintf("%s=%v", name, enabled) }), ",")
}

func (g *defaultFeatureGates) Enabled(f Feature) bool {
	g.lock.RLock()
	defer g.lock.RUnlock()

	return g.features[f.Name()]
}

func (g *defaultFeatureGates) Enable(f MutableFeature) {
	g.lock.Lock()
	defer g.lock.Unlock()

	g.features[f.Name()] = true
}

func (g *defaultFeatureGates) Disable(f MutableFeature) {
	g.lock.Lock()
	defer g.lock.Unlock()

	g.features[f.Name()] = false
}

// SplitMapStringBool parse comma-separated string of key1=value1,key2=value2. value is either true or false
func (g *defaultFeatureGates) SplitMapStringBool(str string) (map[string]bool, error) {
	result := make(map[string]bool)
	for _, s := range strings.Split(str, ",") {
		if len(s) == 0 {
			continue
		}
		parts := strings.SplitN(s, "=", 2)
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid mapStringBool: %v", s)
		}
		k := strings.TrimSpace(parts[0])
		v, err := strconv.ParseBool(strings.TrimSpace(parts[1]))
		if err != nil {
			return nil, fmt.Errorf("invalid mapStringBool: %v", s)
		}
		result[k] = v
	}
	return result, nil
}

func (g *defaultFeatureGates) Set(value string) error {
	settings, err := g.SplitMapStringBool(value)
	if err != nil {
		return fmt.Errorf("failed to parse feature-gates settings due to %w", err)
	}
	g.features = map[string]bool{}
	features := lo.Map(KnownFeatures, func(f Feature, _ int) string { return f.Name() })

	g.lock.Lock()
	defer g.lock.Unlock()

	for k, v := range settings {
		if !lo.Contains(features, k) {
			return fmt.Errorf("unknown feature: %v", k)
		}
		g.features[k] = v
	}
	return nil
}

func (g *defaultFeatureGates) AddFlags(fs *pflag.FlagSet) {
	fs.Var(g, "feature-gates", "A set of key=bool pairs enable/disable features")
}

func (*defaultFeatureGates) Type() string {
	return "mapStringBool"
}
