package featuregates

import (
	"testing"

	"github.com/spf13/pflag"
	"github.com/stretchr/testify/assert"
)

func TestFeatureGates(t *testing.T) {
	// 初始化测试用的FeatureGates实例
	featureGates := NewFeatureGates(map[Feature]bool{})
	fg, _ := featureGates.(*defaultFeatureGates)

	// 测试Enabled方法
	t.Run("Enabled returns false for disabled feature", func(t *testing.T) {
		assert.False(t, fg.Enabled(GlobalRouteDirectAccess))
	})

	// 测试Enable方法
	t.Run("Enable sets feature to true", func(t *testing.T) {
		fg.Enable(GlobalRouteDirectAccess)
		assert.True(t, fg.Enabled(GlobalRouteDirectAccess))
	})

	// 测试Disable方法
	t.Run("Disable sets feature to false", func(t *testing.T) {
		fg.Disable(GlobalRouteDirectAccess)
		assert.False(t, fg.Enabled(GlobalRouteDirectAccess))
	})

	// 测试Set方法
	t.Run("Set parses string and updates features", func(t *testing.T) {
		err := fg.Set("GlobalRouteDirectAccess=true,MergedIngressController=false")
		assert.NoError(t, err)
		assert.True(t, fg.Enabled(GlobalRouteDirectAccess))
		assert.False(t, fg.Enabled(MergedIngressController))
	})

	// 测试Set方法处理无效输入
	t.Run("Set returns error for invalid input", func(t *testing.T) {
		err := fg.Set("GlobalRouteDirectAccess=true,InvalidFeature=true")
		assert.Error(t, err)
	})

	// 测试Set方法处理格式错误
	t.Run("Set returns error for malformed input", func(t *testing.T) {
		err := fg.Set("GlobalRouteDirectAccess=true,IngressController")
		assert.Error(t, err)
	})

	// 测试AddFlags方法
	t.Run("AddFlags adds feature-gates flag to pflag.FlagSet", func(t *testing.T) {
		fs := pflag.NewFlagSet("test", pflag.ContinueOnError)
		fg.AddFlags(fs)
		err := fs.Parse([]string{"--feature-gates=GlobalRouteDirectAccess=true"})
		assert.NoError(t, err)
		assert.True(t, fg.Enabled(GlobalRouteDirectAccess))
	})
}
