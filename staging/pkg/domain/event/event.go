package event

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	metatypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
)

type defaultEventService struct {
	kubeClient    *kubernetes.Clientset
	eventRecorder record.EventRecorder
	componentName string
}

func (s *defaultEventService) EventError(object types.Object, serviceError *types.Error) {
	if cluster.Instance.DryRun() {
		return
	}

	name := object.Name()
	namespace := object.Namespace()

	existEvent, err := s.kubeClient.CoreV1().Events(namespace).Get(context.Background(), fmt.Sprintf("%v.%v.%v", name, strings.ToLower(object.Kind()), serviceError.ErrorCode.Code), metav1.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			klog.Errorf("list event error %s.", err)
			return
		}

		event := s.buildEvent(object, serviceError)
		if _, err := s.kubeClient.CoreV1().Events(namespace).Create(context.Background(), event, metav1.CreateOptions{}); err != nil {
			klog.Errorf("EventService Error. Create events error %s. %s.", lo.Must(json.Marshal(event)), err)
		}
		return
	}

	existEvent.Source = v1.EventSource{
		Component: s.componentName,
		Host:      lo.Must(os.Hostname()),
	}
	existEvent.LastTimestamp = metav1.Now()
	existEvent.Message = serviceError.Error()
	existEvent.Count = existEvent.Count + 1
	if _, err := s.kubeClient.CoreV1().Events(namespace).Update(context.Background(), existEvent, metav1.UpdateOptions{}); err != nil {
		klog.Errorf("EventService Error. Update events error %s. %s.", lo.Must(json.Marshal(existEvent)), err)
		return
	}
	klog.Infof("Event(%#v): type: '%v' reason: '%v' %v", existEvent.InvolvedObject, existEvent.Type, existEvent.Reason, existEvent.Message)
}

func (s *defaultEventService) buildEvent(object types.Object, err *types.Error) *v1.Event {
	t := metav1.Time{Time: time.Now()}

	event := &v1.Event{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%v.%v.%v", object.Name(), strings.ToLower(object.Kind()), err.ErrorCode.Code),
			Namespace: object.Namespace(),
			Annotations: map[string]string{
				"ReturnCode": err.ErrorCode.Code,
			},
		},
		InvolvedObject: v1.ObjectReference{
			APIVersion:      object.Version(),
			Kind:            object.Kind(),
			Namespace:       object.Namespace(),
			Name:            object.Name(),
			ResourceVersion: object.ResourceVersion(),
			UID:             metatypes.UID(object.UID()),
		},
		FirstTimestamp: t,
		LastTimestamp:  t,
		Count:          1,
		Message:        err.Error(),
		Source: v1.EventSource{
			Component: s.componentName,
			Host:      lo.Must(os.Hostname()),
		},
	}

	if err.IsSuccess() {
		event.Type = v1.EventTypeNormal
		event.Reason = fmt.Sprintf("Ensure%sSuccess", object.Kind())
	} else if err.IsWarning() {
		event.Type = v1.EventTypeNormal
		event.Reason = fmt.Sprintf("Ensure%sWarning", object.Kind())
	} else {
		event.Type = v1.EventTypeWarning
		event.Reason = fmt.Sprintf("Ensure%sFailed", object.Kind())
	}

	return event
}

func (s *defaultEventService) EventPod(pod *v1.Pod, eventType string, message string) {
	if cluster.Instance.DryRun() {
		return
	}

	fullName, err := cache.MetaNamespaceKeyFunc(pod)
	if err != nil {
		klog.Errorf("EventService Error. Get service name error %s.", err)
		return
	}
	namespace, name, err := cache.SplitMetaNamespaceKey(fullName)
	if err != nil {
		klog.Errorf("EventService Error. Split service name error %s.", err)
		return
	}

	existEvent, err := s.kubeClient.CoreV1().Events(namespace).Get(context.Background(), fmt.Sprintf("%v.pod.readiness.loadbalancerbackendgroup", name), metav1.GetOptions{})
	if err != nil {
		if !errors.IsNotFound(err) {
			klog.Errorf("EventService Error. Events list error %s.", err)
			return
		}

		event := buildPodEvent(pod, eventType, message)
		if _, err := s.kubeClient.CoreV1().Events(namespace).Create(context.Background(), event, metav1.CreateOptions{}); err != nil {
			klog.Errorf("EventService Error. Create events error %s. %s.", lo.Must(json.Marshal(event)), err)
		}
		return
	}

	reason := "ReadinessGateFailed"
	if eventType == v1.EventTypeNormal {
		reason = "ReadinessGate"
	}
	existEvent.LastTimestamp = metav1.Now()
	existEvent.Message = message
	existEvent.Reason = reason
	existEvent.Type = eventType
	existEvent.Count = existEvent.Count + 1
	if _, err := s.kubeClient.CoreV1().Events(namespace).Update(context.Background(), existEvent, metav1.UpdateOptions{}); err != nil {
		klog.Errorf("EventService Error. Update events error %s. %s.", lo.Must(json.Marshal(existEvent)), err)
		return
	}
	klog.Infof("Event(%#v): type: '%v' reason: '%v' %v", existEvent.InvolvedObject, existEvent.Type, existEvent.Reason, existEvent.Message)
}

func buildPodEvent(pod *v1.Pod, eventType string, message string) *v1.Event {
	t := metav1.Time{Time: time.Now()}
	reason := "ReadinessGateFailed"
	if eventType == v1.EventTypeNormal {
		reason = "ReadinessGate"
	}

	event := &v1.Event{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("%v.pod.readiness.loadbalancerbackendgroup", pod.Name),
			Namespace: pod.Namespace,
		},
		InvolvedObject: v1.ObjectReference{
			APIVersion:      "v1",
			Kind:            "Pod",
			Namespace:       pod.Namespace,
			Name:            pod.Name,
			ResourceVersion: pod.ResourceVersion,
			UID:             pod.UID,
		},
		Type:           eventType,
		Reason:         reason,
		FirstTimestamp: t,
		LastTimestamp:  t,
		Count:          1,
		Message:        message,
	}
	return event
}
