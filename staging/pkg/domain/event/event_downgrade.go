package event

import (
	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/tools/record"

	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
)

type downgradeEventService struct {
	eventRecorder record.EventRecorder
	componentName string
}

func (s *downgradeEventService) EventError(service types.Object, serviceError *types.Error) {
	if cluster.Instance.DryRun() {
		return
	}

	if serviceError.IsSuccess() {
		s.eventRecorder.AnnotatedEventf(service.GetRuntimeObject(), map[string]string{"ReturnCode": serviceError.ErrorCode.Code}, v1.EventTypeNormal, "EnsureServiceSuccess", serviceError.Error())
	} else if serviceError.IsWarning() {
		s.eventRecorder.AnnotatedEventf(service.GetRuntimeObject(), map[string]string{"ReturnCode": serviceError.ErrorCode.Code}, v1.EventTypeNormal, "EnsureServiceWarning", serviceError.Error())
	} else {
		s.eventRecorder.AnnotatedEventf(service.GetRuntimeObject(), map[string]string{"ReturnCode": serviceError.ErrorCode.Code}, v1.EventTypeWarning, "EnsureServiceFailed", serviceError.Error())
	}
}

func (s *downgradeEventService) EventPod(pod *v1.Pod, eventType string, message string) {
	if cluster.Instance.DryRun() {
		return
	}
}
