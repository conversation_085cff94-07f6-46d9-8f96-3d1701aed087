package event

import (
	"sync"

	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	v1core "k8s.io/client-go/kubernetes/typed/core/v1"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/types"
)

type Interface interface {
	EventError(service types.Object, error *types.Error)
	EventPod(pod *v1.Pod, eventType string, message string)
}

var (
	Instance Interface = nil
	once     sync.Once

	DefaultEventType   = "default"
	DowngradeEventType = "downgrade"
)

func Init(kubeConfig *restclient.Config, eventType string, componentName string) {
	kubeClient := kubernetes.NewForConfigOrDie(kubeConfig)

	broadcaster := record.NewBroadcaster()
	broadcaster.StartLogging(klog.Infof)
	broadcaster.StartRecordingToSink(&v1core.EventSinkImpl{Interface: kubeClient.CoreV1().Events("")})
	recorder := broadcaster.NewRecorder(scheme.Scheme, v1.EventSource{Component: componentName})

	once.Do(func() {
		if eventType == DefaultEventType {
			Instance = &defaultEventService{
				eventRecorder: recorder,
				kubeClient:    kubeClient,
				componentName: componentName,
			}
		} else if eventType == DowngradeEventType {
			Instance = &downgradeEventService{
				eventRecorder: recorder,
				componentName: componentName,
			}
		}
	})
}
