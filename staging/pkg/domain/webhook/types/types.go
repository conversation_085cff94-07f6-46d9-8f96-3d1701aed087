package types

import (
	ingressAnnotation "git.woa.com/kateway/pkg/domain/ingress/annotation"
	serviceAnnotation "git.woa.com/kateway/pkg/domain/service/annotation"
	"git.woa.com/kateway/pkg/domain/types"
	"github.com/samber/lo"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

func NotTkeManagedResource(obj runtime.Object) bool {
	meta, ok := obj.(metav1.Object)
	if !ok {
		return false
	}
	annotations := meta.GetAnnotations()
	if managed, ok := annotations[types.TkeManagementAnnotation]; ok {
		if managed == "false" {
			return true
		}
	}
	return false
}

func MultiClusterService(gvk metav1.GroupVersionKind) bool {
	return gvk.Kind == "MultiClusterService"
}

func MultiClusterIngress(gvk metav1.GroupVersionKind) bool {
	return gvk.Kind == "MultiClusterIngress"
}

func BackendOnlyService(gvk metav1.GroupVersionKind, obj runtime.Object) bool {
	if gvk.Kind != "Service" {
		return false
	}

	meta, ok := obj.(metav1.Object)
	if !ok {
		return false
	}
	annotations := meta.GetAnnotations()
	svcAnnotationKeys := []string{types.ServiceAnnotationBackendManageOnly, types.ServiceAnnotationBackendManagementMode, types.ServiceAnnotationFromOtherCluster}

	return len(lo.PickByKeys(annotations, svcAnnotationKeys)) == 3
}

func BackendOnlyIngress(gvk metav1.GroupVersionKind, obj runtime.Object) bool {
	if gvk.Kind != "Ingress" {
		return false
	}

	meta, ok := obj.(metav1.Object)
	if !ok {
		return false
	}
	annotations := meta.GetAnnotations()
	ingAnnotationKeys := []string{types.IngressAnnotationFromOtherCluster, types.IngressAnnotationBackendManageOnly, types.IngressAnnotationBackendManagementMode}
	return len(lo.PickByKeys(annotations, ingAnnotationKeys)) == 3
}

func NormalService(gvk metav1.GroupVersionKind, obj runtime.Object) bool {
	if gvk.Kind != "Service" {
		return false
	}

	meta, ok := obj.(metav1.Object)
	if !ok {
		return false
	}
	annotations := meta.GetAnnotations()
	svcAnnotationKeys := []string{types.ServiceAnnotationBackendManageOnly, types.ServiceAnnotationBackendManagementMode, types.ServiceAnnotationFromOtherCluster}

	return len(lo.PickByKeys(annotations, svcAnnotationKeys)) != 3
}

func NormalIngress(gvk metav1.GroupVersionKind, obj runtime.Object) bool {
	if gvk.Kind != "Ingress" {
		return false
	}

	meta, ok := obj.(metav1.Object)
	if !ok {
		return false
	}
	annotations := meta.GetAnnotations()
	ingAnnotationKeys := []string{types.IngressAnnotationFromOtherCluster, types.IngressAnnotationBackendManageOnly, types.IngressAnnotationBackendManagementMode}
	return len(lo.PickByKeys(annotations, ingAnnotationKeys)) != 3
}

func EnabledDeletionProtect(gvk metav1.GroupVersionKind, obj runtime.Object) bool {
	meta, ok := obj.(metav1.Object)
	if !ok {
		return false
	}
	annotations := meta.GetAnnotations()
	if meta.GetAnnotations() == nil {
		return false
	}

	if NormalIngress(gvk, obj) {
		if enabled := ingressAnnotation.New(&annotations).DeletionProtection(); ok {
			if enabled != nil && *enabled {
				return true
			}
		}
	} else if NormalService(gvk, obj) {
		if enabled := serviceAnnotation.New(&annotations).DeletionProtection(); ok {
			if enabled != nil && *enabled {
				return true
			}
		}
	}

	return false
}
