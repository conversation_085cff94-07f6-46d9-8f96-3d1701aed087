package mutators

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"

	"github.com/tidwall/gjson"
	corev1 "k8s.io/api/core/v1"

	"git.woa.com/kateway/pkg/jsonpatch"
	"git.woa.com/kateway/pkg/telemetry/log"
	"git.woa.com/kateway/pkg/webhook"
)

func mutatePod(ctx context.Context, _ *webhook.AdmissionRequest[*corev1.Pod]) (jsonpatch.Patch, error) {
	body := webhook.RequestBodyFromContext(ctx)
	req, err := http.NewRequestWithContext(ctx, http.MethodPost,
		"http://127.0.0.1:17445/service-controller-readinessgate-webhook", bytes.NewReader(body))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	log.FromContext(ctx).Info("Get response successfully", "response", string(body))
	result := gjson.GetBytes(body, "response.patch")
	if !result.Exists() {
		return nil, nil
	}
	decoded, err := base64.StdEncoding.DecodeString(result.String())
	if err != nil {
		return nil, err
	}
	var p jsonpatch.Patch
	if err = json.Unmarshal(decoded, &p); err != nil {
		return nil, err
	}
	return p, nil
}
