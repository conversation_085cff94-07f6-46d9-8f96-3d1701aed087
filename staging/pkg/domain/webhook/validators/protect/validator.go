package protect

import (
	"context"
	"fmt"

	v1 "k8s.io/api/admission/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"git.woa.com/kateway/pkg/domain/webhook/types"
	"git.woa.com/kateway/pkg/webhook"
)

func ValidateDeletionProtect[T runtime.Object](ctx context.Context, ar *webhook.AdmissionRequest[T]) (string, error) {
	if ar.Operation != v1.Delete {
		return "", nil
	}
	if types.NotTkeManagedResource(ar.Object) {
		return "", nil
	}
	if types.EnabledDeletionProtect(ar.Kind, ar.Object) {
		return fmt.Sprintf("enabled deletion protect, not allowed to delete %s: %s/%s", ar.Kind, ar.Namespace, ar.Name), nil
	}

	return "", nil
}
