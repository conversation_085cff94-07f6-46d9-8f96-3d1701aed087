package skip

import (
	"context"

	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/webhook"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

func IgnoreObjectWithSkipAnnotation[T runtime.Object](_ context.Context, ar *webhook.AdmissionRequest[T]) (string, error) {
	obj, _ := any(ar.Object).(metav1.Object)
	anno := obj.GetAnnotations()
	v, exists := anno[types.AnnotationSkipValidating]
	if exists && v == "true" {
		return "", webhook.ErrSkipRemaining
	}
	return "", nil
}
