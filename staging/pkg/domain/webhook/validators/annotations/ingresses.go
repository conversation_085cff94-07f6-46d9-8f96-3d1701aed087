package annotations

import (
	v1 "k8s.io/api/admission/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"git.woa.com/kateway/pkg/domain/types"
)

func newIngressAnnotationValidators(_ runtime.Object, operation v1.Operation) map[string]annotationMutatingValidator {
	defaultValidator := annotationMutatingValidator{
		update: reject,
		delete: reject,
	}

	return map[string]annotationMutatingValidator{
		types.IngressAnnotationClientToken:            {create: reject},
		types.IngressAnnotationLoadBalancerNetType:    defaultValidator,
		types.IngressAnnotationExistingLoadBalancerID: {update: reject},
		types.IngressAnnotationInternalSubnetID:       defaultValidator,
		types.IngressAnnotationCrossVPCID:             defaultValidator,
		types.IngressAnnotationCrossRegionID:          defaultValidator,
	}
}

func newBackendOnlyIngressAnnotationValidators(_ runtime.Object, operation v1.Operation) map[string]annotationMutatingValidator {
	return map[string]annotationMutatingValidator{}
}

func newMultiClusterIngressAnnotationValidators(obj runtime.Object, operation v1.Operation) map[string]annotationMutatingValidator {
	return map[string]annotationMutatingValidator{}
}
