package annotations

import (
	"context"
	"errors"
	"fmt"
	"reflect"

	"github.com/samber/lo"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"git.woa.com/kateway/pkg/domain/webhook/types"
	"git.woa.com/kateway/pkg/webhook"
)

func ValidateAnnotations[T runtime.Object](ctx context.Context, ar *webhook.AdmissionRequest[T]) (string, error) {
	var validators map[string]annotationMutatingValidator
	switch {
	case types.NormalService(ar.Kind, ar.Object):
		validators = newServiceAnnotationValidators(ar.Object, ar.Operation)
	case types.NormalIngress(ar.Kind, ar.Object):
		validators = newIngressAnnotationValidators(ar.Object, ar.Operation)
	case types.BackendOnlyService(ar.Kind, ar.Object):
		validators = newBackendOnlyServiceAnnotationValidators(ar.Object, ar.Operation)
	case types.BackendOnlyIngress(ar.Kind, ar.Object):
		validators = newBackendOnlyIngressAnnotationValidators(ar.Object, ar.Operation)
	case types.MultiClusterService(ar.Kind):
		validators = newMultiClusterServiceAnnotationValidators(ar.Object, ar.Operation)
	case types.MultiClusterIngress(ar.Kind):
		validators = newMultiClusterIngressAnnotationValidators(ar.Object, ar.Operation)
	}

	if err := doValidateAnnotations(any(ar.OldObject).(metav1.Object), any(ar.Object).(metav1.Object), validators); err != nil {
		return err.Error(), nil
	}
	return "", nil
}

func doValidateAnnotations(old, cur metav1.Object, validators map[string]annotationMutatingValidator) error {
	var (
		oldAnnotations = map[string]string{}
		curAnnotations = cur.GetAnnotations()
		oldKeys        = []string{}
		curKeys        = lo.Keys(curAnnotations)
		errs           = []error{}
		initialAdd     bool
	)
	if len(validators) == 0 {
		return nil
	}
	if old != nil && !reflect.ValueOf(old).IsNil() {
		oldAnnotations = old.GetAnnotations()
		oldKeys = lo.Keys(oldAnnotations)
	} else {
		initialAdd = true
	}
	addedKeys, deletedKeys := lo.Difference(curKeys, oldKeys)
	sameKeys := lo.Intersect(curKeys, oldKeys)
	for _, k := range addedKeys {
		v, exists := validators[k]
		if !exists {
			continue
		}
		var (
			err   error
			value = curAnnotations[k]
		)
		if initialAdd {
			// 创建 Service ｜ Ingress
			err = v.ValidateCreate(value)
		} else {
			// 新添加某个注解
			err = v.ValidateAdd(value)
		}
		if err != nil {
			errs = append(errs, fmt.Errorf("failed to set annotation %q: %w", k, err))
		}
	}
	// 被删除的注解
	for _, k := range deletedKeys {
		v, exists := validators[k]
		if !exists {
			continue
		}
		if err := v.ValidateDelete(oldAnnotations[k]); err != nil {
			errs = append(errs, fmt.Errorf("failed to delete annotation %q: %w", k, err))
		}
	}
	// 更新的注解
	for _, k := range sameKeys {
		v, exists := validators[k]
		if !exists {
			continue
		}
		if oldAnnotations[k] != curAnnotations[k] {
			if err := v.ValidateUpdate(oldAnnotations[k], curAnnotations[k]); err != nil {
				errs = append(errs, fmt.Errorf("failed to update annotation %q from %q to %q: %w",
					k, oldAnnotations[k], curAnnotations[k], err))
			}
		}
	}
	return errors.Join(errs...)
}

var (
	ErrNotAllowed = errors.New("not allowed")
)

var (
	reject = func(_, _ string) error { return ErrNotAllowed }
)

type annotationMutatingValidator struct {
	create func(old, cur string) error
	add    func(old, cur string) error
	update func(old, cur string) error
	delete func(old, cur string) error
}

func (v annotationMutatingValidator) ValidateCreate(value string) error {
	if v.create == nil {
		return nil
	}
	return v.create("", value)
}

func (v annotationMutatingValidator) ValidateAdd(value string) error {
	if v.add == nil {
		return nil
	}
	return v.add("", value)
}

func (v annotationMutatingValidator) ValidateUpdate(old, cur string) error {
	if v.update == nil {
		return nil
	}
	return v.update(old, cur)
}

func (v annotationMutatingValidator) ValidateDelete(value string) error {
	if v.delete == nil {
		return nil
	}
	return v.delete(value, "")
}
