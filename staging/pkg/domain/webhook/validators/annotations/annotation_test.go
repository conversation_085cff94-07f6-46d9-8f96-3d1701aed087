package annotations

import (
	"errors"
	"fmt"
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kateway/pkg/domain/types"
)

func TestDoValidateAnnotations(t *testing.T) {
	testCases := []struct {
		name        string
		old         metav1.Object
		cur         metav1.Object
		validators  map[string]annotationMutatingValidator
		expectedErr error
	}{
		{
			name: "Test added annotation",
			cur: &metav1.ObjectMeta{
				Annotations: map[string]string{
					"test-key": "test-value",
				},
			},
			validators: map[string]annotationMutatingValidator{
				"test-key": {create: reject},
			},
			expectedErr: ErrNotAllowed,
		},
		{
			name: "Test deleted annotation",
			old: &metav1.ObjectMeta{
				Annotations: map[string]string{
					"test-key": "test-value",
				},
			},
			cur: &metav1.ObjectMeta{
				Annotations: map[string]string{},
			},
			validators: map[string]annotationMutatingValidator{
				"test-key": {delete: reject},
			},
			expectedErr: ErrNotAllowed,
		},
		{
			name: "Test updated annotation",
			old: &metav1.ObjectMeta{
				Annotations: map[string]string{
					"test-key": "old-value",
				},
			},
			cur: &metav1.ObjectMeta{
				Annotations: map[string]string{
					"test-key": "new-value",
				},
			},
			validators: map[string]annotationMutatingValidator{
				"test-key": {update: reject},
			},
			expectedErr: ErrNotAllowed,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := doValidateAnnotations(tc.old, tc.cur, tc.validators)
			if !errors.Is(err, tc.expectedErr) {
				t.Errorf("Expected error: %v, got: %v", tc.expectedErr, err)
			}
		})
	}
}

func TestValidateSpecifyProtoJSON(t *testing.T) {
	testCases := []struct {
		name      string
		jsonStr   string
		expectErr error
	}{
		{
			name: "Valid JSON with mixed types",
			jsonStr: `{
                "4200":{"protocol":["QUIC"],"tls":"kateway-cert"},
                "5000":{"protocol":["TCP_SSL"],"tls":"kateway-cert"},
                "7000":{"protocol":["HTTP"],"hosts":{"kateway.tke-demo.cn":{}}},
                "7100":{"protocol":["HTTPS"],"hosts":{"kateway.tke-demo.cn":{"tls":"kateway-cert"}}}
            }`,
			expectErr: nil,
		},
		{
			name: "Invalid JSON with trailing comma",
			jsonStr: `{
                "4200":{"protocol":["QUIC"],"tls":"kateway-cert"},
                "5000":{"protocol":["TCP_SSL"],"tls":"kateway-cert"},
                "7000":{"protocol":["HTTP"],"hosts":{"kateway.tke-demo.cn":{}}},
                "7100":{"protocol":["HTTPS"],"hosts":{"kateway.tke-demo.cn":{"tls":"kateway-cert"}}},
            }`,
			expectErr: types.ErrInvalidFormat,
		},
		{
			name: "Valid JSON with empty hosts",
			jsonStr: `{
                "4200":{"protocol":["QUIC"],"tls":"kateway-cert"},
                "5000":{"protocol":["TCP_SSL"],"tls":"kateway-cert"},
                "7000":{"protocol":["HTTP"],"hosts":{}},
                "7100":{"protocol":["HTTPS"],"hosts":{"kateway.tke-demo.cn":{"tls":"kateway-cert"}}}
            }`,
			expectErr: nil,
		},
		{
			name: "Invalid JSON with missing protocol",
			jsonStr: `{
                "4200":{"tls":"kateway-cert"},
                "5000":{"protocol":["TCP_SSL"],"tls":"kateway-cert"},
                "7000":{"protocol":["HTTP"],"hosts":{"kateway.tke-demo.cn":{}}},
                "7100":{"protocol":["HTTPS"],"hosts":{"kateway.tke-demo.cn":{"tls":"kateway-cert"}}}
            }`,
			expectErr: nil,
		},
		{
			name: "Invalid JSON with incorrect data type",
			jsonStr: `{
                "4200":{"protocol":"QUIC","tls":"kateway-cert"},
                "5000":{"protocol":["TCP_SSL"],"tls":"kateway-cert"},
                "7000":{"protocol":["HTTP"],"hosts":{"kateway.tke-demo.cn":{}}},
                "7100":{"protocol":["HTTPS"],"hosts":{"kateway.tke-demo.cn":{"tls":"kateway-cert"}}}
            }`,
			expectErr: types.ErrInvalidFormat,
		},
		{
			name: "Valid JSON with multiple protocols",
			jsonStr: `{
                "4200":{"protocol":["QUIC", "HTTP"],"tls":"kateway-cert"},
                "5000":{"protocol":["TCP_SSL"],"tls":"kateway-cert"},
                "7000":{"protocol":["HTTP"],"hosts":{"kateway.tke-demo.cn":{}}},
                "7100":{"protocol":["HTTPS"],"hosts":{"kateway.tke-demo.cn":{"tls":"kateway-cert"}}}
            }`,
			expectErr: nil,
		},
		{
			name: "Invalid JSON with non-string tls",
			jsonStr: `{
                "4200":{"protocol":["QUIC"],"tls":123},
                "5000":{"protocol":["TCP_SSL"],"tls":"kateway-cert"},
                "7000":{"protocol":["HTTP"],"hosts":{"kateway.tke-demo.cn":{}}},
                "7100":{"protocol":["HTTPS"],"hosts":{"kateway.tke-demo.cn":{"tls":"kateway-cert"}}}
            }`,
			expectErr: types.ErrInvalidFormat,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := types.ValidateSpecifyProtoJSON(tc.jsonStr)
			if !errors.Is(err, tc.expectErr) {
				t.Errorf("expected error %v, got %v", tc.expectErr, err)
			}
		})
	}
}

// 测试 ValidateProtocols 函数
func TestValidateProtocols(t *testing.T) {
	tests := []struct {
		protocols []string
		expectErr error
	}{
		{
			protocols: []string{"TCP", "UDP"},
			expectErr: nil,
		},
		{
			protocols: []string{"TCP", "TCP_SSL"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"HTTP", "HTTPS", "TCP"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"QUIC", "UDP"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"TCP", "HTTP", "TCP"},
			expectErr: fmt.Errorf("%w: TCP", types.ErrRepeatProtocol),
		},
		{
			protocols: []string{"TCP", "TCP_SSL", "HTTP", "HTTPS"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"HTTP", "HTTPS"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		// 新增的测试用例
		{
			protocols: []string{"UDP", "QUIC"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"TCP", "HTTP", "TCP_SSL"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"TCP_SSL", "HTTP", "HTTPS"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"TCP", "TCP", "UDP"},
			expectErr: fmt.Errorf("%w: TCP", types.ErrRepeatProtocol),
		},
		{
			protocols: []string{"QUIC", "TCP", "UDP"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"TCP", "HTTP", "HTTPS", "TCP_SSL"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"TCP", "TCP_SSL", "QUIC"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"UDP", "TCP", "TCP_SSL"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"HTTP", "TCP", "QUIC"},
			expectErr: types.ErrIncompatibleProtocols,
		},
		{
			protocols: []string{"HTTP", "QUIC"},
			expectErr: nil,
		},
		{
			protocols: []string{"HTTPS", "QUIC"},
			expectErr: nil,
		},
		{
			protocols: []string{"HTTPS", "UDP"},
			expectErr: nil,
		},
		{
			protocols: []string{"TCP_SSL", "QUIC"},
			expectErr: nil,
		},
		{
			protocols: []string{"PPPOE"},
			expectErr: errors.New("do not support the protocol(PPPOE). ValidProtocol:TCP,UDP,TCP_SSL,QUIC,HTTP,HTTPS"),
		},
		{
			protocols: []string{"tcp"},
			expectErr: errors.New("do not support the protocol(tcp). ValidProtocol:TCP,UDP,TCP_SSL,QUIC,HTTP,HTTPS"),
		},
		{
			protocols: []string{"https"},
			expectErr: errors.New("do not support the protocol(https). ValidProtocol:TCP,UDP,TCP_SSL,QUIC,HTTP,HTTPS"),
		},
		{
			protocols: []string{"tcp_ssl"},
			expectErr: errors.New("do not support the protocol(tcp_ssl). ValidProtocol:TCP,UDP,TCP_SSL,QUIC,HTTP,HTTPS"),
		},
	}

	for _, test := range tests {
		err := types.ValidateProtocols(test.protocols)
		if test.expectErr != nil {
			if err == nil || err.Error() != test.expectErr.Error() {
				t.Errorf("expected error: %v, got: %v", test.expectErr, err)
			}
		} else {
			if err != nil {
				t.Errorf("expected no error, got: %v", err)
			}
		}
	}
}
