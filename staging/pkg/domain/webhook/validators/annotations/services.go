package annotations

import (
	"github.com/samber/lo"
	v1 "k8s.io/api/admission/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"

	"git.woa.com/kateway/pkg/domain/types"
	otypes "git.woa.com/kateway/pkg/domain/webhook/types"
)

func newServiceAnnotationValidators(obj runtime.Object, operation v1.Operation) map[string]annotationMutatingValidator {
	if otypes.NotTkeManagedResource(obj) {
		return nil
	}
	var isLoadBalancer bool
	switch svc := obj.(type) {
	case *corev1.Service:
		isLoadBalancer = svc.Spec.Type == corev1.ServiceTypeLoadBalancer
	default:
		panic("unexpected type")
	}
	rejectIfLoadBalancer := lo.Ternary(isLoadBalancer, reject, nil)
	defaultValidator := annotationMutatingValidator{
		update: reject,
		delete: rejectIfLoadBalancer,
	}
	// 创建一个新的 Service 扩展协议注解 校验器
	specifyProtocolValidator := newSpecifyProtocolValidator()
	return map[string]annotationMutatingValidator{
		types.ServiceAnnotationClientToken:            {create: reject},
		types.ServiceAnnotationExistingLoadBalancerID: {update: reject},
		types.ServiceAnnotationLoadBalancerType:       defaultValidator,
		types.ServiceAnnotationInternalSubnetID:       defaultValidator,
		types.ServiceAnnotationCrossRegionID:          defaultValidator,
		types.ServiceAnnotationCrossVPCID:             defaultValidator,
		types.SpecifyProtocolAnnotation:               specifyProtocolValidator,
	}
}

// specifyProtocolValidator 创建一个新的 扩展协议 校验器
func newSpecifyProtocolValidator() annotationMutatingValidator {
	return annotationMutatingValidator{
		update: func(_, cur string) error {
			return types.ValidateSpecifyProtoJSON(cur)
		},
		add: func(_, cur string) error {
			return types.ValidateSpecifyProtoJSON(cur)
		},
		create: func(_, cur string) error {
			return types.ValidateSpecifyProtoJSON(cur)
		},
	}
}

func newBackendOnlyServiceAnnotationValidators(obj runtime.Object, operation v1.Operation) map[string]annotationMutatingValidator {
	return map[string]annotationMutatingValidator{}
}

func newMultiClusterServiceAnnotationValidators(obj runtime.Object, operation v1.Operation) map[string]annotationMutatingValidator {
	return map[string]annotationMutatingValidator{}
}
