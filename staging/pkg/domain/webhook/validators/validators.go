package validators

import (
	corev1 "k8s.io/api/core/v1"
	extensionsv1beta1 "k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	networkingv1beta1 "k8s.io/api/networking/v1beta1"

	lbrv1alpha1 "git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	mciv1alpha1 "git.woa.com/kateway/multi-cluster-ingress-api/apis/multiclusteringress/v1alpha1"
	mcsv1alpha1 "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"
	tscv1alpha1 "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"git.woa.com/kateway/pkg/domain/webhook/validators/annotations"
	"git.woa.com/kateway/pkg/domain/webhook/validators/protect"
	"git.woa.com/kateway/pkg/domain/webhook/validators/skip"
	"git.woa.com/kateway/pkg/webhook"
)

func NewLoadBalancerResourceValidators() []webhook.ValidateFunc[*lbrv1alpha1.LoadBalancerResource] {
	return []webhook.ValidateFunc[*lbrv1alpha1.LoadBalancerResource]{}
}

func NewTKEServiceConfigValidators() []webhook.ValidateFunc[*tscv1alpha1.TkeServiceConfig] {
	return []webhook.ValidateFunc[*tscv1alpha1.TkeServiceConfig]{}
}

func NewMCSValidators() []webhook.ValidateFunc[*mcsv1alpha1.MultiClusterService] {
	return []webhook.ValidateFunc[*mcsv1alpha1.MultiClusterService]{
		skip.IgnoreObjectWithSkipAnnotation[*mcsv1alpha1.MultiClusterService],
		annotations.ValidateAnnotations[*mcsv1alpha1.MultiClusterService],
	}
}

func NewMCIValidators() []webhook.ValidateFunc[*mciv1alpha1.MultiClusterIngress] {
	return []webhook.ValidateFunc[*mciv1alpha1.MultiClusterIngress]{
		skip.IgnoreObjectWithSkipAnnotation[*mciv1alpha1.MultiClusterIngress],
		annotations.ValidateAnnotations[*mciv1alpha1.MultiClusterIngress],
	}
}

func NewServiceValidators() []webhook.ValidateFunc[*corev1.Service] {
	return []webhook.ValidateFunc[*corev1.Service]{
		protect.ValidateDeletionProtect[*corev1.Service],
		skip.IgnoreObjectWithSkipAnnotation[*corev1.Service],
		annotations.ValidateAnnotations[*corev1.Service],
	}
}

func NewExtensionsIngressValidators() []webhook.ValidateFunc[*extensionsv1beta1.Ingress] {
	return []webhook.ValidateFunc[*extensionsv1beta1.Ingress]{
		protect.ValidateDeletionProtect[*extensionsv1beta1.Ingress],
		skip.IgnoreObjectWithSkipAnnotation[*extensionsv1beta1.Ingress],
		annotations.ValidateAnnotations[*extensionsv1beta1.Ingress],
	}
}

func NewNetworkingV1IngressValidators() []webhook.ValidateFunc[*networkingv1.Ingress] {
	return []webhook.ValidateFunc[*networkingv1.Ingress]{
		protect.ValidateDeletionProtect[*networkingv1.Ingress],
		skip.IgnoreObjectWithSkipAnnotation[*networkingv1.Ingress],
		annotations.ValidateAnnotations[*networkingv1.Ingress],
	}
}

func NewNetworkingV1beta1IngressValidators() []webhook.ValidateFunc[*networkingv1beta1.Ingress] {
	return []webhook.ValidateFunc[*networkingv1beta1.Ingress]{
		protect.ValidateDeletionProtect[*networkingv1beta1.Ingress],
		skip.IgnoreObjectWithSkipAnnotation[*networkingv1beta1.Ingress],
		annotations.ValidateAnnotations[*networkingv1beta1.Ingress],
	}
}
