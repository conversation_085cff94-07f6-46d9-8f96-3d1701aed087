package webhook

import (
	"fmt"

	lbrv1alpha1 "git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	mciv1alpha1 "git.woa.com/kateway/multi-cluster-ingress-api/apis/multiclusteringress/v1alpha1"
	mcsv1alpha1 "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"
	tscv1alpha1 "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	"github.com/samber/lo"
	admissionregistrationv1 "k8s.io/api/admissionregistration/v1"
	extensionsv1beta1 "k8s.io/api/extensions/v1beta1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/domain/webhook/mutators"
	"git.woa.com/kateway/pkg/domain/webhook/validators"
	"git.woa.com/kateway/pkg/webhook"
	"git.woa.com/kateway/pkg/webhook/components"
	"git.woa.com/kateway/pkg/webhook/server"
)

const (
	name = "service-controller-readiness-gate"
	port = 17443
)

func init() {
	mcsv1alpha1.AddToScheme(webhook.Scheme)
	mciv1alpha1.AddToScheme(webhook.Scheme)
	lbrv1alpha1.AddToScheme(webhook.Scheme)
	tscv1alpha1.AddToScheme(webhook.Scheme)
}

func NewServer(cli *kubernetes.Clientset) *server.Server {
	svr := server.New(server.Config{
		Client:     cli,
		Addr:       fmt.Sprintf(":%d", port),
		SecretName: name,
	})
	svr.RegisterValidator(webhook.NewValidator(validators.NewServiceValidators()...))
	svr.RegisterValidator(webhook.NewValidator(validators.NewMCSValidators()...))
	svr.RegisterValidator(webhook.NewValidator(validators.NewMCIValidators()...))
	svr.RegisterValidator(webhook.NewValidator(validators.NewExtensionsIngressValidators()...))
	svr.RegisterValidator(webhook.NewValidator(validators.NewNetworkingV1beta1IngressValidators()...))
	svr.RegisterValidator(webhook.NewValidator(validators.NewNetworkingV1IngressValidators()...))
	svr.RegisterValidator(webhook.NewValidator(validators.NewTKEServiceConfigValidators()...))
	svr.RegisterValidator(webhook.NewValidator(validators.NewLoadBalancerResourceValidators()...))

	svr.RegisterMutator(webhook.NewMutator(mutators.NewPodMutators()...))
	return svr
}

// nolint: funlen
func NewManager(cli *kubernetes.Clientset, clusterID, clusterType string, mutatingEnabled bool) *components.Manager {
	independentCluster := clusterType == types.ClusterTypeIndependent
	ops := []admissionregistrationv1.OperationType{
		admissionregistrationv1.Create,
		admissionregistrationv1.Update,
		admissionregistrationv1.Delete,
	}
	return components.NewManager(cli,
		components.ServiceMeta{
			Name:      name,
			Namespace: lo.Ternary(independentCluster, metav1.NamespaceSystem, clusterID),
			Port:      port,
		},
		components.Params{
			Service: components.ServiceParams{
				Enabled: independentCluster,
				Selector: map[string]string{
					"qcloud-app": "service-controller",
				},
			},
			ValidatingWebhookConfig: components.WebhookConfigParams{
				Enabled: true,
				Rules: []admissionregistrationv1.RuleWithOperations{
					{
						Operations: ops,
						Rule: admissionregistrationv1.Rule{
							APIGroups:   []string{""},
							APIVersions: []string{"v1"},
							Resources:   []string{"services"},
							Scope:       lo.ToPtr(admissionregistrationv1.AllScopes),
						},
					},
					{
						Operations: ops,
						Rule: admissionregistrationv1.Rule{
							APIGroups:   []string{mcsv1alpha1.SchemeGroupVersion.Group},
							APIVersions: []string{"*"},
							Resources:   []string{mcsv1alpha1.MultiClusterServiceResourcePlural},
							Scope:       lo.ToPtr(admissionregistrationv1.AllScopes),
						},
					},
					{
						Operations: ops,
						Rule: admissionregistrationv1.Rule{
							APIGroups:   []string{mciv1alpha1.SchemeGroupVersion.Group},
							APIVersions: []string{"*"},
							Resources:   []string{"multiclusteringresses"},
							Scope:       lo.ToPtr(admissionregistrationv1.AllScopes),
						},
					},
					{
						Operations: ops,
						Rule: admissionregistrationv1.Rule{
							APIGroups:   []string{extensionsv1beta1.SchemeGroupVersion.Group, networkingv1.SchemeGroupVersion.Group},
							APIVersions: []string{"*"},
							Resources:   []string{"ingresses"},
							Scope:       lo.ToPtr(admissionregistrationv1.AllScopes),
						},
					},
					{
						Operations: ops,
						Rule: admissionregistrationv1.Rule{
							APIGroups:   []string{tscv1alpha1.SchemeGroupVersion.Group},
							APIVersions: []string{"*"},
							Resources:   []string{tscv1alpha1.TkeServiceConfigResourcePlural},
							Scope:       lo.ToPtr(admissionregistrationv1.AllScopes),
						},
					},
					{
						Operations: ops,
						Rule: admissionregistrationv1.Rule{
							APIGroups:   []string{lbrv1alpha1.SchemeGroupVersion.Group},
							APIVersions: []string{"*"},
							Resources:   []string{lbrv1alpha1.LoadBalancerResourceResourcePlural},
							Scope:       lo.ToPtr(admissionregistrationv1.AllScopes),
						},
					},
				},
			},
			MutatingWebhookConfig: components.WebhookConfigParams{
				Enabled: mutatingEnabled,
				Rules: []admissionregistrationv1.RuleWithOperations{
					{
						Operations: []admissionregistrationv1.OperationType{admissionregistrationv1.Create},
						Rule: admissionregistrationv1.Rule{
							APIGroups:   []string{""},
							APIVersions: []string{"v1"},
							Resources:   []string{"pods"},
							Scope:       lo.ToPtr(admissionregistrationv1.AllScopes),
						},
					},
				},
			},
		},
	)
}
