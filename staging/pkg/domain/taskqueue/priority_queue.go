package taskqueue

import (
	"bytes"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/gosuri/uitable"
	"k8s.io/utils/clock"

	"git.woa.com/kateway/pkg/domain/metrics"
)

type Interface interface {
	Add(item Item)
	Len() int
	Get() (item Item, shutdown bool)
	Done(item Item)
	ShutDown()
	ShuttingDown() bool

	ServeHTTP(w http.ResponseWriter, req *http.Request)
}

var _ Interface = (*Type)(nil)

// New constructs a new work queue (see the package comment).
func New() *Type {
	return NewNamed("")
}

func NewNamed(name string) *Type {
	rc := clock.RealClock{}
	return newQueue(
		name,
		rc,
		metrics.GetQueueMetricsFactory().NewQueueMetric(name, rc),
		defaultUnfinishedWorkUpdatePeriod,
	)
}

func newQueue(_ string, c clock.WithTicker, metrics metrics.QueueMetric, updatePeriod time.Duration) *Type {
	t := &Type{
		clock:                      c,
		dirty:                      set{},
		processing:                 set{},
		cond:                       sync.NewCond(&sync.Mutex{}),
		metrics:                    metrics,
		unfinishedWorkUpdatePeriod: updatePeriod,
	}

	go t.updateUnfinishedWorkLoop()
	return t
}

const defaultUnfinishedWorkUpdatePeriod = 500 * time.Millisecond

// Type is a work queue (see the package comment).
type Type struct {
	// queue defines the order in which we will work on items. Every
	// element of queue should be in the dirty set and not in the
	// processing set.
	queue []Item

	// dirty defines all of the items that need to be processed.
	dirty set

	// Things that are currently being processed are in the processing set.
	// These things may be simultaneously in the dirty set. When we finish
	// processing something and remove it from this set, we'll check if
	// it's in the dirty set, and if so, add it to the queue.
	processing set

	cond *sync.Cond

	shuttingDown bool

	metrics metrics.QueueMetric

	unfinishedWorkUpdatePeriod time.Duration
	clock                      clock.WithTicker
}

type t interface{}

type set map[t]Item

// getKey 统一处理键的获取逻辑
func (item Item) getKey() interface{} {
	if str, ok := item.Data.(fmt.Stringer); ok {
		return str.String()
	}
	return item.Data
}

// has 检查item是否存在于集合中
func (s set) has(item Item) bool {
	_, exists := s[item.getKey()]
	return exists
}

// get 从集合中获取item
func (s set) get(item Item) Item {
	return s[item.getKey()]
}

// insert 将item插入集合
func (s set) insert(item Item) {
	s[item.getKey()] = item
}

// delete 从集合中删除item
func (s set) delete(item Item) {
	delete(s, item.getKey())
}

func (q *Type) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	var buf bytes.Buffer
	items := q.ListProcessing()
	buf.WriteString(fmt.Sprintf("处理中(长度=%d):\n", len(items)))

	table := uitable.New()
	table.Separator = " "
	table.AddRow("序号", "名称", "权重", "添加时间", "处理时间")
	for i, item := range items {
		table.AddRow(i, item.Data, item.Weight, item.AddTime.Format(time.RFC3339), item.GetTime.Format(time.RFC3339))
	}
	buf.WriteString(table.String())

	buf.WriteString("\n\n")

	items = q.List()
	buf.WriteString(fmt.Sprintf("排队中(长度=%d):\n", len(items)))
	table = uitable.New()
	table.AddRow("序号", "名称", "权重", "添加时间", "处理时间")
	for i, item := range items {
		table.AddRow(i, item.Data, item.Weight, item.AddTime.Format(time.RFC3339), item.GetTime.Format(time.RFC3339))
	}
	buf.WriteString(table.String())
	buf.WriteString("\n")

	w.Write(buf.Bytes())
}

func (q *Type) List() []Item {
	q.cond.L.Lock()
	defer q.cond.L.Unlock()

	items := make([]Item, len(q.queue))
	copy(items, q.queue)
	return items
}

// ListProcessing 返回当前正在处理的所有Item，并按Data的字符串表示排序
func (q *Type) ListProcessing() []Item {
	q.cond.L.Lock()
	defer q.cond.L.Unlock()

	items := make([]Item, 0, len(q.processing))
	for _, v := range q.processing {
		items = append(items, v)
	}

	// 根据Data的字符串表示进行排序
	sort.Slice(items, func(i, j int) bool {
		return strings.Compare(getDataString(items[i]), getDataString(items[j])) < 0
	})
	return items
}

func getDataString(data interface{}) string {
	switch v := data.(type) {
	case fmt.Stringer:
		return v.String()
	case string:
		return v
	default:
		return fmt.Sprintf("%v", v)
	}
}

// Add marks item as needing processing.
func (q *Type) Add(item Item) {
	q.cond.L.Lock()
	defer q.cond.L.Unlock()

	item.AddTime = time.Now()

	if q.shuttingDown {
		return
	}
	if q.dirty.has(item) {
		dirtyItem := q.dirty.get(item)
		if item.Weight < dirtyItem.Weight { // 新加入的Item权重较高，需要处理
			if q.processing.has(item) { // 正在执行中，不在排队队列中，所以只需要更新dirty中的对象权重
				q.dirty.delete(item)
				q.dirty.insert(item)
				return
			} else { // 已经在排队队列中，需要将队列中的对象弹出，然后按照正常流程重新入列
				q.dirty.delete(item)
				q.removeItem(item)
				q.metrics.Get(item.Data)
			}
		} else { // 新加入的Item权重没有更高的优先级，默认忽略
			return
		}
	}

	q.metrics.Add(item.Data)

	q.dirty.insert(item)
	if q.processing.has(item) {
		return
	}

	q.insertItem(item)
	q.cond.Signal()
}

// Len returns the current queue length, for informational purposes only. You
// shouldn't e.g. gate a call to Add() or Get() on Len() being a particular
// value, that can't be synchronized properly.
func (q *Type) Len() int {
	q.cond.L.Lock()
	defer q.cond.L.Unlock()
	return len(q.queue)
}

// Get blocks until it can return an item to be processed. If shutdown = true,
// the caller should end their goroutine. You must call Done with item when you
// have finished processing it.
func (q *Type) Get() (item Item, shutdown bool) {
	q.cond.L.Lock()
	defer q.cond.L.Unlock()
	for len(q.queue) == 0 && !q.shuttingDown {
		q.cond.Wait()
	}
	if len(q.queue) == 0 {
		// We must be shutting down.
		return Item{Data: nil, Weight: 0}, true
	}

	item, q.queue = q.queue[0], q.queue[1:]

	item.GetTime = time.Now()

	q.metrics.Get(item.Data)

	q.processing.insert(item)
	q.dirty.delete(item)

	return item, false
}

// Done marks item as done processing, and if it has been marked as dirty again
// while it was being processed, it will be re-added to the queue for
// re-processing.
func (q *Type) Done(item Item) {
	q.cond.L.Lock()
	defer q.cond.L.Unlock()

	q.metrics.Done(item.Data)

	q.processing.delete(item)
	if q.dirty.has(item) {
		q.insertItem(q.dirty.get(item))
		q.cond.Signal()
	}
}

// ShutDown will cause q to ignore all new items added to it. As soon as the
// worker goroutines have drained the existing items in the queue, they will be
// instructed to exit.
func (q *Type) ShutDown() {
	q.cond.L.Lock()
	defer q.cond.L.Unlock()
	q.shuttingDown = true
	q.cond.Broadcast()
}

func (q *Type) ShuttingDown() bool {
	q.cond.L.Lock()
	defer q.cond.L.Unlock()

	return q.shuttingDown
}

func (q *Type) updateUnfinishedWorkLoop() {
	t := q.clock.NewTicker(q.unfinishedWorkUpdatePeriod)
	defer t.Stop()
	for range t.C() {
		if !func() bool {
			q.cond.L.Lock()
			defer q.cond.L.Unlock()
			if !q.shuttingDown {
				q.metrics.UpdateUnfinishedWork()
				return true
			}
			return false

		}() {
			return
		}
	}
}

type Item struct {
	Data    t
	Weight  int
	AddTime time.Time
	GetTime time.Time
}

func NewItem(Data t) *Item {
	return &Item{
		Data:   Data,
		Weight: 1,
	}
}

func (q *Type) insertItem(item Item) {
	position := -1
	for i := len(q.queue) - 1; i >= 0; i = i - 1 {
		if item.Weight >= q.queue[i].Weight {
			position = i
			break
		}
	}
	newQueue := make([]Item, len(q.queue)+1)
	for i := 0; i <= position; i = i + 1 {
		newQueue[i] = q.queue[i]
	}
	newQueue[position+1] = item
	for i := position + 1; i < len(q.queue); i = i + 1 {
		newQueue[i+1] = q.queue[i]
	}
	q.queue = newQueue
}

func (q *Type) removeItem(item Item) {
	position := -1
	for i := 0; i < len(q.queue); i = i + 1 {
		if item.Data == q.queue[i].Data {
			position = i
			break
		}
	}
	if position != -1 {
		newQueue := make([]Item, len(q.queue)-1)
		for i := 0; i < position; i = i + 1 {
			newQueue[i] = q.queue[i]
		}
		for i := position + 1; i < len(q.queue); i = i + 1 {
			newQueue[i-1] = q.queue[i]
		}
		q.queue = newQueue
	}
}
