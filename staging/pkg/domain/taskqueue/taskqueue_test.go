package taskqueue

import (
	"fmt"
	"math/rand"
	"net/http"
	"net/http/httptest"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"k8s.io/klog/v2"
)

var timeRecord time.Time

func TestTaskQueue(t *testing.T) {
	rand.Seed(time.Now().Unix())

	asd := func(s string) []error {
		timestring := fmt.Sprintf("%ds", rand.Intn(3)+1)
		duration, _ := time.ParseDuration(timestring)
		time.Sleep(duration)
		fmt.Printf("Test Error %d %s\n", duration, s)
		return nil
	}

	queue := NewTaskQueueRateLimit(asd, 4, "test", 5*time.Second, 1, NewMaxOfRateLimiter(
		NewItemExponentialFailureRateLimiter(1*time.Second, 15*time.Minute),
	))

	queue.Run()

	timeRecord = time.Now()

	for i := 0; i < 8; i++ {
		go func(i int) {
			for {
				weight := rand.Intn(100) + 1
				item := Item{
					Data:   fmt.Sprintf("%d", i),
					Weight: weight,
				}
				klog.Errorf("Enqueue %v", item)
				queue.Enqueue(item)
				time.Sleep(1 * time.Second)
			}
		}(i)
	}

	time.Sleep(5 * time.Minute)
}

func TestGenericTaskQueueString(t *testing.T) {
	// 测试字符串去重功能
	t.Run("字符串去重验证", func(t *testing.T) {
		var processedItems []string
		var mu sync.Mutex

		q := NewGenericTaskQueue[string](func(s string) []error {
			mu.Lock()
			defer mu.Unlock()
			time.Sleep(1000 * time.Millisecond)
			processedItems = append(processedItems, s)
			return nil
		}, 1, "dedup-string-queue") // 单worker确保顺序

		q.Run()
		defer q.Shutdown()

		// 测试数据 - 包含重复字符串
		dupItem := "duplicate-item"
		testItems := []string{
			dupItem,
			dupItem, // 重复项
			"unique-item",
		}

		// 快速连续添加相同字符串
		for _, item := range testItems {
			q.Enqueue(Item{Data: item})
		}

		// 等待处理完成
		time.Sleep(5 * time.Second)

		// 验证处理结果
		mu.Lock()
		defer mu.Unlock()

		// 1. 验证处理数量(应只处理2个: 第一个重复项和唯一项)
		if len(processedItems) != 2 {
			t.Errorf("期望处理 2 个item(去重后), 实际处理 %d", len(processedItems))
		}

		// 2. 验证处理顺序(应保留第一个添加的字符串)
		if len(processedItems) > 0 && processedItems[0] != dupItem {
			t.Errorf("期望保留第一个添加的字符串(%s), 实际处理的是 %s", dupItem, processedItems[0])
		}

		// 3. 验证唯一字符串被处理
		uniqueProcessed := false
		for _, item := range processedItems {
			if item == "unique-item" {
				uniqueProcessed = true
				break
			}
		}
		klog.Infof("processedItems: %v", processedItems)
		if !uniqueProcessed {
			t.Error("唯一字符串未被处理")
		}
	})

	// 测试基本功能
	t.Run("基本操作", func(t *testing.T) {
		var processedItems []string
		var mu sync.Mutex

		// 创建自定义队列
		q := NewGenericTaskQueue[string](func(s string) []error {
			mu.Lock()
			defer mu.Unlock()
			processedItems = append(processedItems, s)
			return nil
		}, 2, "test-queue")

		// 启动队列
		q.Run()
		defer q.Shutdown()

		// 添加测试数据
		testItems := []string{"item1", "item2", "item3"}
		for _, item := range testItems {
			q.Enqueue(Item{Data: item})
		}

		// 等待处理完成
		time.Sleep(500 * time.Millisecond)

		// 验证处理结果
		mu.Lock()
		if len(processedItems) != len(testItems) {
			t.Errorf("期望处理 %d 个item, 实际处理 %d", len(testItems), len(processedItems))
		}
		mu.Unlock()
	})

	// 测试错误重试
	t.Run("错误重试", func(t *testing.T) {
		var attemptCount int
		var mu sync.Mutex

		// 创建带重试的队列
		q := NewGenericTaskQueueRateLimit[int](
			func(_ int) []error {
				mu.Lock()
				defer mu.Unlock()
				attemptCount++
				if attemptCount < 3 {
					return []error{fmt.Errorf("模拟错误")}
				}
				return nil
			},
			1, // workers
			"retry-queue",
			100*time.Millisecond, // requeueDelay
			1,                    // requeueWeight
			NewItemExponentialFailureRateLimiter(100*time.Millisecond, 1*time.Second),
		)

		q.Run()
		defer q.Shutdown()

		// 添加测试数据
		q.Enqueue(Item{Data: 123})

		// 等待重试完成
		time.Sleep(1 * time.Second)

		// 验证重试次数
		mu.Lock()
		if attemptCount != 3 {
			t.Errorf("期望重试 3 次, 实际重试 %d 次", attemptCount)
		}
		mu.Unlock()
	})

	// 测试并发安全
	t.Run("并发安全", func(t *testing.T) {
		var processedCount int32

		// 创建队列
		q := NewGenericTaskQueue[int](func(_ int) []error {
			atomic.AddInt32(&processedCount, 1)
			return nil
		}, 5, "concurrent-queue")

		q.Run()
		defer q.Shutdown()

		// 并发添加100个item
		var wg sync.WaitGroup
		for i := 0; i < 100; i++ {
			wg.Add(1)
			go func(i int) {
				defer wg.Done()
				q.Enqueue(Item{Data: i})
			}(i)
		}
		wg.Wait()

		// 等待处理完成
		time.Sleep(1 * time.Second)

		// 验证处理数量
		if atomic.LoadInt32(&processedCount) != 100 {
			t.Errorf("期望处理 100 个item, 实际处理 %d", processedCount)
		}
	})

	// 测试优雅关闭
	t.Run("优雅关闭", func(t *testing.T) {
		var processedCount int
		var mu sync.Mutex

		q := NewGenericTaskQueue[string](func(_ string) []error {
			time.Sleep(100 * time.Millisecond) // 模拟耗时操作
			mu.Lock()
			processedCount++
			mu.Unlock()
			return nil
		}, 3, "shutdown-queue")

		q.Run()

		// 添加10个item
		for i := 0; i < 10; i++ {
			q.Enqueue(Item{Data: fmt.Sprintf("item-%d", i)})
		}

		// 立即关闭并等待
		done := make(chan struct{})
		go func() {
			q.Shutdown()
			close(done)
		}()

		select {
		case <-done:
			mu.Lock()
			if processedCount != 10 {
				t.Errorf("关闭前应处理所有item, 实际处理 %d/10", processedCount)
			}
			mu.Unlock()
		case <-time.After(2 * time.Second):
			t.Error("关闭超时")
		}
	})

	// 测试快速关闭
	t.Run("快速关闭", func(t *testing.T) {
		q := NewGenericTaskQueue[string](func(_ string) []error {
			time.Sleep(200 * time.Millisecond) // 模拟耗时操作
			return nil
		}, 2, "fast-shutdown-queue")

		q.Run()

		// 添加5个item
		for i := 0; i < 5; i++ {
			q.Enqueue(Item{Data: fmt.Sprintf("item-%d", i)})
		}

		// 快速关闭
		start := time.Now()
		q.FastShutdown()
		elapsed := time.Since(start)

		if elapsed > 1000*time.Millisecond {
			t.Errorf("快速关闭耗时过长: %v", elapsed)
		}
	})

	// 测试高负载队列
	t.Run("高负载队列测试", func(t *testing.T) {
		const (
			totalItems  = 1000 // 测试数据总量
			workerCount = 5    // 工作协程数
			delayMin    = 10   // 最小处理延时(毫秒)
			delayMax    = 50   // 最大处理延时(毫秒)
		)

		var processed int32
		var wg sync.WaitGroup
		startTime := time.Now()

		// 创建带延时处理的队列
		q := NewGenericTaskQueue[string](func(_ string) []error {
			// 模拟随机处理延时
			delay := time.Duration(rand.Intn(delayMax-delayMin)+delayMin) * time.Millisecond
			time.Sleep(delay)

			atomic.AddInt32(&processed, 1)
			wg.Done() // 标记一个任务完成
			return nil
		}, workerCount, "stress-queue")

		q.Run()
		defer q.Shutdown()

		// 启动实时监控协程
		stopMonitor := make(chan struct{})
		go func() {
			ticker := time.NewTicker(500 * time.Millisecond) // 每500ms检查一次
			defer ticker.Stop()

			for {
				select {
				case <-ticker.C:
					req := httptest.NewRequest("GET", "/queue/stress-queue", nil)
					w := httptest.NewRecorder()
					q.ServeHTTP(w, req)

					if w.Code == http.StatusOK {
						t.Logf("[实时监控] 队列状态: %s", w.Body.String())
					} else {
						t.Logf("[实时监控] HTTP错误: %d", w.Code)
					}
				case <-stopMonitor:
					return
				}
			}
		}()

		// 确保测试结束时停止监控
		defer close(stopMonitor)

		// 添加大量测试数据
		wg.Add(totalItems)
		for i := 0; i < totalItems; i++ {
			q.Enqueue(Item{Data: fmt.Sprintf("item-%d", i)})
		}

		// 等待所有任务完成或超时
		done := make(chan struct{})
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			elapsed := time.Since(startTime)
			t.Logf("处理完成 %d 个任务, 耗时: %v, 平均吞吐量: %.1f ops/sec",
				processed, elapsed, float64(totalItems)/elapsed.Seconds())
		case <-time.After(30 * time.Second):
			t.Errorf("处理超时, 已完成 %d/%d", atomic.LoadInt32(&processed), totalItems)
		}
	})
}

func TestGenericTaskQueueElement(t *testing.T) {
	// 测试高负载Element处理
	t.Run("高负载Element处理", func(t *testing.T) {
		const (
			totalItems  = 1000 // 测试数据总量
			workerCount = 5    // 工作协程数
			delayMin    = 10   // 最小处理延时(毫秒)
			delayMax    = 50   // 最大处理延时(毫秒)
		)

		var processed int32
		var wg sync.WaitGroup
		startTime := time.Now()

		// 创建带延时处理的队列
		q := NewGenericTaskQueue[Element](func(_ Element) []error {
			// 模拟随机处理延时
			delay := time.Duration(rand.Intn(delayMax-delayMin)+delayMin) * time.Millisecond
			time.Sleep(delay)

			atomic.AddInt32(&processed, 1)
			wg.Done() // 标记一个任务完成
			return nil
		}, workerCount, "element-stress-queue")

		q.Run()
		defer q.Shutdown()

		// 启动实时监控协程
		stopMonitor := make(chan struct{})
		go func() {
			ticker := time.NewTicker(500 * time.Millisecond) // 每500ms检查一次
			defer ticker.Stop()

			for {
				select {
				case <-ticker.C:
					req := httptest.NewRequest("GET", "/queue/element-stress-queue", nil)
					w := httptest.NewRecorder()
					q.ServeHTTP(w, req)

					if w.Code == http.StatusOK {
						t.Logf("[实时监控] 队列状态: %s", w.Body.String())
					} else {
						t.Logf("[实时监控] HTTP错误: %d", w.Code)
					}
				case <-stopMonitor:
					return
				}
			}
		}()
		defer close(stopMonitor)

		// 添加大量测试数据
		wg.Add(totalItems)
		for i := 0; i < totalItems; i++ {
			q.Enqueue(Item{Data: Element{
				Event: Event{Type: AddEvent, Core: fmt.Sprintf("data-%d", i)},
				Key:   fmt.Sprintf("key-%d", i),
			}})
		}

		// 等待所有任务完成或超时
		done := make(chan struct{})
		go func() {
			wg.Wait()
			close(done)
		}()

		select {
		case <-done:
			elapsed := time.Since(startTime)
			t.Logf("处理完成 %d 个Element, 耗时: %v, 平均吞吐量: %.1f ops/sec",
				processed, elapsed, float64(totalItems)/elapsed.Seconds())
		case <-time.After(30 * time.Second):
			t.Errorf("处理超时, 已完成 %d/%d", atomic.LoadInt32(&processed), totalItems)
		}
	})
	// 测试基本事件处理
	t.Run("处理不同类型事件", func(t *testing.T) {
		var processedEvents []EventType
		var mu sync.Mutex

		q := NewGenericTaskQueue[Element](func(e Element) []error {
			mu.Lock()
			defer mu.Unlock()
			processedEvents = append(processedEvents, e.Event.Type)
			return nil
		}, 3, "event-type-queue")

		q.Run()
		defer q.Shutdown()

		// 测试数据 - 各种事件类型
		testEvents := []Element{
			{Event: Event{Type: AddEvent}, Key: "add-1"},
			{Event: Event{Type: UpdateEvent}, Key: "update-1"},
			{Event: Event{Type: DeleteEvent}, Key: "delete-1"},
		}

		for _, event := range testEvents {
			q.Enqueue(Item{Data: event})
		}

		// 等待处理完成
		time.Sleep(300 * time.Millisecond)

		// 验证处理结果
		mu.Lock()
		if len(processedEvents) != len(testEvents) {
			t.Errorf("期望处理 %d 个事件, 实际处理 %d", len(testEvents), len(processedEvents))
		}
		mu.Unlock()
	})

	// 测试键值处理
	t.Run("键值唯一性处理", func(t *testing.T) {
		var processedKeys []string
		var mu sync.Mutex

		q := NewGenericTaskQueue[Element](func(e Element) []error {
			mu.Lock()
			defer mu.Unlock()
			processedKeys = append(processedKeys, e.Key)
			return nil
		}, 2, "key-unique-queue")

		q.Run()
		defer q.Shutdown()

		// 相同key不同事件的元素
		dupKey := "same-key"
		testElements := []Element{
			{Event: Event{Type: AddEvent}, Key: dupKey},
			{Event: Event{Type: UpdateEvent}, Key: dupKey},
			{Key: "unique-key"},
		}

		for _, elem := range testElements {
			q.Enqueue(Item{Data: elem})
		}

		// 等待处理完成
		time.Sleep(5000 * time.Millisecond)

		// 验证处理结果
		mu.Lock()
		if len(processedKeys) != 2 {
			t.Errorf("期望处理 %d 个元素, 实际处理 %d", 2, len(processedKeys))
		}
		mu.Unlock()
	})

	// 测试核心数据完整性
	t.Run("核心数据完整性", func(t *testing.T) {
		type testCore struct {
			ID   int
			Name string
		}

		var processedCores []testCore
		var mu sync.Mutex

		q := NewGenericTaskQueue[Element](func(e Element) []error {
			if core, ok := e.Event.Core.(testCore); ok {
				mu.Lock()
				processedCores = append(processedCores, core)
				mu.Unlock()
			}
			return nil
		}, 1, "core-data-queue")

		q.Run()
		defer q.Shutdown()

		// 测试数据
		testData := []testCore{
			{ID: 1, Name: "test1"},
			{ID: 2, Name: "test2"},
		}

		for _, data := range testData {
			q.Enqueue(Item{
				Data: Element{
					Event: Event{
						Type: UpdateEvent,
						Core: data,
					},
					Key: fmt.Sprintf("key-%d", data.ID),
				},
			})
		}

		// 等待处理完成
		time.Sleep(1 * time.Second)

		// 验证处理结果
		mu.Lock()
		if len(processedCores) != len(testData) {
			t.Errorf("期望处理 %d 个核心数据, 实际处理 %d", len(testData), len(processedCores))
		}
		for i, core := range processedCores {
			if core != testData[i] {
				t.Errorf("第 %d 个核心数据不匹配, 期望 %v, 实际 %v", i, testData[i], core)
			}
		}
		mu.Unlock()
	})

	// 测试Element去重功能
	t.Run("Element去重验证", func(t *testing.T) {
		var processedItems []Element
		var mu sync.Mutex

		asd := func(e Element) []error {
			mu.Lock()
			defer mu.Unlock()
			time.Sleep(1000 * time.Millisecond)
			processedItems = append(processedItems, e)
			return nil
		}

		q := NewGenericTaskQueueRateLimit[Element](asd, 1, "test", 0, 0, NewMaxOfRateLimiter(
			NewItemExponentialFailureRateLimiter(1*time.Second, 15*time.Minute),
		))

		q.Run()
		defer q.Shutdown()

		// 测试数据 - 相同key不同内容的Element
		dupKey := "duplicate-key"
		testElements := []Element{
			{Event: Event{Type: AddEvent, Core: "data1"}, Key: dupKey},
			{Event: Event{Type: UpdateEvent, Core: "data2"}, Key: dupKey},
			{Event: Event{Type: UpdateEvent, Core: "data3"}, Key: dupKey},
			{Event: Event{Type: DeleteEvent, Core: "data4"}, Key: "unique-key"},
		}

		// 快速连续添加相同key的元素
		for _, elem := range testElements {
			q.Enqueue(Item{Data: elem})
		}

		// 等待处理完成
		time.Sleep(5 * time.Second)

		// 验证处理结果
		mu.Lock()
		defer mu.Unlock()

		// 1. 验证处理数量(应只处理2个: 第一个重复key和唯一key)
		if len(processedItems) != 2 {
			t.Errorf("期望处理 2 个元素(去重后), 实际处理 %d", len(processedItems))
		}

		// 2. 验证处理顺序(应保留第一个添加的元素)
		if len(processedItems) > 0 && processedItems[0].Event.Type != AddEvent {
			t.Errorf("期望保留第一个添加的元素(AddEvent), 实际处理的是 %v", processedItems[0].Event.Type)
		}

		// 3. 验证唯一key的元素被处理
		uniqueProcessed := false
		for _, item := range processedItems {
			if item.Key == "unique-key" {
				uniqueProcessed = true
				break
			}
		}
		klog.Infof("processedItems: %v", processedItems)
		if !uniqueProcessed {
			t.Error("唯一key的元素未被处理")
		}
	})

	// 测试错误处理和重试
	t.Run("元素处理错误重试", func(t *testing.T) {
		var attemptCount int
		var mu sync.Mutex

		q := NewGenericTaskQueueRateLimit[Element](
			func(_ Element) []error {
				mu.Lock()
				defer mu.Unlock()
				attemptCount++
				if attemptCount < 2 {
					return []error{fmt.Errorf("模拟错误")}
				}
				return nil
			},
			1,
			"element-retry-queue",
			100*time.Millisecond,
			1,
			NewItemExponentialFailureRateLimiter(100*time.Millisecond, 1*time.Second),
		)

		q.Run()
		defer q.Shutdown()

		// 添加测试元素
		q.Enqueue(Item{Data: Element{
			Event: Event{Type: AddEvent},
			Key:   "retry-item",
		}})

		// 等待重试完成
		time.Sleep(500 * time.Millisecond)

		// 验证重试次数
		mu.Lock()
		if attemptCount != 2 {
			t.Errorf("期望重试 2 次, 实际重试 %d 次", attemptCount)
		}
		mu.Unlock()
	})

	// 测试错误重试
	t.Run("错误重试", func(t *testing.T) {
		var attemptCount int
		var mu sync.Mutex

		// 创建带重试的队列
		q := NewGenericTaskQueueRateLimit[int](
			func(_ int) []error {
				mu.Lock()
				defer mu.Unlock()
				attemptCount++
				if attemptCount < 3 {
					return []error{fmt.Errorf("模拟错误")}
				}
				return nil
			},
			1, // workers
			"retry-queue",
			100*time.Millisecond, // requeueDelay
			1,                    // requeueWeight
			NewItemExponentialFailureRateLimiter(100*time.Millisecond, 1*time.Second),
		)

		q.Run()
		defer q.Shutdown()

		// 添加测试数据
		q.Enqueue(Item{Data: 123})

		// 等待重试完成
		time.Sleep(1 * time.Second)

		// 验证重试次数
		mu.Lock()
		if attemptCount != 3 {
			t.Errorf("期望重试 3 次, 实际重试 %d 次", attemptCount)
		}
		mu.Unlock()
	})

	// 测试并发安全
	t.Run("并发安全", func(t *testing.T) {
		var processedCount int32

		// 创建队列
		q := NewGenericTaskQueue[int](func(_ int) []error {
			atomic.AddInt32(&processedCount, 1)
			return nil
		}, 5, "concurrent-queue")

		q.Run()
		defer q.Shutdown()

		// 并发添加100个item
		var wg sync.WaitGroup
		for i := 0; i < 100; i++ {
			wg.Add(1)
			go func(i int) {
				defer wg.Done()
				q.Enqueue(Item{Data: i})
			}(i)
		}
		wg.Wait()

		// 等待处理完成
		time.Sleep(1 * time.Second)

		// 验证处理数量
		if atomic.LoadInt32(&processedCount) != 100 {
			t.Errorf("期望处理 100 个item, 实际处理 %d", processedCount)
		}
	})

	// 测试优雅关闭
	t.Run("优雅关闭", func(t *testing.T) {
		var processedCount int
		var mu sync.Mutex

		q := NewGenericTaskQueue[string](func(_ string) []error {
			time.Sleep(100 * time.Millisecond) // 模拟耗时操作
			mu.Lock()
			processedCount++
			mu.Unlock()
			return nil
		}, 3, "shutdown-queue")

		q.Run()

		// 添加10个item
		for i := 0; i < 10; i++ {
			q.Enqueue(Item{Data: fmt.Sprintf("item-%d", i)})
		}

		// 立即关闭并等待
		done := make(chan struct{})
		go func() {
			q.Shutdown()
			close(done)
		}()

		select {
		case <-done:
			mu.Lock()
			if processedCount != 10 {
				t.Errorf("关闭前应处理所有item, 实际处理 %d/10", processedCount)
			}
			mu.Unlock()
		case <-time.After(2 * time.Second):
			t.Error("关闭超时")
		}
	})

	// 测试快速关闭
	t.Run("快速关闭", func(t *testing.T) {
		q := NewGenericTaskQueue[string](func(_ string) []error {
			time.Sleep(200 * time.Millisecond) // 模拟耗时操作
			return nil
		}, 2, "fast-shutdown-queue")

		q.Run()

		// 添加5个item
		for i := 0; i < 5; i++ {
			q.Enqueue(Item{Data: fmt.Sprintf("item-%d", i)})
		}

		// 快速关闭
		start := time.Now()
		q.FastShutdown()
		elapsed := time.Since(start)

		if elapsed > 100*time.Millisecond {
			t.Errorf("快速关闭耗时过长: %v", elapsed)
		}
	})

	// 测试HTTP端点
	t.Run("HTTP端点", func(t *testing.T) {
		q := NewGenericTaskQueue[string](func(_ string) []error {
			return nil
		}, 1, "http-queue")

		q.Run()
		defer q.Shutdown()

		// 模拟HTTP请求
		req := httptest.NewRequest("GET", "/", nil)
		w := httptest.NewRecorder()
		q.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("HTTP状态码错误: %d", w.Code)
		}
	})
}

// 定义一个实现 fmt.Stringer 接口的类型用于测试
type testStringer struct {
	value string
}

func (t testStringer) String() string {
	return t.value
}

func TestSetMethods(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name     string
		dataType string // "stringer", "string", "other"
		value    interface{}
	}{
		{"stringer type", "stringer", testStringer{"test1"}},
		// {"string type", "string", "test2"},
		// {"int type", "other", 123},
		// {"struct type", "other", struct{ name string }{name: "test3"}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := make(set)
			item := Item{Data: tt.value}

			// 测试 insert 和 has
			t.Run("insert and has", func(t *testing.T) {
				s.insert(item)
				if !s.has(item) {
					t.Errorf("insert() failed, has() returned false for %v", tt.value)
				}
			})

			// 测试 get
			t.Run("get", func(t *testing.T) {
				got := s.get(item)
				if got.Data != item.Data {
					t.Errorf("get() = %v, want %v", got.Data, item.Data)
				}
			})

			// 测试 delete
			t.Run("delete", func(t *testing.T) {
				s.delete(item)
				if s.has(item) {
					t.Errorf("delete() failed, has() returned true after deletion")
				}

				// 验证 get 返回空 Item
				if got := s.get(item); got != (Item{}) {
					t.Errorf("get() after delete = %v, want empty Item", got)
				}
			})
		})
	}
}

// 测试空值处理
func TestEmptyValueHandling(t *testing.T) {
	s := make(set)

	t.Run("empty string", func(t *testing.T) {
		item := Item{Data: ""}
		s.insert(item)
		if !s.has(item) {
			t.Error("has() returned false for empty string")
		}
		s.delete(item)
		if s.has(item) {
			t.Error("has() returned true after deleting empty string")
		}
	})

	t.Run("nil value", func(t *testing.T) {
		item := Item{Data: nil}
		s.insert(item)
		if !s.has(item) {
			t.Error("has() returned false for nil")
		}
		s.delete(item)
		if s.has(item) {
			t.Error("has() returned true after deleting nil")
		}
	})

	t.Run("empty Stringer", func(t *testing.T) {
		item := Item{Data: testStringer{""}}
		s.insert(item)
		if !s.has(item) {
			t.Error("has() returned false for empty Stringer")
		}
		s.delete(item)
		if s.has(item) {
			t.Error("has() returned true after deleting empty Stringer")
		}
	})
}

// 测试不同类型之间的区别
func TestTypeDistinction(t *testing.T) {
	s := make(set)

	stringItem := Item{Data: "123"}
	intItem := Item{Data: 123}
	stringerItem := Item{Data: testStringer{"123"}}

	s.insert(stringItem)
	s.insert(intItem)
	s.insert(stringerItem) // 因为具有同样的key，所以stringerItem覆盖了stringItem

	if !s.has(stringItem) || !s.has(intItem) || !s.has(stringerItem) {
		t.Error("has() failed to distinguish between different types")
	}
	if got := s.get(intItem); got.Data != intItem.Data {
		t.Errorf("get(int) = %v, want %v", got.Data, intItem.Data)
	}
	if got := s.get(stringerItem); got.Data != stringerItem.Data {
		t.Errorf("get(stringer) = %v, want %v", got.Data, stringerItem.Data)
	}

	s.delete(stringItem)
	if s.has(stringItem) {
		t.Error("delete(string) failed")
	}
	if !s.has(intItem) {
		t.Error("delete(string) affected other types")
	}
}

// 测试并发安全性（虽然 set 本身不保证并发安全，但可以测试基本功能）
func TestConcurrentAccess(t *testing.T) {
	s := make(set)
	item := Item{Data: "test"}
	done := make(chan bool)

	go func() {
		s.insert(item)
		done <- true
	}()
	<-done

	if !s.has(item) {
		t.Error("insert in goroutine not visible in main goroutine")
	}

	go func() {
		s.delete(item)
		done <- true
	}()
	<-done

	if s.has(item) {
		t.Error("delete in goroutine not visible in main goroutine")
	}
}

// 测试 Item 结构体完整性的保持
func TestItemIntegrity(t *testing.T) {
	s := make(set)
	now := time.Now()
	item := Item{
		Data:    "test",
		Weight:  10,
		AddTime: now,
		GetTime: now.Add(time.Second),
	}

	s.insert(item)
	got := s.get(item)

	if got.Weight != item.Weight || got.AddTime != item.AddTime || got.GetTime != item.GetTime {
		t.Errorf("get() returned item with modified fields, got %+v, want %+v", got, item)
	}
}

// 测试用的实现了 fmt.Stringer 的结构体
type testItem struct {
	key string
}

func (t testItem) String() string {
	return t.key
}

// BenchmarkHas-8   	100000000	        10.2 ns/op
func BenchmarkHas(b *testing.B) {
	s := make(set)
	item := Item{Data: testItem{key: "test"}}
	s.insert(item)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		s.has(item)
	}
}

// BenchmarkGet-8   	100000000	        11.5 ns/op
func BenchmarkGet(b *testing.B) {
	s := make(set)
	item := Item{Data: testItem{key: "test"}}
	s.insert(item)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		s.get(item)
	}
}

// BenchmarkInsert-8   	50000000	        30.1 ns/op
func BenchmarkInsert(b *testing.B) {
	s := make(set)
	item := Item{Data: testItem{key: "test"}}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		s.insert(item)
	}
}

// BenchmarkDelete-8   	100000000	        12.8 ns/op
func BenchmarkDelete(b *testing.B) {
	s := make(set)
	item := Item{Data: testItem{key: "test"}}
	s.insert(item)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		s.delete(item)
	}
}

// 混合操作基准测试
// BenchmarkMixedOps-8   	30000000	        45.3 ns/op
func BenchmarkMixedOps(b *testing.B) {
	s := make(set)
	items := make([]Item, 100)
	for i := range items {
		items[i] = Item{Data: testItem{key: string(rune(i))}}
		s.insert(items[i])
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		item := items[i%100]
		s.has(item)
		s.get(item)
		s.delete(item)
		s.insert(item)
	}
}
