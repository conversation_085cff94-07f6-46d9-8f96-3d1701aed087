package taskqueue

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/samber/lo"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/server"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/runtime"
)

var (
	keyFunc = cache.DeletionHandlingMetaNamespaceKeyFunc
)

// 定义服务入队权重常量
const (
	EnqueueHighPriorityWeight = 1   // 最高优先级权重 （kubernetes-intranet/extranet service入队）
	EnqueueDefaultWeight      = 50  // 默认权重（普通service事件）
	EnqueueNodeSyncWeight     = 75  // 节点同步权重
	EnqueueLowPriorityWeight  = 100 // 低优先级权重
)

// TaskQueue manages a work queue through an independent worker that
// invokes the given sync function for every work item inserted.
type TaskQueue struct {
	// queue is the work queue the worker polls
	queue RateLimitingInterface
	// sync is called for each item in the queue
	sync func(string) []error
	// when item need to requeue, delay any time to requeue
	requeueDelay time.Duration
	// when item need to requeue, add any weight for the item to requeue
	requeueWeight int
	workers       int
	rateLimiter   RateLimiter
	name          string
	workerGroup   *sync.WaitGroup

	rw      sync.RWMutex
	running bool
}

func (t *TaskQueue) FastShutdown() {
	t.rw.Lock()
	defer t.rw.Unlock()

	if !t.running {
		return
	}
	t.queue.ShutDown()
	// 队列ShutDown之后如果队列里边已经有元素则worker会持续处理，导致暂停时间过长，因此这里使用循环快速排空队列
	t.drain()
	t.workerGroup.Wait()

	t.running = false
}

func (t *TaskQueue) drain() {
	for {
		item, quit := t.queue.Get()
		if quit {
			return
		}
		t.queue.Forget(item)
		t.queue.Done(item)
	}
}

func (t *TaskQueue) Run() {
	t.rw.Lock()
	defer t.rw.Unlock()

	if t.running {
		return
	}

	t.queue = NewNamedRateLimitingQueue(t.rateLimiter, t.name)
	for i := range t.workers {
		t.workerGroup.Add(1)
		go func(index int) {
			defer t.workerGroup.Done()

			t.process()
			klog.Infof("TaskQueue worker[%d] done.", index)
		}(i)
	}
	t.running = true
}

// Enqueue enqueues ns/name of the given api object in the task queue.
func (t *TaskQueue) Enqueue(item Item) {
	t.rw.RLock()
	defer t.rw.RUnlock()

	if !t.running {
		return
	}

	t.queue.Forget(item)
	t.queue.Add(item)
}

// process processes process in the queue through sync.
func (t *TaskQueue) process() {
	for {
		if t.doWork() {
			return
		}
	}
}

func (t *TaskQueue) doWork() bool {
	item, quit := t.queue.Get()
	if quit {
		return true
	}

	defer func(item Item) {
		t.queue.Done(item)
		if r := recover(); r != nil {
			klog.Errorf("E6007 internal error(%v): %v[stacktrace done]", item.Data, runtime.GetPanicError(r))
			t.requeue(item)
		}
	}(item)

	if errs := t.sync(item.Data.(string)); len(errs) != 0 {
		isRequeue := false
		for _, err := range errs {
			if typesError, ok := lo.ErrorsAs[*types.Error](err); ok {
				if typesError.NeedRetryImmediate() {
					klog.Errorf("sync immediate requeue %v, err %v", item, errs)
					t.done(item)
					t.requeue(*NewItem(item.Data))
					return false
				}
				if typesError.NeedRetry() {
					isRequeue = true
				}
			} else {
				isRequeue = true
			}
		}

		if isRequeue {
			klog.Errorf("sync requeue %v, err %v", item, errs)
			t.requeue(item)
		} else {
			klog.Errorf("sync ignore %v, err %v", item, errs)
			t.done(item)
		}
	} else {
		klog.Infof("sync ok %v", item.Data)
		t.done(item)
	}
	return false
}

func (t *TaskQueue) requeue(item Item) {
	item.Weight = item.Weight + t.requeueWeight
	if t.requeueDelay != 0 {
		time.AfterFunc(t.requeueDelay, func() {
			t.queue.AddRateLimited(item)
		})
	} else {
		t.queue.AddRateLimited(item)
	}
}

func (t *TaskQueue) done(item Item) {
	t.queue.Forget(item)
}

// Shutdown shuts down the work queue and waits for the worker to ACK
func (t *TaskQueue) Shutdown() {
	t.rw.Lock()
	defer t.rw.Unlock()

	if !t.running {
		return
	}

	t.queue.ShutDown()
	t.workerGroup.Wait()
	t.running = false
}

func (t *TaskQueue) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	t.rw.RLock()
	defer t.rw.RUnlock()

	if t.queue == nil {
		w.WriteHeader(http.StatusOK)
		return
	}

	t.queue.ServeHTTP(w, req)
}

// NewTaskQueue creates a new task queue with the given sync function.
// The sync function is called for every element inserted into the queue.
func NewTaskQueue(syncFn func(string) []error, workers int, name string) *TaskQueue {
	return &TaskQueue{
		sync:        syncFn,
		workers:     workers,
		rateLimiter: DefaultControllerRateLimiter(),
		name:        name,
		workerGroup: &sync.WaitGroup{},
	}
}

// NewTaskQueue creates a new task queue with the given sync function.
// The sync function is called for every element inserted into the queue.
func NewTaskQueueRateLimit(syncFn func(string) []error, workers int, name string, requeueDelay time.Duration,
	requeueWeight int, rateLimiter RateLimiter) *TaskQueue {

	t := &TaskQueue{
		sync:          syncFn,
		workers:       workers,
		requeueDelay:  requeueDelay,
		requeueWeight: requeueWeight,
		rateLimiter:   rateLimiter,
		name:          name,
		workerGroup:   &sync.WaitGroup{},
	}

	server.Register(fmt.Sprintf("/queue/%s", name), t.ServeHTTP)

	return t
}
