package taskqueue

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/samber/lo"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/server"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/runtime"
)

// TaskQueue 泛型任务队列，支持任意类型T的任务项
type GenericTaskQueue[T any] struct {
	// queue is the work queue the worker polls
	queue RateLimitingInterface
	// sync 泛型处理函数，支持任意类型T的入参
	sync func(T) []error
	// when item need to requeue, delay any time to requeue
	requeueDelay time.Duration
	// when item need to requeue, add any weight for the item to requeue
	requeueWeight int
	workers       int
	rateLimiter   RateLimiter
	name          string
	workerGroup   *sync.WaitGroup

	rw      sync.RWMutex
	running bool
}

type EventType string

type Event struct {
	Type EventType
	Core interface{}
}

const (
	AddEvent EventType = "ADD"
	// UpdateEvent event associated with an object update in an informer
	UpdateEvent EventType = "UPDATE"

	DeleteEvent EventType = "DELETE"
)

type Element struct {
	Event Event
	Key   string
}

func (e Element) String() string {
	return e.Key
}

func (t *GenericTaskQueue[T]) FastShutdown() {
	t.rw.Lock()
	defer t.rw.Unlock()

	if !t.running {
		return
	}
	t.queue.ShutDown()
	// 队列ShutDown之后如果队列里边已经有元素则worker会持续处理，导致暂停时间过长，因此这里使用循环快速排空队列
	t.drain()
	t.workerGroup.Wait()

	t.running = false
}

func (t *GenericTaskQueue[T]) drain() {
	for {
		item, quit := t.queue.Get()
		if quit {
			return
		}
		t.queue.Forget(item)
		t.queue.Done(item)
	}
}

func (t *GenericTaskQueue[T]) Run() {
	t.rw.Lock()
	defer t.rw.Unlock()

	if t.running {
		return
	}

	t.queue = NewNamedRateLimitingQueue(t.rateLimiter, t.name)
	for i := range t.workers {
		t.workerGroup.Add(1)
		go func(index int) {
			defer t.workerGroup.Done()

			t.process()
			klog.Infof("TaskQueue worker[%d] done.", index)
		}(i)
	}
	t.running = true
}

// Enqueue enqueues ns/name of the given api object in the task queue.
func (t *GenericTaskQueue[T]) Enqueue(item Item) {
	t.rw.RLock()
	defer t.rw.RUnlock()

	if !t.running {
		return
	}

	t.queue.Forget(item)
	t.queue.Add(item)
}

// process processes process in the queue through sync.
func (t *GenericTaskQueue[T]) process() {
	for {
		if t.doWork() {
			return
		}
	}
}

func (t *GenericTaskQueue[T]) doWork() bool {
	item, quit := t.queue.Get()
	if quit {
		return true
	}

	defer func(item Item) {
		t.queue.Done(item)
		if r := recover(); r != nil {
			klog.Errorf("E6007 internal error(%v): %v[stacktrace done]", item.Data, runtime.GetPanicError(r))
			t.requeue(item)
		}
	}(item)

	// 安全类型转换检查
	data, ok := item.Data.(T)
	if !ok {
		panic(fmt.Errorf("invalid type %T", item.Data))
	}

	if errs := t.sync(data); len(errs) != 0 {
		isRequeue := false
		for _, err := range errs {
			if typesError, ok := lo.ErrorsAs[*types.Error](err); ok {
				if typesError.NeedRetryImmediate() {
					t.done(item)
					t.requeue(*NewItem(item.Data))
					return false
				}
				if typesError.NeedRetry() {
					isRequeue = true
				}
			} else {
				isRequeue = true
			}
		}

		if isRequeue {
			klog.Errorf("Requeuing %v, err %v", item, errs)
			t.requeue(item)
		} else {
			t.done(item)
		}
	} else {
		t.done(item)
	}
	return false
}

func (t *GenericTaskQueue[T]) requeue(item Item) {
	item.Weight = item.Weight + t.requeueWeight
	if t.requeueDelay != 0 {
		time.AfterFunc(t.requeueDelay, func() {
			t.queue.AddRateLimited(item)
		})
	} else {
		t.queue.AddRateLimited(item)
	}
}

func (t *GenericTaskQueue[T]) done(item Item) {
	t.queue.Forget(item)
}

// Shutdown shuts down the work queue and waits for the worker to ACK
func (t *GenericTaskQueue[T]) Shutdown() {
	t.rw.Lock()
	defer t.rw.Unlock()

	if !t.running {
		return
	}

	t.queue.ShutDown()
	t.workerGroup.Wait()
	t.running = false
}

func (t *GenericTaskQueue[T]) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	t.rw.RLock()
	defer t.rw.RUnlock()

	if t.queue == nil {
		w.WriteHeader(http.StatusOK)
		return
	}

	t.queue.ServeHTTP(w, req)
}

// NewTaskQueue creates a new task queue with the given sync function.
// The sync function is called for every element inserted into the queue.
func NewGenericTaskQueue[T any](syncFn func(T) []error, workers int, name string) *GenericTaskQueue[T] {
	return &GenericTaskQueue[T]{
		sync:        syncFn,
		workers:     workers,
		rateLimiter: DefaultControllerRateLimiter(),
		name:        name,
		workerGroup: &sync.WaitGroup{},
	}
}

// NewTaskQueueRateLimit creates a new task queue with the given sync function.
// The sync function is called for every element inserted into the queue.
func NewGenericTaskQueueRateLimit[T any](syncFn func(T) []error, workers int, name string, requeueDelay time.Duration, requeueWeight int, rateLimiter RateLimiter) *GenericTaskQueue[T] {
	t := &GenericTaskQueue[T]{
		sync:          syncFn,
		workers:       workers,
		requeueDelay:  requeueDelay,
		requeueWeight: requeueWeight,
		rateLimiter:   rateLimiter,
		name:          name,
		workerGroup:   &sync.WaitGroup{},
	}

	server.Register(fmt.Sprintf("/queue/%s", name), t.ServeHTTP)

	return t
}

// 添加泛型参数T到TaskQueueOps结构体
// TaskQueueOps 任务队列配置选项
type Ops[T any] struct {
	SyncFn        func(T) []error // 同步处理函数
	Workers       int             // 工作协程数量
	Name          string          // 队列名称
	RequeueDelay  time.Duration   // 重试延迟时间
	RequeueWeight int             // 重试权重
	RateLimiter   RateLimiter     // 限流器
	DisableHTTP   bool            // 是否禁用HTTP端点
}

// NewTaskQueueRateLimitWithOps 创建带配置选项的任务队列
func NewGenericTaskQueueRateLimitWithOps[T any](ops *Ops[T]) *GenericTaskQueue[T] {
	t := &GenericTaskQueue[T]{
		sync:          ops.SyncFn,
		workers:       ops.Workers,
		requeueDelay:  ops.RequeueDelay,
		requeueWeight: ops.RequeueWeight,
		rateLimiter:   ops.RateLimiter,
		name:          ops.Name,
		workerGroup:   &sync.WaitGroup{},
	}

	// 只有当不禁用HTTP时才注册端点
	if !ops.DisableHTTP {
		server.Register(fmt.Sprintf("/queue/%s", ops.Name), t.ServeHTTP)
	}

	return t
}
