package cloudctx

import (
	"context"
	"strings"

	"github.com/google/uuid"

	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/intstr"
)

type contextKey int

const (
	requestIDContextKey contextKey = iota
	regionContextKey
	nameContextKey
	namespaceContextKey
	appidContextKey
	uinContextKey
	subUINContextKey
	instanceIDContextKey
	dealnameContextKey
	actionContextKey
	traceContextKey
	objectContextKey
)

func From(ctx context.Context, object types.Object, region string) context.Context {
	return WithRegion(WithObject(ctx, object), region)
}

func New(object types.Object, region string) context.Context {
	return WithRegion(WithObject(context.Background(), object), region)
}

func WithValue(parent context.Context, key contextKey, value interface{}) context.Context {
	return context.WithValue(parent, key, value)
}

func ValueFrom(ctx context.Context, key contextKey) interface{} {
	return ctx.Value(key)
}

func WithAction(parent context.Context, value string) context.Context {
	return WithValue(parent, actionContextKey, value)
}

func Action(ctx context.Context) string {
	value, ok := ValueFrom(ctx, actionContextKey).(string)
	if !ok {
		return "invalid action"
	}
	return value
}

func WithRequestID(parent context.Context, value string) context.Context {
	return WithValue(parent, requestIDContextKey, value)
}

func RequestID(ctx context.Context) string {
	value, ok := ValueFrom(ctx, requestIDContextKey).(string)
	if !ok {
		s := uuid.New().String()
		value = strings.Replace(s, "-", "", -1)
	}

	return value
}

func WithRegion(parent context.Context, value string) context.Context {
	if value == "" {
		value = cluster.Instance.RegionName()
	}
	return WithValue(parent, regionContextKey, value)
}

func Region(ctx context.Context) string {
	value, ok := ValueFrom(ctx, regionContextKey).(string)
	if !ok {
		panic("can't get region")
	}

	return value
}

func WithName(parent context.Context, value string) context.Context {
	return WithValue(parent, nameContextKey, value)
}

func Name(ctx context.Context) string {
	value, ok := ValueFrom(ctx, nameContextKey).(string)
	if !ok {
		panic("can't get name")
	}

	return value
}

func WithNamespace(parent context.Context, value string) context.Context {
	return WithValue(parent, namespaceContextKey, value)
}

func Namespace(ctx context.Context) string {
	value, ok := ValueFrom(ctx, namespaceContextKey).(string)
	if !ok {
		panic("can't get namespace")
	}

	return value
}

func WithAppid(parent context.Context, value intstr.Uint64OrString) context.Context {
	return WithValue(parent, appidContextKey, value)
}

func Appid(ctx context.Context) intstr.Uint64OrString {
	value, ok := ValueFrom(ctx, appidContextKey).(intstr.Uint64OrString)
	if !ok {
		panic("can't get Appid")
	}

	return value
}

func WithUin(parent context.Context, value string) context.Context {
	return WithValue(parent, uinContextKey, value)
}

func UIN(ctx context.Context) string {
	value, ok := ValueFrom(ctx, uinContextKey).(string)
	if !ok {
		panic("can't get UIN")
	}

	return value
}

func WithSubUIN(parent context.Context, value string) context.Context {
	return WithValue(parent, subUINContextKey, value)
}

func HasSubUIN(ctx context.Context) bool {
	_, ok := ValueFrom(ctx, subUINContextKey).(string)
	return ok
}

func SubUIN(ctx context.Context) string {
	value, ok := ValueFrom(ctx, subUINContextKey).(string)
	if !ok {
		panic("can't get SubUIN")
	}

	return value
}

func WithInstanceID(parent context.Context, value string) context.Context {
	return WithValue(parent, instanceIDContextKey, value)
}

func InstanceID(ctx context.Context) string {
	value, ok := ValueFrom(ctx, instanceIDContextKey).(string)
	if !ok {
		return ""
	}

	return value
}

func WithDealname(parent context.Context, value string) context.Context {
	return WithValue(parent, dealnameContextKey, value)
}

func Dealname(ctx context.Context) string {
	value, ok := ValueFrom(ctx, dealnameContextKey).(string)
	if !ok {
		return ""
	}

	return value
}
func WithTraceID(parent context.Context, value string) context.Context {
	return WithValue(parent, traceContextKey, value)
}
func TraceID(ctx context.Context) string {
	value, ok := ValueFrom(ctx, traceContextKey).(string)
	if !ok {
		s := uuid.New().String()
		value = strings.Replace(s, "-", "", -1)
	}

	return value
}

func WithObject(parent context.Context, value types.Object) context.Context {
	return WithValue(parent, objectContextKey, value)
}
func Object(ctx context.Context) types.Object {
	value, ok := ValueFrom(ctx, objectContextKey).(types.Object)
	if !ok {
		panic("can't get Object")
	}

	return value
}

func GetObject(ctx context.Context) (types.Object, bool) {
	value, ok := ValueFrom(ctx, objectContextKey).(types.Object)
	return value, ok
}
