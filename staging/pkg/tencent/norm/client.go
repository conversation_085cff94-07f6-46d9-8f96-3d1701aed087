package norm

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"time"

	//"github.com/golang/glog"
	glog "k8s.io/klog/v2"
)

// 对方返回的请求中,code部分不为0,此时的code为对方返回的code,含义不等于内部Err中的code
type RequestResultError struct {
	Code          int
	Msg           string
	InterfaceName string
}

func (c RequestResultError) IsSucc() bool {
	return c.Code == 0
}

func (c RequestResultError) Error() string {
	return fmt.Sprintf("RequestInvokeError,Code : %d , Interface: %s, Msg : %s", c.Code, c.InterfaceName, c.Msg)
}

func newRequestResponseError(code int, msg string, interfaceName string) *RequestResultError {
	return &RequestResultError{
		Code:          code,
		Msg:           msg,
		InterfaceName: interfaceName,
	}
}

type packer interface {
	packRequest(interfaceName string, reqObj interface{}) ([]byte, error)
	unPackResponse(data []byte, responseObj interface{}) error
	getResult() (int, string)
}

type httpClient struct {
	url     string
	timeout uint
	caller  string
	callee  string
	packer
}

func (c *httpClient) DoRequest(interfaceName string, request interface{}, response interface{}) error {
	var code int
	var msg string

	requestData, err := c.packRequest(interfaceName, request)
	if err != nil {
		return fmt.Errorf("failed to pack the request: %w", err)
	}
	httpRequest, err := http.NewRequest("POST", c.url, bytes.NewReader(requestData))
	if err != nil {
		return fmt.Errorf("failed to build the request: %w", err)
	}
	//glog.Info("Http request to: ", c.url, ", interface: ", interfaceName, ", request: ", string(requestData))

	http.DefaultClient.Timeout = time.Duration(c.timeout) * time.Second
	httpResponse, err := http.DefaultClient.Do(httpRequest)
	defer func() {
		if httpResponse != nil {
			httpResponse.Body.Close()
		}
	}()
	if err != nil {
		return fmt.Errorf("failed to exec the request: %w", err)
	}

	if httpResponse.StatusCode != 200 {
		return fmt.Errorf("invalid http response status: %s", httpResponse.Status)
	}
	body, err := io.ReadAll(httpResponse.Body)
	if err != nil {
		return fmt.Errorf("failed to read http response: %w", err)
	}

	//glog.Info("Http response from: ", c.url, ", interface: ", interfaceName, ", response: ", string(body))

	err = c.unPackResponse(body, response)
	if err != nil {
		return fmt.Errorf("failed to unpack the response body(%s): %w", body, err)
	}
	code, msg = c.getResult()
	if code != 0 {
		glog.Error("interfaceName:", interfaceName, " c.getResult code: ", code, "msg:", msg)
		return newRequestResponseError(code, msg, interfaceName)
	}
	return nil
}
