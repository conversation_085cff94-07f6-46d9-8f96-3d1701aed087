package norm

import (
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"os"
	"time"

	"github.com/bitly/go-simplejson"

	//"github.com/golang/glog"
	glog "k8s.io/klog/v2"
)

type Config struct {
	URL string
}

var (
	ErrTKERoleNotFound          = errors.New("TKE_QCS_Role not found")
	ErrIPMasqAgentConfigChanged = errors.New("ip-masq-agent-config may have changed")
	ErrInternal                 = errors.New("")

	config *Config
)

const (
	InterfaceAssumeTKECredential = "NORM.AssumeTkeCredential"
	InterfaceGetClusterUIN       = "NORM.GetClusterUin"

	EnvClusterID = "CLUSTER_ID"
	EnvAPPID     = "APPID"
)

func Init(cfg Config) {
	config = &cfg
}

type Request map[string]interface{}

type Response struct {
	Code     int         `json:"returnValue"`
	Msg      string      `json:"returnMsg"`
	Version  string      `json:"version"`
	Password string      `json:"password"`
	Data     interface{} `json:"returnData"`
}

type client struct {
	httpClient
	request  Request
	response Response
}

func newClient() *client {
	c := &client{
		request:  Request{},
		response: Response{},
	}
	c.httpClient = httpClient{
		url:     config.URL,
		timeout: 10,
		caller:  "cloudprovider",
		callee:  "NORM",
		packer:  c,
	}
	return c
}

// 从环境变量中获取clusterID和appId放入norm的para中 for meta cluster
func SetNormReqExt(body []byte) []byte {
	js, err := simplejson.NewJson(body)
	if err != nil {
		glog.Error("SetNormReqExt NewJson error,", err)
		return nil
	}
	if os.Getenv(EnvClusterID) != "" {
		js.SetPath([]string{"interface", "para", "unClusterId"}, os.Getenv(EnvClusterID))
		glog.V(4).Info("SetNormReqExt set unClusterId", os.Getenv(EnvClusterID))

	}
	if os.Getenv(EnvAPPID) != "" {
		js.SetPath([]string{"interface", "para", "appId"}, os.Getenv(EnvAPPID))
		glog.V(4).Info("SetNormReqExt set appId", os.Getenv(EnvAPPID))

	}

	out, err := js.Encode()
	if err != nil {
		glog.Error("SetNormReqExt Encode error,", err)
		return body
	}

	return out
}

func (c *client) packRequest(interfaceName string, reqObj interface{}) ([]byte, error) {
	c.request = map[string]interface{}{
		"eventId":   rand.Uint32(),
		"timestamp": time.Now().Unix(),
		"caller":    c.httpClient.caller,
		"callee":    c.httpClient.callee,
		"version":   "1",
		"password":  "cloudprovider",
		"interface": map[string]interface{}{
			"interfaceName": interfaceName,
			"para":          reqObj,
		},
	}

	b, err := json.Marshal(c.request)
	if err != nil {
		glog.Error("packRequest failed:", err, ", req:", c.request)
		return nil, err
	}
	b = SetNormReqExt(b)
	return b, nil
}

func (c *client) unPackResponse(data []byte, responseData interface{}) (err error) {
	c.response.Data = responseData
	err = json.Unmarshal(data, &c.response)
	if err != nil {
		glog.Error("Unmarshal goods response err:", err)
		return

	}
	return
}

func (c *client) getResult() (int, string) {
	return c.response.Code, c.response.Msg
}

// nolint: revive
type TKECredential struct {
	Credentials struct {
		SessionToken string `json:"sessionToken"`
		TmpSecretId  string `json:"tmpSecretId"`
		TmpSecretKey string `json:"tmpSecretKey"`
	} `json:"credentials"`
	ExpiredTime int `json:"expiredTime"`
}

func GetTKECredential() (*TKECredential, error) {
	c := newClient()
	rsp := &TKECredential{}
	err := c.DoRequest(InterfaceAssumeTKECredential, struct{}{}, rsp)
	if err != nil {
		if normError, ok := err.(RequestResultError); ok {
			if normError.Code == -8002 {
				return nil, ErrTKERoleNotFound
			} else if normError.Code == -8017 {
				return nil, ErrIPMasqAgentConfigChanged
			}
			return nil, fmt.Errorf("%s: %w", normError, ErrInternal)
		}
		glog.Errorf("Failed to get credentials: %s\n", err)
		return nil, err
	}
	return rsp, nil
}

type GetClusterUINResponse struct {
	Uin int64 `json:"uin"`
}

func GetClusterUIN() (*int64, error) {
	c := newClient()
	rsp := &GetClusterUINResponse{}
	err := c.DoRequest(InterfaceGetClusterUIN, struct{}{}, rsp)
	if err != nil {
		glog.Errorf("Failed to get cluster uin: %s\n", err)
		return nil, err
	}
	return &rsp.Uin, nil
}
