// Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package clbinternal

import (
	"encoding/json"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
)

type ModifyLBOperateProtectRequest struct {
	*tchttp.BaseRequest

	// 负载均衡实例ID。
	LoadBalancerId *string `json:"LoadBalancerId,omitempty" name:"LoadBalancerId"`

	// 操作保护，true：开启，false：关闭。
	OperateProtect *bool `json:"OperateProtect,omitempty" name:"OperateProtect"`

	// 描述信息。
	Description *string `json:"Description,omitempty" name:"Description"`

	// 服务角色名，服务角色设置时必填。
	RoleName *string `json:"RoleName,omitempty" name:"RoleName"`

	// 是否允许服务角色操作负载均衡。
	AllowOperate *bool `json:"AllowOperate,omitempty" name:"AllowOperate"`

	// 链接到服务角色所需信息
	LinkInfo *string `json:"LinkInfo,omitempty" name:"LinkInfo"`
}

func (r *ModifyLBOperateProtectRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *ModifyLBOperateProtectRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type ModifyLBOperateProtectResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *ModifyLBOperateProtectResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *ModifyLBOperateProtectResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type DescribeLBOperateProtectRequest struct {
	*tchttp.BaseRequest

	// 负载均衡实例ID。
	LoadBalancerIds []*string `json:"LoadBalancerIds,omitempty" name:"LoadBalancerIds" list`

	// 获取相关链接信息。
	GetLink *bool `json:"GetLink,omitempty" name:"GetLink"`
}

func (r *DescribeLBOperateProtectRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeLBOperateProtectRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type DescribeLBOperateProtectResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`

		LoadBalancerSet []*LBOperateProtectInfo `json:"LoadBalancerSet,omitempty" name:"LoadBalancerSet" list`
	} `json:"Response"`
}

func (r *DescribeLBOperateProtectResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeLBOperateProtectResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type LBOperateProtectInfo struct {
	// 负载均衡实例 ID。
	LoadBalancerId *string `json:"LoadBalancerId,omitempty" name:"LoadBalancerId"`

	// 保护状态，true：表示开启了操作保护，false：表示未开启操作保护。
	ProtectState *bool `json:"ProtectState,omitempty" name:"ProtectState"`

	// 操作保护的设置uin。
	// 注意：此字段可能返回 null，表示取不到有效值。
	OperatorUin *string `json:"OperatorUin,omitempty" name:"OperatorUin"`

	// 设置操作保护时的描述信息。
	// 注意：此字段可能返回 null，表示取不到有效值。
	Description *string `json:"Description,omitempty" name:"Description"`

	// 最后修改时间。
	// 注意：此字段可能返回 null，表示取不到有效值。
	ModifyTime *string `json:"ModifyTime,omitempty" name:"ModifyTime"`
}

type DescribeQuotaRequest struct {
	*tchttp.BaseRequest
	//是否返回实例配额信息
	ResourceQuota *bool `json:"ResourceQuota,omitnil,omitempty" name:"ResourceQuota"`
}

func (r *DescribeQuotaRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeQuotaRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type Quota struct {
	// 配额名称，取值范围：
	// <li> TOTAL_OPEN_CLB_QUOTA：用户当前地域下的公网CLB配额 </li>
	// <li> TOTAL_INTERNAL_CLB_QUOTA：用户当前地域下的内网CLB配额 </li>
	// <li> TOTAL_LISTENER_QUOTA：一个CLB下的监听器配额 </li>
	// <li> TOTAL_LISTENER_RULE_QUOTA：一个监听器下的转发规则配额 </li>
	// <li> TOTAL_TARGET_BIND_QUOTA：一条转发规则下可绑定设备的配额 </li>
	// <li> TOTAL_SNAP_IP_QUOTA： 一个CLB实例下跨地域2.0的SNAT IP配额 </li>
	// <li>TOTAL_ISP_CLB_QUOTA：用户当前地域下的三网CLB配额 </li>
	QuotaId *string `json:"QuotaId,omitnil,omitempty" name:"QuotaId"`

	// 当前使用数量，为 null 时表示无意义。
	// 注意：此字段可能返回 null，表示取不到有效值。
	QuotaCurrent *int64 `json:"QuotaCurrent,omitnil,omitempty" name:"QuotaCurrent"`

	// 配额数量。
	QuotaLimit *int64 `json:"QuotaLimit,omitnil,omitempty" name:"QuotaLimit"`
	//实例级别配额资源ID
	ResourceId *string `json:"ResourceId,omitnil,omitempty" name:"ResourceId"`
}

// Predefined struct for user
type DescribeQuotaResponseParams struct {
	// 配额列表
	QuotaSet []*Quota `json:"QuotaSet,omitnil,omitempty" name:"QuotaSet"`

	// 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitnil,omitempty" name:"RequestId"`
}

type DescribeQuotaResponse struct {
	*tchttp.BaseResponse
	Response *DescribeQuotaResponseParams `json:"Response"`
}

func (r *DescribeQuotaResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *DescribeQuotaResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type DescribeLoadBalancersRequest struct {
	*tchttp.BaseRequest

	// 负载均衡实例 ID。
	LoadBalancerIds []*string `json:"LoadBalancerIds,omitempty" name:"LoadBalancerIds" list`

	// 负载均衡实例的网络类型：
	// OPEN：公网属性， INTERNAL：内网属性。
	LoadBalancerType *string `json:"LoadBalancerType,omitempty" name:"LoadBalancerType"`

	// 负载均衡实例的类型。1：通用的负载均衡实例，0：传统型负载均衡实例。如果不传此参数，则查询所有类型的负载均衡实例。
	Forward *int64 `json:"Forward,omitempty" name:"Forward"`

	// 负载均衡实例的名称。
	LoadBalancerName *string `json:"LoadBalancerName,omitempty" name:"LoadBalancerName"`

	// 腾讯云为负载均衡实例分配的域名，本参数仅对传统型公网负载均衡才有意义。
	Domain *string `json:"Domain,omitempty" name:"Domain"`

	// 负载均衡实例的 VIP 地址，支持多个。
	LoadBalancerVips []*string `json:"LoadBalancerVips,omitempty" name:"LoadBalancerVips" list`

	// 负载均衡绑定的后端服务的外网 IP。
	BackendPublicIps []*string `json:"BackendPublicIps,omitempty" name:"BackendPublicIps" list`

	// 负载均衡绑定的后端服务的内网 IP。
	BackendPrivateIps []*string `json:"BackendPrivateIps,omitempty" name:"BackendPrivateIps" list`

	// 数据偏移量，默认为 0。
	Offset *int64 `json:"Offset,omitempty" name:"Offset"`

	// 返回负载均衡实例的数量，默认为20，最大值为100。
	Limit *int64 `json:"Limit,omitempty" name:"Limit"`

	// 排序参数，支持以下字段：LoadBalancerName，CreateTime，Domain，LoadBalancerType。
	OrderBy *string `json:"OrderBy,omitempty" name:"OrderBy"`

	// 1：倒序，0：顺序，默认按照创建时间倒序。
	OrderType *int64 `json:"OrderType,omitempty" name:"OrderType"`

	// 搜索字段，模糊匹配名称、域名、VIP。
	SearchKey *string `json:"SearchKey,omitempty" name:"SearchKey"`

	// 负载均衡实例所属的项目 ID，可以通过 DescribeProject 接口获取。
	ProjectId *int64 `json:"ProjectId,omitempty" name:"ProjectId"`

	// 负载均衡是否绑定后端服务，0：没有绑定后端服务，1：绑定后端服务，-1：查询全部。
	WithRs *int64 `json:"WithRs,omitempty" name:"WithRs"`

	// 负载均衡实例所属私有网络唯一ID，如 vpc-bhqkbhdx，
	// 基础网络可传入'0'。
	VpcId *string `json:"VpcId,omitempty" name:"VpcId"`

	// 安全组ID，如 sg-m1cc9123
	SecurityGroup *string `json:"SecurityGroup,omitempty" name:"SecurityGroup"`

	// 主可用区ID，如 ："100001" （对应的是广州一区）
	MasterZone *string `json:"MasterZone,omitempty" name:"MasterZone"`

	// 每次请求的`Filters`的上限为10，`Filter.Values`的上限为100。详细的过滤条件如下：
	// <li> internet-charge-type - String - 是否必填：否 - （过滤条件）按照 CLB 的网络计费模式过滤，包括"BANDWIDTH_PREPAID","TRAFFIC_POSTPAID_BY_HOUR","BANDWIDTH_POSTPAID_BY_HOUR","BANDWIDTH_PACKAGE"。</li>
	// <li> master-zone-id - String - 是否必填：否 - （过滤条件）按照 CLB 的主可用区ID过滤，如 ："100001" （对应的是广州一区）。</li>
	// <li> tag-key - String - 是否必填：否 - （过滤条件）按照 CLB 标签的键过滤。</li>
	// <li> tag-value - String - 是否必填：否 - （过滤条件）按照 CLB 标签的值过滤。</li>
	// <li> function-name - String - 是否必填：否 - （过滤条件）按照 CLB 后端绑定的SCF云函数的函数名称过滤。</li>
	Filters []*Filter `json:"Filters,omitempty" name:"Filters" list`
}

func (r *DescribeLoadBalancersRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeLoadBalancersRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type DescribeLoadBalancersResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 满足过滤条件的负载均衡实例总数。此数值与入参中的Limit无关。
		TotalCount *uint64 `json:"TotalCount,omitempty" name:"TotalCount"`

		// 返回的负载均衡实例数组。
		LoadBalancerSet []*LoadBalancer `json:"LoadBalancerSet,omitempty" name:"LoadBalancerSet" list`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *DescribeLoadBalancersResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeLoadBalancersResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type Filter struct {
	// 过滤器的名称
	Name *string `json:"Name,omitempty" name:"Name"`

	// 过滤器的值数组
	Values []*string `json:"Values,omitempty" name:"Values" list`
}

type LoadBalancer struct {
	// 负载均衡实例 ID。
	LoadBalancerId *string `json:"LoadBalancerId,omitnil,omitempty" name:"LoadBalancerId"`

	// 负载均衡实例的名称。
	LoadBalancerName *string `json:"LoadBalancerName,omitnil,omitempty" name:"LoadBalancerName"`

	// 负载均衡实例的网络类型：
	// OPEN：公网属性， INTERNAL：内网属性。
	LoadBalancerType *string `json:"LoadBalancerType,omitnil,omitempty" name:"LoadBalancerType"`

	// 负载均衡类型标识，1：负载均衡，0：传统型负载均衡。
	Forward *uint64 `json:"Forward,omitnil,omitempty" name:"Forward"`

	// 负载均衡实例的域名，仅公网传统型和域名型负载均衡实例才提供该字段。逐步下线中，建议用LoadBalancerDomain替代。
	// 注意：此字段可能返回 null，表示取不到有效值。
	Domain *string `json:"Domain,omitnil,omitempty" name:"Domain"`

	// 负载均衡实例的 VIP 列表。
	// 注意：此字段可能返回 null，表示取不到有效值。
	LoadBalancerVips []*string `json:"LoadBalancerVips,omitnil,omitempty" name:"LoadBalancerVips"`

	// 负载均衡实例的状态，包括
	// 0：创建中，1：正常运行。
	// 注意：此字段可能返回 null，表示取不到有效值。
	Status *uint64 `json:"Status,omitnil,omitempty" name:"Status"`

	// 负载均衡实例的创建时间。
	// 注意：此字段可能返回 null，表示取不到有效值。
	CreateTime *string `json:"CreateTime,omitnil,omitempty" name:"CreateTime"`

	// 负载均衡实例的上次状态转换时间。
	// 注意：此字段可能返回 null，表示取不到有效值。
	StatusTime *string `json:"StatusTime,omitnil,omitempty" name:"StatusTime"`

	// 负载均衡实例所属的项目 ID， 0 表示默认项目。
	ProjectId *uint64 `json:"ProjectId,omitnil,omitempty" name:"ProjectId"`

	// 私有网络的 ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	VpcId *string `json:"VpcId,omitnil,omitempty" name:"VpcId"`

	// 高防 LB 的标识，1：高防负载均衡 0：非高防负载均衡。
	// 注意：此字段可能返回 null，表示取不到有效值。
	OpenBgp *uint64 `json:"OpenBgp,omitnil,omitempty" name:"OpenBgp"`

	// 在 2016 年 12 月份之前的传统型内网负载均衡都是开启了 snat 的。
	// 注意：此字段可能返回 null，表示取不到有效值。
	Snat *bool `json:"Snat,omitnil,omitempty" name:"Snat"`

	// 0：表示未被隔离，1：表示被隔离。
	// 注意：此字段可能返回 null，表示取不到有效值。
	Isolation *uint64 `json:"Isolation,omitnil,omitempty" name:"Isolation"`

	// 用户开启日志的信息，日志只有公网属性创建了 HTTP 、HTTPS 监听器的负载均衡才会有日志。
	// 注意：此字段可能返回 null，表示取不到有效值。
	//
	// Deprecated: Log is deprecated.
	Log *string `json:"Log,omitnil,omitempty" name:"Log"`

	// 负载均衡实例所在的子网（仅对内网VPC型LB有意义）
	// 注意：此字段可能返回 null，表示取不到有效值。
	SubnetId *string `json:"SubnetId,omitnil,omitempty" name:"SubnetId"`

	// 负载均衡实例的标签信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	Tags []*clb.TagInfo `json:"Tags,omitnil,omitempty" name:"Tags"`

	// 负载均衡实例的安全组
	// 注意：此字段可能返回 null，表示取不到有效值。
	SecureGroups []*string `json:"SecureGroups,omitnil,omitempty" name:"SecureGroups"`

	// 负载均衡实例绑定的后端设备的基本信息
	// 注意：此字段可能返回 null，表示取不到有效值。
	TargetRegionInfo *clb.TargetRegionInfo `json:"TargetRegionInfo,omitnil,omitempty" name:"TargetRegionInfo"`

	// anycast负载均衡的发布域，对于非anycast的负载均衡，此字段返回为空字符串
	// 注意：此字段可能返回 null，表示取不到有效值。
	AnycastZone *string `json:"AnycastZone,omitnil,omitempty" name:"AnycastZone"`

	// IP版本，ipv4 | ipv6
	// 注意：此字段可能返回 null，表示取不到有效值。
	AddressIPVersion *string `json:"AddressIPVersion,omitnil,omitempty" name:"AddressIPVersion"`

	// 数值形式的私有网络 ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	NumericalVpcId *uint64 `json:"NumericalVpcId,omitnil,omitempty" name:"NumericalVpcId"`

	// 负载均衡IP地址所属的运营商。取值范围（BGP、CMCC、CTCC、CUCC）
	// 注意：此字段可能返回 null，表示取不到有效值。
	VipIsp *string `json:"VipIsp,omitnil,omitempty" name:"VipIsp"`

	// 主可用区
	// 注意：此字段可能返回 null，表示取不到有效值。
	MasterZone *clb.ZoneInfo `json:"MasterZone,omitnil,omitempty" name:"MasterZone"`

	// 备可用区
	// 注意：此字段可能返回 null，表示取不到有效值。
	BackupZoneSet []*clb.ZoneInfo `json:"BackupZoneSet,omitnil,omitempty" name:"BackupZoneSet"`

	// 负载均衡实例被隔离的时间
	// 注意：此字段可能返回 null，表示取不到有效值。
	IsolatedTime *string `json:"IsolatedTime,omitnil,omitempty" name:"IsolatedTime"`

	// 负载均衡实例的过期时间，仅对预付费负载均衡生效
	// 注意：此字段可能返回 null，表示取不到有效值。
	ExpireTime *string `json:"ExpireTime,omitnil,omitempty" name:"ExpireTime"`

	// 负载均衡实例的计费类型，PREPAID：包年包月，POSTPAID_BY_HOUR：按量计费
	// 注意：此字段可能返回 null，表示取不到有效值。
	ChargeType *string `json:"ChargeType,omitnil,omitempty" name:"ChargeType"`

	// 负载均衡实例的网络属性
	// 注意：此字段可能返回 null，表示取不到有效值。
	NetworkAttributes *clb.InternetAccessible `json:"NetworkAttributes,omitnil,omitempty" name:"NetworkAttributes"`

	// 负载均衡实例的预付费相关属性
	// 注意：此字段可能返回 null，表示取不到有效值。
	PrepaidAttributes *clb.LBChargePrepaid `json:"PrepaidAttributes,omitnil,omitempty" name:"PrepaidAttributes"`

	// 负载均衡日志服务(CLS)的日志集ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	LogSetId *string `json:"LogSetId,omitnil,omitempty" name:"LogSetId"`

	// 负载均衡日志服务(CLS)的日志主题ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	LogTopicId *string `json:"LogTopicId,omitnil,omitempty" name:"LogTopicId"`

	// 负载均衡实例的IPv6地址
	// 注意：此字段可能返回 null，表示取不到有效值。
	AddressIPv6 *string `json:"AddressIPv6,omitnil,omitempty" name:"AddressIPv6"`

	// 暂做保留，一般用户无需关注。
	// 注意：此字段可能返回 null，表示取不到有效值。
	ExtraInfo *clb.ExtraInfo `json:"ExtraInfo,omitnil,omitempty" name:"ExtraInfo"`

	// 是否可绑定高防包
	// 注意：此字段可能返回 null，表示取不到有效值。
	IsDDos *bool `json:"IsDDos,omitnil,omitempty" name:"IsDDos"`

	// 负载均衡维度的个性化配置ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	ConfigId *string `json:"ConfigId,omitnil,omitempty" name:"ConfigId"`

	// 后端服务是否放通来自LB的流量
	// 注意：此字段可能返回 null，表示取不到有效值。
	LoadBalancerPassToTarget *bool `json:"LoadBalancerPassToTarget,omitnil,omitempty" name:"LoadBalancerPassToTarget"`

	// 内网独占集群
	// 注意：此字段可能返回 null，表示取不到有效值。
	ExclusiveCluster *clb.ExclusiveCluster `json:"ExclusiveCluster,omitnil,omitempty" name:"ExclusiveCluster"`

	// IP地址版本为ipv6时此字段有意义， IPv6Nat64 | IPv6FullChain
	// 注意：此字段可能返回 null，表示取不到有效值。
	IPv6Mode *string `json:"IPv6Mode,omitnil,omitempty" name:"IPv6Mode"`

	// 是否开启SnatPro。
	// 注意：此字段可能返回 null，表示取不到有效值。
	SnatPro *bool `json:"SnatPro,omitnil,omitempty" name:"SnatPro"`

	// 开启SnatPro负载均衡后，SnatIp列表。
	// 注意：此字段可能返回 null，表示取不到有效值。
	SnatIps []*clb.SnatIp `json:"SnatIps,omitnil,omitempty" name:"SnatIps"`

	// 性能容量型规格。<ul><li> clb.c2.medium：标准型规格 </li><li> clb.c3.small：高阶型1规格 </li><li> clb.c3.medium：高阶型2规格 </li><li> clb.c4.small：超强型1规格 </li><li> clb.c4.medium：超强型2规格 </li><li> clb.c4.large：超强型3规格 </li><li> clb.c4.xlarge：超强型4规格 </li><li>null：共享型实例</li></ul>
	// 注意：此字段可能返回 null，表示取不到有效值。
	SlaType *string `json:"SlaType,omitnil,omitempty" name:"SlaType"`

	// vip是否被封堵
	// 注意：此字段可能返回 null，表示取不到有效值。
	IsBlock *bool `json:"IsBlock,omitnil,omitempty" name:"IsBlock"`

	// 封堵或解封时间
	// 注意：此字段可能返回 null，表示取不到有效值。
	IsBlockTime *string `json:"IsBlockTime,omitnil,omitempty" name:"IsBlockTime"`

	// IP类型是否是本地BGP
	// 注意：此字段可能返回 null，表示取不到有效值。
	LocalBgp *bool `json:"LocalBgp,omitnil,omitempty" name:"LocalBgp"`

	// 7层独占标签。
	// 注意：此字段可能返回 null，表示取不到有效值。
	ClusterTag *string `json:"ClusterTag,omitnil,omitempty" name:"ClusterTag"`

	// 开启IPv6FullChain负载均衡7层监听器支持混绑IPv4/IPv6目标功能。
	// 注意：此字段可能返回 null，表示取不到有效值。
	MixIpTarget *bool `json:"MixIpTarget,omitnil,omitempty" name:"MixIpTarget"`

	// 私有网络内网负载均衡，就近接入模式下规则所落在的可用区
	// 注意：此字段可能返回 null，表示取不到有效值。
	Zones []*string `json:"Zones,omitnil,omitempty" name:"Zones"`

	// CLB是否为NFV，空：不是，l7nfv：七层是NFV。
	// 注意：此字段可能返回 null，表示取不到有效值。
	NfvInfo *string `json:"NfvInfo,omitnil,omitempty" name:"NfvInfo"`

	// 负载均衡日志服务(CLS)的健康检查日志集ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	HealthLogSetId *string `json:"HealthLogSetId,omitnil,omitempty" name:"HealthLogSetId"`

	// 负载均衡日志服务(CLS)的健康检查日志主题ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	HealthLogTopicId *string `json:"HealthLogTopicId,omitnil,omitempty" name:"HealthLogTopicId"`

	// 集群ID
	// 注意：此字段可能返回 null，表示取不到有效值。
	ClusterIds []*string `json:"ClusterIds,omitnil,omitempty" name:"ClusterIds"`

	// 负载均衡的属性
	// 注意：此字段可能返回 null，表示取不到有效值。
	AttributeFlags []*string `json:"AttributeFlags,omitnil,omitempty" name:"AttributeFlags"`

	// 负载均衡实例的域名。
	// 注意：此字段可能返回 null，表示取不到有效值。
	LoadBalancerDomain *string `json:"LoadBalancerDomain,omitnil,omitempty" name:"LoadBalancerDomain"`

	// 网络出口
	// 注意：此字段可能返回 null，表示取不到有效值。
	Egress *string `json:"Egress,omitnil,omitempty" name:"Egress"`
}

type CreateListenerRequest struct {
	*tchttp.BaseRequest

	// 负载均衡实例 ID
	LoadBalancerId *string `json:"LoadBalancerId,omitempty" name:"LoadBalancerId"`

	// 要将监听器创建到哪些端口，每个端口对应一个新的监听器
	Ports []*int64 `json:"Ports,omitempty" name:"Ports" list`

	// 监听器协议： TCP | UDP | HTTP | HTTPS | TCP_SSL（TCP_SSL 正在内测中，如需使用请通过工单申请）
	Protocol *string `json:"Protocol,omitempty" name:"Protocol"`

	// 要创建的监听器名称列表，名称与Ports数组按序一一对应，如不需立即命名，则无需提供此参数
	ListenerNames []*string `json:"ListenerNames,omitempty" name:"ListenerNames" list`

	// 健康检查相关参数，此参数仅适用于TCP/UDP/TCP_SSL监听器
	HealthCheck *HealthCheck `json:"HealthCheck,omitempty" name:"HealthCheck"`

	// 证书相关信息，此参数仅适用于TCP_SSL监听器和未开启SNI特性的HTTPS监听器。
	Certificate *clb.CertificateInput `json:"Certificate,omitempty" name:"Certificate"`

	// [qingyangwu] 多证书信息，支持同时传入不同算法类型的多本服务端证书；此参数仅适用于未开启SNI特性的HTTPS监听器。此参数和Certificate不能同时传入。
	MultiCertInfo *clb.MultiCertInfo `json:"MultiCertInfo,omitempty" name:"MultiCertInfo"`

	// 会话保持时间，单位：秒。可选值：30~3600，默认 0，表示不开启。此参数仅适用于TCP/UDP监听器。
	SessionExpireTime *int64 `json:"SessionExpireTime,omitempty" name:"SessionExpireTime"`

	// 监听器转发的方式。可选值：WRR、LEAST_CONN
	// 分别表示按权重轮询、最小连接数， 默认为 WRR。此参数仅适用于TCP/UDP/TCP_SSL监听器。
	Scheduler *string `json:"Scheduler,omitempty" name:"Scheduler"`

	// 是否开启SNI特性，此参数仅适用于HTTPS监听器。
	SniSwitch *int64 `json:"SniSwitch,omitempty" name:"SniSwitch"`

	// 后端目标类型，NODE表示绑定普通节点，TARGETGROUP表示绑定目标组。
	TargetType *string `json:"TargetType,omitempty" name:"TargetType"`

	// 会话保持类型。不传或传NORMAL表示默认会话保持类型。QUIC_CID 表示根据Quic Connection ID做会话保持。QUIC_CID只支持UDP协议。
	SessionType *string `json:"SessionType,omitempty" name:"SessionType"`

	// 是否开启长连接，此参数仅适用于HTTP/HTTPS监听器，0:关闭；1:开启， 默认关闭
	KeepaliveEnable *int64 `json:"KeepaliveEnable,omitempty" name:"KeepaliveEnable"`

	// 创建端口段监听器时必须传入此参数，用以标识结束端口。同时，入参Ports只允许传入一个成员，用以标识开始端口。【如果您需要体验端口段功能，请通过 [工单申请](https://console.cloud.tencent.com/workorder/category)】。
	EndPort *uint64 `json:"EndPort,omitempty" name:"EndPort"`

	// 解绑后端目标时，是否发RST给客户端，此参数仅适用于TCP监听器。
	DeregisterTargetRst *bool `json:"DeregisterTargetRst,omitempty" name:"DeregisterTargetRst"`

	// 是否开启ProxyProtocol
	ProxyProtocol *bool `json:"ProxyProtocol,omitempty" name:"ProxyProtocol"`
}

func (r *CreateListenerRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *CreateListenerRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type CreateListenerResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 创建的监听器的唯一标识数组
		ListenerIds []*string `json:"ListenerIds,omitempty" name:"ListenerIds" list`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *CreateListenerResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *CreateListenerResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type HealthCheck struct {
	// 是否开启健康检查：1（开启）、0（关闭）。
	HealthSwitch *int64 `json:"HealthSwitch,omitempty" name:"HealthSwitch"`

	// 健康检查的响应超时时间（仅适用于四层监听器），可选值：2~60，默认值：2，单位：秒。响应超时时间要小于检查间隔时间。
	// 注意：此字段可能返回 null，表示取不到有效值。
	TimeOut *int64 `json:"TimeOut,omitempty" name:"TimeOut"`

	// 健康检查探测间隔时间，默认值：5，可选值：5~300，单位：秒。
	// 注意：此字段可能返回 null，表示取不到有效值。
	IntervalTime *int64 `json:"IntervalTime,omitempty" name:"IntervalTime"`

	// 健康阈值，默认值：3，表示当连续探测三次健康则表示该转发正常，可选值：2~10，单位：次。
	// 注意：此字段可能返回 null，表示取不到有效值。
	HealthNum *int64 `json:"HealthNum,omitempty" name:"HealthNum"`

	// 不健康阈值，默认值：3，表示当连续探测三次不健康则表示该转发异常，可选值：2~10，单位：次。
	// 注意：此字段可能返回 null，表示取不到有效值。
	UnHealthNum *int64 `json:"UnHealthNum,omitempty" name:"UnHealthNum"`

	// 健康检查状态码（仅适用于HTTP/HTTPS转发规则、TCP监听器的HTTP健康检查方式）。可选值：1~31，默认 31。
	// 1 表示探测后返回值 1xx 代表健康，2 表示返回 2xx 代表健康，4 表示返回 3xx 代表健康，8 表示返回 4xx 代表健康，16 表示返回 5xx 代表健康。若希望多种返回码都可代表健康，则将相应的值相加。注意：TCP监听器的HTTP健康检查方式，只支持指定一种健康检查状态码。
	// 注意：此字段可能返回 null，表示取不到有效值。
	HttpCode *int64 `json:"HttpCode,omitempty" name:"HttpCode"`

	// 健康检查路径（仅适用于HTTP/HTTPS转发规则、TCP监听器的HTTP健康检查方式）。
	// 注意：此字段可能返回 null，表示取不到有效值。
	HttpCheckPath *string `json:"HttpCheckPath,omitempty" name:"HttpCheckPath"`

	// 健康检查域名（仅适用于HTTP/HTTPS转发规则、TCP监听器的HTTP健康检查方式）。
	// 注意：此字段可能返回 null，表示取不到有效值。
	HttpCheckDomain *string `json:"HttpCheckDomain,omitempty" name:"HttpCheckDomain"`

	// 健康检查方法（仅适用于HTTP/HTTPS转发规则、TCP监听器的HTTP健康检查方式），默认值：HEAD，可选值HEAD或GET。
	// 注意：此字段可能返回 null，表示取不到有效值。
	HttpCheckMethod *string `json:"HttpCheckMethod,omitempty" name:"HttpCheckMethod"`

	// 自定义探测相关参数。健康检查端口，默认为后端服务的端口，除非您希望指定特定端口，否则建议留空。（仅适用于TCP/UDP监听器）。
	// 注意：此字段可能返回 null，表示取不到有效值。
	CheckPort *int64 `json:"CheckPort,omitempty" name:"CheckPort"`

	// 自定义探测相关参数。健康检查协议CheckType的值取CUSTOM时，必填此字段，代表健康检查的输入格式，可取值：HEX或TEXT；取值为HEX时，SendContext和RecvContext的字符只能在0123456789ABCDEF中选取且长度必须是偶数位。（仅适用于TCP/UDP监听器）
	// 注意：此字段可能返回 null，表示取不到有效值。
	ContextType *string `json:"ContextType,omitempty" name:"ContextType"`

	// 自定义探测相关参数。健康检查协议CheckType的值取CUSTOM时，必填此字段，代表健康检查发送的请求内容，只允许ASCII可见字符，最大长度限制500。（仅适用于TCP/UDP监听器）。
	// 注意：此字段可能返回 null，表示取不到有效值。
	SendContext *string `json:"SendContext,omitempty" name:"SendContext"`

	// 自定义探测相关参数。健康检查协议CheckType的值取CUSTOM时，必填此字段，代表健康检查返回的结果，只允许ASCII可见字符，最大长度限制500。（仅适用于TCP/UDP监听器）。
	// 注意：此字段可能返回 null，表示取不到有效值。
	RecvContext *string `json:"RecvContext,omitempty" name:"RecvContext"`

	// 自定义探测相关参数。健康检查使用的协议：TCP | HTTP | CUSTOM（仅适用于TCP/UDP监听器，其中UDP监听器只支持CUSTOM；如果使用自定义健康检查功能，则必传）。
	// 注意：此字段可能返回 null，表示取不到有效值。
	CheckType *string `json:"CheckType,omitempty" name:"CheckType"`

	// 自定义探测相关参数。健康检查协议CheckType的值取HTTP时，必传此字段，代表后端服务的HTTP版本：HTTP/1.0、HTTP/1.1；（仅适用于TCP监听器）
	// 注意：此字段可能返回 null，表示取不到有效值。
	HttpVersion *string `json:"HttpVersion,omitempty" name:"HttpVersion"`

	// 自定义探测来源
	SourceIpType *int64 `json:"SourceIpType,omitempty" name:"SourceIpType"`

	// GRPC健康检查状态码（仅适用于后端转发协议为GRPC的规则）。默认值为 12，可输入值为数值、多个数值、或者范围，例如 20 或 20,25 或 0-99
	// 注意：此字段可能返回 null，表示取不到有效值。
	ExtendedCode *string `json:"ExtendedCode,omitnil" name:"ExtendedCode"`
}

type CreateRuleRequest struct {
	*tchttp.BaseRequest

	// 负载均衡实例 ID
	LoadBalancerId *string `json:"LoadBalancerId,omitempty" name:"LoadBalancerId"`

	// 监听器 ID
	ListenerId *string `json:"ListenerId,omitempty" name:"ListenerId"`

	// 新建转发规则的信息
	Rules []*RuleInput `json:"Rules,omitempty" name:"Rules" list`
}

func (r *CreateRuleRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *CreateRuleRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type CreateRuleResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 创建的转发规则的唯一标识数组
		LocationIds []*string `json:"LocationIds,omitempty" name:"LocationIds" list`

		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *CreateRuleResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *CreateRuleResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type RuleInput struct {
	// 转发规则的域名。长度限制为：1~80。
	Domain *string `json:"Domain,omitempty" name:"Domain"`

	// 转发规则的路径。长度限制为：1~200。
	Url *string `json:"Url,omitempty" name:"Url"`

	// 会话保持时间。设置为0表示关闭会话保持，开启会话保持可取值30~3600，单位：秒。
	SessionExpireTime *int64 `json:"SessionExpireTime,omitempty" name:"SessionExpireTime"`

	// 健康检查信息。详情请参见：[健康检查](https://cloud.tencent.com/document/product/214/6097)
	HealthCheck *HealthCheck `json:"HealthCheck,omitempty" name:"HealthCheck"`

	// 证书信息
	Certificate *clb.CertificateInput `json:"Certificate,omitempty" name:"Certificate"`
	// [qingyangwu] 多证书信息，支持同时传入不同算法类型的多本服务端证书；此参数仅适用于未开启SNI特性的HTTPS监听器。此参数和Certificate不能同时传入。

	MultiCertInfo *clb.MultiCertInfo `json:"MultiCertInfo,omitempty" name:"MultiCertInfo"`

	// 规则的请求转发方式，可选值：WRR、LEAST_CONN、IP_HASH
	// 分别表示按权重轮询、最小连接数、按IP哈希， 默认为 WRR。
	Scheduler *string `json:"Scheduler,omitempty" name:"Scheduler"`

	// 负载均衡与后端服务之间的转发协议，目前支持 HTTP/HTTPS/TRPC
	ForwardType *string `json:"ForwardType,omitempty" name:"ForwardType"`

	// 是否将该域名设为默认域名，注意，一个监听器下只能设置一个默认域名。
	DefaultServer *bool `json:"DefaultServer,omitempty" name:"DefaultServer"`

	// 是否开启Http2，注意，只有HTTPS域名才能开启Http2。
	Http2 *bool `json:"Http2,omitempty" name:"Http2"`

	// 后端目标类型，NODE表示绑定普通节点，TARGETGROUP表示绑定目标组
	TargetType *string `json:"TargetType,omitempty" name:"TargetType"`

	// TRPC被调服务器路由，ForwardType为TRPC时必填
	TrpcCallee *string `json:"TrpcCallee,omitempty" name:"TrpcCallee"`

	// TRPC调用服务接口，ForwardType为TRPC时必填
	TrpcFunc *string `json:"TrpcFunc,omitempty" name:"TrpcFunc"`

	// 是否开启QUIC，注意，只有HTTPS域名才能开启QUIC
	Quic *bool `json:"Quic,omitempty" name:"Quic"`
}

type ModifyListenerRequest struct {
	*tchttp.BaseRequest

	// 负载均衡实例 ID
	LoadBalancerId *string `json:"LoadBalancerId,omitempty" name:"LoadBalancerId"`

	// 负载均衡监听器 ID
	ListenerId *string `json:"ListenerId,omitempty" name:"ListenerId"`

	// 新的监听器名称
	ListenerName *string `json:"ListenerName,omitempty" name:"ListenerName"`

	// 会话保持时间，单位：秒。可选值：30~3600，默认 0，表示不开启。此参数仅适用于TCP/UDP监听器。
	SessionExpireTime *int64 `json:"SessionExpireTime,omitempty" name:"SessionExpireTime"`

	// 健康检查相关参数，此参数仅适用于TCP/UDP/TCP_SSL监听器
	HealthCheck *HealthCheck `json:"HealthCheck,omitempty" name:"HealthCheck"`

	// 证书相关信息，此参数仅适用于HTTPS/TCP_SSL监听器
	Certificate *clb.CertificateInput `json:"Certificate,omitempty" name:"Certificate"`

	// [qingyangwu] 多证书信息，支持同时传入不同算法类型的多本服务端证书；此参数仅适用于未开启SNI特性的HTTPS监听器。此参数和Certificate不能同时传入。
	MultiCertInfo *clb.MultiCertInfo `json:"MultiCertInfo,omitempty" name:"MultiCertInfo"`

	// 监听器转发的方式。可选值：WRR、LEAST_CONN
	// 分别表示按权重轮询、最小连接数， 默认为 WRR。
	Scheduler *string `json:"Scheduler,omitempty" name:"Scheduler"`

	// 是否开启SNI特性，此参数仅适用于HTTPS监听器。注意：未开启SNI的监听器可以开启SNI；已开启SNI的监听器不能关闭SNI
	SniSwitch *int64 `json:"SniSwitch,omitempty" name:"SniSwitch"`

	// 是否开启长连接，此参数仅适用于HTTP/HTTPS监听器
	KeepaliveEnable *int64 `json:"KeepaliveEnable,omitempty" name:"KeepaliveEnable"`

	// 解绑后端目标时，是否发RST给客户端，此参数仅适用于TCP监听器。
	DeregisterTargetRst *bool `json:"DeregisterTargetRst,omitempty" name:"DeregisterTargetRst"`

	// 会话保持类型。NORMAL表示默认会话保持类型。QUIC_CID表示根据Quic Connection ID做会话保持。QUIC_CID只支持UDP协议。
	SessionType *string `json:"SessionType,omitempty" name:"SessionType"`

	// 是否开启ProxyProtocol
	ProxyProtocol *bool `json:"ProxyProtocol,omitempty" name:"ProxyProtocol"`
}

func (r *ModifyListenerRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *ModifyListenerRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type ModifyListenerResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *ModifyListenerResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *ModifyListenerResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type ModifyRuleRequest struct {
	*tchttp.BaseRequest

	// 负载均衡实例 ID
	LoadBalancerId *string `json:"LoadBalancerId,omitempty" name:"LoadBalancerId"`

	// 负载均衡监听器 ID
	ListenerId *string `json:"ListenerId,omitempty" name:"ListenerId"`

	// 要修改的转发规则的 ID。
	LocationId *string `json:"LocationId,omitempty" name:"LocationId"`

	// 转发规则的新的转发路径，如不需修改Url，则不需提供此参数
	Url *string `json:"Url,omitempty" name:"Url"`

	// 健康检查信息
	HealthCheck *HealthCheck `json:"HealthCheck,omitempty" name:"HealthCheck"`

	// 规则的请求转发方式，可选值：WRR、LEAST_CONN、IP_HASH
	// 分别表示按权重轮询、最小连接数、按IP哈希， 默认为 WRR。
	Scheduler *string `json:"Scheduler,omitempty" name:"Scheduler"`

	// 会话保持时间
	SessionExpireTime *int64 `json:"SessionExpireTime,omitempty" name:"SessionExpireTime"`

	// 负载均衡实例与后端服务之间的转发协议，默认HTTP，可取值：HTTP、HTTPS、TRPC
	ForwardType *string `json:"ForwardType,omitempty" name:"ForwardType"`

	// TRPC被调服务器路由，ForwardType为TRPC时必填
	TrpcCallee *string `json:"TrpcCallee,omitempty" name:"TrpcCallee"`

	// TRPC调用服务接口，ForwardType为TRPC时必填
	TrpcFunc *string `json:"TrpcFunc,omitempty" name:"TrpcFunc"`
}

func (r *ModifyRuleRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *ModifyRuleRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type ModifyRuleResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
	} `json:"Response"`
}

func (r *ModifyRuleResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *ModifyRuleResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type DescribeRegionsRequest struct {
	*tchttp.BaseRequest

	AllRegion *bool `json:"AllRegion,omitempty" name:"AllRegion"`
}

func (r *DescribeRegionsRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeRegionsRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type RegionSet struct {
	Region             *string `json:"Region,omitempty" name:"Region"`
	RegionId           *int64  `json:"RegionId,omitempty" name:"RegionId"`
	RegionAbbreviation *string `json:"RegionAbbreviation,omitempty" name:"RegionAbbreviation"`
}

type DescribeRegionsResponse struct {
	*tchttp.BaseResponse
	Response *struct {
		// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
		RequestId *string `json:"RequestId,omitempty" name:"RequestId"`

		// 总数
		TotalCount *int64 `json:"TotalCount,omitempty" name:"TotalCount"`

		// 地域信息
		RegionSet []*RegionSet `json:"RegionSet,omitempty" name:"RegionSet"`
	} `json:"Response"`
}

func (r *DescribeRegionsResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

func (r *DescribeRegionsResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type ModifyLoadBalancerAttributesRequestParams struct {
	// 负载均衡的唯一ID
	LoadBalancerId *string `json:"LoadBalancerId,omitnil,omitempty" name:"LoadBalancerId"`

	// 负载均衡实例名称
	LoadBalancerName *string `json:"LoadBalancerName,omitnil,omitempty" name:"LoadBalancerName"`

	// 设置负载均衡跨地域绑定1.0的后端服务信息
	TargetRegionInfo *clb.TargetRegionInfo `json:"TargetRegionInfo,omitnil,omitempty" name:"TargetRegionInfo"`

	// 网络计费相关参数
	InternetChargeInfo *clb.InternetAccessible `json:"InternetChargeInfo,omitnil,omitempty" name:"InternetChargeInfo"`

	// Target是否放通来自CLB的流量。开启放通（true）：只验证CLB上的安全组；不开启放通（false）：需同时验证CLB和后端实例上的安全组。
	LoadBalancerPassToTarget *bool `json:"LoadBalancerPassToTarget,omitnil,omitempty" name:"LoadBalancerPassToTarget"`

	// 负载均衡实例的计费类型，后付费：POSTPAID_BY_HOUR，预付费：PREPAID。
	ChargeType *string `json:"ChargeType,omitnil,omitempty" name:"ChargeType"`

	// 不同计费模式之间的切换：0表示不切换，1表示预付费和后付费切换，2表示后付费之间切换。默认值：0
	SwitchFlag *uint64 `json:"SwitchFlag,omitnil,omitempty" name:"SwitchFlag"`

	// 负载均衡实例的预付费相关属性
	PrepaidInfo *clb.LBChargePrepaid `json:"PrepaidInfo,omitnil,omitempty" name:"PrepaidInfo"`

	// 7层集群列表
	ExclusiveCluster *clb.ExclusiveCluster `json:"ExclusiveCluster,omitnil,omitempty" name:"ExclusiveCluster"`

	// 是否开启跨地域绑定2.0功能
	SnatPro *bool `json:"SnatPro,omitnil,omitempty" name:"SnatPro"`

	// 是否开启删除保护
	DeleteProtect *bool `json:"DeleteProtect,omitnil,omitempty" name:"DeleteProtect"`

	// 设置基础网络CLB绑定VPC内rs的VPC ID。字符串形式的ID。
	MixVpcId *string `json:"MixVpcId,omitnil,omitempty" name:"MixVpcId"`

	// 是否使用自研上云跨vpc新架构
	NoLBNat *bool `json:"NoLBNat,omitnil,omitempty" name:"NoLBNat"`

	// 将负载均衡二级域名由mycloud.com改为tencentclb.com，子域名也会变换。修改后mycloud.com域名将失效。
	ModifyClassicDomain *bool `json:"ModifyClassicDomain,omitnil,omitempty" name:"ModifyClassicDomain"`

	// 特性开关
	AttributeSwitch []*AttributeSwitch `json:"AttributeSwitch,omitnil,omitempty" name:"AttributeSwitch"`
}

type ModifyLoadBalancerAttributesRequest struct {
	*tchttp.BaseRequest

	// 负载均衡的唯一ID
	LoadBalancerId *string `json:"LoadBalancerId,omitnil,omitempty" name:"LoadBalancerId"`

	// 负载均衡实例名称
	LoadBalancerName *string `json:"LoadBalancerName,omitnil,omitempty" name:"LoadBalancerName"`

	// 设置负载均衡跨地域绑定1.0的后端服务信息
	TargetRegionInfo *clb.TargetRegionInfo `json:"TargetRegionInfo,omitnil,omitempty" name:"TargetRegionInfo"`

	// 网络计费相关参数
	InternetChargeInfo *clb.InternetAccessible `json:"InternetChargeInfo,omitnil,omitempty" name:"InternetChargeInfo"`

	// Target是否放通来自CLB的流量。开启放通（true）：只验证CLB上的安全组；不开启放通（false）：需同时验证CLB和后端实例上的安全组。
	LoadBalancerPassToTarget *bool `json:"LoadBalancerPassToTarget,omitnil,omitempty" name:"LoadBalancerPassToTarget"`

	// 负载均衡实例的计费类型，后付费：POSTPAID_BY_HOUR，预付费：PREPAID。
	ChargeType *string `json:"ChargeType,omitnil,omitempty" name:"ChargeType"`

	// 不同计费模式之间的切换：0表示不切换，1表示预付费和后付费切换，2表示后付费之间切换。默认值：0
	SwitchFlag *uint64 `json:"SwitchFlag,omitnil,omitempty" name:"SwitchFlag"`

	// 负载均衡实例的预付费相关属性
	PrepaidInfo *clb.LBChargePrepaid `json:"PrepaidInfo,omitnil,omitempty" name:"PrepaidInfo"`

	// 7层集群列表
	ExclusiveCluster *clb.ExclusiveCluster `json:"ExclusiveCluster,omitnil,omitempty" name:"ExclusiveCluster"`

	// 是否开启跨地域绑定2.0功能
	SnatPro *bool `json:"SnatPro,omitnil,omitempty" name:"SnatPro"`

	// 是否开启删除保护
	DeleteProtect *bool `json:"DeleteProtect,omitnil,omitempty" name:"DeleteProtect"`

	// 设置基础网络CLB绑定VPC内rs的VPC ID。字符串形式的ID。
	MixVpcId *string `json:"MixVpcId,omitnil,omitempty" name:"MixVpcId"`

	// 是否使用自研上云跨vpc新架构
	NoLBNat *bool `json:"NoLBNat,omitnil,omitempty" name:"NoLBNat"`

	// 将负载均衡二级域名由mycloud.com改为tencentclb.com，子域名也会变换。修改后mycloud.com域名将失效。
	ModifyClassicDomain *bool `json:"ModifyClassicDomain,omitnil,omitempty" name:"ModifyClassicDomain"`

	// 特性开关
	AttributeSwitch []*AttributeSwitch `json:"AttributeSwitch,omitnil,omitempty" name:"AttributeSwitch"`
}

func (r *ModifyLoadBalancerAttributesRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *ModifyLoadBalancerAttributesRequest) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type ModifyLoadBalancerAttributesResponseParams struct {
	// 切换负载均衡计费方式时，可用此参数查询切换任务是否成功。
	// 注意：此字段可能返回 null，表示取不到有效值。
	DealName *string `json:"DealName,omitnil,omitempty" name:"DealName"`

	// 唯一请求 ID，由服务端生成，每次请求都会返回（若请求因其他原因未能抵达服务端，则该次请求不会获得 RequestId）。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitnil,omitempty" name:"RequestId"`
}

type ModifyLoadBalancerAttributesResponse struct {
	*tchttp.BaseResponse
	Response *ModifyLoadBalancerAttributesResponseParams `json:"Response"`
}

func (r *ModifyLoadBalancerAttributesResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

type AttributeSwitch struct {
	// 特性名称
	AttributeName *string `json:"AttributeName,omitnil,omitempty" name:"AttributeName"`

	// 开关
	Switch *bool `json:"Switch,omitnil,omitempty" name:"Switch"`
}
