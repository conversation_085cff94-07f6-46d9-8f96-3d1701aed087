// Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package clbinternal

import (
	"context"
	"errors"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

const APIVersion = "2018-03-17"

type Client struct {
	common.Client
}

// Deprecated
func NewClientWithSecretId(secretId, secretKey, region string) (client *Client, err error) {
	cpf := profile.NewClientProfile()
	client = &Client{}
	client.Init(region).WithSecretId(secretId, secretKey).WithProfile(cpf)
	return
}

func NewClient(credential common.CredentialIface, region string, clientProfile *profile.ClientProfile) (client *Client, err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

func NewModifyLBOperateProtectRequest() (request *ModifyLBOperateProtectRequest) {
	request = &ModifyLBOperateProtectRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("clb", APIVersion, "ModifyLBOperateProtect")
	return
}

func NewModifyLBOperateProtectResponse() (response *ModifyLBOperateProtectResponse) {
	response = &ModifyLBOperateProtectResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 本接口(ModifyLBOperateProtect)用来将目标组绑定到负载均衡的监听器（四层协议）或转发规则（七层协议）上。
// 本接口为异步接口，本接口返回成功后需以返回的 RequestID 为入参，调用 DescribeTaskStatus 接口查询本次任务是否成功。
func (c *Client) ModifyLBOperateProtect(request *ModifyLBOperateProtectRequest) (response *ModifyLBOperateProtectResponse, err error) {
	if request == nil {
		request = NewModifyLBOperateProtectRequest()
	}
	response = NewModifyLBOperateProtectResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeLBOperateProtectRequest() (request *DescribeLBOperateProtectRequest) {
	request = &DescribeLBOperateProtectRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("clb", APIVersion, "DescribeLBOperateProtect")
	return
}

func NewDescribeLBOperateProtectResponse() (response *DescribeLBOperateProtectResponse) {
	response = &DescribeLBOperateProtectResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 本接口(DescribeLBOperateProtect)用来将目标组绑定到负载均衡的监听器（四层协议）或转发规则（七层协议）上。
// 本接口为异步接口，本接口返回成功后需以返回的 RequestID 为入参，调用 DescribeTaskStatus 接口查询本次任务是否成功。
func (c *Client) DescribeLBOperateProtect(request *DescribeLBOperateProtectRequest) (response *DescribeLBOperateProtectResponse, err error) {
	if request == nil {
		request = NewDescribeLBOperateProtectRequest()
	}
	response = NewDescribeLBOperateProtectResponse()
	err = c.Send(request, response)
	return
}
func NewDescribeQuotaRequest() (request *DescribeQuotaRequest) {
	request = &DescribeQuotaRequest{
		BaseRequest: &tchttp.BaseRequest{}}
	request.Init().WithApiInfo("clb", APIVersion, "DescribeQuota")
	return
}
func NewDescribeQuotaResponse() (response *DescribeQuotaResponse) {
	response = &DescribeQuotaResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return

}
func (c *Client) DescribeQuota(request *DescribeQuotaRequest) (response *DescribeQuotaResponse, err error) {
	if request == nil {
		request = NewDescribeQuotaRequest()
	}
	response = NewDescribeQuotaResponse()
	err = c.Send(request, response)
	return
}
func NewDescribeLoadBalancersRequest() (request *DescribeLoadBalancersRequest) {
	request = &DescribeLoadBalancersRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("clb", APIVersion, "DescribeLoadBalancers")
	return
}

func NewDescribeLoadBalancersResponse() (response *DescribeLoadBalancersResponse) {
	response = &DescribeLoadBalancersResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 查询一个地域的负载均衡实例列表
func (c *Client) DescribeLoadBalancers(request *DescribeLoadBalancersRequest) (response *DescribeLoadBalancersResponse, err error) {
	if request == nil {
		request = NewDescribeLoadBalancersRequest()
	}
	response = NewDescribeLoadBalancersResponse()
	err = c.Send(request, response)
	return
}

func NewCreateListenerRequest() (request *CreateListenerRequest) {
	request = &CreateListenerRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("clb", APIVersion, "CreateListener")
	return
}

func NewCreateListenerResponse() (response *CreateListenerResponse) {
	response = &CreateListenerResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// 在一个负载均衡实例下创建监听器。
// 本接口为异步接口，接口返回成功后，需以返回的 RequestId 为入参，调用 DescribeTaskStatus 接口查询本次任务是否成功。
func (c *Client) CreateListener(request *CreateListenerRequest) (response *CreateListenerResponse, err error) {
	if request == nil {
		request = NewCreateListenerRequest()
	}
	response = NewCreateListenerResponse()
	err = c.Send(request, response)
	return
}

func NewCreateRuleRequest() (request *CreateRuleRequest) {
	request = &CreateRuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("clb", APIVersion, "CreateRule")
	return
}

func NewCreateRuleResponse() (response *CreateRuleResponse) {
	response = &CreateRuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// CreateRule 接口用于在一个已存在的负载均衡七层监听器下创建转发规则，七层监听器中，后端服务必须绑定到规则上而非监听器上。
// 本接口为异步接口，本接口返回成功后需以返回的RequestID为入参，调用DescribeTaskStatus接口查询本次任务是否成功。
func (c *Client) CreateRule(request *CreateRuleRequest) (response *CreateRuleResponse, err error) {
	if request == nil {
		request = NewCreateRuleRequest()
	}
	response = NewCreateRuleResponse()
	err = c.Send(request, response)
	return
}

func NewModifyListenerRequest() (request *ModifyListenerRequest) {
	request = &ModifyListenerRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("clb", APIVersion, "ModifyListener")
	return
}

func NewModifyListenerResponse() (response *ModifyListenerResponse) {
	response = &ModifyListenerResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// ModifyListener接口用来修改负载均衡监听器的属性，包括监听器名称、健康检查参数、证书信息、转发策略等。本接口不支持传统型负载均衡。
// 本接口为异步接口，本接口返回成功后需以返回的RequestID为入参，调用DescribeTaskStatus接口查询本次任务是否成功。
func (c *Client) ModifyListener(request *ModifyListenerRequest) (response *ModifyListenerResponse, err error) {
	if request == nil {
		request = NewModifyListenerRequest()
	}
	response = NewModifyListenerResponse()
	err = c.Send(request, response)
	return
}

func NewModifyRuleRequest() (request *ModifyRuleRequest) {
	request = &ModifyRuleRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("clb", APIVersion, "ModifyRule")
	return
}

func NewModifyRuleResponse() (response *ModifyRuleResponse) {
	response = &ModifyRuleResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// ModifyRule 接口用来修改负载均衡七层监听器下的转发规则的各项属性，包括转发路径、健康检查属性、转发策略等。
// 本接口为异步接口，本接口返回成功后需以返回的RequestID为入参，调用DescribeTaskStatus接口查询本次任务是否成功。
func (c *Client) ModifyRule(request *ModifyRuleRequest) (response *ModifyRuleResponse, err error) {
	if request == nil {
		request = NewModifyRuleRequest()
	}
	response = NewModifyRuleResponse()
	err = c.Send(request, response)
	return
}

func NewDescribeRegionsRequest() (request *DescribeRegionsRequest) {
	request = &DescribeRegionsRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}
	request.Init().WithApiInfo("clb", APIVersion, "DescribeRegions")
	return
}

func NewDescribeRegionsResponse() (response *DescribeRegionsResponse) {
	response = &DescribeRegionsResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// DescribeRegions 接口用来修改负载均衡七层监听器下的转发规则的各项属性，包括转发路径、健康检查属性、转发策略等。
// 本接口为异步接口，本接口返回成功后需以返回的RequestID为入参，调用DescribeTaskStatus接口查询本次任务是否成功。
func (c *Client) DescribeRegions(request *DescribeRegionsRequest) (response *DescribeRegionsResponse, err error) {
	if request == nil {
		request = NewDescribeRegionsRequest()
	}
	response = NewDescribeRegionsResponse()
	err = c.Send(request, response)
	return
}

func NewModifyLoadBalancerAttributesRequest() (request *ModifyLoadBalancerAttributesRequest) {
	request = &ModifyLoadBalancerAttributesRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("clb", APIVersion, "ModifyLoadBalancerAttributes")

	return
}

func NewModifyLoadBalancerAttributesResponse() (response *ModifyLoadBalancerAttributesResponse) {
	response = &ModifyLoadBalancerAttributesResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return

}

// ModifyLoadBalancerAttributes
// 修改负载均衡实例的属性。支持修改负载均衡实例的名称、设置负载均衡的跨域属性。
//
// 本接口为异步接口，接口返回成功后，需以得到的 RequestID 为入参，调用 DescribeTaskStatus 接口查询本次任务是否成功。
//
// 可能返回的错误码:
//
//	FAILEDOPERATION = "FailedOperation"
//	FAILEDOPERATION_RESOURCEINOPERATING = "FailedOperation.ResourceInOperating"
//	INTERNALERROR = "InternalError"
//	INVALIDPARAMETER = "InvalidParameter"
//	INVALIDPARAMETER_FORMATERROR = "InvalidParameter.FormatError"
//	INVALIDPARAMETER_LBIDNOTFOUND = "InvalidParameter.LBIdNotFound"
//	INVALIDPARAMETER_REGIONNOTFOUND = "InvalidParameter.RegionNotFound"
//	INVALIDPARAMETERVALUE = "InvalidParameterValue"
//	INVALIDPARAMETERVALUE_DUPLICATE = "InvalidParameterValue.Duplicate"
//	INVALIDPARAMETERVALUE_LENGTH = "InvalidParameterValue.Length"
//	LIMITEXCEEDED = "LimitExceeded"
//	MISSINGPARAMETER = "MissingParameter"
//	RESOURCEINSUFFICIENT = "ResourceInsufficient"
//	UNAUTHORIZEDOPERATION = "UnauthorizedOperation"
func (c *Client) ModifyLoadBalancerAttributes(request *ModifyLoadBalancerAttributesRequest) (response *ModifyLoadBalancerAttributesResponse, err error) {
	return c.ModifyLoadBalancerAttributesWithContext(context.Background(), request)
}

// ModifyLoadBalancerAttributes
// 修改负载均衡实例的属性。支持修改负载均衡实例的名称、设置负载均衡的跨域属性。
//
// 本接口为异步接口，接口返回成功后，需以得到的 RequestID 为入参，调用 DescribeTaskStatus 接口查询本次任务是否成功。
//
// 可能返回的错误码:
//
//	FAILEDOPERATION = "FailedOperation"
//	FAILEDOPERATION_RESOURCEINOPERATING = "FailedOperation.ResourceInOperating"
//	INTERNALERROR = "InternalError"
//	INVALIDPARAMETER = "InvalidParameter"
//	INVALIDPARAMETER_FORMATERROR = "InvalidParameter.FormatError"
//	INVALIDPARAMETER_LBIDNOTFOUND = "InvalidParameter.LBIdNotFound"
//	INVALIDPARAMETER_REGIONNOTFOUND = "InvalidParameter.RegionNotFound"
//	INVALIDPARAMETERVALUE = "InvalidParameterValue"
//	INVALIDPARAMETERVALUE_DUPLICATE = "InvalidParameterValue.Duplicate"
//	INVALIDPARAMETERVALUE_LENGTH = "InvalidParameterValue.Length"
//	LIMITEXCEEDED = "LimitExceeded"
//	MISSINGPARAMETER = "MissingParameter"
//	RESOURCEINSUFFICIENT = "ResourceInsufficient"
//	UNAUTHORIZEDOPERATION = "UnauthorizedOperation"
func (c *Client) ModifyLoadBalancerAttributesWithContext(ctx context.Context, request *ModifyLoadBalancerAttributesRequest) (response *ModifyLoadBalancerAttributesResponse, err error) {
	if request == nil {
		request = NewModifyLoadBalancerAttributesRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("ModifyLoadBalancerAttributes require credential")
	}

	request.SetContext(ctx)

	response = NewModifyLoadBalancerAttributesResponse()
	err = c.Send(request, response)
	return
}
