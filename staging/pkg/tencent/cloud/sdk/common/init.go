package common

import (
	common "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
)

var config *Config

type Config struct {
	RootDomain string `yaml:"rootDomain" default:"internal.tencentcloudapi.com"`
	WithSend   func(sender Sender) Sender
	BeforeSend func(req common.Request) error
	AfterSend  func(req common.Request, resp common.Response)
}

func (c *Config) defaults() {
	if c.RootDomain == "" {
		c.RootDomain = "internal.tencentcloudapi.com"
	}
}

func Init(cfg *Config) {
	cfg.defaults()
	config = cfg
	common.RootDomain = config.RootDomain
}
