package clientset

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"sync"

	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloud/tkeinternal"
	"k8s.io/klog/v2"

	clbv2 "github.com/howardshaw/qcloudapi-sdk-go/clb"
	commonv2 "github.com/howardshaw/qcloudapi-sdk-go/common"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	ssl "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ssl/v20191205"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"go.uber.org/ratelimit"
)

type RegionInterface interface {
	CLBv2(action string) *clbv2.Client
	CLB(action string) *clb.Client
	CLBInternal(action string) *clbinternal.Client
	CVM(action string) *cvm.Client
	TKE(action string) *tke.Client
	TKEInternal(action string) *tkeinternal.Client
	Tag(action string) *tag.Client
	VPC(action string) *vpc.Client
	SSL(action string) *ssl.Client
}

func NewForRegion(region string, config Config) (*RegionClientset, error) {
	klog.Infof("new clientset for region %s", region)
	p := &RegionClientset{
		Region: region,
		Config: config,
	}

	cred, err := p.getCredential()
	if err != nil {
		return nil, err
	}

	clientProfile := profile.NewClientProfile()
	// sdk默认域名为公网地址，metacluster不支持公网访问，同时因为管控组件都是部署在腾讯云环境中，所以这里直接使用内网地址
	clientProfile.HttpProfile.RootDomain = "internal.tencentcloudapi.com"
	clientProfile.Debug = config.Debug
	// 暂时都是通过内网访问，无法使用自动切换能力
	// 开启熔断自动切换备份能力
	// clientProfile.DisableRegionBreaker = false
	// // 设置备用请求地址，不需要指定服务，SDK 会自动在头部加上服务名(如cvm)
	// clientProfile.BackupEndpoint = "ap-guangzhou.internal.tencentcloudapi.com"

	p.clbClient, _ = clb.NewClient(cred, region, clientProfile)
	p.clbinternalClient, _ = clbinternal.NewClient(cred, region, clientProfile)
	p.cvmClient, _ = cvm.NewClient(cred, region, clientProfile)
	p.tkeClient, _ = tke.NewClient(cred, region, clientProfile)
	p.tkeinternalClient, _ = tkeinternal.NewClient(cred, region, clientProfile)
	p.tagClient, _ = tag.NewClient(cred, region, clientProfile)
	p.vpcClient, _ = vpc.NewClient(cred, region, clientProfile)
	p.sslClient, _ = ssl.NewClient(cred, region, clientProfile)

	v2Cred := &v2Credential{Cred: cred}
	p.clbV2Client, _ = clbv2.NewClient(v2Cred, commonv2.Opts{Region: p.Region})

	// 如果在TCE环境中，则关闭TLS认证
	if config.InTCE {
		tr := &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		}
		p.clbClient.WithHttpTransport(tr)
		p.clbinternalClient.WithHttpTransport(tr)
		p.cvmClient.WithHttpTransport(tr)
		p.tkeClient.WithHttpTransport(tr)
		p.tkeinternalClient.WithHttpTransport(tr)
		p.sslClient.WithHttpTransport(tr)
		p.tagClient.WithHttpTransport(tr)
		p.vpcClient.WithHttpTransport(tr)
	}

	return p, nil
}

func (p *RegionClientset) getCredential() (common.CredentialIface, error) {
	if p.AKSKConfig != nil {
		klog.Infof("use aksk config")
		return common.NewCredential(p.AKSKConfig.SecretID, p.AKSKConfig.SecretKey), nil
	}

	if p.ServiceRoleConfig != nil {
		klog.Infof("use service role config")
		return common.NewRoleArnProvider(p.ServiceRoleConfig.SecretID, p.ServiceRoleConfig.SecretKey, p.ServiceRoleConfig.ARN, p.ServiceRoleConfig.SessionName, p.ServiceRoleConfig.DurationSeconds).GetCredential()
	}

	if p.LockSmithConfig != nil {
		klog.Infof("use locksmith config")
		return NewLocksmithProvider(p.LockSmithConfig).GetCredential()
	}

	return nil, fmt.Errorf("no credential config")
}

type RegionClientset struct {
	Config
	Region           string
	RateLimitSyncMap sync.Map

	clbV2Client       *clbv2.Client
	clbClient         *clb.Client
	clbinternalClient *clbinternal.Client
	cvmClient         *cvm.Client
	sslClient         *ssl.Client
	tkeClient         *tke.Client
	tkeinternalClient *tkeinternal.Client
	tagClient         *tag.Client
	vpcClient         *vpc.Client
}

func (p *RegionClientset) checkRateLimit(request, method string) {
	action := fmt.Sprintf("%s_%s", request, method)
	if rl, ok := p.RateLimitSyncMap.LoadOrStore(action, ratelimit.New(p.RateLimit, ratelimit.WithoutSlack)); ok {
		rl.(ratelimit.Limiter).Take()
	}
}

func (p *RegionClientset) CLB(action string) *clb.Client {
	p.checkRateLimit("clb", action)

	return p.clbClient
}

func (p *RegionClientset) CLBInternal(action string) *clbinternal.Client {
	p.checkRateLimit("clb", action)

	return p.clbinternalClient
}

func (p *RegionClientset) CLBv2(action string) *clbv2.Client {
	p.checkRateLimit("clbv2", action)

	return p.clbV2Client
}

func (p *RegionClientset) CVM(action string) *cvm.Client {
	p.checkRateLimit("cvm", action)

	return p.cvmClient
}

func (p *RegionClientset) SSL(action string) *ssl.Client {
	p.checkRateLimit("ssl", action)

	return p.sslClient
}

func (p *RegionClientset) TKE(action string) *tke.Client {
	p.checkRateLimit("tke", action)

	return p.tkeClient
}

func (p *RegionClientset) TKEInternal(action string) *tkeinternal.Client {
	p.checkRateLimit("tkeinternal", action)

	return p.tkeinternalClient
}

func (p *RegionClientset) Tag(action string) *tag.Client {
	p.checkRateLimit("tag", action)

	return p.tagClient
}

func (p *RegionClientset) VPC(action string) *vpc.Client {
	p.checkRateLimit("vpc", action)

	return p.vpcClient
}
