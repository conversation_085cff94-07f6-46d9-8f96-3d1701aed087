package clientset

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/samber/lo"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.opentelemetry.io/otel"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/tke-library/locksmith/client-go/constant"
	"git.woa.com/tke-library/locksmith/client-go/credential"
	"git.woa.com/tke-library/locksmith/client-go/provider"
	"git.woa.com/tke-library/locksmith/client-go/provider/chain"
	"git.woa.com/tke-library/locksmith/client-go/provider/grpc/clientwrapper"
	"git.woa.com/tke-library/locksmith/client-go/provider/norm/providerwrapper"
	"git.woa.com/tke-library/locksmith/client-go/serviceaccount/secrettoken"
	"git.woa.com/tke-library/locksmith/client-go/serviceaccount/synccache"
	"git.woa.com/tke-library/locksmith/client-go/serviceaccount/tokenrequest"
	"git.woa.com/tke-library/locksmith/client-go/serviceaccount/versionagnostic"
	normclient "git.woa.com/tke-library/norm-client-go/client"
	normcred "git.woa.com/tke-library/norm-client-go/credential"
	credentialsvc "git.woa.com/tke/locksmith-api-go/locksmithpb/service/credential_exchange"

	"git.woa.com/kateway/pkg/domain/services/cluster"
)

type LocksmithProvider struct {
	*LockSmithConfig
}

func NewLocksmithProvider(config *LockSmithConfig) *LocksmithProvider {
	return &LocksmithProvider{
		LockSmithConfig: config,
	}
}

func (p *LocksmithProvider) GetCredential() (common.CredentialIface, error) {
	ctx := context.Background()
	kubeClient, err := kubernetes.NewForConfig(p.RESTConfig)
	if err != nil {
		return nil, err
	}
	// 兼容托管集群 eks集群，没有创建对应的rbac，Locksmith需要对应的ServiceAccount运行，这里暂时只创建ServiceAccount
	_, err = kubeClient.CoreV1().ServiceAccounts(p.ServiceAccountNamespace).Get(ctx, p.ServiceAccount, metav1.GetOptions{})
	if apierrors.IsNotFound(err) { // 忽略其他错误，只需要检测到明确不存在，则尝试创建
		_, err = kubeClient.CoreV1().ServiceAccounts(p.ServiceAccountNamespace).Create(context.Background(), &corev1.ServiceAccount{
			ObjectMeta: metav1.ObjectMeta{
				Name: p.ServiceAccount,
			},
		}, metav1.CreateOptions{})
		if err != nil {
			return nil, err
		}

		// 当集群版本<=1.22时，等待对应的secret创建完成
		if lo.Must(cluster.Instance.CheckVersion("<=v1.22")) {
			err = wait.Poll(time.Second, time.Minute, func() (done bool, err error) {
				sa, err := kubeClient.CoreV1().ServiceAccounts(p.ServiceAccountNamespace).Get(ctx, p.ServiceAccount, metav1.GetOptions{})
				if err != nil {
					return false, nil
				}
				if len(sa.Secrets) > 0 {
					return true, nil
				}

				return false, nil
			})
			if err != nil {
				return nil, fmt.Errorf("wait ServiceAccount ready error: %w", err)
			}
		}
	}

	consoleEncoder := zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig())
	core := zapcore.NewCore(consoleEncoder, os.Stdout, zap.InfoLevel)
	logger := zap.New(core)

	loader := versionagnostic.NewTokenProvider(
		kubeClient,
		p.ServiceAccount,
		p.ServiceAccountNamespace,
		versionagnostic.WithLogger(logger),
		// k8s >= v1.22 版本，使用TokenProvider，使用TokenRequest API获取临时token
		versionagnostic.WithTokenProvider(
			tokenrequest.NewTokenProvider(kubeClient, p.ServiceAccount, p.ServiceAccountNamespace,
				tokenrequest.WithLogger(logger),
				tokenrequest.WithDesiredTokenLifetime(p.CredentialDuration),
			),
		),
		// k8s < v1.22 版本，使用LegacyTokenProvider，使用ServiceAccount里面对应的Secret获取长期token
		versionagnostic.WithLegacyTokenProvider(
			secrettoken.NewTokenProvider(kubeClient, p.ServiceAccount, p.ServiceAccountNamespace,
				secrettoken.WithLogger(logger),
			),
		),
	)

	// 预先获取一个token，缓存起来，避免后面调用时，频繁调用API server获取token
	tokenProvider := synccache.NewTokenProvider(loader, synccache.WithLifetime(p.CredentialDuration), synccache.WithLogger(logger))
	token, err := tokenProvider.GetToken(context.Background())
	if err != nil {
		return nil, err
	}
	klog.Infof("Got token: %s", token)

	conn, err := grpc.NewClient(
		constant.DefaultLocksmithGRPCEndpoint,
		grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
	)
	if err != nil {
		return nil, err
	}
	client := credentialsvc.NewLocksmithCredentialExchangeServiceClient(conn)

	// 使用ServiceAccount TokenProvider获取临时密钥，会按需调用获取token
	locksmithProvider, err := provider.NewTKEServiceRoleCredentialProvider(
		clientwrapper.NewClientWrapper(client),
		provider.WithClusterID(p.ClusterID),
		provider.WithServiceAccountTokenProvider(tokenProvider),
		provider.WithLogger(logger),
		provider.WithCredentialLifetime(p.CredentialDuration),
		provider.WithTimeRefreshCredentialInAdvance(p.CredentialDuration/2),
	)
	if err != nil {
		return nil, err
	}

	httpClient := &http.Client{
		Transport: otelhttp.NewTransport(
			http.DefaultTransport,
			otelhttp.WithTracerProvider(otel.GetTracerProvider()),
		),
	}
	normClient, err := normclient.NewNormClient(
		normclient.WithHTTPClient(httpClient),
		normclient.WithCaller(p.Name),
		normclient.WithLogger(logger),
	)
	if err != nil {
		return nil, err
	}

	normProvider, err := normcred.NewTKEServiceRoleProvider(
		normcred.WithLogger(logger),
		normcred.WithNormClient(normClient),
		normcred.WithClusterID(p.ClusterID),
		normcred.WithCredentialValidPeriod(int(p.CredentialDuration.Seconds())),
	)
	if err != nil {
		return nil, err
	}

	wrappedNormProvider := providerwrapper.NewProviderWrapper(
		normProvider,
	)

	providerChain := chain.NewTKEServiceRoleCredentialProviderChain(
		[]credential.TKEServiceRoleCredentialProvider{
			locksmithProvider,   // 先尝试使用Locksmith获取临时密钥
			wrappedNormProvider, // 再尝试使用Norm获取临时密钥
		},
		chain.WithLogger(logger),
		chain.WithTracerProvider(otel.GetTracerProvider()),
		// 默认20秒超时，切换到下一个provider获取凭证
		chain.WithDefaultGetCredentialTimeout(time.Second*20),
	)

	return providerChain.GetCredential()
}
