package clientset

import (
	commonv2 "github.com/howardshaw/qcloudapi-sdk-go/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
)

type v2Credential struct {
	Cred common.CredentialIface
}

func (c *v2Credential) GetSecretId() (string, error) {
	return c.Cred.GetSecretId(), nil
}

func (c *v2Credential) GetSecretKey() (string, error) {
	return c.Cred.GetSecretKey(), nil
}

func (c *v2Credential) Values() (commonv2.CredentialValues, error) {
	return commonv2.CredentialValues{"Token": c.Cred.GetToken()}, nil
}
