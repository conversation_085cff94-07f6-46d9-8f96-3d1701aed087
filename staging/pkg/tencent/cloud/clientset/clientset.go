package clientset

import (
	"sync"
	"time"

	clbv2 "github.com/howardshaw/qcloudapi-sdk-go/clb"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	ssl "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/ssl/v20191205"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"k8s.io/client-go/rest"

	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloud/tkeinternal"
)

type Interface interface {
	CLBv2(region string, action string) (*clbv2.Client, error)
	CLB(region string, action string) (*clb.Client, error)
	CLBInternal(region string, action string) (*clbinternal.Client, error)
	CVM(region string, action string) (*cvm.Client, error)
	TKE(region string, action string) (*tke.Client, error)
	TKEInternal(region string, action string) (*tkeinternal.Client, error)
	Tag(region string, action string) (*tag.Client, error)
	VPC(region string, action string) (*vpc.Client, error)
	SSL(region string, action string) (*ssl.Client, error)
}

func New(config Config) *Clientset {
	return &Clientset{
		Config:    config,
		RegionMap: make(map[string]RegionInterface),
	}
}

type Config struct {
	AKSKConfig        *AKSKConfig
	ServiceRoleConfig *ServiceRoleConfig // 优先级最高
	LockSmithConfig   *LockSmithConfig   // 默认
	Debug             bool
	InTCE             bool
	RateLimit         int
}

type AKSKConfig struct {
	SecretID  string
	SecretKey string
}

type ServiceRoleConfig struct {
	AKSKConfig
	OwnerUIN        string
	ARN             string
	SessionName     string
	DurationSeconds int64
}

type LockSmithConfig struct {
	Name                    string
	ClusterID               string
	RESTConfig              *rest.Config
	ServiceAccount          string
	ServiceAccountNamespace string
	CredentialDuration      time.Duration
}

type Clientset struct {
	m sync.Mutex
	Config
	RegionMap map[string]RegionInterface
}

func (c *Clientset) CLBv2(region string, action string) (*clbv2.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.CLBv2(action), nil
}

func (c *Clientset) CLB(region string, action string) (*clb.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.CLB(action), nil
}

func (c *Clientset) CLBInternal(region string, action string) (*clbinternal.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.CLBInternal(action), nil
}

func (c *Clientset) CVM(region string, action string) (*cvm.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.CVM(action), nil
}

func (c *Clientset) TKE(region string, action string) (*tke.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.TKE(action), nil
}

func (c *Clientset) TKEInternal(region string, action string) (*tkeinternal.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.TKEInternal(action), nil
}

func (c *Clientset) Tag(region string, action string) (*tag.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.Tag(action), nil
}

func (c *Clientset) VPC(region string, action string) (*vpc.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.VPC(action), nil
}

func (c *Clientset) SSL(region string, action string) (*ssl.Client, error) {
	service, err := c.GetForRegion(region)
	if err != nil {
		return nil, err
	}
	return service.SSL(action), nil
}

func (c *Clientset) GetForRegion(region string) (RegionInterface, error) {
	var err error
	service, exist := c.RegionMap[region]
	if !exist {
		c.m.Lock()
		defer c.m.Unlock()
		service, exist := c.RegionMap[region]
		if !exist {
			service, err = NewForRegion(region, c.Config)
			if err != nil {
				return nil, err
			}
			c.RegionMap[region] = service
		}
		return service, nil
	}
	return service, nil
}
