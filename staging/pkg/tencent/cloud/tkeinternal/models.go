// Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package tkeinternal

import (
	"encoding/json"

	tcerr "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
)

// Predefined struct for user
type AdmitResourceOperationRequestParams struct {
	// 来源模块
	Origin *string `json:"Origin,omitempty" name:"Origin"`

	// 待授权的资源操作类型
	Operation *string `json:"Operation,omitempty" name:"Operation"`

	// 待调用接口
	TargetAction *string `json:"TargetAction,omitempty" name:"TargetAction"`

	// 待授权操作的资源集
	ResourceSet []*Resource `json:"ResourceSet,omitempty" name:"ResourceSet"`

	// 关联的 TKE 集群 ID
	ClusterID *string `json:"ClusterID,omitempty" name:"ClusterID"`

	// 用于保证请求幂等性的字符串。该字符串由客户生成，需保证不同请求之间唯一，最大值不超过64个ASCII字符。若不指定该参数，则无法保证请求的幂等性。
	// 示例值：system-f3827db9-c58a-49cc-bf10-33fc1923a34a
	ClientToken *string `json:"ClientToken,omitempty" name:"ClientToken"`
}

type AdmitResourceOperationRequest struct {
	*tchttp.BaseRequest

	// 来源模块
	Origin *string `json:"Origin,omitempty" name:"Origin"`

	// 待授权的资源操作类型
	Operation *string `json:"Operation,omitempty" name:"Operation"`

	// 待调用接口
	TargetAction *string `json:"TargetAction,omitempty" name:"TargetAction"`

	// 待授权操作的资源集
	ResourceSet []*Resource `json:"ResourceSet,omitempty" name:"ResourceSet"`

	// 关联的 TKE 集群 ID
	ClusterID *string `json:"ClusterID,omitempty" name:"ClusterID"`

	// 用于保证请求幂等性的字符串。该字符串由客户生成，需保证不同请求之间唯一，最大值不超过64个ASCII字符。若不指定该参数，则无法保证请求的幂等性。
	// 示例值：system-f3827db9-c58a-49cc-bf10-33fc1923a34a
	ClientToken *string `json:"ClientToken,omitempty" name:"ClientToken"`
}

func (r *AdmitResourceOperationRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *AdmitResourceOperationRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "Origin")
	delete(f, "Operation")
	delete(f, "TargetAction")
	delete(f, "ResourceSet")
	delete(f, "ClusterID")
	delete(f, "ClientToken")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "AdmitResourceOperationRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type AdmitResourceOperationResponseParams struct {
	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type AdmitResourceOperationResponse struct {
	*tchttp.BaseResponse
	Response *AdmitResourceOperationResponseParams `json:"Response"`
}

func (r *AdmitResourceOperationResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *AdmitResourceOperationResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

type CloudResource struct {
	// 腾讯云资源服务类型，如：vpc、clb 等等
	ResourceType *string `json:"ResourceType,omitempty" name:"ResourceType"`

	// 资源 ID
	ResourceID *string `json:"ResourceID,omitempty" name:"ResourceID"`
}

type KubernetesResource struct {
	// 资源所在的命名空间
	Namespace *string `json:"Namespace,omitempty" name:"Namespace"`

	// 资源类型（比如：Pod）
	Kind *string `json:"Kind,omitempty" name:"Kind"`

	// 取 metadata.name
	Name *string `json:"Name,omitempty" name:"Name"`

	// 取 metadata.uuid
	UUID *string `json:"UUID,omitempty" name:"UUID"`
}

type Resource struct {
	// 需要操作的腾讯云资源，可选
	Cloud *CloudResource `json:"Cloud,omitempty" name:"Cloud"`

	// 当未指定 Cloud 时表示需要操作的kubernetes 资源；当指定 Cloud 时，表示所操作云资源关联的 kubernetes 资源
	Kubernetes *KubernetesResource `json:"Kubernetes,omitempty" name:"Kubernetes"`
}

// Predefined struct for user
type ForwardRequestRequestParams struct {
	// 请求tke-apiserver http请求对应的方法
	Method *string `json:"Method,omitempty" name:"Method"`

	// 请求tke-apiserver  http请求访问路径
	Path *string `json:"Path,omitempty" name:"Path"`

	// 请求tke-apiserver http头中Accept参数
	Accept *string `json:"Accept,omitempty" name:"Accept"`

	// 请求tke-apiserver http头中ContentType参数
	ContentType *string `json:"ContentType,omitempty" name:"ContentType"`

	// 请求tke-apiserver http请求body信息
	RequestBody *string `json:"RequestBody,omitempty" name:"RequestBody"`

	// 请求tke-apiserver http头中X-TKE-ClusterName参数
	ClusterName *string `json:"ClusterName,omitempty" name:"ClusterName"`

	// 是否编码请求body信息
	EncodedBody *bool `json:"EncodedBody,omitempty" name:"EncodedBody"`
}

type ForwardRequestRequest struct {
	*tchttp.BaseRequest

	// 请求tke-apiserver http请求对应的方法
	Method *string `json:"Method,omitempty" name:"Method"`

	// 请求tke-apiserver  http请求访问路径
	Path *string `json:"Path,omitempty" name:"Path"`

	// 请求tke-apiserver http头中Accept参数
	Accept *string `json:"Accept,omitempty" name:"Accept"`

	// 请求tke-apiserver http头中ContentType参数
	ContentType *string `json:"ContentType,omitempty" name:"ContentType"`

	// 请求tke-apiserver http请求body信息
	RequestBody *string `json:"RequestBody,omitempty" name:"RequestBody"`

	// 请求tke-apiserver http头中X-TKE-ClusterName参数
	ClusterName *string `json:"ClusterName,omitempty" name:"ClusterName"`

	// 是否编码请求body信息
	EncodedBody *bool `json:"EncodedBody,omitempty" name:"EncodedBody"`
}

func (r *ForwardRequestRequest) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *ForwardRequestRequest) FromJsonString(s string) error {
	f := make(map[string]interface{})
	if err := json.Unmarshal([]byte(s), &f); err != nil {
		return err
	}
	delete(f, "Method")
	delete(f, "Path")
	delete(f, "Accept")
	delete(f, "ContentType")
	delete(f, "RequestBody")
	delete(f, "ClusterName")
	delete(f, "EncodedBody")
	if len(f) > 0 {
		return tcerr.NewTencentCloudSDKError("ClientError.BuildRequestError", "ForwardRequestRequest has unknown keys!", "")
	}
	return json.Unmarshal([]byte(s), &r)
}

// Predefined struct for user
type ForwardRequestResponseParams struct {
	// 请求tke-apiserver http请求返回的body信息
	ResponseBody *string `json:"ResponseBody,omitempty" name:"ResponseBody"`

	// 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
	RequestId *string `json:"RequestId,omitempty" name:"RequestId"`
}

type ForwardRequestResponse struct {
	*tchttp.BaseResponse
	Response *ForwardRequestResponseParams `json:"Response"`
}

func (r *ForwardRequestResponse) ToJsonString() string {
	b, _ := json.Marshal(r)
	return string(b)
}

// FromJsonString It is highly **NOT** recommended to use this function
// because it has no param check, nor strict type check
func (r *ForwardRequestResponse) FromJsonString(s string) error {
	return json.Unmarshal([]byte(s), &r)
}
