// Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package tkeinternal

import (
	"context"
	"errors"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tchttp "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/http"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
)

const APIVersion = "2018-05-25"

type Client struct {
	common.Client
}

// Deprecated
func NewClientWithSecretId(secretId, secretKey, region string) (client *Client, err error) {
	cpf := profile.NewClientProfile()
	client = &Client{}
	client.Init(region).WithSecretId(secretId, secretKey).WithProfile(cpf)
	return
}

func NewClient(credential common.CredentialIface, region string, clientProfile *profile.ClientProfile) (client *Client, err error) {
	client = &Client{}
	client.Init(region).
		WithCredential(credential).
		WithProfile(clientProfile)
	return
}

func NewAdmitResourceOperationRequest() (request *AdmitResourceOperationRequest) {
	request = &AdmitResourceOperationRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "AdmitResourceOperation")

	return
}

func NewAdmitResourceOperationResponse() (response *AdmitResourceOperationResponse) {
	response = &AdmitResourceOperationResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// AdmitResourceOperation
// 获取资源操作许可
//
// 可能返回的错误码:
//
//	OPERATIONDENIED_POLICYNOTALLOWED = "OperationDenied.PolicyNotAllowed"
func (c *Client) AdmitResourceOperation(request *AdmitResourceOperationRequest) (response *AdmitResourceOperationResponse, err error) {
	return c.AdmitResourceOperationWithContext(context.Background(), request)
}

// AdmitResourceOperation
// 获取资源操作许可
//
// 可能返回的错误码:
//
//	OPERATIONDENIED_POLICYNOTALLOWED = "OperationDenied.PolicyNotAllowed"
func (c *Client) AdmitResourceOperationWithContext(ctx context.Context, request *AdmitResourceOperationRequest) (response *AdmitResourceOperationResponse, err error) {
	if request == nil {
		request = NewAdmitResourceOperationRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("AdmitResourceOperation require credential")
	}

	request.SetContext(ctx)

	response = NewAdmitResourceOperationResponse()
	err = c.Send(request, response)
	return
}

func NewForwardRequestRequest() (request *ForwardRequestRequest) {
	request = &ForwardRequestRequest{
		BaseRequest: &tchttp.BaseRequest{},
	}

	request.Init().WithApiInfo("tke", APIVersion, "ForwardRequest")

	return
}

func NewForwardRequestResponse() (response *ForwardRequestResponse) {
	response = &ForwardRequestResponse{
		BaseResponse: &tchttp.BaseResponse{},
	}
	return
}

// ForwardRequest
// 查询、新增、删除、编辑EKS集群内资源
//
// 可能返回的错误码:
//
//	FAILEDOPERATION = "FailedOperation"
//	FAILEDOPERATION_RBACFORBIDDEN = "FailedOperation.RBACForbidden"
//	INTERNALERROR = "InternalError"
//	INVALIDPARAMETER = "InvalidParameter"
//	LIMITEXCEEDED = "LimitExceeded"
//	MISSINGPARAMETER = "MissingParameter"
//	RESOURCEINUSE = "ResourceInUse"
//	RESOURCENOTFOUND = "ResourceNotFound"
//	RESOURCEUNAVAILABLE = "ResourceUnavailable"
//	UNAUTHORIZEDOPERATION = "UnauthorizedOperation"
//	UNKNOWNPARAMETER = "UnknownParameter"
//	UNSUPPORTEDOPERATION = "UnsupportedOperation"
func (c *Client) ForwardRequest(request *ForwardRequestRequest) (response *ForwardRequestResponse, err error) {
	return c.ForwardRequestWithContext(context.Background(), request)
}

// ForwardRequest
// 查询、新增、删除、编辑EKS集群内资源
//
// 可能返回的错误码:
//
//	FAILEDOPERATION = "FailedOperation"
//	FAILEDOPERATION_RBACFORBIDDEN = "FailedOperation.RBACForbidden"
//	INTERNALERROR = "InternalError"
//	INVALIDPARAMETER = "InvalidParameter"
//	LIMITEXCEEDED = "LimitExceeded"
//	MISSINGPARAMETER = "MissingParameter"
//	RESOURCEINUSE = "ResourceInUse"
//	RESOURCENOTFOUND = "ResourceNotFound"
//	RESOURCEUNAVAILABLE = "ResourceUnavailable"
//	UNAUTHORIZEDOPERATION = "UnauthorizedOperation"
//	UNKNOWNPARAMETER = "UnknownParameter"
//	UNSUPPORTEDOPERATION = "UnsupportedOperation"
func (c *Client) ForwardRequestWithContext(ctx context.Context, request *ForwardRequestRequest) (response *ForwardRequestResponse, err error) {
	if request == nil {
		request = NewForwardRequestRequest()
	}

	if c.GetCredential() == nil {
		return nil, errors.New("ForwardRequest require credential")
	}

	request.SetContext(ctx)

	response = NewForwardRequestResponse()
	err = c.Send(request, response)
	return
}
