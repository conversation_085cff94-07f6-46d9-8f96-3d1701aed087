package fmt

import (
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"os"
	"os/user"
	"time"

	logsdk "git.woa.com/zhiyan-log/sdk-go/v2"
)

type Config struct {
	ID        string
	user      string
	host      string
	startTime time.Time

	ProjectID  string
	Env        string
	EnvID      string
	ViewID     string
	Topic      string
	ServerAddr string
	Proto      string
}

var (
	client *logsdk.LogClient
	config *Config
)

func Init(c *Config) {
	config = c
	config.host, _ = os.Hostname()
	u, _ := user.Current()
	config.user = u.Username
	config.startTime = time.Now()
	client = logsdk.NewLogWithServerAddr(c.<PERSON>, c.Topic, c.Proto, config.host)
	client.InitConnect()
}

func sendMessage(msg string) error {
	data, _ := json.Marshal(map[string]interface{}{
		"id":      config.ID,
		"user":    config.user,
		"message": msg,
	})
	return client.SendMessage(string(data))
}

func URL() string {
	params := url.Values{}
	params.Add("id", config.ViewID)
	params.Add("env", config.Env)
	params.Add("env_id", config.EnvID)
	params.Add("startTimeType", "absolute")
	params.Add("startTimeValue", fmt.Sprint(config.startTime.UnixMilli()))
	params.Add("endTimeType", "absolute")
	params.Add("endTimeValue", fmt.Sprint(time.Now().Add(time.Minute).UnixMilli()))
	filters, _ := json.Marshal([]map[string]interface{}{
		{
			"field":     "id",
			"operation": "eq",
			"selected":  "=",
			"fieldValue": map[string]interface{}{
				"value": config.ID,
			},
		},
	})
	params.Add("filters", string(filters))

	return fmt.Sprintf("https://zhiyan.woa.com/log/%s/dataflow/#/analyze/query/view?%s", config.ProjectID, params.Encode())
}

func Println(a ...any) (n int, err error) {
	s := fmt.Sprintln(a...)
	sendMessage(s)
	return fmt.Print(s)
}

func Printf(format string, a ...any) (n int, err error) {
	s := fmt.Sprintf(format, a...)
	sendMessage(s)
	return os.Stdout.Write([]byte(s))
}

func Sprintf(format string, a ...any) string {
	return fmt.Sprintf(format, a...)
}

func Fprintf(w io.Writer, format string, a ...any) (n int, err error) {
	return fmt.Fprintf(w, format, a...)
}

func Errorf(format string, a ...any) error {
	return fmt.Errorf(format, a...)
}

func Close() {
	client.Close()
}
