package account

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.woa.com/kateway/pkg/tencent/account/core"
)

const (
	serviceURL = "http://account.tencentyun.com:50001"
	timeout    = 20 * time.Second
)

var (
	ErrNotExist = errors.New("error not exist")
)

// UserExistInList returns true if the user with uin exist in the account list referenced by the key
func UserExistInList(ctx context.Context, key, uin string) (bool, error) {
	s := core.New(serviceURL, timeout)
	resp, err := s.BatchGetWhitelist(ctx, []string{key}, []string{uin}, 1)
	if err != nil {
		return false, fmt.Errorf("failed to retrieve account list from the service: %w", err)
	}
	list, exist := resp[key]
	if !exist {
		return false, fmt.Errorf("account list %q does not exist: %w", key, ErrNotExist)
	}
	for _, u := range list {
		if uin == u {
			return true, nil
		}
	}
	return false, nil
}

// GetUserNickname retrieves the nickname of a user
func GetUserNickname(ctx context.Context, uin string) (string, error) {
	s := core.New(serviceURL, timeout)
	resp, err := s.GetNickname(ctx, uin, "")
	if err != nil || resp == nil {
		return "", fmt.Errorf("failed to get the nickname for the user: %w", err)
	}
	return resp.Nickname, nil
}

func GetUinWithAppID(ctx context.Context, appID uint64) (string, error) {
	s := core.New(serviceURL, timeout)
	resp, err := s.GetOwnerUinByAppID(ctx, int64(appID))
	if err != nil {
		return "", err
	}
	return resp.Uin, nil
}
