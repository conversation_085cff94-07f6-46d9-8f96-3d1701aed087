// 根据手机号、 email 、身份证号获取用户的 UIN 和 APPID
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000679169

package core

import (
	"context"
	"encoding/json"
	"fmt"
)

// QueryType 查询类型
type QueryType string

const (
	// QueryTypePhone 根据手机号查询
	QueryTypePhone QueryType = "phone"
	// QueryTypeEmail 根据邮箱号查询
	QueryTypeEmail QueryType = "mail"
	// QueryTypeIDCard 根据身份证/护照/回乡证/台胞证/营业执照号/社会信用代码查询
	QueryTypeIDCard QueryType = "idCard"
)

// UinAppID 接口返回 json 列表元素结构
type UinAppID struct {
	AppID string `json:"appId"`
	Uin   string `json:"uin"`
}

// GetUinByAuthinfo 根据手机号、 email 、身份证号获取用户的 UIN 和 APPID
func (s *Service) GetUinByAuthinfo(ctx context.Context, querytype QueryType, queryvalue string) ([]UinAppID, error) {
	para := map[string]interface{}{
		"queryType":  querytype,
		"queryValue": queryvalue,
	}
	interfaceName := "qcloud.account.getUinbyAuthInfo"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	b, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("json Marshal result.Data error:%+v with %+v", err, result.Data)
	}
	r := []UinAppID{}
	if err := json.Unmarshal(b, &r); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the result data %s: %w", b, err)
	}
	return r, nil
}
