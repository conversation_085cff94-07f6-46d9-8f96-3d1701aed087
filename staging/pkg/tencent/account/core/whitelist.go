// 批量查询白名单接口
// 文档地址 http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000678455

package core

import (
	"context"
	"fmt"
	"net/http"
	"time"
)

// WhitelistService 文档里面还有一个单独的白名单接口链接，接口地址和名称不同，但是参数和返回相同，多提供了一个分页查询的接口
// 文档地址 http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000680703
type WhitelistService struct {
	// URL 不同环境的服务 URL 地址
	URL string
	// HTTPClient http 请求客户端
	HTTPClient *http.Client
}

// NewWhitelist 创建白名单服务
func NewWhitelist(url string, timeout time.Duration) *WhitelistService {
	if url == "" {
		panic("NewWhitelist url is empty")
	}
	client := &http.Client{
		Timeout: timeout,
	}
	return &WhitelistService{
		URL:        url,
		HTTPClient: client,
	}
}

// BatchGetWhitelist account 文档页面中的批量查询白名单
// 请求参数：
//
//	typelist 白名单类型名称列表
//	whitelist 白名单 uin 列表
//	platform 平台（ 0=开平， 1=云平）
//
// 返回 map ：
//
//	  {
//	      "type_item1": [
//	          "uin1",
//	           ...
//			 ],
//			 "type_item2": [
//			     "uin1",
//				 ...
//		     ],
//			 ...
//		 }
func (s *Service) BatchGetWhitelist(ctx context.Context, typelist,
	whitelist []string, platform int) (map[string][]string, error) {
	para := map[string]interface{}{
		"typeList":  typelist,
		"whiteList": whitelist,
		"platform":  platform,
	}
	interfaceName := "qcloud.Qconfig.batchGetWhiteList"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	r := make(map[string][]string)
	data, ok := result.Data.(map[string]interface{})
	if !ok {
		// 没有数据的时候返回结果是个空数组而不是 map ，需要进一步断言
		if emptyData, ok := result.Data.([]interface{}); ok && len(emptyData) == 0 {
			return r, nil
		}
		return nil, fmt.Errorf("assert result data :%+v to map[string]interface{} failed", result.Data)
	}
	for k, i := range data {
		si, ok := i.([]interface{})
		if !ok {
			return nil, fmt.Errorf("assert data item:%+v to []interface{} failed", i)
		}
		var siv []string
		for _, i := range si {
			siv = append(siv, i.(string))
		}
		r[k] = siv
	}
	return r, nil
}

// BatchGetWhitelist 功能同上 不过是在 WhitelistService 下，接口地址和接口名称不同
func (s *WhitelistService) BatchGetWhitelist(ctx context.Context, typelist, whitelist []string,
	platform int) (map[string][]string, error) {
	para := map[string]interface{}{
		"typeList":  typelist,
		"whiteList": whitelist,
		"platform":  platform,
	}
	interfaceName := "qcloud.Qconfig.qconfigBatchGetWhiteList"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	r := make(map[string][]string)
	data, ok := result.Data.(map[string]interface{})
	if !ok {
		// 没有数据的时候返回结果是个空数组而不是 map ，需要进一步断言
		if emptyData, ok := result.Data.([]interface{}); ok && len(emptyData) == 0 {
			return r, nil
		}
		return nil, fmt.Errorf("assert result data:%+v to map[string]interface{} failed", result.Data)
	}
	for k, i := range data {
		si, ok := i.([]interface{})
		if !ok {
			return nil, fmt.Errorf("assert data item:%+v to []interface{} failed", i)
		}
		var siv []string
		for _, i := range si {
			siv = append(siv, i.(string))
		}
		r[k] = siv
	}
	return r, nil
}

// ResultGetWhitelistPage GetWhitelistPage 返回结构
type ResultGetWhitelistPage struct {
	Status    int // 0 灰度 1 全量
	Total     int
	Whitelist []string
}

// GetWhitelistPage 分页获取白名单
func (s *WhitelistService) GetWhitelistPage(ctx context.Context, typename string,
	pagenum, pagesize, platform int) (*ResultGetWhitelistPage, error) {
	para := map[string]interface{}{
		"type":     typename,
		"page":     pagenum,
		"rp":       pagesize,
		"platform": platform,
	}
	interfaceName := "qcloud.whiteList.getWhiteListPage"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	data, ok := result.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("assert result data:%+v to map[string]interface{} failed", result.Data)
	}
	r := &ResultGetWhitelistPage{}
	status, ok := data["status"].(float64)
	if !ok {
		return nil, fmt.Errorf("assert status:%+v to float64 failed", data["status"])
	}
	r.Status = int(status)

	total, ok := data["total"].(float64)
	if !ok {
		return nil, fmt.Errorf("assert total:%+v to float64 failed", data["total"])
	}
	r.Total = int(total)

	whitelisti, ok := data["whiteList"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("assert whitelist:%+v to []interface{} failed", data["whitelist"])
	}
	whitelist := []string{}
	for _, i := range whitelisti {
		whitelist = append(whitelist, i.(string))
	}
	r.Whitelist = whitelist
	return r, nil
}
