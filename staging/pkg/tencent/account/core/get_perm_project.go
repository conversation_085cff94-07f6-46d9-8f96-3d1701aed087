// 获取用户项目权限信息
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000678413

package core

import (
	"context"
	"encoding/json"
	"fmt"
)

// ProjectInfo 项目权限信息
type ProjectInfo struct {
	Appid         int    `json:"appid"`
	CreateTime    string `json:"createTime"`
	CreatorUin    int    `json:"creatorUin"`
	Info          string `json:"info"`
	IsDefault     int    `json:"isDefault"`
	Name          string `json:"name"`
	OwnerUin      int    `json:"ownerUin"`
	ProjectID     int    `json:"projectId"`
	SrcAppid      int    `json:"srcAppid"`
	SrcPlat       string `json:"srcPlat"`
	Status        int    `json:"status"`
	Class         string `json:"class,omitempty"`
	Disable       int    `json:"disable,omitempty"`
	Onlinestatus  int    `json:"onlinestatus,omitempty"`
	Servicestatus int    `json:"servicestatus,omitempty"`
	SourceAppID   string `json:"sourceAppId,omitempty"`
	SourceAppkey  string `json:"sourceAppkey,omitempty"`
}

// GetPermProject 获取用户项目权限信息
func (s *Service) GetPermProject(ctx context.Context, ownerUin, uin string) ([]ProjectInfo, error) {
	para := map[string]interface{}{
		"ownerUin": ownerUin,
		"uin":      uin,
	}
	interfaceName := "qcloud.Qcauth.getPermProject"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	b, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("json Marshal result.Data error:%+v with %+v", err, result.Data)
	}
	r := []ProjectInfo{}
	if err := json.Unmarshal(b, &r); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the result data %s: %w", b, err)
	}
	return r, nil
}
