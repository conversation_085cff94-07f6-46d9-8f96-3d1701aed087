// 查询用户昵称
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000678813

package core

import (
	"context"
	"encoding/json"
	"fmt"
)

// Name displayname and nickname
type Name struct {
	DisplayName string `json:"displayName"`
	Nickname    string `json:"nickname"`
}

// GetNickname 查询用户昵称
// 用户登录态skey， 若无，传空字符串即可
func (s *Service) GetNickname(ctx context.Context, uin, skey string) (*Name, error) {
	para := map[string]interface{}{
		"skey": skey,
		"uin":  uin,
	}
	interfaceName := "qcloud.Quser.getNickname"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	b, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("json Marshal result.Data error:%+v with %+v", err, result.Data)
	}
	r := &Name{}
	if err := json.Unmarshal(b, r); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the result data %s: %w", b, err)
	}
	return r, nil
}
