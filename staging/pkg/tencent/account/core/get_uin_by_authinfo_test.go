package core

// func TestGetUinByAuthinfo(t *testing.T) {
// 	phone := "18676362065"
// 	email := "<EMAIL>"
// 	idCard := "372922199112168899"
// 	edata, err := serviceT.GetUinByAuthinfo(context.TODO(), QueryTypeEmail, email)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	t.Log("email GetUinByAuthinfo:", edata)
// 	pdata, err := serviceT.GetUinByAuthinfo(context.TODO(), QueryTypePhone, phone)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	t.Log("pdata GetUinByAuthinfo:", pdata)
// 	idata, err := serviceT.GetUinByAuthinfo(context.TODO(), QueryTypeIDCard, idCard)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	t.Log("idCard GetUinByAuthinfo:", idata)
// }
