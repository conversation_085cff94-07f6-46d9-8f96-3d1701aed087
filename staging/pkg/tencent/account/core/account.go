package core

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

// Service account 服务定义
type Service struct {
	// URL 不同环境的服务 URL 地址
	URL string
	// HTTPClient http 请求客户端
	HTTPClient *http.Client
}

// Result 接口返回结构
type Result struct {
	Version       int         `json:"version"`
	Timestamp     int64       `json:"timestamp"`
	EventID       int64       `json:"eventId"`
	ComponentName string      `json:"conponentName"`
	ReturnValue   int         `json:"returnValue"`
	ReturnCode    int         `json:"returnCode"`
	ReturnMessage string      `json:"returnMessage"`
	Interface     string      `json:"interface"`
	Data          interface{} `json:"data"`
}

// New 创建 account 示例
func New(url string, timeout time.Duration) *Service {
	if url == "" {
		panic("New account service url is empty")
	}
	client := &http.Client{
		Timeout: timeout,
	}
	return &Service{
		URL:        url,
		HTTPClient: client,
	}
}

// post 请求接口返回 Result 结构
// body post 请求的 json 参数
func post(ctx context.Context, client *http.Client, url string, data interface{}) (*Result, error) {
	buf, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal the payload %v: %w", data, err)
	}
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(buf))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute the http request: %w", err)
	}
	defer resp.Body.Close()

	buff, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read the response: %w", err)
	}
	result := &Result{}
	if err := json.Unmarshal(buff, result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the response %s: %w", buff, err)
	}
	if result.ReturnCode != 0 {
		return result, fmt.Errorf("[%d.%d]%s", result.ReturnCode, result.ReturnValue, result.ReturnMessage)
	}
	return result, nil
}

func newParam(interfaceName string, para interface{}) map[string]map[string]interface{} {
	return map[string]map[string]interface{}{
		"interface": {
			"interfaceName": interfaceName,
			"para":          para,
		},
	}
}
