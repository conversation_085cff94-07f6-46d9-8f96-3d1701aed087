package core

// func TestBatchGetWhitelist(t *testing.T) {
// 	typelist := []string{"other_multi_project", "messageconfig_whitelist"}
// 	whitelist := []string{"909619400", "798950673"}
// 	platform := 1
// 	r, err := serviceT.BatchGetWhitelist(context.TODO(), typelist, whitelist, platform)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	v, exists := r["messageconfig_whitelist"]
// 	if !exists {
// 		t.Fatal("not return messageconfig_whitelist")
// 	}
// 	if reflect.TypeOf(v) != reflect.TypeOf([]string{"thisisastring"}) {
// 		t.Fatal("map value asserted failed")
// 	}
// 	found := false
// 	for _, uin := range v {
// 		if "909619400" == uin {
// 			found = true
// 		}
// 	}
// 	if !found {
// 		t.Fatal("909619400 not in return list")
// 	}
// 	t.Log("BatchGetWhitelist:", r)

// 	r, err = whitelistT.BatchGetWhitelist(context.TODO(), typelist, whitelist, platform)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	v, exists = r["messageconfig_whitelist"]
// 	if !exists {
// 		t.Fatal("not return messageconfig_whitelist")
// 	}
// 	if reflect.TypeOf(v) != reflect.TypeOf([]string{"thisisastring"}) {
// 		t.Fatal("map value asserted failed")
// 	}
// 	found = false
// 	for _, uin := range v {
// 		if "909619400" == uin {
// 			found = true
// 		}
// 	}
// 	if !found {
// 		t.Fatal("909619400 not in return list")
// 	}
// 	t.Log("BatchGetWhitelist:", r)
// }

// func TestGetWhitelistPage(t *testing.T) {
// 	r, err := whitelistT.GetWhitelistPage(context.TODO(), "CVM_bandwidtd", 1, 10, 1)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	if len(r.Whitelist) == 0 {
// 		t.Fatal("whitelist len error:", r)
// 	}
// 	t.Log("GetWhitelistPage:", r)
// }
