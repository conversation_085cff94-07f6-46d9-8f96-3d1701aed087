package core

// func TestGetUserArea(t *testing.T) {
// 	ownerUin := 909619400
// 	data, err := serviceT.GetUserArea(context.TODO(), ownerUin)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	if data.Area == 0 {
// 		t.Fatal("area error:", data)
// 	}
// 	t.Log("GetUserArea:", data)
// }

// func TestGetUserAreaBatch(t *testing.T) {
// 	ownerUin := 909619400
// 	ownerUin1 := 274652179
// 	data, err := serviceT.GetUserAreaBatch(context.TODO(), ownerUin, ownerUin1)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	if len(data) == 0 {
// 		t.Fatal("data len error:", data)
// 	}
// 	t.Log("GetUserAreaBatch:", data)
// }
