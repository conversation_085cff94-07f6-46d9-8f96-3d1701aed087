// 根据appid获取用户uin
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000687541

package core

// func TestGetOwnerUinByAppId(t *testing.T) {
// 	appID := int64(1251763868)
// 	uin := "1500000688"
// 	info, err := serviceT.GetOwnerUinByAppID(context.TODO(), appID)
// 	if err != nil {
// 		t.Fatal(err)
// 	}
// 	if info == nil {
// 		t.Fatal("info == nil")
// 	}

// 	if info.Uin != uin {
// 		t.Fatal("result mismatch")
// 	}

// 	t.Log("GetOwnerUinByAppId:", info)
// }
