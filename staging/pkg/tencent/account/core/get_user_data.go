// 获取官网账号非敏感信息
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000679061

package core

import (
	"context"
	"encoding/json"
	"fmt"
)

// UserDataField 参数 fields 类型
type UserDataField string

const (
	// MaxFieldsCount 最多只能查 5 个 fields
	MaxFieldsCount = 5

	// FieldIsAuthenticated 是否实名， 0 未实名， 1 已实名。新增功能，查询该属性会同时返回 isTenPayMasked 是否财付通实名打码用户
	FieldIsAuthenticated UserDataField = "isAuthenticated"
	// FieldIsLocalUser 是否内部开发者， 0 不是， 1 是
	FieldIsLocalUser UserDataField = "isLocalUser"
	// FieldIsAgentUser 是否代理商， 0 不是， 1 是
	FieldIsAgentUser UserDataField = "isAgentUser"
	// FieldIsAgentClientUser 是否代客， 0 不是， 1 是
	FieldIsAgentClientUser UserDataField = "isAgentClientUser"
	// FieldIsCooperatorUser 是否协作者， 0 不是， 1 是
	FieldIsCooperatorUser UserDataField = "isCooperatorUser"
	// FieldIsRegisteredUser 是否注册用户， 0 不是， 1 是
	FieldIsRegisteredUser UserDataField = "isRegisteredUser"
	// FieldIsActivatedUser 是否激活， 0 不是， 1 是
	FieldIsActivatedUser UserDataField = "isActivatedUser"
	// FieldIsBigCustomer 是否为大客户， 0 不是， 1 是
	FieldIsBigCustomer UserDataField = "isBigCustomer"
	// FieldAuthTime 认证时间
	FieldAuthTime UserDataField = "authTime"
	// FieldRegisterTime 注册时间
	FieldRegisterTime UserDataField = "registerTime"
	// FieldAuthData 认证信息
	FieldAuthData UserDataField = "authData"
)

// AuthData authData 字段有值时的返回 json 结构
type AuthData struct {
	Addr             string `json:"addr"`
	Area             string `json:"area"`
	AuthType         string `json:"authType"`
	AuthenticateType string `json:"authenticateType"`
	CheckMsg         string `json:"checkMsg"`
	Checker          string `json:"checker"`
	Contact          string `json:"contact"`
	CustomerID       string `json:"customerId"`
	Erpaccountid     string `json:"erpaccountid"`
	From             string `json:"from"`
	Idcard           string `json:"idcard"`
	IdcardType       string `json:"idcardType"`
	Name             string `json:"name"`
	OrgCode          string `json:"orgCode"`
	PassTime         string `json:"passTime"`
	Type             string `json:"type"`
}

// UserData 用户数据结构体，因为最多只能查 5 个字段，所以非指定的查询字段为默认值，只信任 fields 中指定的字段值
type UserData struct {
	// 是否为理财通认证打码用户
	IsTenPayMasked    string   `json:"isTenPayMasked"`
	IsAuthenticated   string   `json:"isAuthenticated"`
	IsLocalUser       string   `json:"isLocalUser"`
	IsAgentUser       string   `json:"isAgentUser"`
	IsAgentClientUser string   `json:"isAgentClientUser"`
	IsCooperatorUser  string   `json:"isCooperatorUser"`
	IsRegisteredUser  string   `json:"isRegisteredUser"`
	IsActivatedUser   string   `json:"isActivatedUser"`
	IsBigCustomer     string   `json:"isBigCustomer"`
	AuthTime          string   `json:"authTime"`
	RegisterTime      string   `json:"registerTime"`
	AuthData          AuthData `json:"authData"`
}

// GetUserData 获取官网账号非敏感信息， 最多只能查 5 个 fields ，非指定的查询字段为默认值，只信任 fields 中指定的字段值
func (s *Service) GetUserData(ctx context.Context, id string, fields []UserDataField) (*UserData, error) {
	if len(fields) > MaxFieldsCount {
		return nil, fmt.Errorf("最多只能查 %d 个 fields", MaxFieldsCount)
	}
	para := map[string]interface{}{
		"id":     id,
		"fields": fields,
	}
	interfaceName := "account.cauth.getUserData"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	b, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("json Marshal result.Data error:%+v with %+v", err, result.Data)
	}
	userData := &UserData{}
	if err := json.Unmarshal(b, userData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the result data %s: %w", b, err)
	}
	return userData, nil
}
