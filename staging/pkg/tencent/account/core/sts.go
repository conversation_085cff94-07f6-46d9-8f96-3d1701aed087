// 安全凭证服务 sts 集成

package core

import (
	"fmt"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	sts "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sts/v20180813"
)

// StsClient sts 接口调用实例
type StsClient struct {
	Client *sts.Client
}

// StsRoleCredential 角色扮演接口返回结构
type StsRoleCredential struct {
	// Token token
	Token string
	// TmpSecretID 临时证书密钥 ID
	TmpSecretID string
	// TmpSecretKey 临时证书密钥 key
	TmpSecretKey string
	// ExpiredTime 证书过期时间戳（秒）
	ExpiredTime int64
}

// NewSts 创建 sts 调用实例
// host sts 服务地址 host ，区分内外网，外网传空字符串即可
// timeoutSec 请求超时时间秒数
// serviceSecretID 你服务的 secret id
// serviceSecretKey 你服务的 secret key
// region 地域
func NewSts(host string, timeoutSec int, serviceSecretID, serviceSecretKey, region string) (*StsClient, error) {
	credential := common.NewCredential(serviceSecretID, serviceSecretKey)
	cp := profile.NewClientProfile()
	cp.HttpProfile.Endpoint = host
	cp.HttpProfile.ReqTimeout = timeoutSec
	s, err := sts.NewClient(credential, region, cp)
	if err != nil {
		return nil, err
	}
	sc := &StsClient{
		Client: s,
	}
	return sc, nil
}

// ArnByRoleName 按主账号 uin 和 rolename 生成角色的资源描述
func (s *StsClient) ArnByRoleName(uin, rolename string) string {
	return fmt.Sprint("qcs::cam::uin/", uin, ":roleName/", rolename)
}

// ArnByRoleID 按主账号 uin 和 roleid 生成角色的资源描述
func (s *StsClient) ArnByRoleID(uin, roleid string) string {
	return fmt.Sprint("qcs::cam::uin/", uin, ":role/", roleid)
}

// AssumeRoleWithRoleName 使用 role name 申请扮演角色
func (s *StsClient) AssumeRoleWithRoleName(uin, rolename string) (*StsRoleCredential, error) {
	roleArn := s.ArnByRoleName(uin, rolename)
	return s.AssumeRole(roleArn)
}

// AssumeRoleWithRoleID 使用 role name 申请扮演角色
func (s *StsClient) AssumeRoleWithRoleID(uin, roleid string) (*StsRoleCredential, error) {
	roleArn := s.ArnByRoleID(uin, roleid)
	return s.AssumeRole(roleArn)
}

// AssumeRole 使用 arn 申请扮演角色
// 根据当前用户的 主账号 uin 调用 sts 扮演成用户，获取临时的用户 secret id 和 secret key
func (s *StsClient) AssumeRole(arn string) (*StsRoleCredential, error) {
	req := sts.NewAssumeRoleRequest()
	req.RoleArn = common.StringPtr(arn)
	req.RoleSessionName = common.StringPtr("account_go_sdk")
	// 指定临时证书的有效期，单位：秒，默认 7200 秒，最长可设定有效期为 43200 秒
	req.DurationSeconds = common.Uint64Ptr(43200)
	rsp, err := s.Client.AssumeRole(req)
	if err != nil {
		return nil, err
	}
	c := &StsRoleCredential{
		Token:        *rsp.Response.Credentials.Token,
		TmpSecretID:  *rsp.Response.Credentials.TmpSecretId,
		TmpSecretKey: *rsp.Response.Credentials.TmpSecretKey,
		ExpiredTime:  *rsp.Response.ExpiredTime,
	}
	return c, nil
}
