// 获取用户信息
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000678415
// 这里不使用 fields 参数，直接查询全部字段内容以结构体的形式返回

package core

import (
	"context"
	"encoding/json"
	"fmt"
)

// UserInfo 用户信息完整结构体
type UserInfo struct {
	AcceptOpen          string      `json:"accept_open"`
	AcceptOpenProtocol  int         `json:"accept_open_protocol"`
	AcceptYunProtocol   int         `json:"accept_yun_protocol"`
	Account             string      `json:"account"`
	Accredit            interface{} `json:"accredit"` // 没有实名认证的 uin 这个字段为空字符串，其他时候为 int
	Addr                string      `json:"addr"`
	AddrPrefix          string      `json:"addr_prefix"`
	Area                string      `json:"area"`
	AuthMethod          int         `json:"auth_method"`
	AuthenticateType    string      `json:"authenticateType"`
	BankMsg             string      `json:"bankMsg"`
	BankStatus          string      `json:"bankStatus"`
	BankAccount         string      `json:"bank_account"`
	BankNumber          string      `json:"bank_number"`
	BizInfo             string      `json:"bizInfo"`
	CollMail            string      `json:"coll_mail"`
	CollName            string      `json:"coll_name"`
	CollTel             string      `json:"coll_tel"`
	Contact             string      `json:"contact"`
	CountryCode         string      `json:"countryCode"`
	CurAccredit         int         `json:"cur_accredit"`
	CurArea             string      `json:"cur_area"`
	CurAuthenticateType interface{} `json:"cur_authenticateType"`
	CurDevcheckMsg      string      `json:"cur_devcheck_msg"`
	CurDevcheckState    string      `json:"cur_devcheck_state"`
	CurIDCard           string      `json:"cur_id_card"`
	CurIDCardType       string      `json:"cur_id_card_type"`
	CurInfo             struct {
		Area             string      `json:"area"`
		AuthenticateType interface{} `json:"authenticateType"`
		IDCard           string      `json:"id_card"`
		IDCardType       string      `json:"id_card_type"`
		Name             string      `json:"name"`
		OrganizationCode string      `json:"organization_code"`
		Type             string      `json:"type"`
	} `json:"cur_info"`
	CurMail              string `json:"cur_mail"`
	CurMailPass          int    `json:"cur_mail_pass"`
	CurName              string `json:"cur_name"`
	CurOrganizationCode  string `json:"cur_organization_code"`
	CurType              string `json:"cur_type"`
	DeployName           string `json:"deployName"`
	DevcheckMsg          string `json:"devcheck_msg"`
	DevcheckPass         int    `json:"devcheck_pass"`
	DevcheckTime         int    `json:"devcheck_time"`
	EntryCardCacheURLNew string `json:"entry_card_cache_url_new"`
	EntryCardURLNew      string `json:"entry_card_url_new"`
	FirstDevcheckTime    int    `json:"first_devcheck_time"`
	IDCard               string `json:"id_card"`
	IDCardCacheURL       string `json:"id_card_cache_url"`
	IDCardCacheURLNew    string `json:"id_card_cache_url_new"`
	IDCardCacheUUID      string `json:"id_card_cache_uuid"`
	IDCardCacheUUIDNew   string `json:"id_card_cache_uuid_new"`
	IDCardType           string `json:"id_card_type"`
	IDCardURL            string `json:"id_card_url"`
	IDCardURLNew         string `json:"id_card_url_new"`
	IDCardUUID           string `json:"id_card_uuid"`
	IsModify             int    `json:"isModify"`
	Mail                 string `json:"mail"`
	MailPass             int    `json:"mail_pass"`
	MailVerify           string `json:"mail_verify"`
	MailcheckTime        string `json:"mailcheck_time"`
	MsgLang              string `json:"msgLang"`
	Name                 string `json:"name"`
	NeedCheckBankInfo    struct {
		AccountBank  string `json:"account_bank"`
		AccountID    string `json:"account_id"`
		AccountName  string `json:"account_name"`
		BankID       string `json:"bank_id"`
		BankName     string `json:"bank_name"`
		CityID       string `json:"city_id"`
		CityName     string `json:"city_name"`
		ProvinceID   string `json:"province_id"`
		ProvinceName string `json:"province_name"`
	} `json:"needCheckBankInfo"`
	NeedInfo         int    `json:"need_info"`
	OpenUser         int    `json:"open_user"`
	OrganizationCode string `json:"organization_code"`
	OwnerUin         int    `json:"owner_uin"`
	PassedBankInfo   struct {
		AccountBank  string `json:"account_bank"`
		AccountID    string `json:"account_id"`
		AccountName  string `json:"account_name"`
		BankID       string `json:"bank_id"`
		BankName     string `json:"bank_name"`
		CityID       string `json:"city_id"`
		CityName     string `json:"city_name"`
		ProvinceID   string `json:"province_id"`
		ProvinceName string `json:"province_name"`
	} `json:"passedBankInfo"`
	Perfection     int    `json:"perfection"`
	RegisterStatus string `json:"register_status"`
	SrcPlatform    string `json:"srcPlatform"`
	SubExist       int    `json:"subExist"`
	SubSystemType  string `json:"subSystemType"`
	Tel            string `json:"tel"`
	Type           string `json:"type"`
	Uin            string `json:"uin"`
	UserType       string `json:"user_type"`
	WanIPTime      string `json:"wanIpTime"`
	WanRestrict    string `json:"wanRestrict"`
}

// GetUserInfo 获取用户信息
func (s *Service) GetUserInfo(ctx context.Context, uin string) (*UserInfo, error) {
	para := map[string]interface{}{
		"uin": uin,
	}

	interfaceName := "qcloud.Quser.getUserInfo"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	b, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("json Marshal result.Data error:%+v with %+v", err, result.Data)
	}
	userInfo := &UserInfo{}
	if err := json.Unmarshal(b, userInfo); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the result data %s: %w", b, err)
	}
	return userInfo, nil
}
