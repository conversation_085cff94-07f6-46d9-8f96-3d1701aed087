// 获取owner用户信息
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000678417

package core

import (
	"context"
	"encoding/json"
	"fmt"
)

// OwnerInfoItem OwnerInfo json对应结构
type OwnerInfoItem struct {
	CollabratorList []struct {
		AccountList     string `json:"accountList"`
		AccountType     int    `json:"accountType"`
		AddTimestamp    string `json:"addTimestamp"`
		Area            string `json:"area"`
		Attributes      int    `json:"attributes"`
		BizInfo         string `json:"bizInfo"`
		CheckStatus     int    `json:"checkStatus"`
		ClientFrom      string `json:"clientFrom"`
		CountryCode     string `json:"countryCode"`
		DefaultOwner    int    `json:"defaultOwner"`
		DeployName      string `json:"deployName"`
		GuideBit        int    `json:"guideBit"`
		IsAcceptProMsg  int    `json:"isAcceptProMsg"`
		IsAuthenticate  int    `json:"isAuthenticate"`
		IsOwner         int    `json:"isOwner"`
		IsRegSucc       bool   `json:"isRegSucc"`
		IsSeeGuidelines int    `json:"isSeeGuidelines"`
		IsTestUser      int    `json:"isTestUser"`
		Isprotect       int    `json:"isprotect"`
		Mail            string `json:"mail"`
		MailFlag        int    `json:"mailFlag"`
		MailStatus      int    `json:"mailStatus"`
		MailVerify      int    `json:"mailVerify"`
		ModTimestamp    string `json:"modTimestamp"`
		MsgLang         string `json:"msgLang"`
		Nickname        string `json:"nickname"`
		OfflineStatus   int    `json:"offlineStatus"`
		OwnerUin        int    `json:"ownerUin"`
		PermList        []struct {
			AddTimestamp string `json:"addTimestamp"`
			AppID        string `json:"appId"`
			OpName       string `json:"opName"`
			OwnerUin     int    `json:"ownerUin"`
			ProjectID    int    `json:"projectId"`
			Uin          int64  `json:"uin"`
		} `json:"permList"`
		PhoneNumber     string `json:"phoneNumber"`
		PhoneNumberFlag int    `json:"phoneNumberFlag"`
		Referer         string `json:"referer"`
		SrcPlatform     string `json:"srcPlatform"`
		UID             int    `json:"uid"`
		Uin             int64  `json:"uin"`
		UserName        string `json:"userName"`
		WanIPTime       string `json:"wanIpTime"`
		WanRestrict     int    `json:"wanRestrict"`
		WechatFlag      string `json:"wechatFlag"`
		WechatOpenID    string `json:"wechatOpenId"`
	} `json:"collabratorList"`
	OwnerInfo struct {
		Account         int64  `json:"account"`
		AccountList     string `json:"accountList"`
		AccountType     int    `json:"accountType"`
		AddTimestamp    string `json:"addTimestamp"`
		Area            string `json:"area"`
		Attributes      int    `json:"attributes"`
		BizInfo         string `json:"bizInfo"`
		CheckStatus     int    `json:"checkStatus"`
		ClientFrom      string `json:"clientFrom"`
		CountryCode     string `json:"countryCode"`
		DefaultOwner    int    `json:"defaultOwner"`
		DeployName      string `json:"deployName"`
		GuideBit        int    `json:"guideBit"`
		IsAcceptProMsg  int    `json:"isAcceptProMsg"`
		IsAuthenticate  int    `json:"isAuthenticate"`
		IsOwner         int    `json:"isOwner"`
		IsRegSucc       bool   `json:"isRegSucc"`
		IsSeeGuidelines int    `json:"isSeeGuidelines"`
		IsTestUser      int    `json:"isTestUser"`
		Isprotect       int    `json:"isprotect"`
		Mail            string `json:"mail"`
		MailStatus      int    `json:"mailStatus"`
		MailVerify      int    `json:"mailVerify"`
		ModTimestamp    string `json:"modTimestamp"`
		MsgLang         string `json:"msgLang"`
		Nickname        string `json:"nickname"`
		OfflineStatus   int    `json:"offlineStatus"`
		OwnerUin        int    `json:"ownerUin"`
		PhoneNumber     string `json:"phoneNumber"`
		ReceiverMail    string `json:"receiverMail"`
		ReceiverPhone   string `json:"receiverPhone"`
		ReceiverWechat  string `json:"receiverWechat"`
		Referer         string `json:"referer"`
		SrcPlatform     string `json:"srcPlatform"`
		Uin             int    `json:"uin"`
		UserName        string `json:"userName"`
		WanIPTime       string `json:"wanIpTime"`
		WanRestrict     int    `json:"wanRestrict"`
	} `json:"ownerInfo"`
}

// GetOwnerInfo 获取owner用户信息
func (s *Service) GetOwnerInfo(ctx context.Context, ownerUin ...int) ([]OwnerInfoItem, error) {
	para := map[string]interface{}{
		"ownerUin": ownerUin,
	}
	interfaceName := "qcloud.Qcauth.getOwnerInfo"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	b, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("json Marshal result.Data error:%+v with %+v", err, result.Data)
	}
	r := []OwnerInfoItem{}
	if err := json.Unmarshal(b, &r); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the response data %s: %w", b, err)
	}
	return r, nil
}
