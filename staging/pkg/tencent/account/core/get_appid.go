// 获取appid
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000678411

package core

import (
	"context"
)

// GetAppID 根据字符串的 UIN 获取 数字 AppID
func (s *Service) GetAppID(ctx context.Context, uin string) ([]int, error) {
	para := map[string]interface{}{
		"uin": uin,
	}
	interfaceName := "qcloud.Qcauth.getAppId"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	data, _ := result.Data.([]interface{})
	appIDs := []int{}
	for _, iAppID := range data {
		appID, _ := iAppID.(float64)
		appIDs = append(appIDs, int(appID))
	}
	return appIDs, nil
}
