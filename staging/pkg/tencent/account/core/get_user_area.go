// 获取用户地域属性
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000678891

package core

import (
	"context"
	"encoding/json"
	"fmt"
)

// Area 地域属性信息
type Area struct {
	Area        int    `json:"area"`        // 1 中国  2 国际
	CountryName string `json:"countryName"` // 国家名称 ISO2 标准
	CountryCode string `json:"countryCode"` // 国家编号
}

// GetUserArea 获取用户地域属性
// 只能是 owner 主账号
func (s *Service) GetUserArea(ctx context.Context, ownerUin int) (*Area, error) {
	para := map[string]interface{}{
		"ownerUin": ownerUin,
	}

	interfaceName := "account.account.getUserArea"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	b, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("json Marshal result.Data error:%+v with %+v", err, result.Data)
	}
	r := &Area{}
	if err := json.Unmarshal(b, r); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the result data %s: %w", b, err)
	}
	return r, nil
}

// MaxOwnerUinsCount 获取用户地域属性一次最大允许查询的记录条数
const MaxOwnerUinsCount = 100

// GetUserAreaBatch 批量查询
// 限制一次最多只能查询100个
func (s *Service) GetUserAreaBatch(ctx context.Context, ownerUins ...int) (map[string]int, error) {
	if len(ownerUins) > MaxOwnerUinsCount {
		return nil, fmt.Errorf("ownerUins max count must < 100")
	}
	para := map[string]interface{}{
		"ownerUins": ownerUins,
	}
	interfaceName := "account.account.getUserAreaBatch"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	data, ok := result.Data.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("assert result data:%+v to map[string]interface{} failed", result.Data)
	}
	r := make(map[string]int)
	for k, iv := range data {
		v, ok := iv.(float64)
		if !ok {
			return nil, fmt.Errorf("assert data item:%+v to float64 failed", iv)
		}
		r[k] = int(v)
	}
	return r, nil
}
