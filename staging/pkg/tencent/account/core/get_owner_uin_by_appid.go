// 根据appid获取用户uin
// http://tapd.oa.com/QCloud_2015/markdown_wikis/show/#1210103951000687541

package core

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
)

// AppIDUinInfo 对应关系
type AppIDUinInfo struct {
	AppID        string `json:"appid"`
	Uin          string `json:"uin"`
	AddTimestamp string `json:"addTimestamp"`
}

// GetPermProject 获取用户项目权限信息
func (s *Service) GetOwnerUinByAppID(ctx context.Context, appID int64) (*AppIDUinInfo, error) {
	para := map[string]interface{}{
		"appid": appID,
	}
	interfaceName := "account.Qcauth.getOwnerUinByAppid"
	param := newParam(interfaceName, para)
	result, err := post(ctx, s.HTTPClient, s.URL, param)
	if err != nil {
		return nil, err
	}
	b, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("json Marshal result.Data error:%+v with %+v", err, result.Data)
	}
	r := []AppIDUinInfo{}
	if err := json.Unmarshal(b, &r); err != nil {
		return nil, fmt.Errorf("failed to unmarshal the result data %s: %w", b, err)
	}
	if len(r) == 0 {
		return nil, errors.New("appid not exist")
	}
	return &r[0], nil
}
