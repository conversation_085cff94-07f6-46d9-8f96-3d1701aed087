package rand

import (
	fake "github.com/brianvoe/gofakeit/v7"
)

func Ternary[T any](probability float64, a, b T) T {
	if fake.Float64Range(0, 1) < probability {
		return a
	}
	return b
}

func TernaryF[T any](probability float64, fna, fnb func() T) T {
	if fake.Float64Range(0, 1) < probability {
		return fna()
	}
	return fnb()
}

func Invoke(probability float64, fn func()) {
	InvokeElse(probability, fn, nil)
}

func InvokeElse(probability float64, fn, elseFn func()) {
	if fake.Float64Range(0, 1) < probability {
		fn()
	} else if elseFn != nil {
		elseFn()
	}
}

func PickN[T any](items []T, n int) []T {
	fake.ShuffleAnySlice(items)
	n = min(n, len(items))
	return items[:n]
}

func Subset[T any](items []T) []T {
	return PickN(items, fake.IntN(len(items)))
}

func Pick1[T any](items ...T) T {
	return items[fake.IntN(len(items))]
}

func Pick1Else[T any](probability float64, items, elseItems []T) T {
	if fake.Float64Range(0, 1) < probability {
		return Pick1(items...)
	}
	return Pick1(elseItems...)
}
