package net

const (
	ProtocolTCP    = "TCP"
	ProtocolUDP    = "UDP"
	ProtocolTCPSSL = "TCP_SSL"
	ProtocolQUIC   = "QUIC"
	ProtocolHTTP   = "HTTP"
	ProtocolHTTPS  = "HTTPS"
)

var (
	l4Protocols = []string{
		ProtocolTCP,
		ProtocolUDP,
		ProtocolTCPSSL,
		ProtocolQUIC,
	}
	l7Protocols = []string{
		ProtocolHTTP,
		ProtocolHTTPS,
	}
	udpFamilyProtocols = []string{
		ProtocolUDP,
		ProtocolQUIC,
	}
	tcpFamilyProtocols = []string{
		ProtocolTCP,
		ProtocolTCPSSL,
		ProtocolHTTP,
		ProtocolHTTPS,
	}
)
