package net

import "testing"

func TestIsInnerIP(t *testing.T) {
	type args struct {
		ip string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			"*******/8 false",
			args{"*******"},
			false,
		},
		{
			"*******/8 true",
			args{"*******"},
			true,
		},
		{
			"10.0.0.0/8 true",
			args{"********"},
			true,
		},
		{
			"********/8 true",
			args{"********"},
			true,
		},
		{
			"********/8",
			args{"********"},
			false,
		},
		{
			"**********/12",
			args{"**********"},
			true,
		},
		{
			"**********/12",
			args{"**********"},
			true,
		},
		{
			"**********/12",
			args{"**********"},
			false,
		},
		{
			"***********/16",
			args{"***********"},
			true,
		},
		{
			"***********/16",
			args{"***********"},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsPrivateIP(tt.args.ip); got != tt.want {
				t.Errorf("IsInnerIP() = %v, want %v", got, tt.want)
			}
		})
	}
}
