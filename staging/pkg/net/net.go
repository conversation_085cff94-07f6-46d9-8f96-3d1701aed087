package net

import (
	"net"

	"github.com/samber/lo"
)

var (
	privateIPRanges = []string{
		"*******/8",
		"10.0.0.0/8",
		"********/8",
		"**********/12",
		"***********/16",
	}
)

func IsPrivateIP(ip string) bool {
	return lo.ContainsBy(privateIPRanges, func(cidr string) bool {
		return IPInCIDR(ip, cidr)
	})
}

func IPInCIDR(ipStr, cidrStr string) bool {
	ip := net.ParseIP(ipStr)

	_, cidr, err := net.ParseCIDR(cidrStr)
	if err != nil {
		return false
	}

	return cidr.Contains(ip)
}

func IsL4Protocol(protocol string) bool {
	return lo.Contains(l4Protocols, protocol)
}

func IsL7Protocol(protocol string) bool {
	return lo.Contains(l7Protocols, protocol)
}

func IsUDPFamilyProtocol(protocol string) bool {
	return lo.Contains(UDPFamilyProtocols(), protocol)
}

func UDPFamilyProtocols() []string {
	return udpFamilyProtocols
}

func IsTCPFamilyProtocol(protocol string) bool {
	return lo.Contains(TCPFamilyProtocols(), protocol)
}

func TCPFamilyProtocols() []string {
	return tcpFamilyProtocols
}
