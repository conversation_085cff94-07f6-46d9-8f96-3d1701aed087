/*
Copyright 2014 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package intstr

import (
	"encoding/json"
	"fmt"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	yaml2 "gopkg.in/yaml.v2"
	k8syaml "sigs.k8s.io/yaml"
)

func TestFromUint64(t *testing.T) {
	i := FromUint64(93)
	if i.dataType != Uint64 || i.uint64Val != 93 {
		t.Errorf("Expected IntVal=93, got %+v", i)
	}
}

func TestFromString(t *testing.T) {
	i := FromString("76")
	if i.dataType != String || i.strVal != "76" {
		t.<PERSON><PERSON><PERSON>("Expected StrVal=\"76\", got %+v", i)
	}
}

type IntOrStringHolder struct {
	Val Uint64OrString `yaml:"val" json:"val"`
}

func TestIntOrStringUnmarshalJSON(t *testing.T) {
	cases := []struct {
		input  string
		result Uint64OrString
	}{
		{"{\"val\": 123}", FromUint64(123)},
		{"{\"val\": \"123\"}", FromString("123")},
	}

	for _, c := range cases {
		var result IntOrStringHolder
		if err := json.Unmarshal([]byte(c.input), &result); err != nil {
			t.Errorf("Failed to unmarshal input '%v': %v", c.input, err)
		}
		if result.Val != c.result {
			t.Errorf("Failed to unmarshal input '%v': expected %+v, got %+v", c.input, c.result, result)
		}
	}
}

func TestIntOrStringMarshalJSON(t *testing.T) {
	cases := []struct {
		input  Uint64OrString
		result string
	}{
		{FromUint64(123), "{\"val\":123}"},
		{FromString("123"), "{\"val\":\"123\"}"},
	}

	for _, c := range cases {
		input := IntOrStringHolder{c.input}
		result, err := json.Marshal(&input)
		if err != nil {
			t.Errorf("Failed to marshal input '%v': %v", input, err)
		}
		if string(result) != c.result {
			t.Errorf("Failed to marshal input '%v': expected: %+v, got %q", input, c.result, string(result))
		}
	}
}

func TestIntOrStringMarshalJSONUnmarshalYAML(t *testing.T) {
	cases := []struct {
		input Uint64OrString
	}{
		{FromUint64(123)},
		{FromString("123")},
	}

	for _, c := range cases {
		input := IntOrStringHolder{c.input}
		jsonMarshalled, err := json.Marshal(&input)
		if err != nil {
			t.Errorf("1: Failed to marshal input: '%v': %v", input, err)
		}

		var result IntOrStringHolder
		err = k8syaml.Unmarshal(jsonMarshalled, &result)
		if err != nil {
			t.Errorf("2: Failed to unmarshal '%+v': %v", string(jsonMarshalled), err)
		}

		if !reflect.DeepEqual(input, result) {
			t.Errorf("3: Failed to marshal input '%+v': got %+v", input, result)
		}
	}
}

func TestUint64OrStringMarshalJSONUnmarshalYAML2(t *testing.T) {
	cases := []struct {
		input Uint64OrString
	}{
		{FromUint64(123)},
		{FromString("123")},
	}

	for _, c := range cases {
		input := IntOrStringHolder{c.input}
		yamlMarshalled, err := k8syaml.Marshal(&input)
		if err != nil {
			t.Errorf("1: Failed to marshal input: '%v': %v", input, err)
		}

		var result IntOrStringHolder
		err = yaml2.Unmarshal(yamlMarshalled, &result)
		if err != nil {
			t.Errorf("2: Failed to unmarshal '%+v': %v", string(yamlMarshalled), err)
		}

		if !reflect.DeepEqual(input, result) {
			t.Errorf("3: Failed to marshal input '%+v': got %+v", input, result)
		}
	}
}

func TestUint64OrStringMarshalJSONUnmarshalYAML2_1(t *testing.T) {
	data := []byte(`{}`)
	var v Uint64OrString
	err := yaml2.Unmarshal(data, &v)
	fmt.Println(err)
	assert.NotNil(t, err)
}
