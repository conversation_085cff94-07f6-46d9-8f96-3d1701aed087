package intstr

import (
	"encoding/json"
	"fmt"
	"strconv"

	"gopkg.in/yaml.v2"
)

// Uint64OrString is a type that can hold an uint64 or a string.  When used in
// JSON or YAML marshalling and unmarshalling, it produces or consumes the
// inner type.  This allows you to have, for example, a JSON field that can
// accept a name or number.
type Uint64OrString struct {
	dataType  dataType
	uint64Val uint64
	strVal    string
}

// dataType represents the stored type of Uint64OrString.
type dataType int64

const (
	Uint64 dataType = iota // The Uint64OrString holds an int.
	String                 // The Uint64OrString holds a string.
)

// FromUint64 creates an Uint64OrString object with an uint64 value. It is
// your responsibility not to call this method with a value greater
// than uint64.
func FromUint64(val uint64) Uint64OrString {
	return Uint64OrString{dataType: Uint64, uint64Val: val}
}

// FromString creates an Uint64OrString object with a string value.
func FromString(val string) Uint64OrString {
	return Uint64OrString{dataType: String, strVal: val}
}

// Parse the given string and try to convert it to an integer before
// setting it as a string value.
func Parse(val string) Uint64OrString {
	v, err := strconv.ParseUint(val, 10, 64)
	if err != nil {
		return FromString(val)
	}
	return FromUint64(v)
}

// String returns the string value, or the Itoa of the int value.
func (uint64str Uint64OrString) String() string {
	if uint64str.dataType == String {
		return uint64str.strVal
	}
	return strconv.FormatUint(uint64str.Uint64(), 10)
}

// Uint64 returns the uint64Val if type Uint64, or if
// it is a String, will attempt a conversion to int,
// returning 0 if a parsing error occurs.
func (uint64str Uint64OrString) Uint64() uint64 {
	if uint64str.dataType == String {
		v, _ := strconv.ParseUint(uint64str.strVal, 10, 64)
		return v
	}
	return uint64str.uint64Val
}

// MarshalJSON implements the json.Marshaller interface.
func (uint64str Uint64OrString) MarshalJSON() ([]byte, error) {
	switch uint64str.dataType {
	case Uint64:
		return json.Marshal(uint64str.uint64Val)
	case String:
		return json.Marshal(uint64str.strVal)
	default:
		return []byte{}, fmt.Errorf("impossible IntOrString.Type")
	}
}

// UnmarshalJSON implements the json.Unmarshaller interface.
func (uint64str *Uint64OrString) UnmarshalJSON(value []byte) error {
	if value[0] == '"' {
		uint64str.dataType = String
		return json.Unmarshal(value, &uint64str.strVal)
	}
	uint64str.dataType = Uint64
	return json.Unmarshal(value, &uint64str.uint64Val)
}

// MarshalYAML implements the yaml.Marshaer interface.
func (uint64str Uint64OrString) MarshalYAML() ([]byte, error) {
	switch uint64str.dataType {
	case Uint64:
		return yaml.Marshal(uint64str.uint64Val)
	case String:
		return yaml.Marshal(uint64str.strVal)
	default:
		return []byte{}, fmt.Errorf("impossible IntOrString.Type")
	}
}

// UnmarshalYAML implements the yaml.Unmarshal interface.
func (uint64str *Uint64OrString) UnmarshalYAML(unmarshal func(interface{}) error) error {
	var (
		uint64Val uint64
		strVal    string
	)
	err := unmarshal(&uint64Val)
	if err == nil {
		uint64str.dataType = Uint64
		uint64str.uint64Val = uint64Val
		return nil
	}

	err = unmarshal(&strVal)
	if err == nil {
		uint64str.dataType = String
		uint64str.strVal = strVal
		return nil
	}

	return err
}
