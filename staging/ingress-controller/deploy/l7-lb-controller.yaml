apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: qcloud
spec:
  controller: cloud.tencent.com/ingress-controller
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: lb-ingress
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: lb-ingress-clusterrole
rules:
- apiGroups:
  - ""
  resources:
  - services
  - endpoints
  - nodes
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  - replicasets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  - secrets
  verbs:
  - '*'
- apiGroups:
  - extensions
  - networking.k8s.io
  resources:
  - ingresses
  - ingresses/status
  verbs:
  - '*'
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - get
  - create
  - patch
  - list
  - update
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - '*'
- apiGroups:
  - cloud.tencent.com
  resources:
  - tkeserviceconfigs
  verbs:
  - '*'
- apiGroups:
  - discovery.k8s.io
  resources:
  - endpointslices
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - networking.tke.cloud.tencent.com
  resources:
  - loadbalancerresources
  - loadbalancerresources/status
  verbs:
  - '*'
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: lb-ingress-clusterrole-nisa-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: lb-ingress-clusterrole
subjects:
- kind: ServiceAccount
  name: lb-ingress
  namespace: kube-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    k8s-app: l7-lb-controller
    qcloud-app: l7-lb-controller
    version: 0.0.1
  name: l7-lb-controller
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: l7-lb-controller
      qcloud-app: l7-lb-controller
      version: 0.0.1
  template:
    metadata:
      annotations:
        eks.tke.cloud.tencent.com/norm: "true"
      labels:
        k8s-app: l7-lb-controller
        qcloud-app: l7-lb-controller
        version: 0.0.1
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: node.kubernetes.io/instance-type
                operator: NotIn
                values:
                - external
      containers:
      - args:
        - --cluster-name=cls-7m2mnrz0
        env:
        - name: TKE_REGION
          valueFrom:
            configMapKeyRef:
              key: TKE_REGION
              name: tke-config
        - name: TKE_VPC_ID
          valueFrom:
            configMapKeyRef:
              key: TKE_VPC_ID
              name: tke-config
        - name: SILENT_START
          valueFrom:
            configMapKeyRef:
              key: SILENT_START
              name: tke-ingress-controller-config
        image: ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.4.1
        imagePullPolicy: Always
        name: l7-lb-controller
        volumeMounts:
        - mountPath: /etc/localtime
          name: tz-config
      dnsPolicy: ClusterFirst
      hostAliases:
      - hostnames:
        - cbs.api.qcloud.com
        - cvm.api.qcloud.com
        - lb.api.qcloud.com
        - tag.api.qcloud.com
        - snapshot.api.qcloud.com
        - monitor.api.qcloud.com
        - scaling.api.qcloud.com
        - ccs.api.qcloud.com
        ip: ************
      - hostnames:
        - tke.internal.tencentcloudapi.com
        - clb.internal.tencentcloudapi.com
        - cvm.internal.tencentcloudapi.com
        - tag.internal.tencentcloudapi.com
        - vpc.internal.tencentcloudapi.com
        - ssl.internal.tencentcloudapi.com
        - as.tencentcloudapi.com
        - cbs.tencentcloudapi.com
        - cvm.tencentcloudapi.com
        - vpc.tencentcloudapi.com
        ip: ************
      restartPolicy: Always
      serviceAccount: lb-ingress
      serviceAccountName: lb-ingress
      tolerations:
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
      volumes:
      - hostPath:
          path: /etc/localtime
          type: ""
        name: tz-config
