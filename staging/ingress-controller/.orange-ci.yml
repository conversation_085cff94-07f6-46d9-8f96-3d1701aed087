# Configuration Docs:
#   http://doc.orange-ci.oa.com/configuration.html
#   http://doc.orange-ci.oa.com/internal-steps/

# Main Process
#   Set Environment (Test)
#   Build (Docker in Docker, Build with host network)
#   Push

"go-mod":
  push:
    - network: devnet
      docker:
        image: dockerimage.isd.com/orange-ci/default-env
      services:
        - docker
      env:
        IMAGE: ccr.ccs.tencentyun.com/library/qcloud_ingress_controller
      stages:
        - name: Set Enviroment Version
          script: echo -n "${ORANGE_BRANCH}_${ORANGE_COMMIT_SHORT}"
          envExport:
            stdout: VERSION
        - name: Set Enviroment BuildTime
          script: echo -n $(date +%FT%T%z)
          envExport:
            stdout: BUILD_TIME
        - name: Build
          script: docker build --network=host -f Dockerfile
            --build-arg LDFLAGS="-X main.GitCommit=$ORANGE_COMMIT -X main.BuildTime=$BUILD_TIME"
            --build-arg HTTP_PROXY=$HTTP_PROXY --build-arg HTTPS_PROXY=$HTTPS_PROXY
            -t "$IMAGE:$VERSION" .
        - name: Push
          script: docker push $IMAGE:$VERSION
