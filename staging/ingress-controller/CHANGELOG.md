# [v2.5.2](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.5.1...v2.5.2) (2025-02-28)

* 发布范围: 增量发布
* 发布负责人: qingyangwu


### Bug Fixes

* 修复版本信息配置获取 ([6f5d004](https://git.woa.com/kateway/tke-ingress-controller/commits/6f5d004726eda8f3a338e2d3fa2b983218bdb8fe))
* 修复 service-controller 提前创建 configmap 导致独立 ingress-controller 无法启动的问题([abfa294e](https://git.woa.com/kateway/tke-ingress-controller/commits/abfa294eca8b18029bd50e6e7b3c838087726235))


# [v2.5.1](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.5.0...v2.5.1) (2025-02-12)

* 发布范围: 增量发布
* 发布负责人: qingyangwu

### Bug Fixes

* 服务端证书切片未排序，导致 Ingress 对账不一致 ([fc70f04](https://git.woa.com/kateway/tke-ingress-controller/commits/fc70f04c1db3fcea60eec64d61af03375d12a766))
* 过滤service时间相关annotation变更导致无效入队 ([0861e6d](https://git.woa.com/kateway/tke-ingress-controller/commits/0861e6d0c70d4a37f33d792ce41b3682f3fdc82a))
* 新增 IPDC 类型 Target 避免 panic ([4381cea](https://git.woa.com/kateway/tke-ingress-controller/commits/4381ceac529d88108d68368abd42e3818967e808))
* 修复截断转发规则时的超限错误 ([da79b59](https://git.woa.com/kateway/tke-ingress-controller/commits/da79b5933e4c97b4ca294d5343e6d81d7e66a2ba))
* service新增泛域名转发规则健康检查失效 ([817a7d4](https://git.woa.com/kateway/tke-ingress-controller/commits/817a7d4f040b2dea73343d5d73c9ecb08027fc47))
* TCP类型健康检查HTTP字段引发校验错误 ([b83cfc9](https://git.woa.com/kateway/tke-ingress-controller/commits/b83cfc9e2385624948578bdd0954def1af60f725))


### Features

* 使用featuregate对相应功能状态进行判断 ([c0a91ad](https://git.woa.com/kateway/tke-ingress-controller/commits/c0a91ad5217d404452cf60a06e239f64e196be52))
* 支持对指定service进行dryrun ([3396fbf](https://git.woa.com/kateway/tke-ingress-controller/commits/3396fbf9e1789157c471b857f615247c3718341b))
* 支持工作队列可观测 ([f978256](https://git.woa.com/kateway/tke-ingress-controller/commits/f9782562e3afe95bf20c72656ae4e78f21ac5d69))
* 指标融合 ([4073395](https://git.woa.com/kateway/tke-ingress-controller/commits/40733954718ba9b94335e5d5f3b9ac8358726ada))
* pod rs 优雅注册，等待pod里的容器都ready后再注册rs，避免出现clb rs 不健康告警。 ([fa34107](https://git.woa.com/kateway/tke-ingress-controller/commits/fa341077d6288d8035856a173839dc6f31c684cd))
* 支持通过yaml 注解解绑 clb的安全组 ([3f596e3](https://git.woa.com/kateway/tke-ingress-controller/commits/3f596e3a3a25f8c5173bb682b926336cfb00012c))


# [v2.5.0](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.4.2...v2.5.0) (2024-11-26)

* 发布范围: 增量发布
* 发布负责人: qingyangwu
* 镜像哈希: sha256:729faf7501909f18603b073098309800b579462b1433ac77aab6627ad12dfdc6

### Bug Fixes

* 优化 RS patch 标签逻辑 ([93e5d39](https://git.woa.com/kateway/tke-ingress-controller/commits/93e5d3988f6f7835c375797f77eda444a35a95b4))
* 修复直连类型ingress在无node情况下提示rs为空告警的问题 ([3e969f3](https://git.woa.com/kateway/tke-ingress-controller/commits/3e969f3d00ec54407b300c62b3102d9fdc12a5a7))
* 修复使用已有 MCI 删除时RS删除保护不生效的问题 ([3775fb8](https://git.woa.com/kateway/tke-ingress-controller/commits/3775fb85003013bb458afe66a4d2e1b45766825f))
* 修复一些tls证书相关问题 ([b28f9dc](https://git.woa.com/kateway/tke-ingress-controller/commits/b28f9dccc06614d59f4f6e406292836311b93c18))


### Features

* 增加 pvgw-pro 支持 ([c7dacbd](https://git.woa.com/kateway/tke-ingress-controller/commits/c7dacbd176213df8b563d33ee4652ecddba0dee7))
* TKE ingress 支持 '对同一个域名配置两个证书 rsa和ecc证书都配置' 功能 ([d369968](https://git.woa.com/kateway/tke-ingress-controller/commits/d36996827b456b6c24b28a4eb9a51435a0c6d5ed))
* 增加非直连场景 'node单点rs风险' 事件 ([ce5077d](https://git.woa.com/kateway/tke-ingress-controller/commits/ce5077de6d2ba1f2699bf08d506b8762529a81ff))
* 开启CLB实例的删除保护 ([9de9a20](https://git.woa.com/kateway/tke-ingress-controller/commits/9de9a20263815b2be5666e9e8f353938ee462734))
* 支持pod优雅删除 ([c0dd6ad](https://git.woa.com/kateway/tke-ingress-controller/commits/c0dd6add4f38670a6083937c110846f176c32b6c))
* 构建IngressController用于维护ingress controller状态 ([d309908](https://git.woa.com/kateway/tke-ingress-controller/commits/d309908a134ec083b79c3e05149fa20cb0f6b20c))



# [v2.4.2](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.4.1...v2.4.2) (2024-11-19)


### Bug Fixes

* 修复节点优雅删除node删除缓慢的问题 ([ce43200](https://git.woa.com/kateway/tke-ingress-controller/commits/ce432004f1fcca04b8fc3751beff943c388b6119))
* 增加node优雅删除，修复荣耀缩容bug ([a2eb6b5](https://git.woa.com/kateway/tke-ingress-controller/commits/a2eb6b5aaf253757a688d045d3e7a08399e78ef2))
* 解决panic捕获以及输出的相关问题 ([d517f21](https://git.woa.com/kateway/tke-ingress-controller/commits/d517f2199b39f4b14b2247a186252c571e400fd0))


### Features

* support patch rs tags ([edaeeef](https://git.woa.com/kateway/tke-ingress-controller/commits/edaeeef811c8747af6066fc5f912740b0b27f562))
* 优先使用clb实例配额 ([d17a3c9](https://git.woa.com/kateway/tke-ingress-controller/commits/d17a3c967a7c2de17635e2dab657aad9ee8dd0dc))



# [v2.4.1](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.4.0...v2.4.1) (2024-08-20)


### Features

* 支持自定义监听端口 ([b1c9ef6](https://git.woa.com/kateway/tke-ingress-controller/commits/b1c9ef6a57b55f7e7b0f9f5cb110bb3dd569a4aa))
* 优化全死全活逻辑 ([3b2cc5c](https://git.woa.com/kateway/tke-ingress-controller/commit/3b2cc5cc4282a31e8466a24e43afbe899c417468))



# [v2.4.0](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.2.6...v2.4.0) (2024-07-18)

### Bug Fixes

* addRule 超限不要阻塞后续 deleteRule 流程 ([5198533](https://git.woa.com/kateway/tke-ingress-controller/commits/5198533f2063f809efdf9b100bfc60b4078b04b0))
* enqueue pod deletion for local service ([af6d94b](https://git.woa.com/kateway/tke-ingress-controller/commits/af6d94bd820a21e838f37476df011c4daf360661))
* 修复隔离、原地升级升级场景下全死全活异常的问题 ([412ec73](https://git.woa.com/kateway/tke-ingress-controller/commits/412ec734a6c38a613f8668ecaac95ce5234718ed))
* 修复ModifyTags接口调用 ([cfbd29e](https://git.woa.com/kateway/tke-ingress-controller/commits/cfbd29e02544a4909703c1b934758ed4da3bec36))
* 修复健康检查域名配置错误 ([6ca6fd1](https://git.woa.com/kateway/tke-ingress-controller/commits/6ca6fd1d98156a95012801c09544a33491494540))
* 修复后端rs排序导致的rs解关联问题 ([3a68ca8](https://git.woa.com/kateway/tke-ingress-controller/commits/3a68ca89a5f75b9ab8821d3e9425b74f25872e5b))
* 修复相同 Pod 的权重无法在不同 Path 中设置的问题 ([b8e8435](https://git.woa.com/kateway/tke-ingress-controller/commits/b8e8435cc3ca375586c4d05800a58885fe0ec2fd))
* 修复默认域名配置错误 ([108cb25](https://git.woa.com/kateway/tke-ingress-controller/commits/108cb25a9ca91ee01ed9982e466bf6837a3a1056))
* 创建LBR失败时不删除LB --bug=https://tapd.woa.com/paasdesign/bugtrace/bugs/view?bug_id=1020360782124123279 ([39207b9](https://git.woa.com/kateway/tke-ingress-controller/commits/39207b9408078d8f46a546a4f7d442172830cb97))
* 在getCurrentTKEIngressTags加入lifecycleowner标签 ([b691e1a](https://git.woa.com/kateway/tke-ingress-controller/commits/b691e1a08a9db6e073b004a09bcca794c26a2758))

### Features

* add multicluster ingress support ([e505f8f](https://git.woa.com/kateway/tke-ingress-controller/commits/e505f8f493afa10d7696ab73f7010ac625b44661))
* add parameter Zones to ExpandCreateLoadBalancerRequest ([33dbe3b](https://git.woa.com/kateway/tke-ingress-controller/commits/33dbe3bd28d7ae503baadf6a180016d9bf2d9c97))
* 允许Ingress的Backend中使用Port名称指定后端 ([4570d5b](https://git.woa.com/kateway/tke-ingress-controller/commits/4570d5bc1dfaaf756997bd0e0761b301478a0522))
* 增加防误删逻辑 ([7cc292f](https://git.woa.com/kateway/tke-ingress-controller/commits/7cc292fbacecfc65314fd0dd020d2d08c1e6bfe9))
* 将MockError改为即时输出 ([bdc1820](https://git.woa.com/kateway/tke-ingress-controller/commits/bdc1820ec9f0dd77f41f909a53511d835a347a16))
* 支持在TkeServiceConfig中对七层健康检查协议类型进行配置(TCP or HTTP) ([e3b34b8](https://git.woa.com/kateway/tke-ingress-controller/commits/e3b34b847fdec8233a4a82c5739273c9ef0b4c82))
* 支持对七层https监听器HTTP2属性进行配置 ([daca1e3](https://git.woa.com/kateway/tke-ingress-controller/commits/daca1e356bab68f0e7773dc3ab0fddb2a3e5b301))
* 调整新建七层规则健康检查的来源IP默认值 ([0f7ba9f](https://git.woa.com/kateway/tke-ingress-controller/commits/0f7ba9f93f35cda5e7795ec9fb78237806234d2a))

# [v2.3.0](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.2.8...v2.3.0) (2024-07-18)


### Bug Fixes

* addRule 超限不要阻塞后续 deleteRule 流程 ([5198533](https://git.woa.com/kateway/tke-ingress-controller/commits/5198533f2063f809efdf9b100bfc60b4078b04b0))
* fake all down ([412ec73](https://git.woa.com/kateway/tke-ingress-controller/commits/412ec734a6c38a613f8668ecaac95ce5234718ed))
* enqueue pod deletion for local service ([af6d94b](https://git.woa.com/kateway/tke-ingress-controller/commits/af6d94bd820a21e838f37476df011c4daf360661))
* 修复ModifyTags接口调用 ([cfbd29e](https://git.woa.com/kateway/tke-ingress-controller/commits/cfbd29e02544a4909703c1b934758ed4da3bec36))
* 修复健康检查域名配置错误 ([6ca6fd1](https://git.woa.com/kateway/tke-ingress-controller/commits/6ca6fd1d98156a95012801c09544a33491494540))
* 修复后端rs排序导致的rs解关联问题 ([3a68ca8](https://git.woa.com/kateway/tke-ingress-controller/commits/3a68ca89a5f75b9ab8821d3e9425b74f25872e5b))
* 修复相同 Pod 的权重无法在不同 Path 中设置的问题 ([b8e8435](https://git.woa.com/kateway/tke-ingress-controller/commits/b8e8435cc3ca375586c4d05800a58885fe0ec2fd))
* 修复默认域名配置错误 ([108cb25](https://git.woa.com/kateway/tke-ingress-controller/commits/108cb25a9ca91ee01ed9982e466bf6837a3a1056))
* 创建LBR失败时不删除LB --bug=https://tapd.woa.com/paasdesign/bugtrace/bugs/view?bug_id=1020360782124123279 ([39207b9](https://git.woa.com/kateway/tke-ingress-controller/commits/39207b9408078d8f46a546a4f7d442172830cb97))
* 在getCurrentTKEIngressTags加入lifecycleowner标签 ([b691e1a](https://git.woa.com/kateway/tke-ingress-controller/commits/b691e1a08a9db6e073b004a09bcca794c26a2758))
* 在getCurrentTKEIngressTags加入lifecycleowner标签 ([b691e1a](https://git.woa.com/kateway/tke-ingress-controller/commits/b691e1a08a9db6e073b004a09bcca794c26a2758))

### Features

* add multicluster ingress support ([e505f8f](https://git.woa.com/kateway/tke-ingress-controller/commits/e505f8f493afa10d7696ab73f7010ac625b44661))
* add parameter Zones to ExpandCreateLoadBalancerRequest ([33dbe3b](https://git.woa.com/kateway/tke-ingress-controller/commits/33dbe3bd28d7ae503baadf6a180016d9bf2d9c97))
* 允许Ingress的Backend中使用Port名称指定后端 ([4570d5b](https://git.woa.com/kateway/tke-ingress-controller/commits/4570d5bc1dfaaf756997bd0e0761b301478a0522))
* 增加防误删逻辑 ([7cc292f](https://git.woa.com/kateway/tke-ingress-controller/commits/7cc292fbacecfc65314fd0dd020d2d08c1e6bfe9))
* 支持在TkeServiceConfig中对七层健康检查协议类型进行配置(TCP or HTTP) ([e3b34b8](https://git.woa.com/kateway/tke-ingress-controller/commits/e3b34b847fdec8233a4a82c5739273c9ef0b4c82))
* 支持对七层https监听器HTTP2属性进行配置 ([daca1e3](https://git.woa.com/kateway/tke-ingress-controller/commits/daca1e356bab68f0e7773dc3ab0fddb2a3e5b301))
* 调整新建七层规则健康检查的来源IP默认值 ([e0e5361](https://git.woa.com/kateway/tke-ingress-controller/commits/e0e5361075801d712cda86de234d130c4fb3b556))
* 增加多集群逻辑 ([a118840](https://git.woa.com/kateway/tke-ingress-controller/commits/a1188408cf90e1fef840c675fbd267b705883967))
* 增加防误删逻辑 ([7cc292f](https://git.woa.com/kateway/tke-ingress-controller/commits/7cc292fbacecfc65314fd0dd020d2d08c1e6bfe9))


# [v2.2.8](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.2.7...v2.2.8) (2024-06-07)


### Bug Fixes

* 修复相同 Pod 的权重无法在不同 Path 中设置的问题 ([b8e8435](https://git.woa.com/kateway/tke-ingress-controller/commits/b8e8435cc3ca375586c4d05800a58885fe0ec2fd))
* 修复后端rs排序导致的rs解关联问题 ([3a68ca8](https://git.woa.com/kateway/tke-ingress-controller/commits/3a68ca89a5f75b9ab8821d3e9425b74f25872e5b))
* 创建LBR失败时不删除LB --bug=https://tapd.woa.com/paasdesign/bugtrace/bugs/view?bug_id=1020360782124123279 ([39207b9](https://git.woa.com/kateway/tke-ingress-controller/commits/39207b9408078d8f46a546a4f7d442172830cb97))

### Features

* 允许Ingress的Backend中使用Port名称指定后端 ([4570d5b](https://git.woa.com/kateway/tke-ingress-controller/commits/4570d5bc1dfaaf756997bd0e0761b301478a0522))
* 支持对七层https监听器HTTP2属性进行配置 ([daca1e3](https://git.woa.com/kateway/tke-ingress-controller/commits/daca1e356bab68f0e7773dc3ab0fddb2a3e5b301))



# [v2.2.7](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.2.6...v2.2.7) (2024-05-14)


### Bug Fixes

* addRule 超限不要阻塞后续 deleteRule 流程 ([5198533](https://git.woa.com/kateway/tke-ingress-controller/commits/5198533f2063f809efdf9b100bfc60b4078b04b0))
* enqueue pod deletion for local service ([af6d94b](https://git.woa.com/kateway/tke-ingress-controller/commits/af6d94bd820a21e838f37476df011c4daf360661))
* 修复ModifyTags接口调用 ([cfbd29e](https://git.woa.com/kateway/tke-ingress-controller/commits/cfbd29e02544a4909703c1b934758ed4da3bec36))
* enqueue pod deletion for local service ([af6d94b](https://git.woa.com/kateway/tke-ingress-controller/commits/af6d94bd820a21e838f37476df011c4daf360661))


### Features

* add parameter Zones to ExpandCreateLoadBalancerRequest ([33dbe3b](https://git.woa.com/kateway/tke-ingress-controller/commits/33dbe3bd28d7ae503baadf6a180016d9bf2d9c97))
* 支持在TkeServiceConfig中对七层健康检查协议类型进行配置(TCP or HTTP) ([e3b34b8](https://git.woa.com/kateway/tke-ingress-controller/commits/e3b34b847fdec8233a4a82c5739273c9ef0b4c82))
* 支持在TkeServiceConfig中对七层健康检查协议类型进行配置(TCP or HTTP) ([5a8cdd2](https://git.woa.com/kateway/tke-ingress-controller/commits/5a8cdd23080235a26febbd3f13ab8316d7e5e9bb))



# [](https://git.woa.com/kateway/tke-ingress-controller/compare/v2.2.5...v) (2024-05-06)

# v2.2.6

发布日期：待定

发布镜像：ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.6

imageID：[ccr.ccs.tencentyun.com/tkeimages/ingress-controller@sha256:8c5eb53049876e5e7ba7e5e22c3c38494e3f7972f325c01bc449d08c62e614fe](http://ccr.ccs.tencentyun.com/tkeimages/ingress-controller@sha256:8c5eb53049876e5e7ba7e5e22c3c38494e3f7972f325c01bc449d08c62e614fe)

发布内容：

缺陷修复：

- 修复同步ingress时流程被非致命错误中断的问题 [0ac45b17864a5a8e61ce41bc3ca40827ecf6f351](https://git.woa.com/kateway/tke-ingress-controller/merge_requests/9)

# v2.2.5

发布日期：2024.3.25

发布镜像：[ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.5](http://ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.5)

imageID: [ccr.ccs.tencentyun.com/tkeimages/ingress-controller@sha256:8c5eb53049876e5e7ba7e5e22c3c38494e3f7972f325c01bc449d08c62e614fe](http://ccr.ccs.tencentyun.com/tkeimages/ingress-controller@sha256:8c5eb53049876e5e7ba7e5e22c3c38494e3f7972f325c01bc449d08c62e614fe)

发布内容：

2024.4.16： 同步为2.2.6

缺陷修复：

- 修复ingress切换域名如果涉及到泛域名时规则创建失败的问题，同时导致规则被误删除的问题

[91cd6c3b9e5968a877f4b25d50f61cc26033b2c5](https://git.woa.com/kateway/tke-ingress-controller/merge_requests/8)

# v2.2.4

发布日期： 2024.3.6

发布镜像：[ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.4](http://ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.4)

imageID: ccr.ccs.tencentyun.com/tkeimages/ingress-controller@sha256:4567dd76725af0a27dbf5ee26fcf755f5b913ad8db9a1d0cdde446ec9eeeec6a

发布内容：

缺陷修复:

- 允许path的正则表达包含反斜杠 [1100f7ac06cd3b95078a17cbec1123fa89c877aa](https://git.woa.com/kateway/tke-ingress-controller/commit/1100f7ac06cd3b95078a17cbec1123fa89c877aa)

[ccr.ccs.tencentyun.com/paas/ingress-controller:v2.2.3-1-g1100f7a-linux-amd64](http://ccr.ccs.tencentyun.com/paas/ingress-controller:v2.2.3-1-g1100f7a-linux-amd64)

# v2.2.3

发布日期： 2024.3.1

发布镜像：[ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.3](http://ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.3)

imageID: [ccr.ccs.tencentyun.com/tkeimages/ingress-controller@sha256:8c5eb53049876e5e7ba7e5e22c3c38494e3f7972f325c01bc449d08c62e614fe](http://ccr.ccs.tencentyun.com/tkeimages/ingress-controller@sha256:8c5eb53049876e5e7ba7e5e22c3c38494e3f7972f325c01bc449d08c62e614fe)

2024.4.16: 同步为 2.2.6

发布内容：

- 泛域名支持指定健康检查域名 [d7ff255934c3ba796abeaaf19d04a0fd3f919e69](https://git.woa.com/kateway/tke-ingress-controller/commit/d7ff255934c3ba796abeaaf19d04a0fd3f919e69)

主要功能：

- 支持 sub eni  [35637f47cd4485260d3633c1de4c2e942b90af6a](https://git.woa.com/kateway/tke-ingress-controller/commit/35637f47cd4485260d3633c1de4c2e942b90af6a)

# v2.2.2 【TKE\\EKS增量版本】

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.2

- 发布日期: 2023.12

- 发布镜像：（支持ARM结构）

- 发布内容

1.  【组件优化】支持负载均衡绑定CVM类型的原生节点。

2.  【主要功能】 适配保饭碗项目，重点写接口支持审计回调与拦截机制。

3.  【组件优化】 添加节点截流能力，节点上的服务可优雅下线功能。

4.  【缺陷修复】 修复clientToken幂等导致创建负载均衡时死锁的问题。

5.  【主要功能】添加 ConfigMaps CRD资源的版本信息。

# v2.2.1

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.1

- 发布日期: 2023.9.20

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】Local模式下强制开启Local加权平衡。（原逻辑参考Service该功能开关）

2.  【主要功能】Local模式下强制开启优雅停机和优雅剔除逻辑。

3.  【主要功能】增加Finalizer，避免资源删除事件丢失导致资源泄露。

4.  【主要功能】创建负载均衡时，添加ClientToken。保证接口多次调用的事务性，避免资源重复创建。

5.  支持Service的AllocateLoadBalancerNodePorts新特性，避免用户关闭NodePort的情况下绑定报错。

6.  Service资源的同步结果优化，减少同步对Service资源造成的频繁变更，减少同步状态。

7.  部署策略更新，增量开始支持组件部署在超级节点上。（独立集群、托管集群场景）

8.  预检避免锁冲突，支持多队列并发，提高预检效率。

9.  组件提供版本信息。

# v2.2.0

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.2.0

- 发布日期: 2023.6.28

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】优雅停机、优雅剔除两个特性将变为默认行为强制开启。

2.  修复集群后端总数的运营数据统计。

3.  去除组件启动时的标签转换逻辑

4.  增加go process相关的监控Metrics数据

5.  缺陷修复：忽略EKS的Node变化事件，防止资源反复入列

6.  缺陷修复：EKS Pod绑定时状态异常的错误已经包装为错误码，避免反馈内部错误。

7.  缺陷修复：用户修改资源集群标签，转给其他集群使用之后。集群仍然会对这个资源进行同步，可能造成用户不预期的变更。

# v2.1.3【TKE\\EKS线上主要版本】

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.1.3

- 发布日期: 2023.4.21

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  生命周期管理的优化，避免用户不预期更新

2.  TKE弹EKS场景，Local转发兜底

# v2.1.2

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.1.2

- 发布日期：2022.11.1

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  缺陷修复：Event输出时未携带Kind和APIVersion，导致Event没有关联资源

# v2.1.1

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.1.1

- 发布日期：2022.11.1

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】支持CLB域名化改造

2.  将CLB管理属性判断(create-by)从标签迁移至CRD

# v2.1.0

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.1.0

- 发布日期：2022.11.1

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】对资源同步处理的产品策略更新，在同步时忽略资源的部分错误配置。

2.  【主要功能】将资源的同步状态显示在资源的注解中。

3.  缺陷修复：更新了Event模块，避免在更新Event的时候大量List Event，容易对API Server造成压力。

4.  缺陷修复：修复了直接更新从Informer中获取到的资源的情况。从Informer中获取的是缓存的直接副本，更新前需要对其进行深复制。

# v2.0.4

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.0.4

- 发布日期：2022.11.1

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  缺陷修复：用户跨集群使用已有负载均衡时，回收逻辑没有校验资源的集群归属，导致资源或监听器资源被错误释放。

2.  缺陷修复：LoadBalancerResource CRD锁的抢占错误，没有Event和监控事件透出

# v2.0.3

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.0.3

- 发布日期：2022.9.17

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】增加对LoadBalancerResource CRD资源的灾难恢复能力

2.  增加CRD被删除场景下的自动恢复能力

3.  增加LoadBalancerResource CRD锁的资源重入机制。

# v2.0.2

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.0.2

- 发布日期：2022.8.12

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  缺陷修复：兼容CRD对Subresources的使用限制，支持1.10及以下集群。

# v2.0.1

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.0.1

- 发布日期：2022.8.11

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  缺陷修复：标签转CRD升级中，对转换生成的CRD增加端口声明。避免Ingress\\Service混用场景下，监听器被Service资源同步时删除。

# v2.0.0

- **如果出现 1.x 版本升级 2.x 版本，需要先联系TKE运维和misakazhou。**

- **除特殊情况外，不能从2.x版本降级到1.x版本，需要先联系TKE运维和misakazhou。**

- ccr.ccs.tencentyun.com/tkeimages/ingress-controller:v2.0.0**（开始启用新仓库）**

- 发布日期：2022.8.8

- 发布镜像：（支持ARM结构）

- 发布内容：

- **特别注意：**

1.  【主要功能】标签依赖改造，接入层资源管理不再依赖标签。

2.  【主要功能】集群支持原生节点接入。

3.  【主要功能】集群支持云联网场景下的IDC节点接入。

4.  自动重定向功能放开转发规则的格式限制。

5.  缺陷修复：TKE超级节点场景下，用户在特定场景下出现绑定EKS节点报错。

6.  缺陷修复：跨租户弹性网卡的EKS集群中，绑定出错。

7.  缺陷修复：同步Event更新时出现错误，可能导致同步结果被更新到同名资源的Event中。

# v1.8.3

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.8.3

- 发布日期：2022.7.2

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】EKS跨集群复用特性收拢。不允许用户直接在EKS集群中进行跨集群复用。

2.  【主要功能】更新Pod绑定规则。以下类型的Pod不会再进行绑定。Pod已经失效被驱逐等（PodFailed）、Job Pod已经结束任务（PodSucceeded）。

3.  新增CLB创建参数：ExclusiveCluster。

4.  缺陷修复：在SNI开启的情况下，KeepaliveEnable属性配置不生效的问题。

5.  缺陷修复：修复在用户配置错误域名的情况下出现Panic的情况，在未开启TLS但是又开启重定向功能时Panic的情况。

# v1.8.2

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.8.2

- 发布日期：2022.5.6

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】组件支持透出运营数据

2.  缺陷修复：不允许用户直连HostNetwork类型的工作负载（之前的版本没有阻止该行为，导致出现异常）

3.  缺陷修复：TKEServiceConfig的CRD定义，部分字段没有声明 nullable:true，在EKS集群升级之后影响CRD资源对象创建。

4.  缺陷修复：没有声明SNI的情况下，声明开启自动重定向。或部分删除Https监听器的场景。组件出现空指针Panic。

# v1.8.1

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.8.1

- 发布日期：2022.4.28

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】接入层组件支持IPv6。(因弹性网卡问题，七层数据面流量暂时不通)

2.  【主要功能】支持配置负载均衡安全组、负载均衡的默认放通功能

3.  缺陷修复：修复手动重定向功能和直连功能同时使用时，工作负载就绪检查失败的问题。

4.  缺陷修复：云联网关联VPC超过20个时，云联网下VPC获取不完整。导致跨地域接入失败的问题。

# v1.8.0

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.8.0

- 发布日期：2021.3.18

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要功能】支持Kubernetes 1.22版本集群

2.  【主要功能】支持社区规范，根据节点标签屏蔽接入层节点。（node.kubernetes.io/exclude-from-external-load-balancers）

3.  【组件优化】适配EKS原地重建的场景，不绑定NodeLost状态下的工作负载。

4.  【组件优化】集群将调整NodePort模式下的默认策略。默认绑定Unschedulable节点作为NodePort后端。

5.  【组件优化】GlobalRoute直连功能不再依赖负载均衡开启SNAT Pro功能。使用GlobalRoute直连功能也不再要求用户申请CLB白名单。

6.  缺陷修复：跨地域场景下，增加地域属性校验，避免用户指定错误地域之后，资源同步出现死锁。

7.  缺陷修复：响应Secret资源变化的时候，没有做SNI适配。导致出现Secret更新之后，CLB上的证书没有做更新。

# v1.7.4

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.7.4

- 发布日期：2021.12.18

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要能力】支持IDC混合云场景（内部PVGW打通）

2.  【主要能力】支持跨地域绑定场景（内部PVGW打通）。通过跨地域2.0方案进行接入

3.  【组件优化】默认TKE扩展EKS集群，在存在TKE节点时，为保证流量均衡不绑定EKS节点。在Local模式下导致EKS Pod失去流量入口。

4.  修复缺陷：在非直连情况下，Ingress使用NoSelectService时也会被拒绝（v1.7.3引入）

# v1.7.3

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.7.3

- 发布日期：2021.11.25

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  更换基础镜像到 alpine:3.13.7，解决镜像安全相关问题。

2.  【主要能力】同时支持优雅停机和优雅踢除。

3.  【主要能力】在优雅停机和优雅踢除场景下，避免权重全部设置为0。

4.  【主要能力】支持资源开启配置保护能力。

5.  【主要能力】支持配置健康访问来源IP类型，防止出现四层回环。

6.  【组件优化】直连场景下，拒绝用户使用NoSelector类型Service。

# v1.7.2

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.7.2

- 发布日期：2021.11.9

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  更换基础镜像到 alpine:3.12.8，解决镜像安全相关问题。

2.  组件优化：优化Event事件的分类，降低并发冲突等用户不关心Event的事件级别。

3.  缺陷修复：修改后端权重的接口分批策略与服务端不匹配。

4.  缺陷修复：自动重定向功能依赖七层混合协议功能，在未开启七层混合协议的场景下，同步可能会导致监听器重建。

# v1.7.1

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.7.1

- 发布日期：2021.11.4

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  支持负载均衡七层SNAT长连接配置

# v1.7.0

- ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:v1.7.0

- 发布日期：2021.10.25

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  支持CLB产品计费转移

# v1.6.5

- [ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)v1.6.5

- 发布日期：2021.08.05

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要能力】支持Kubernetes 1.18引入的IngressClass特性。

2.  【主要能力】对负载均衡跨域绑定1.0方案提供支持，内部专线打通场景可以通过这个方案跨地域绑定负载均衡资源。

3.  动态轮询ProjectID，避免集群ProjectID变更导致资源创建失败。

4.  规避标签服务的落库延迟问题，创建负载均衡之后确认能够通过标签检索到资源。

5.  规避负载均衡清理标签资源的失败情况，检查标签检索到资源的存在性。

# v1.6.4

- [ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)v1.6.4

- 发布日期：2021.07.16

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要能力】支持正则类型的转发规则。

2.  【主要能力】支持精确匹配类型的转发规则。

# v1.6.3

- [ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)v1.6.3

- 发布日期：2021.06.30

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  重定向功能改进，通过调整调用接口顺序，避免在重定向规则变更时出现断流的情况。

2.  重定向功能改进，修复重定向接口调用时，批量数量超过接口上限的情况。

3.  修复自定义权重在不同后端下，权重配置出现混乱的情况。

# v1.6.2

- [ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)v1.6.2

- 发布日期：2021.05.11

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要能力】支持通过云联网方案，跨地域的负载均衡管理

2.  【主要能力】支持配置后端转发协议

3.  【主要能力】【性能优化】EKS\\TKE 实现架构统一

4.  修复缺陷：修改修改后端权重时的批量个数过多超过API单次调用上限的问题。

5.  修复缺陷：defaultWeight只对StatefulSet生效。不能对所有工作负载生效。

# v1.6.0

- [ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:0cb16bea](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)

- [ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:d11bc645](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)（BugFix）

- 发布日期：2021.03.18

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要能力】支持集群容器网络工作负载，通过GlobalRouter方式进行直连

2.  【主要能力】指定独立指定后端权重

3.  【主要能力】支持配置七层默认域名

4.  【主要能力】支持纯IPv6类型负载均衡

5.  【主要能力】支持手动重定向配置

6.  【主要能力】支持自动重定向配置

7.  【主要能力】支持配置双向证书

8.  【主要能力】【性能优化】优化任务队列为优先级队列，避免低优先级任务过多导致阻塞。

9.  【性能优化】优化组件架构，通过上下文方式避免重复的API调用。

10. 【运维测试】优化EventRecorder，避免重复事件上报导致Event限频。

11. Ingress组件支持选主

# v1.5.2

- [ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:d32c0a24](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)

- 发布日期：2020.12.15

- 发布镜像：（支持ARM结构）

- 发布内容：

1.  【主要能力】适配TKE扩EKS特性：对于非直连模式过滤VK nodes，并且在无CVM节点时退化成负载均衡直连运行在EKS资源池中的工作负载

2.  【主要能力】以新增注解的方式，支持了适配tkex和公有云用户的pod优雅退出特性：在pod退出过程中权重为0，彻底消失时解绑。

3.  【性能优化】性能优化，避免在同一个同步任务中多次查询负载均衡状态信息。

4.  加强 Ingress Status 写入时机。避免仅在负载均衡创建时进行写入，如果遇到接口内部错误导致错过写入时机之后，没有恢复手段。

# v1.5.1

- 发布日期：2020.10.29

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:dff01037](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)

- 发布内容：

1.  修复对Node状态突变的容忍期间，并发的同步任务摘除后端后。因为节点状态容忍导致没有重新挂载后端的问题。

# v1.5.0

- 发布日期：2020.09.15

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:864c1f8c](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)

- 发布内容：

1.  【主要能力】支持Ingress SNI功能，支持多证书配置。

2.  【主要能力】对新的独立网卡的网络模式直连的支持。

3.  【主要能力】对TkeServiceConfig功能提供同步更新的支持。

4.  【性能优化】根据负载均衡配额查询接口进行优化。避免更新后端时，绑定后端数量超过限制导致错误。

5.  提高Ingress组件的API Server访问频率限制

6.  修复SNI与混合规则同时使用时的证书校验问题。

7.  新增负载均衡对自研提供的特殊能力的参数支持。（TgwGroupName、ZhiTong）

8.  错误处理以及其他易用性优化。

# v1.4.0

- 发布日期：2020.06.29

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:f0189075](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)

- 发布内容：

1.  【运维测试】自动化测试基本补全。测试框架配置化。

2.  修复直连场景下的一些问题

3.  增加对用户模板的预检测。

4.  Local模式下，绑定后端不再过滤掉Master节点。避免后端只有Master节点的情况下被踢空。

5.  后端绑定端口的错误选择问题。

6.  增加对默认VPC-CNI网络模式的判断。

7.  对于处于停止状态的Pod依旧会进行挂载的问题。

# v1.3.0

- 发布日期：2020.04.23

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)242ad32c

- 发布内容：

1.  【主要能力】支持外挂配置TkeServiceConfig，控制负载均衡的相关属性配置。

2.  【主要能力】支持直绑功能，直绑模式下支持来源IP和会话保持等特性。

3.  【运维测试】自动化测试第一批。

# v1.2.1

- 发布日期：2020.03.30

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:300a270a](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)

- 发布内容：

1.  【主要能力】更新支持NAT IPv6，解决KubeProxy对于混合IPv4\\IPv6的支持问题。

2.  【运维测试】Ingress的预发布Mock支持。

3.  【运维测试】Ingress的静默发布支持。

4.  Ingress的服务化改造。

# v1.2.0

- 发布日期：2019.12.19

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:45909f61)

- 发布内容：

1.  【主要能力】支持NET IPv6类型的LoadBalancer

2.  【性能优化】腾讯云服务内部调用限频，防止因超频错误而反复重试。

3.  支持自定义HostName

4.  修复用户修改kubernetes.io/ingress.class字段后资源不释放的问题

5.  修复证书更新的逻辑缺陷，集中证书更新逻辑到Secret资源

# v1.1.1

- 发布日期：2019.11.14

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:a48d4b5a](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:a48d4b5a)

- 发布内容：

1.  CLB修复广州北京地域的批量接口异常。

2.  修复部分业务ErrorCode被覆盖的问题。

3.  不再在用户Service不存在时，清理用户CLB后端实例。

4.  追加存量集群的标签服务的域名绑定。

# v1.1.0

- 发布日期：2019.11.07

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:ec73e079](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:ec73e079)

- 发布内容：

1.  【性能优化】更换CLB提供的新版后端绑定接口，以及优化后端信息的获取逻辑，以改进Ingress的同步性能。

2.  【运维测试】更新Ingress Controller的错误码，为用户提供更为准确的Ingress错误信息。

3.  Ingress支持继承集群标签到CLB资源。

# v1.0.0

- 发布日期：2019.10.30

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:80220f82](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:80220f82)

- 发布内容：

1.  【运维测试】新增业务错误码对应用户帮助文档

2.  【运维测试】增强Ingress的任务执行结果统计，区分失败任务的类型

3.  【运维测试】增强依赖服务的任务执行结果统计，新增接口返回码的信息

4.  添加云产品域名在集群内有固定的IP解析。

5.  修复同步过程中Panic的问题

6.  修复用户部分Ingress模板格式导致Crash的问题

- 史前版本

- 发布日期：2019.8.27

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:085aa741](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:085aa741)

- 发布内容：

1.  修复clb接口返回不兼容格式

- 史前版本

- 发布日期：2019.8.21

- 发布镜像：[ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:237d870a](http://ccr.ccs.tencentyun.com/library/qcloud_ingress_controller:237d870a)

- 发布内容：

1.  diff先删除后添加

1.  切换到go mod
