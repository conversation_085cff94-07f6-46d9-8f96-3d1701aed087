import os
import yaml

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
CLUSTER_INFO_TMP = '''masterURL:  https://{clusterId}.ccs.tencent-cloud.com
configFilePath: {configFilePath}
region: {region}
secretID: {secretID}
secretKey: {secretKey}
qCloudAPIReadOnly: false
qCloudAPIRate: 18
'''


if __name__ == '__main__':
    with open('environment.yaml', 'r') as fp:
        env = yaml.safe_load(fp)

    resources = env['environments'][0]['resources']
    tke_cluster = resources['tke'][0]
    uin = resources['uin'][0]
    kube_config_path = os.path.join(BASE_DIR, 'kube_config')

    kube_config = tke_cluster['properties']['kube_config']
    kube_config = kube_config.replace('\\n', '\n')
    with open(kube_config_path, 'w') as fp:
        fp.write(kube_config)

    params = {
        'clusterId': tke_cluster['properties']['clusterId'],
        'region': tke_cluster['properties']['region'],
        'secretID': uin['properties']['secretID'],
        'secretKey': uin['properties']['secretKey'],
        'configFilePath': kube_config_path
    }

    with open('test/clusterInfo.yaml', 'w') as fp:
        fp.write(CLUSTER_INFO_TMP.format(**params))
