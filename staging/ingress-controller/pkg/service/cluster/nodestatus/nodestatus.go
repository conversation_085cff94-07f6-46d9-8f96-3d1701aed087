package nodestatus

import (
	"sync"
	"time"

	v1 "k8s.io/api/core/v1"
)

var (
	NodeStatusMapInstanceLock                = new(sync.Mutex)
	NodeStatusMapInstance     *NodeStatusMap = nil
)

type NodeStatus struct {
	Node *v1.Node
	Time time.Time
}

type NodeStatusMap struct {
	rw         *sync.RWMutex
	NodeStatus map[string]*NodeStatus
}

func InitNodeStatusMap() {
	if NodeStatusMapInstance == nil {
		NodeStatusMapInstanceLock.Lock()
		defer NodeStatusMapInstanceLock.Unlock()

		if NodeStatusMapInstance == nil {
			NodeStatusMapInstance = &NodeStatusMap{
				rw:         new(sync.RWMutex),
				NodeStatus: map[string]*NodeStatus{},
			}
		}
	}
}

func (this *NodeStatusMap) Add(name string, value *NodeStatus) {
	this.rw.Lock()
	defer this.rw.Unlock()

	this.NodeStatus[name] = value
}

func (this *NodeStatusMap) Get(name string) (*NodeStatus, bool) {
	this.rw.RLock()
	defer this.rw.RUnlock()

	status, exist := this.NodeStatus[name]
	return status, exist
}

func (this *NodeStatusMap) Delete(name string) {
	this.rw.Lock()
	defer this.rw.Unlock()

	delete(this.NodeStatus, name)
}

func (this *NodeStatusMap) Each(callback func(string, *NodeStatus)) {
	this.rw.RLock()
	defer this.rw.RUnlock()

	for k, v := range this.NodeStatus {
		callback(k, v)
	}
}
