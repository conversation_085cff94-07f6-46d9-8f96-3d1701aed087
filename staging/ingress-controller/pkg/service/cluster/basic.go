package cluster

import (
	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	extensions "k8s.io/api/extensions/v1beta1"
	networking "k8s.io/api/networking/v1"
	"k8s.io/apimachinery/pkg/labels"

	"git.woa.com/kateway/pkg/domain/ingress/ingress_wrapper"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"

	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

type basic struct {
	cluster.Interface
}

func newBasic(clusterService cluster.Interface) *basic {
	return &basic{Interface: clusterService}
}

func (*basic) Enqueue(ing types.Ingress) {}

func (*basic) Stop() {}

func (b *basic) listIngresses(namespace string, selector labels.Selector) ([]types.Ingress, error) {
	var err error
	if lo.Must(cluster_service.Instance.IsExtensionsAPIGroupSupported()) {
		var ingresses []*extensions.Ingress
		if namespace != v1.NamespaceAll {
			if ingresses, err = b.ExtensionIngressLister().Ingresses(namespace).List(selector); err != nil {
				return nil, err
			}
		} else {
			if ingresses, err = b.ExtensionIngressLister().List(selector); err != nil {
				return nil, err
			}
		}
		return ingress_wrapper.NewIngressWrapperExtensionsList(ingresses), nil
	} else {
		var ingresses []*networking.Ingress
		if namespace != v1.NamespaceAll {
			if ingresses, err = b.NetworkingIngressLister().Ingresses(namespace).List(selector); err != nil {
				return nil, err
			}
		} else {
			if ingresses, err = b.NetworkingIngressLister().List(selector); err != nil {
				return nil, err
			}
		}
		return ingress_wrapper.NewIngressWrapperNetworkingList(ingresses), nil
	}
}
