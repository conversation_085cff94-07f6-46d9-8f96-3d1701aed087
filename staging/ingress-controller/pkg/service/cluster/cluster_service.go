package cluster

import (
	"git.woa.com/kateway/pkg/domain/services/cluster"

	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

type ClusterServiceType string

const (
	Basic         ClusterServiceType = "Basic"         // 提供 ClusterClient 基础服务
	TaskQueue     ClusterServiceType = "TaskQueue"     // 提供 ClusterClient 基础服务，并对相关资源进行持续监听，持续进行Ingress的对账
	TaskTraversal ClusterServiceType = "TaskTraversal" // 提供 ClusterClient 基础服务，对所有Ingress进行一次对账后退出
)

// NewCollector creates a new metric defaultClusterServiceInstance
func Init(clusterService cluster.Interface, serviceType ClusterServiceType) error {
	if cluster_service.Instance == nil {
		cluster_service.Update.Lock()
		defer cluster_service.Update.Unlock()

		if cluster_service.Instance == nil {
			var clusterServiceInstance cluster.Interface
			var err error
			switch serviceType {
			case Basic:
				clusterServiceInstance = newBasic(clusterService)
			case TaskQueue:
				clusterServiceInstance, err = newTaskQueueClusterService(clusterService)
				if err != nil {
					return err
				}
			case TaskTraversal:
				clusterServiceInstance, err = newTaskTraversalClusterService(clusterService)
				if err != nil {
					return err
				}
			}
			cluster_service.Instance = clusterServiceInstance
			if cluster.Instance == nil {
				cluster.Instance = clusterServiceInstance
			}
		}
	}
	return nil
}
