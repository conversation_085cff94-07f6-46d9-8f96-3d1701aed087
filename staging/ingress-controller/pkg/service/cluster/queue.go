package cluster

import (
	"context"
	"reflect"
	"strconv"
	"time"

	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	extensions "k8s.io/api/extensions/v1beta1"
	networking "k8s.io/api/networking/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	v12 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"

	multiClusteringress "git.woa.com/kateway/multi-cluster-ingress-api/apis/multiclusteringress/v1alpha1"
	"git.woa.com/kateway/pkg/domain/controllercm"
	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/ingress/ingress_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/types"
	tkeserviceapi "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	service2 "git.woa.com/kateway/tke-ingress-controller/pkg/service"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/cluster/nodestatus"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

const (
	storeSyncPollPeriod = 5 * time.Second
	ingressSyncPeriod   = 30 * time.Second
	serviceSyncPeriod   = 30 * time.Second
	configMapSyncPeriod = 30 * time.Minute
	QcloudCertId        = "qcloud_cert_id"
	QcloudCACertId      = "qcloud_ca_cert_id"
	QcloudTlsCrt        = "tls.crt"
	QcloudTlsKey        = "tls.key"
)

type NodeStatus struct {
	Node *v1.Node
	Time time.Time
}

type taskQueueClusterService struct {
	basic
	silent bool
}

func newTaskQueueClusterService(i cluster.Interface) (*taskQueueClusterService, error) {
	nodestatus.InitNodeStatusMap()

	silent := services.GetSilentStart()

	basicClusterService := newBasic(i)
	return &taskQueueClusterService{
		basic:  *basicClusterService,
		silent: silent,
	}, nil
}

func (service *taskQueueClusterService) Run(stopCh <-chan struct{}) {
	// 注册Kubernetes的资源监听
	service.registryEventHandler()

	// 节点状态变更的延时处理
	service.startWatchNode()

	// 启动Informer
	service.basic.Run(stopCh)

	// Worker启动前，静默解除
	service.silent = false

	/*
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()
		cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().Run(ctx)

		// 启动Worker
		cluster_service.QueueServiceInstance.IngressQueue().Run()
		// 启动Worker
		cluster_service.QueueServiceInstance.MultiClusterIngressQueue().Run()
		// 启动secret taskqueue
		cluster_service.QueueServiceInstance.SecretQueue().Run()
	*/
}

func (service *taskQueueClusterService) Stop() {
	cluster_service.QueueServiceInstance.IngressQueue().Shutdown()
	cluster_service.QueueServiceInstance.MultiClusterIngressQueue().Shutdown()
	cluster_service.QueueServiceInstance.SecretQueue().Shutdown()
}

func (service *taskQueueClusterService) registryEventHandler() {
	service.serviceEventHandler()          // Service watch handlers
	service.nodeEventHandler()             // node watch handlers
	service.endpointEventHandler()         // endpoint watcher handlers
	service.secretEventHandler()           // secret watcher handlers
	service.configMapEventHandler()        // configMaps watcher handlers
	service.tkeserviceConfigEventHandler() // tke serviceConfig watcher handlers
	service.podEventHandler()              // pod watcher handlers

	if lo.Must(cluster_service.Instance.IsExtensionsAPIGroupSupported()) {
		service.ingressEventHandler() // Ingress watch handlers
	} else {
		service.newIngressEventHandler() // Ingress watch handlers
	}
	if config.Global.EnableMultiClusterIngress {
		service.multiClusterIngressEventHandler()
	}
}

// kateway ingress 事件处理器
func (service *taskQueueClusterService) ingressEventHandler() {
	service.ExtensionIngressInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				ing, isIngress := obj.(*extensions.Ingress)
				if !isIngress {
					klog.Infof("Controller: Add event contained non-Ingress object: %v", obj)
					return
				}
				ingressWrapper := ingress_wrapper.NewIngressWrapperExtensions(ing)
				if !utils.IsQCLOUDIngress(ingressWrapper) {
					klog.Infof("Ignoring add for ingress %v based on annotation %v", utils.IngressName(ingressWrapper), utils.IngressClassKey)
					return
				}
				service.enqueue(ingressWrapper)
			},
			UpdateFunc: func(old, cur interface{}) {
				curIng, ok1 := cur.(*extensions.Ingress)
				oldIng, ok2 := old.(*extensions.Ingress)
				if !ok1 || !ok2 {
					klog.Infof("Controller: Add event contained non-Ingress old object: %v, cur object: %v", old, cur)
					return
				}
				curIngWrapper := ingress_wrapper.NewIngressWrapperExtensions(curIng)
				oldIngWrapper := ingress_wrapper.NewIngressWrapperExtensions(oldIng)
				if !utils.IsQCLOUDIngress(curIngWrapper) {
					if utils.IsQCLOUDIngress(oldIngWrapper) {
						klog.Infof("Ingress %v class changed, syncing to delete.", utils.IngressName(curIngWrapper))
						service.enqueue(curIngWrapper)
					}
					return
				}
				if ingressChange(oldIng, curIng) {
					klog.Infof("Ingress %v changed, syncing", utils.IngressName(curIngWrapper))
					service.enqueue(curIngWrapper)
				}
			},
			DeleteFunc: func(obj interface{}) {
				ing, isIngress := obj.(*extensions.Ingress)
				if !isIngress {
					deletedState, ok := obj.(cache.DeletedFinalStateUnknown)
					if !ok {
						klog.Errorf("Received unexpected object: %v", obj)
						return
					}
					ing, ok = deletedState.Obj.(*extensions.Ingress)
					if !ok {
						klog.Errorf("DeletedFinalStateUnknown contained non-Ingress object: %v", deletedState.Obj)
						return
					}
				}
				ingressWrapper := ingress_wrapper.NewIngressWrapperExtensions(ing)
				if !utils.IsQCLOUDIngress(ingressWrapper) {
					klog.Infof("Ignoring delete for ingress %v based on annotation %v", utils.IngressName(ingressWrapper), utils.IngressClassKey)
					return
				}
				klog.Infof("Delete notification received for Ingress %v.", utils.IngressName(ingressWrapper))
				service.enqueue(ingressWrapper)
			},
		},
		ingressSyncPeriod,
	)
}

func (service *taskQueueClusterService) newIngressEventHandler() {
	service.NetworkingIngressInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				ing, isIngress := obj.(*networking.Ingress)
				if !isIngress {
					klog.Infof("Controller: Add event contained non-Ingress object: %v", obj)
					return
				}
				ingressWrapper := ingress_wrapper.NewIngressWrapperNetworking(ing)
				if !utils.IsQCLOUDIngress(ingressWrapper) {
					klog.Infof("Ignoring add for ingress %v based on annotation %v", utils.IngressName(ingressWrapper), utils.IngressClassKey)
					return
				}
				service.enqueue(ingressWrapper)
			},
			UpdateFunc: func(old, cur interface{}) {
				curIng, ok1 := cur.(*networking.Ingress)
				oldIng, ok2 := old.(*networking.Ingress)
				if !ok1 || !ok2 {
					klog.Infof("Controller: Add event contained non-Ingress old object: %v, cur object: %v", old, cur)
					return
				}
				curIngWrapper := ingress_wrapper.NewIngressWrapperNetworking(curIng)
				oldIngWrapper := ingress_wrapper.NewIngressWrapperNetworking(oldIng)
				if !utils.IsQCLOUDIngress(curIngWrapper) {
					if utils.IsQCLOUDIngress(oldIngWrapper) {
						klog.Infof("Ingress %v class changed, syncing to delete.", utils.IngressName(curIngWrapper))
						service.enqueue(curIngWrapper)
					}
					return
				}
				if newIngressChange(oldIng, curIng) {
					klog.Infof("Ingress %v changed, syncing", utils.IngressName(curIngWrapper))
					service.enqueue(curIngWrapper)
				}
			},
			DeleteFunc: func(obj interface{}) {
				ing, isIngress := obj.(*networking.Ingress)
				if !isIngress {
					deletedState, ok := obj.(cache.DeletedFinalStateUnknown)
					if !ok {
						klog.Errorf("Received unexpected object: %v", obj)
						return
					}
					ing, ok = deletedState.Obj.(*networking.Ingress)
					if !ok {
						klog.Errorf("DeletedFinalStateUnknown contained non-Ingress object: %v", deletedState.Obj)
						return
					}
				}
				ingressWrapper := ingress_wrapper.NewIngressWrapperNetworking(ing)
				if !utils.IsQCLOUDIngress(ingressWrapper) {
					klog.Infof("Ignoring delete for ingress %v based on annotation %v", utils.IngressName(ingressWrapper), utils.IngressClassKey)
					return
				}
				klog.Infof("Delete notification received for Ingress %v.", utils.IngressName(ingressWrapper))
				service.enqueue(ingressWrapper)
			},
		},
		ingressSyncPeriod,
	)
}

func ingressChange(oldIng *extensions.Ingress, curIng *extensions.Ingress) bool {
	if !reflect.DeepEqual(oldIng.Spec, curIng.Spec) {
		return true
	}
	old := copyAnnotationWithoutCondition(oldIng.Annotations)
	cur := copyAnnotationWithoutCondition(curIng.Annotations)
	if !reflect.DeepEqual(old, cur) {
		return true
	}
	if !oldIng.DeletionTimestamp.Equal(curIng.DeletionTimestamp) {
		return true
	}
	return false
}

func newIngressChange(oldIng *networking.Ingress, curIng *networking.Ingress) bool {
	if !reflect.DeepEqual(oldIng.Spec, curIng.Spec) {
		return true
	}
	old := copyAnnotationWithoutCondition(oldIng.Annotations)
	cur := copyAnnotationWithoutCondition(curIng.Annotations)
	if !reflect.DeepEqual(old, cur) {
		return true
	}
	if !oldIng.DeletionTimestamp.Equal(curIng.DeletionTimestamp) {
		return true
	}
	return false
}

func copyAnnotationWithoutCondition(origin map[string]string) map[string]string {
	result := make(map[string]string)
	for key, value := range origin {
		if key != utils.IngressCondition && key != utils.ServiceCondition && key != utils.ClientTokenAnnotation {
			result[key] = value
		}
	}
	return result
}

// service 事件处理器: 进行判断后最终入队 ingress 资源
func (service *taskQueueClusterService) serviceEventHandler() {
	service.ServiceInformer().Informer().AddEventHandler(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				svc, isService := cur.(*v1.Service)
				if !isService {
					klog.Infof("Controller: Add event contained non-Service object: %v", cur)
					return
				}
				service.enqueueIngressForService(svc)
			},
			UpdateFunc: func(old, cur interface{}) {
				oldSvc, ok1 := old.(*v1.Service)
				curSvc, ok2 := cur.(*v1.Service)
				if !ok1 || !ok2 {
					klog.Infof("Controller: Add event contained non-Service old object: %v, cur object: %v", old, cur)
					return
				}
				if service.needsUpdate(oldSvc, curSvc) {
					service.enqueueIngressForService(curSvc)
				}
			},
		},
	)
}

func (service *taskQueueClusterService) needsUpdate(oldService *v1.Service, newService *v1.Service) bool {
	if oldService.Spec.Type != newService.Spec.Type {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "Type", oldService.Spec.Type, newService.Spec.Type)
		return true
	}
	if !utils.PortSlicesEqualForLB(oldService.Spec.Ports, newService.Spec.Ports) {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "Ports", oldService.Spec.LoadBalancerIP, newService.Spec.LoadBalancerIP)
		return true
	}
	if oldService.Spec.SessionAffinity != newService.Spec.SessionAffinity {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "SessionAffinity", oldService.Spec.SessionAffinity, newService.Spec.SessionAffinity)
		return true
	}
	// 考虑字段变更和新增字段的情况
	if oldService.Spec.SessionAffinity == v1.ServiceAffinityClientIP &&
		newService.Spec.SessionAffinityConfig != nil && newService.Spec.SessionAffinityConfig.ClientIP != nil && newService.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds != nil &&
		(oldService.Spec.SessionAffinityConfig == nil || oldService.Spec.SessionAffinityConfig.ClientIP == nil || oldService.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds == nil || *oldService.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds != *newService.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds) {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "SessionAffinityConfig", *oldService.Spec.SessionAffinityConfig, *newService.Spec.SessionAffinityConfig)
		return true
	}
	if oldService.Spec.LoadBalancerIP != newService.Spec.LoadBalancerIP {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "LoadbalancerIP", oldService.Spec.LoadBalancerIP, newService.Spec.LoadBalancerIP)
		return true
	}
	if len(oldService.Spec.ExternalIPs) != len(newService.Spec.ExternalIPs) {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "ExternalIP", utils.JsonWrapper(oldService.Spec.ExternalIPs), utils.JsonWrapper(newService.Spec.ExternalIPs))
		return true
	}
	for i := range oldService.Spec.ExternalIPs {
		if oldService.Spec.ExternalIPs[i] != newService.Spec.ExternalIPs[i] {
			klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "ExternalIP", utils.JsonWrapper(oldService.Spec.ExternalIPs), utils.JsonWrapper(newService.Spec.ExternalIPs))
			return true
		}
	}
	oldServiceAnnotations := types.NewService(oldService).FilterReadOnly()
	newServiceAnnotations := types.NewService(newService).FilterReadOnly()
	if !reflect.DeepEqual(oldServiceAnnotations, newServiceAnnotations) {
		klog.Infof("Service(%s) %s Changed.", utils.ServiceName(newService), "Annotations")
		return true
	}
	if oldService.UID != newService.UID {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "UID", oldService.UID, newService.UID)
		return true
	}
	if oldService.Spec.ExternalTrafficPolicy != newService.Spec.ExternalTrafficPolicy {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "ExternalTrafficPolicy", oldService.Spec.ExternalTrafficPolicy, newService.Spec.ExternalTrafficPolicy)
		return true
	}
	if oldService.Spec.HealthCheckNodePort != newService.Spec.HealthCheckNodePort {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "HealthCheckNodePort", oldService.Spec.HealthCheckNodePort, newService.Spec.HealthCheckNodePort)
		return true
	}
	if utils.GetServiceIPStack(oldService) != utils.GetServiceIPStack(newService) {
		klog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "IPFamilies", utils.JsonWrapper(oldService.Spec.IPFamilies), utils.JsonWrapper(oldService.Spec.IPFamilies))
		return true
	}
	if !reflect.DeepEqual(oldService.Spec.Selector, newService.Spec.Selector) {
		klog.Infof("Service(%s) %s Changed.", utils.ServiceName(newService), "Selector")
		return true
	}
	return false
}

func (service *taskQueueClusterService) nodeEventHandler() {
	service.NodeInformer().Informer().AddEventHandler(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				node, isNode := cur.(*v1.Node)
				if !isNode {
					klog.Infof("Controller: Add event contained non-Node object: %v", cur)
					return
				}
				klog.Infof("controller: Add event, nodes [%s/%s]\n", node.Namespace, node.Name)
				service.enqueueForNode(node)
			},
			UpdateFunc: func(old, new interface{}) {
				node1, ok1 := old.(*v1.Node)
				node2, ok2 := new.(*v1.Node)
				if !ok1 || !ok2 {
					klog.Infof("Controller: Add event contained non-Node old object: %v, cur object: %v", old, new)
					return
				}
				if utils.IsEKSNode(node2) {
					return
				}
				if nodeStatusChanged(node1, node2) || nodeFinalizerChanged(node1, node2) || nodeDeletionChanged(node1, node2) {
					service.enqueueForNode(node2)
				}
				if nodeLabelChanged(node1, node2) {
					service.enqueueIngressForNodeLabel(node2)
				}
			},
			DeleteFunc: func(cur interface{}) {
				node, isNode := cur.(*v1.Node)
				if !isNode {
					deletedState, ok := cur.(cache.DeletedFinalStateUnknown)
					if !ok {
						klog.Errorf("Received unexpected object: %v", cur)
						return
					}
					node, ok = deletedState.Obj.(*v1.Node)
					if !ok {
						klog.Errorf("DeletedFinalStateUnknown contained non-Node object: %v", deletedState.Obj)
						return
					}
				}
				klog.Infof("controller: Delete event, nodes [%s/%s]\n", node.Namespace, node.Name)
				service.enqueueForNode(node)
			},
		},
	)
}

func (service *taskQueueClusterService) endpointEventHandler() {
	service.EndpointsInformer().Informer().AddEventHandler(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				endpoint, isEndpoint := cur.(*v1.Endpoints)
				if !isEndpoint {
					klog.Infof("Controller: Add event contained non-Endpoints object: %v", cur)
					return
				}
				klog.V(5).Infof("controller: Add event, endpoints [%s/%s]\n", endpoint.Namespace, endpoint.Name)
				service.enqueueIngressForEndpoint(endpoint)
			},
			UpdateFunc: func(obja, objb interface{}) {
				old, ok1 := obja.(*v1.Endpoints)
				cur, ok2 := objb.(*v1.Endpoints)
				if !ok1 || !ok2 {
					klog.Infof("Controller: Add event contained non-Endpoints old object: %v, cur object: %v", old, cur)
					return
				}
				if !reflect.DeepEqual(old, cur) {
					service.enqueueIngressForEndpoint(cur)
				}
			},
		})
}

func (service *taskQueueClusterService) secretNeedEnqueue(old, cur *v1.Secret) {
	if services.SecretNeedEnqueue(old, cur) {
		service.EnqueueSecret(types.JoinKeyStrings("/", cur.Namespace, cur.Name))
	}
}

func (service *taskQueueClusterService) secretEventHandler() {
	service.SecretInformer().Informer().AddEventHandler(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				secret, isSecret := cur.(*v1.Secret)
				if !isSecret {
					klog.Infof("Controller: Add event contained non-Secret object: %v", cur)
					return
				}
				klog.Infof("Add event, secret [%s/%s]\n", secret.Namespace, secret.Name)
				// 考虑ingress-controller重启的情况，会自动触发一次add event，所以不一定会创建证书
				service.secretNeedEnqueue(nil, secret)
				service.enqueueIngressForSecret(secret)
			},
			UpdateFunc: func(old, cur interface{}) {
				oldSecret, ok1 := old.(*v1.Secret)
				curSecret, ok2 := cur.(*v1.Secret)
				if !ok1 || !ok2 {
					klog.Infof("Controller: Add event contained non-Secret old object: %v, cur object: %v", old, cur)
					return
				}
				if !reflect.DeepEqual(oldSecret, curSecret) {
					klog.Infof("Controller: Update event, secret [%s/%s]\n", curSecret.GetNamespace(), curSecret.GetName())
					service.secretNeedEnqueue(oldSecret, curSecret)
					service.enqueueIngressForSecret(curSecret)
				}
			},
		},
	)
}

func (service *taskQueueClusterService) configMapEventHandler() {
	service.ConfigMapInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				configMap, isConfigMap := obj.(*v1.ConfigMap)
				if !isConfigMap {
					klog.Infof("Controller: Add event contained non-ConfigMap object: %v", obj)
					return
				}

				if configMap.Namespace == v12.NamespaceSystem && configMap.Name == controllercm.ConfigMapService {
					klog.Infof("Controller: Add event, configMap [%s/%s]\n", configMap.Namespace, configMap.Name)

					if err := ensureNodeGracefulDeletionManagerState(configMap.Data[controllercm.KeyEnableNodeGracefulDeletion]); err != nil {
						klog.Errorf("change node graceful deletion manger state error: %v", err)
					}
				}
			},
			UpdateFunc: func(_, objb interface{}) {
				configMapNew, isConfigMap := objb.(*v1.ConfigMap)
				if !isConfigMap {
					klog.Infof("Controller: Add event contained non-ConfigMap object: %v", objb)
					return
				}
				if configMapNew.Namespace == v12.NamespaceSystem && configMapNew.Name == service2.TKECNIConfConfigMap {
					service2.ClusterInfoServiceInstance.IsDefaultTKERouteENI(context.Background(), true)
				} else if configMapNew.Namespace == v12.NamespaceSystem && configMapNew.Name == controllercm.ConfigMapService {
					service2.ClusterInfoServiceInstance.IsGlobalRoute(context.Background(), true)

					if err := ensureNodeGracefulDeletionManagerState(configMapNew.Data[controllercm.KeyEnableNodeGracefulDeletion]); err != nil {
						klog.Errorf("change node graceful deletion manger state error: %v", err)
					}
				}
			},
		},
		configMapSyncPeriod,
	)
}

func (service *taskQueueClusterService) podEventHandler() {
	service.PodInformer().Informer().AddEventHandler(
		cache.ResourceEventHandlerFuncs{
			DeleteFunc: func(obj interface{}) {
				pod, isPod := obj.(*v1.Pod)
				if !isPod {
					deletedState, ok := obj.(cache.DeletedFinalStateUnknown)
					if !ok {
						klog.Errorf("Received unexpected object: %v", obj)
						return
					}
					pod, ok = deletedState.Obj.(*v1.Pod)
					if !ok {
						klog.Errorf("DeletedFinalStateUnknown contained non-Pod object: %v", deletedState.Obj)
						return
					}
				}
				service.enqueueIngressForPodIfNeeded(pod)
			},
		},
	)
}

func (service *taskQueueClusterService) tkeserviceConfigEventHandler() {
	service.TkeServiceConfigInformer().Informer().AddEventHandler(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				tkeServiceConfig, isTkeServiceConfig := cur.(*tkeserviceapi.TkeServiceConfig)
				if !isTkeServiceConfig {
					klog.Infof("Controller: Add event contained non-TkeServiceConfig object: %v", cur)
					return
				}
				service.enqueueIngressForTkeServiceConfig(tkeServiceConfig)
			},
			UpdateFunc: func(old, cur interface{}) {
				tkeServiceConfigOld, ok1 := old.(*tkeserviceapi.TkeServiceConfig)
				tkeServiceConfigNew, ok2 := cur.(*tkeserviceapi.TkeServiceConfig)
				if !ok1 || !ok2 {
					klog.Infof("Controller: Add event contained non-TkeServiceConfig old object: %v, cur object: %v", old, cur)
					return
				}
				if !reflect.DeepEqual(tkeServiceConfigOld.Spec, tkeServiceConfigNew.Spec) {
					klog.Infof("controller: Update event, tkeServiceConfig [%s/%s]\n", tkeServiceConfigNew.Namespace, tkeServiceConfigNew.Name)
					service.enqueueIngressForTkeServiceConfig(tkeServiceConfigNew)
				}
			},
			DeleteFunc: func(cur interface{}) {
				tkeServiceConfig, isTkeServiceConfig := cur.(*tkeserviceapi.TkeServiceConfig)
				if !isTkeServiceConfig {
					deletedState, ok := cur.(cache.DeletedFinalStateUnknown)
					if !ok {
						klog.Errorf("Received unexpected object: %v", cur)
						return
					}
					tkeServiceConfig, ok = deletedState.Obj.(*tkeserviceapi.TkeServiceConfig)
					if !ok {
						klog.Errorf("DeletedFinalStateUnknown contained non-TkeServiceConfig object: %v", deletedState.Obj)
						return
					}
				}
				service.enqueueIngressForTkeServiceConfig(tkeServiceConfig)
			},
		},
	)
}

func (service *taskQueueClusterService) multiClusterIngressEventHandler() {
	service.MultiClusterIngressInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				multiClusterIngress, isMultiClusterIngress := cur.(*multiClusteringress.MultiClusterIngress)
				if !isMultiClusterIngress {
					klog.Infof("Controller: Add event contained non-MultiClusterIngress object: %v", cur)
					return
				}
				service.enqueueMultiClusterIngress(multiClusterIngress)
			},
			UpdateFunc: func(old, cur interface{}) {
				multiClusterIngressOld, ok1 := old.(*multiClusteringress.MultiClusterIngress)
				multiClusterIngressNew, ok2 := cur.(*multiClusteringress.MultiClusterIngress)
				if !ok1 || !ok2 {
					klog.Infof("Controller: Add event contained non-MultiClusterIngress old object: %v, cur object: %v", old, cur)
					return
				}
				if !reflect.DeepEqual(multiClusterIngressOld, multiClusterIngressNew) {
					klog.Infof("controller: Update event, multiClusterIngress [%s/%s]\n", multiClusterIngressNew.Namespace, multiClusterIngressNew.Name)
					service.enqueueMultiClusterIngress(multiClusterIngressNew)
				}
			},
			DeleteFunc: func(cur interface{}) {
				multiClusterIngress, isMultiClusterIngress := cur.(*multiClusteringress.MultiClusterIngress)
				if !isMultiClusterIngress {
					deletedState, ok := cur.(cache.DeletedFinalStateUnknown)
					if !ok {
						klog.Errorf("Received unexpected object: %v", cur)
						return
					}
					multiClusterIngress, ok = deletedState.Obj.(*multiClusteringress.MultiClusterIngress)
					if !ok {
						klog.Errorf("DeletedFinalStateUnknown contained non-MultiClusterIngress object: %v", deletedState.Obj)
						return
					}
				}
				service.enqueueMultiClusterIngress(multiClusterIngress)
			},
		},
		serviceSyncPeriod,
	)
}

func (service *taskQueueClusterService) enqueueIngressForService(svc *v1.Service) {
	ingresses, err := service.listIngresses(svc.Namespace, labels.Everything()) // kateway 这里指定了ns，合理
	if err != nil {
		klog.Errorf("error service %v. enqueueIngressForService %v.", utils.ServiceName(svc), err.Error())
		return
	}

	changed := false
	for _, ingress := range ingresses {
		if utils.IsQCLOUDIngress(ingress) {
		outer:
			for _, rule := range ingress.Rules() {
				for _, p := range rule.HTTPPaths {
					if p.Backend.ServiceName == svc.Name {
						service.enqueue(ingress)
						changed = true
						break outer
					}
				}
			}
		}
	}

	if !changed {
		klog.V(4).Infof("ignoring service %v", utils.ServiceName(svc))
		return
	}
}

func nodeStatusChanged(old, cur *v1.Node) bool {
	return nodeStatusReady(old) != nodeStatusReady(cur)
}

func nodeDeletionChanged(old, cur *v1.Node) bool {
	if old.DeletionTimestamp != nil && cur.DeletionTimestamp != nil {
		return !old.DeletionTimestamp.Equal(cur.DeletionTimestamp)
	}
	if old.DeletionTimestamp != nil || cur.DeletionTimestamp != nil {
		return true
	}
	return false
}

func nodeLabelChanged(old, cur *v1.Node) bool {
	return !reflect.DeepEqual(old.Labels, cur.Labels)
}

func nodeFinalizerChanged(old, cur *v1.Node) bool {
	return types.NewNode(old).HasWaitFinalizer() != types.NewNode(cur).HasWaitFinalizer()
}

func nodeStatusReady(node *v1.Node) bool {
	if node.Labels != nil {
		if value, exist := node.Labels["node.kubernetes.io/exclude-from-external-load-balancers"]; exist && value == "true" {
			return false
		}
	}
	for i := range node.Status.Conditions {
		condition := &node.Status.Conditions[i]
		if condition.Type == v1.NodeReady {
			return condition.Status == v1.ConditionTrue
		}
	}
	return false
}

func (service *taskQueueClusterService) enqueueForNode(node *v1.Node) {
	ready := nodeStatusReady(node)
	if ready { // Node Ready情况
		if _, exist := nodestatus.NodeStatusMapInstance.Get(node.Name); exist {
			nodestatus.NodeStatusMapInstance.Delete(node.Name)
		}
		service.enqueueIngressForNode(node)
	} else {
		if _, exist := nodestatus.NodeStatusMapInstance.Get(node.Name); !exist {
			nodestatus.NodeStatusMapInstance.Add(node.Name, &nodestatus.NodeStatus{
				Node: node,
				Time: time.Now(),
			})
		}
	}
}

func (service *taskQueueClusterService) startWatchNode() {
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				nodeStatusList := make([]*nodestatus.NodeStatus, 0)
				nodestatus.NodeStatusMapInstance.Each(func(name string, nodeStatus *nodestatus.NodeStatus) {
					if time.Now().After(nodeStatus.Time.Add(1 * time.Minute)) {
						nodeStatusList = append(nodeStatusList, nodeStatus)
					}
				})

				for _, nodeStatus := range nodeStatusList {
					nodestatus.NodeStatusMapInstance.Delete(nodeStatus.Node.Name)
					service.enqueueIngressForNode(nodeStatus.Node)
				}
			case <-wait.NeverStop:
				return
			}
		}
	}()
}

func (service *taskQueueClusterService) enqueueIngressForNode(node *v1.Node) {
	ingresses, err := service.listIngresses(v1.NamespaceAll, labels.Everything())
	if err != nil {
		klog.Errorf("error node %v. enqueueIngressForNode %v.", node.Name, err.Error())
		return
	}
	for _, ingress := range ingresses {
		if utils.IsQCLOUDIngress(ingress) {
			service.enqueueWithWeight(ingress, 2)
		}
	}
}

func (service *taskQueueClusterService) enqueueIngressForNodeLabel(node *v1.Node) {
	ingresses, err := service.listIngresses(v1.NamespaceAll, labels.Everything())
	if err != nil {
		klog.Errorf("error node %v. enqueueIngressForNode %v.", node.Name, err.Error())
		return
	}
	for _, ingress := range ingresses {
		if utils.IsQCLOUDIngress(ingress) {
			serviceNameMap := make(map[string]bool)
			// if ingress.Spec.Backend != nil {
			//	if ingress.Spec.Backend.ServiceName() != "" {
			//		serviceNameMap[ingress.Spec.Backend.ServiceName()] = true
			//	}
			// }
			for _, rule := range ingress.Rules() {
				for _, path := range rule.HTTPPaths {
					if path.Backend.ServiceName != "" {
						serviceNameMap[path.Backend.ServiceName] = true
					}
				}
			}

			for serviceName, _ := range serviceNameMap {
				useLabelSelector, err := service.IsServiceUseBackendsListLabel(ingress.Namespace(), serviceName)
				if err != nil {
					klog.Errorf("error node %v. enqueueIngressForNode %v.", utils.NodeName(node), err.Error())
					return
				}
				if useLabelSelector {
					service.enqueue(ingress)
					break
				}
			}
		}
	}
}

func (service *taskQueueClusterService) IsServiceUseBackendsListLabel(namespace string, serviceName string) (bool, error) {
	svc, err := service.ServiceLister().Services(namespace).Get(serviceName)
	if err != nil {
		if errors.IsNotFound(err) {
			return false, nil
		}
		return false, err
	}

	if _, exist := utils.GetBackendsListLabel(svc); exist {
		return true, nil
	}
	return false, nil
}

// enqueueIngressForService enqueues all the Ingress' for a Service.
func (service *taskQueueClusterService) enqueueIngressForEndpoint(endpoints *v1.Endpoints) {
	ingresses, err := service.listIngresses(endpoints.Namespace, labels.Everything())
	if err != nil {
		klog.Errorf("error endpoints %v. enqueueIngressForService %v.", utils.EndpointsName(endpoints), err.Error())
		return
	}

	changed := false
	for _, ingress := range ingresses {
		if utils.IsQCLOUDIngress(ingress) {
		outer:
			for _, rule := range ingress.Rules() {
				for _, p := range rule.HTTPPaths {
					if p.Backend.ServiceName == endpoints.Name {
						service.enqueue(ingress) // kateway 可能重复，不过实现里有调用 Forget
						changed = true
						break outer
					}
				}
			}
		}
	}

	if !changed {
		return
	}
}

// enqueueIngressForSecret enqueues all the Ingress' for a Secret.
func (service *taskQueueClusterService) enqueueIngressForSecret(secret *v1.Secret) {
	ingresses, err := service.listIngresses(secret.Namespace, labels.Everything())
	if err != nil {
		klog.Errorf("error secret %v. enqueueIngressForService %v.", utils.SecretName(secret), err.Error())
		return
	}

	changed := false
	for _, ing := range ingresses {
		if utils.IsQCLOUDIngress(ing) {
			ingressTLSWrappers := ing.TLS()
			if ing.Namespace() == secret.Namespace && ingressTLSWrappers != nil && len(ingressTLSWrappers) != 0 {
				for _, ingressTLSWrapper := range ingressTLSWrappers {
					if ingressTLSWrapper.SecretName == secret.Name {
						service.enqueue(ing)
						changed = true
					}
				}
			}
		}
	}

	if config.Global.EnableMultiClusterIngress {
		multiClusterIngresses, err := service.MultiClusterIngressLister().MultiClusterIngresses(secret.Namespace).List(labels.Everything())
		if err != nil {
			klog.Errorf("error secret %v. enqueueIngressForSecret %v.", utils.SecretName(secret), err.Error())
		}
		for _, mci := range multiClusterIngresses {
			ingressWrapper := ingress_wrapper.NewIngressWrapperMultiCluster(mci)
			if utils.IsQCLOUDIngress(ingressWrapper) {
				ingressTLSWrappers := ingressWrapper.TLS()
				if ingressWrapper.Namespace() == secret.Namespace && ingressTLSWrappers != nil && len(ingressTLSWrappers) != 0 {
					for _, ingressTLSWrapper := range ingressTLSWrappers {
						if ingressTLSWrapper.SecretName == secret.Name {
							service.enqueueMultiClusterIngress(mci)
							changed = true
						}
					}
				}
			}
		}
	}

	if !changed {
		klog.V(4).Infof("ignoring secret %v", utils.SecretName(secret))
		return
	}
}

func (service *taskQueueClusterService) enqueueIngressForPodIfNeeded(pod *v1.Pod) {
	ings, _ := service.listIngresses(pod.Namespace, labels.Everything())
	for _, ing := range ings {
		if !utils.IsQCLOUDIngress(ing) {
			continue
		}
		if IsPodSelectedByGraceIngress(pod, ing) {
			service.enqueue(ing)
		}
	}
}

func IsPodSelectedByGraceIngress(pod *v1.Pod, ingress types.Ingress) bool {
	if ingress.Type() == types.CoreIngress {
		for _, rule := range ingress.Rules() {
			for _, p := range rule.HTTPPaths {
				svc, err := cluster_service.Instance.ServiceLister().Services(ingress.Namespace()).Get(p.Backend.ServiceName)
				if err != nil {
					continue
				}
				ep, err := cluster_service.Instance.EndpointsLister().Endpoints(ingress.Namespace()).Get(p.Backend.ServiceName)
				if err != nil {
					continue
				}
				if svc.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal ||
					service2.IsPodClbReachable(context.TODO(), pod) {
					if IsPodSelectByService(svc, ep, pod) {
						klog.Infof("enqueue ingress/service: [%s/%s] for pod %s deletion", ingress.Name(), svc.Name, pod.Name)
						return true
					}
				}
			}
		}
	}
	return false
}

func IsPodSelectByService(service *v1.Service, endpoints *v1.Endpoints, pod *v1.Pod) bool {
	podLabels := labels.Set(pod.Labels)
	if len(service.Spec.Selector) != 0 {
		if service.Namespace == pod.Namespace {
			serviceSelector := labels.SelectorFromSet(service.Spec.Selector)
			if serviceSelector.Matches(podLabels) {
				return true
			}
		}
	} else { // Without Selector类型的Service. 参考 https://kubernetes.io/docs/concepts/services-networking/service/#services-without-selectors
		for _, subset := range endpoints.Subsets {
			for _, address := range append(subset.Addresses, subset.NotReadyAddresses...) {
				if address.IP == pod.Status.PodIP {
					return true
				}
			}
		}
	}
	return false
}

func (service *taskQueueClusterService) enqueueIngressForTkeServiceConfig(tkeServiceConfig *tkeserviceapi.TkeServiceConfig) {
	ingresses, err := service.listIngresses(tkeServiceConfig.Namespace, labels.Everything())
	if err != nil {
		klog.Infof("controller: can not get ingress for tkeServiceConfig[%s/%s], skip sync tkeServiceConfig.\n", tkeServiceConfig.Namespace, tkeServiceConfig.Name)
		return
	}
	for _, ingress := range ingresses {
		config, err := utils.GetTkeServiceConfig(ingress)
		if err != nil {
			continue
		}

		if config == tkeServiceConfig.Name {
			service.enqueue(ingress)
		}
	}

	if config.Global.EnableMultiClusterIngress {
		multiClusterIngresses, err := service.MultiClusterIngressLister().MultiClusterIngresses(tkeServiceConfig.Namespace).List(labels.Everything())
		if err != nil {
			klog.Infof("controller: can not get cached multiClusterIngress for tkeServiceConfig[%s/%s], skip sync tkeServiceConfig.\n", tkeServiceConfig.Namespace, tkeServiceConfig.Name)
			return
		}
		for _, mci := range multiClusterIngresses {
			ingressWrapper := ingress_wrapper.NewIngressWrapperMultiCluster(mci)
			if utils.IsQCLOUDIngress(ingressWrapper) {
				config, err := utils.GetTkeServiceConfig(ingressWrapper)
				if err != nil {
					continue
				}

				if config == tkeServiceConfig.Name {
					service.enqueueMultiClusterIngress(mci)
				}
			}
		}
	}
}

func (service *taskQueueClusterService) Enqueue(ing types.Ingress) {
	service.enqueue(ing)
}

func (service *taskQueueClusterService) enqueue(ing types.Ingress) {
	service.enqueueWithWeight(ing, 1)
}

func (service *taskQueueClusterService) enqueueWithWeight(ing types.Ingress, weight int) {
	if !service.silent {
		cluster_service.QueueServiceInstance.IngressQueue().Enqueue(taskqueue.Item{
			Data:   utils.IngressName(ing),
			Weight: weight,
		})
	}
}

func (service *taskQueueClusterService) EnqueueSecret(name string) {
	service.enqueueSecretWithWeight(name, 1)
}

func (service *taskQueueClusterService) enqueueSecretWithWeight(name string, weight int) {
	if !service.silent {
		cluster_service.QueueServiceInstance.SecretQueue().Enqueue(
			taskqueue.Item{
				Data:   name,
				Weight: weight,
			})
	}
}

func (service *taskQueueClusterService) enqueueMultiClusterIngress(multiClusterIngress *multiClusteringress.MultiClusterIngress) {
	service.enqueueMultiClusterIngressWithWeight(multiClusterIngress, 1)
}

// kateway(bitliu[14]): 创建 mcs 的 key 并入 MCS 的队列
func (service *taskQueueClusterService) enqueueMultiClusterIngressWithWeight(multiClusterIngress *multiClusteringress.MultiClusterIngress, weight int) {
	if !service.silent {
		ingressWrapper := ingress_wrapper.NewIngressWrapperMultiCluster(multiClusterIngress)
		cluster_service.QueueServiceInstance.MultiClusterIngressQueue().Enqueue(taskqueue.Item{
			Data:   utils.IngressName(ingressWrapper),
			Weight: weight,
		})
	}
}

func ensureNodeGracefulDeletionManagerState(value string) error {
	expectEnabled, err := strconv.ParseBool(value)
	if err != nil {
		return err
	}
	if cluster_service.QueueServiceInstance != nil {
		err = cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnsureQueueState(expectEnabled)
		if err != nil {
			return err
		}
	}

	if expectEnabled {
		cluster_service.Instance.Enable(featuregates.NodeGracefulDeletion)
	} else {
		cluster_service.Instance.Disable(featuregates.NodeGracefulDeletion)
	}
	return nil
}
