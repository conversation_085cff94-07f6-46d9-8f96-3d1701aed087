package cluster

import (
	"fmt"
	"time"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/ingress/ingress_wrapper"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/tencentapi/dryrun"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/cluster/nodestatus"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

type taskTraversalClusterService struct {
	basic
}

func newTaskTraversalClusterService(clusterService cluster.Interface) (*taskTraversalClusterService, error) {
	nodestatus.InitNodeStatusMap()

	basicClusterService := newBasic(clusterService)
	return &taskTraversalClusterService{
		basic: *basicClusterService,
	}, nil
}

func (service *taskTraversalClusterService) Run(stopCh <-chan struct{}) {
	// 启动Informer
	service.basic.Run(stopCh)

	ingresses, err := service.listIngresses(v1.NamespaceAll, labels.Everything())
	if err != nil {
		klog.Infof("ingress ingress lister error. %v", err)
		return
	}

	cluster_service.QueueServiceInstance.IngressQueue().Run()
	for _, ingress := range ingresses {
		klog.Infof("enqueue %s", utils.IngressName(ingress))
		cluster_service.QueueServiceInstance.IngressQueue().Enqueue(taskqueue.Item{
			Data:   utils.IngressName(ingress),
			Weight: 1,
		})
	}
	time.Sleep(1 * time.Second) // Wait For Worker Start. If Not Sleep, Worker Probably Stop Immediately.
	cluster_service.QueueServiceInstance.IngressQueue().Shutdown()

	if config.Global.EnableMultiClusterIngress {
		// 检查 MultiClusterIngress 资源
		multiClusterIngresses, err := service.MultiClusterIngressLister().List(labels.Everything())
		if err != nil {
			klog.Infof("multi cluster ingress lister error. %v", err)
			return
		}
		cluster_service.QueueServiceInstance.MultiClusterIngressQueue().Run()
		for _, multiClusterIngress := range multiClusterIngresses {
			ingressWrapper := ingress_wrapper.NewIngressWrapperMultiCluster(multiClusterIngress)
			klog.Infof("enqueue multiClusterIngress %s", utils.IngressName(ingressWrapper))
			cluster_service.QueueServiceInstance.MultiClusterIngressQueue().Enqueue(taskqueue.Item{
				Data:   utils.IngressName(ingressWrapper),
				Weight: 1,
			})
		}
		time.Sleep(1 * time.Second) // Wait For Worker Start. If Not Sleep, Worker Probably Stop Immediately.
		cluster_service.QueueServiceInstance.MultiClusterIngressQueue().Shutdown()
	}

	fmt.Printf("Mock Summary: finish mock with %d error\n", len(dryrun.Records))
}
