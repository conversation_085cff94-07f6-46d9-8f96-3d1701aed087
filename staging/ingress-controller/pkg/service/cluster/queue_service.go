package cluster

import (
	"time"

	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/tencentapi"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

type DefaultQueueService struct {
	ingressQueue                *taskqueue.TaskQueue
	multiClusterIngressQueue    *taskqueue.TaskQueue
	secretQueue                 *taskqueue.TaskQueue
	nodeGracefulDeletionManager *services.NodeGracefulDeletionManager // 处理绑定或者解绑成功的node的finalizer
}

func NewSecretCertService() services.SecretCertService {
	return services.SecretCertService{
		MockRun:                      config.Global.DryRun,
		OwnerUin:                     config.Global.OwnerUin,
		VpcID:                        config.Global.VPCID,
		RegionName:                   config.Global.Region,
		ClusterName:                  config.Global.ClusterName,
		KubeClient:                   cluster_service.Instance.KubeClient(),
		SecretLister:                 cluster_service.Instance.SecretLister(),
		DescribeCertificatesAPI:      tencentapi.Instance.DescribeCertificates,
		DescribeCertificateDetailAPI: tencentapi.Instance.DescribeCertificateDetail,
		UpdateCertificateAPI:         tencentapi.Instance.UploadCertificate,
	}
}

func InitQueueServiceInstance(sync func(string) []error, syncMultiClusterIngress func(string) []error, workers int) {
	if cluster_service.QueueServiceInstance == nil {
		cluster_service.QueueServiceInstanceLock.Lock()
		defer cluster_service.QueueServiceInstanceLock.Unlock()

		if cluster_service.QueueServiceInstance == nil {
			cluster_service.QueueServiceInstance = newDefaultQueueService(sync, syncMultiClusterIngress, workers)
		}
	}
}

func buildNodeGracefulDeletionManagerParam() services.NodeGracefulDeletionManagerParam {
	return services.NodeGracefulDeletionManagerParam{
		MockRun:                    config.Global.DryRun,
		VpcID:                      config.Global.VPCID,
		RegionName:                 config.Global.Region,
		KubeClient:                 cluster_service.Instance.KubeClient(),
		CLBRsProtection:            utils.CLBRsProtection,
		DescribeLBListenersAPI:     tencentapi.Instance.DescribeLBListeners,
		LoadBalancerResourceLister: cluster_service.Instance.LoadBalancerResourceLister(),
		LoadBalancerResourceClient: *cluster_service.Instance.LoadBalancerResourceClient(),
		NodeLister:                 cluster_service.Instance.NodeLister(),
		ManagerType:                services.IngManager,
	}
}

func newDefaultQueueService(sync func(string) []error, syncMultiClusterIngress func(string) []error, workers int) DefaultQueueService {
	ingressQueue := taskqueue.NewTaskQueueRateLimit(sync, workers, "ingress", 5*time.Second, 0, taskqueue.NewMaxOfRateLimiter(
		taskqueue.NewItemExponentialFailureRateLimiter(250*time.Millisecond, 15*time.Minute),
	))

	multiClusterIngressQueue := taskqueue.NewTaskQueueRateLimit(syncMultiClusterIngress, workers, "multiclusteringress", 5*time.Second, 0, taskqueue.NewMaxOfRateLimiter(
		taskqueue.NewItemExponentialFailureRateLimiter(250*time.Millisecond, 15*time.Minute),
	))

	// 处理tls secret的taskqueue，具体处理逻辑解耦到pkg中，方便以后service-controller复用
	secretQueue := NewSecretQueue(workers)

	nodeGracefulDeletionManager := services.NewNodeGracefulDeletionManager(buildNodeGracefulDeletionManagerParam())
	return DefaultQueueService{
		ingressQueue:                ingressQueue,
		multiClusterIngressQueue:    multiClusterIngressQueue,
		nodeGracefulDeletionManager: nodeGracefulDeletionManager,
		secretQueue:                 secretQueue,
	}
}

func (service DefaultQueueService) IngressQueue() *taskqueue.TaskQueue {
	return service.ingressQueue
}

func (service DefaultQueueService) MultiClusterIngressQueue() *taskqueue.TaskQueue {
	return service.multiClusterIngressQueue
}

func (service DefaultQueueService) NodeGracefulDeletionManager() *services.NodeGracefulDeletionManager {
	return service.nodeGracefulDeletionManager
}

func (service DefaultQueueService) SecretQueue() *taskqueue.TaskQueue {
	return service.secretQueue
}

func NewSecretQueue(workers int) *taskqueue.TaskQueue {
	crtService := NewSecretCertService()

	return taskqueue.NewTaskQueueRateLimit(crtService.SyncSecret, workers, "tlssecret", 5*time.Second, 0, taskqueue.NewMaxOfRateLimiter(
		taskqueue.NewItemExponentialFailureRateLimiter(250*time.Millisecond, 15*time.Minute),
	))
}
