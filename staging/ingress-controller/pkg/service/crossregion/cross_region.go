package crossregion

import (
	"context"
	"strconv"
	"sync"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	vpc "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/vpc/v20170312"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloudctx"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
)

var (
	CrossRegionServiceInstanceLock                    = new(sync.Mutex)
	CrossRegionServiceInstance     CrossRegionService = nil
)

const SYNC_CCR_FREQUENCY = 15 * time.Minute

const SYNC_REGION_FREQUENCY = 24 * time.Hour

const SYNC_CCN_PAGESIZE = 100

type CrossRegionService interface {
	CheckIngressRegion(ingress types.Ingress) bool
	CheckCCNInstance(region string, vpcId string) bool
}

// TODO misakazhou 检查用户输入非法RegionID的可能
type defaultCrossRegionService struct {
	// 合法的Region列表
	ValidRegion map[string]bool

	// CCN信息记录
	CCNId  *string
	CCNMap map[string]*vpc.CcnAttachedInstance // VPCId To CcnAttachedInstance
}

func (this *defaultCrossRegionService) CheckIngressRegion(ingress types.Ingress) bool {
	// 检查跨地域属性配置的正确性
	if regionId, existAnnotation := utils.GetCrossRegionId(ingress); existAnnotation {
		if this.ValidRegion != nil {
			if _, exist := this.ValidRegion[regionId]; !exist {
				return false
			}
		}
	}
	return true
}

func (this *defaultCrossRegionService) CheckCCNInstance(region string, vpcId string) bool {
	if instance, exist := this.CCNMap[vpcId]; exist {
		// 检查项
		// 1. VPC资源所属地域
		// 2. VPC资源在云联网中的绑定状态
		// 3. VPC资源不存在跨账户绑定的情况
		if *instance.InstanceRegion == region && *instance.State == "ACTIVE" && *instance.InstanceUin == strconv.FormatInt(config.Global.OwnerUin, 10) {
			return true
		}
	}
	return false
}

func (this *defaultCrossRegionService) SyncCcnAttachedInstances(stopChan <-chan struct{}) {
	cron := time.NewTicker(SYNC_CCR_FREQUENCY)
	defer cron.Stop()
	for {
		select {
		case <-stopChan:
			return
		case <-cron.C:
			this.syncCcnAttachedInstances(context.Background())
		}
	}
}

func (this *defaultCrossRegionService) syncCcnAttachedInstances(ctx context.Context) {
	if this.CCNId == nil {
		this.CCNId = getCcnAttachedInstancesByCluster(ctx)
	}
	if this.CCNId != nil {
		instancesMap, err := getCcnAttachedInstancesByCcnId(ctx, *this.CCNId)
		if err != nil {
			return
		}

		if _, exist := instancesMap[config.Global.VPCID]; !exist { // 当前CCN实例中不存在集群所在VPC，说明实例不属于当前VPC了。
			this.CCNMap = make(map[string]*vpc.CcnAttachedInstance)
			newCCNId := getCcnAttachedInstancesByCluster(ctx)
			if newCCNId != nil && *newCCNId != *this.CCNId { // 获取到新的CCNId和当前记录的CCNId不同的时候，再做一次同步
				this.CCNId = newCCNId
				this.syncCcnAttachedInstances(ctx)
			}
			return
		}
		this.CCNMap = instancesMap
	}
}

func (this *defaultCrossRegionService) SyncRegions(stopChan <-chan struct{}) {
	cron := time.NewTicker(SYNC_REGION_FREQUENCY)
	defer cron.Stop()
	for {
		select {
		case <-stopChan:
			return
		case <-cron.C:
			this.syncRegions(context.Background())
		}
	}
}

func (this *defaultCrossRegionService) syncRegions(ctx context.Context) {
	regions, err := DescribeRegion(ctx)
	if err != nil || regions == nil {
		klog.Errorf("can't get region list from clb api.")
		return
	}
	if config.Global.ValidRegions == nil {
		config.Global.ValidRegions = make(map[string]bool)
	}
	for _, region := range regions {
		this.ValidRegion[*region.Region] = true
		config.Global.ValidRegions[*region.Region] = true
	}
}

func DescribeRegion(ctx context.Context) ([]*clbinternal.RegionSet, error) {
	request := clbinternal.NewDescribeRegionsRequest()
	request.AllRegion = common.BoolPtr(true)

	if rsp, err := tencentapi.Instance.DescribeRegions(cloudctx.From(ctx, nil, config.Global.Region), request); err != nil {
		return nil, err
	} else {
		return rsp.Response.RegionSet, nil
	}
}

func getCcnAttachedInstancesByCluster(ctx context.Context) *string {
	request := vpc.NewDescribeCcnAttachedInstancesRequest()
	request.Filters = []*vpc.Filter{
		buildFilter("instance-type", "VPC"),
		buildFilter("instance-id", config.Global.VPCID),
		buildFilter("instance-region", config.Global.Region),
	}

	response, err := tencentapi.Instance.DescribeCcnAttachedInstances(cloudctx.From(ctx, nil, config.Global.Region), request)
	if err != nil {
		return nil
	}
	if *response.Response.TotalCount == 0 {
		return nil
	}
	return response.Response.InstanceSet[0].CcnId
}

func getCcnAttachedInstancesByCcnId(ctx context.Context, ccnId string) (map[string]*vpc.CcnAttachedInstance, error) {
	request := vpc.NewDescribeCcnAttachedInstancesRequest()
	request.Offset = common.Uint64Ptr(0)
	request.Limit = common.Uint64Ptr(SYNC_CCN_PAGESIZE)
	request.Filters = []*vpc.Filter{
		buildFilter("ccn-id", ccnId),
		buildFilter("instance-type", "VPC"),
	}

	result := make(map[string]*vpc.CcnAttachedInstance)

	response, err := tencentapi.Instance.DescribeCcnAttachedInstances(cloudctx.From(ctx, nil, config.Global.Region), request)
	if err != nil {
		return nil, err
	}
	for index, instance := range response.Response.InstanceSet {
		result[*instance.InstanceId] = response.Response.InstanceSet[index]
	}
	for *request.Offset+SYNC_CCN_PAGESIZE < *response.Response.TotalCount {
		request.Offset = common.Uint64Ptr(*request.Offset + SYNC_CCN_PAGESIZE)
		response, err = tencentapi.Instance.DescribeCcnAttachedInstances(cloudctx.From(ctx, nil, config.Global.Region), request)
		if err != nil {
			return nil, err
		}
		for index, instance := range response.Response.InstanceSet {
			result[*instance.InstanceId] = response.Response.InstanceSet[index]
		}
	}
	return result, nil
}

func buildFilter(key string, value string) *vpc.Filter {
	return &vpc.Filter{
		Name: common.StringPtr(key),
		Values: []*string{
			common.StringPtr(value),
		},
	}
}

func InitCrossRegionService() {
	if CrossRegionServiceInstance == nil {
		CrossRegionServiceInstanceLock.Lock()
		defer CrossRegionServiceInstanceLock.Unlock()

		if CrossRegionServiceInstance == nil {
			crossRegionService := &defaultCrossRegionService{
				CCNId:  getCcnAttachedInstancesByCluster(context.Background()),
				CCNMap: map[string]*vpc.CcnAttachedInstance{},
			}
			crossRegionService.syncCcnAttachedInstances(context.Background())
			go crossRegionService.SyncCcnAttachedInstances(wait.NeverStop)

			CrossRegionServiceInstance = crossRegionService
		}
	}
	return
}
