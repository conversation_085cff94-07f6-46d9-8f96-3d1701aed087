package service

import (
	"context"
	"encoding/json"
	"strings"
	"sync"

	"github.com/samber/lo"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/controllercm"

	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

var (
	ClusterInfoServiceInstance ClusterInfoService = nil
	once                       sync.Once
)

const (
	TKECNIMultusConf = "00-multus.conf"

	TKECNIConfConfigMap   = "tke-cni-agent-conf"
	DelegatesTKEBridge    = "tke-bridge"
	DelegatesTKERouteENI  = "tke-route-eni"
	DelegatesTKEDirectENI = "tke-direct-eni"
	DelegatesTKESubENI    = "tke-sub-eni" // eni trunking https://iwiki.woa.com/p/4008949351

)

type ClusterInfoService interface {
	NeedSupportReadinessGate() bool
	SupportFieldSelector() bool
	SupportExtensionsGroup() bool
	IsDefaultTKERouteENI(ctx context.Context, forceUpdate bool) bool
	IsDefaultTKESubENI(ctx context.Context, forceUpdate bool) bool
	IsDefaultTKEDirectENI(ctx context.Context, forceUpdate bool) bool
	IsGlobalRoute(ctx context.Context, forceUpdate bool) bool
	IsEnableNodeGracefulDeletion(ctx context.Context) bool
}

type defaultClusterInfoService struct {
	supportReadinessGate     *bool
	isSupportExtensionsGroup *bool
	defaultDelegates         *string

	supportGlobalRoute *bool
}

// 初始化 ClusterInfoService
// 集群配置信息主动监听更新。
// 集群的升级并不一定会导致Service Controller的重启
func InitDefaultClusterInfoService() {
	once.Do(func() {
		service := &defaultClusterInfoService{}
		ClusterInfoServiceInstance = service
	})
}

func (service *defaultClusterInfoService) NeedSupportReadinessGate() bool {
	if service.supportReadinessGate == nil {
		service.supportReadinessGate = service.needSupportReadinessGate()
	}
	return *service.supportReadinessGate
}

// 1.12 以上的集群提供直绑Pod的ReadinessGate服务
func (service *defaultClusterInfoService) needSupportReadinessGate() *bool {
	return lo.ToPtr(lo.Must(cluster_service.Instance.CheckVersion(">= 1.12")))
}

func (service *defaultClusterInfoService) SupportFieldSelector() bool {
	if service.supportReadinessGate == nil {
		service.supportReadinessGate = service.supportFieldSelector()
	}
	return *service.supportReadinessGate
}

// 1.12 以上的集群支持 FieldSelector
func (service *defaultClusterInfoService) supportFieldSelector() *bool {
	return lo.ToPtr(lo.Must(cluster_service.Instance.CheckVersion(">= 1.12")))
}

func (service *defaultClusterInfoService) SupportExtensionsGroup() bool {
	if service.isSupportExtensionsGroup == nil {
		service.isSupportExtensionsGroup = service.supportExtensionsGroup()
	}
	return *service.isSupportExtensionsGroup
}

func (service *defaultClusterInfoService) supportExtensionsGroup() *bool {
	return lo.ToPtr(lo.Must(cluster_service.Instance.CheckVersion("< 1.22")))
}

// Docs: https://github.com/qyzhaoxun/multus-cni/blob/master/doc/default-delegates.md
//
//	{
//	  "name": "multus-cni",
//	  "type": "multus",
//	  "kubeconfig": "/root/.kube/config",
//	  "logLevel": "info",
//	  "defaultDelegates": "tke-bridge",
//	  "capabilities": {
//	    "bandwidth": true,
//	    "portMappings": true
//	  }
//	}
type NetConf struct {
	DefaultDelegates string `json:"defaultDelegates"`
}

func (service *defaultClusterInfoService) IsDefaultTKERouteENI(ctx context.Context, forceUpdate bool) bool {
	if forceUpdate { // 强制更新最新的配置
		service.defaultDelegates = nil
	}

	if service.defaultDelegates == nil {
		if typeOfPodNetwork := service.getDefaultPodNetwork(ctx); typeOfPodNetwork != nil {
			service.defaultDelegates = typeOfPodNetwork
		}
	}

	return service.defaultDelegates != nil && *service.defaultDelegates == DelegatesTKERouteENI
}

func (service *defaultClusterInfoService) IsEnableNodeGracefulDeletion(ctx context.Context) bool {
	return false
}

func (service *defaultClusterInfoService) IsGlobalRoute(ctx context.Context, forceUpdate bool) bool {
	if forceUpdate { // 强制更新最新的配置
		service.supportGlobalRoute = nil
	}

	if service.supportGlobalRoute == nil {
		if support := service.getGlobalRoute(ctx); support != nil {
			service.supportGlobalRoute = support
		}
	}

	return service.supportGlobalRoute != nil && *service.supportGlobalRoute == true
}

func (service *defaultClusterInfoService) getGlobalRoute(ctx context.Context) *bool {
	cm, err := cluster_service.Instance.KubeClient().CoreV1().
		ConfigMaps(metav1.NamespaceSystem).Get(ctx, controllercm.ConfigMapService, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return nil
		}
		klog.Warningf("ClusterInfoService Failed to get cm %s/%s. err: %s", metav1.NamespaceSystem, controllercm.ConfigMapService, err.Error())
		return nil
	}

	config, ok := cm.Data[controllercm.KeyEnableGlobalRouteDirectAccess]
	if !ok {
		return lo.ToPtr(false)
	}

	parseBool, err := utils.ParseBool(config)
	if err != nil {
		klog.Warningf("ClusterInfoService Failed to get valid '%s' config in cm %s/%s. err: %s",
			controllercm.KeyEnableGlobalRouteDirectAccess, metav1.NamespaceSystem, controllercm.ConfigMapService, err.Error())
		return nil
	}
	return &parseBool
}

func (service *defaultClusterInfoService) IsDefaultTKEDirectENI(ctx context.Context, forceUpdate bool) bool {
	if forceUpdate { // 强制更新最新的配置
		service.defaultDelegates = nil
	}

	if service.defaultDelegates == nil {
		if support := service.getDefaultPodNetwork(ctx); support != nil {
			service.defaultDelegates = support
		}
	}

	return service.defaultDelegates != nil && *service.defaultDelegates == DelegatesTKEDirectENI
}

func (service *defaultClusterInfoService) IsDefaultTKESubENI(ctx context.Context, forceUpdate bool) bool {
	if forceUpdate { // 强制更新最新的配置
		service.defaultDelegates = nil
	}

	if service.defaultDelegates == nil {
		if support := service.getDefaultPodNetwork(ctx); support != nil {
			service.defaultDelegates = support
		}
	}

	return service.defaultDelegates != nil && *service.defaultDelegates == DelegatesTKESubENI
}

func (service *defaultClusterInfoService) getDefaultPodNetwork(ctx context.Context) *string {
	cm, err := cluster_service.Instance.KubeClient().CoreV1().
		ConfigMaps(metav1.NamespaceSystem).Get(ctx, TKECNIConfConfigMap, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) { // consider tke-route-eni is default cni
			return lo.ToPtr(DelegatesTKERouteENI)
		}
		klog.Warningf("ClusterInfoService Failed to get cm %s/%s. err: %s", metav1.NamespaceSystem, TKECNIConfConfigMap, err.Error())
		return nil
	}

	if config, ok := cm.Data[TKECNIMultusConf]; ok {
		var netConf NetConf
		if err := json.Unmarshal([]byte(config), &netConf); err != nil {
			klog.Warningf("ClusterInfoService Unexpected cm %s/%s. err: %s", metav1.NamespaceSystem, TKECNIConfConfigMap, err.Error())
			return nil
		}
		if strings.Contains(netConf.DefaultDelegates, DelegatesTKERouteENI) {
			return lo.ToPtr(DelegatesTKERouteENI)
		} else if strings.Contains(netConf.DefaultDelegates, DelegatesTKEBridge) {
			return lo.ToPtr(DelegatesTKEBridge)
		} else if strings.Contains(netConf.DefaultDelegates, DelegatesTKEDirectENI) {
			return lo.ToPtr(DelegatesTKEDirectENI)
		} else if strings.Contains(netConf.DefaultDelegates, DelegatesTKESubENI) {
			return lo.ToPtr(DelegatesTKESubENI)
		}
	}
	return nil
}
