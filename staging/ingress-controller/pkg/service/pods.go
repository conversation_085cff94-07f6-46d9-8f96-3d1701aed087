package service

import (
	"context"
	"strings"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	v1 "k8s.io/api/core/v1"

	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
)

const (
	AnnoPodNetworks       = "tke.cloud.tencent.com/networks"
	AnnoNetworksENI       = "tke-route-eni"
	AnnoNetworksDirectENI = "tke-direct-eni"
	AnnoNetworksSubENI    = "tke-sub-eni" // eni trunking https://iwiki.woa.com/p/4008949351
)

func IsPodClbReachable(ctx context.Context, pod *v1.Pod) bool {
	return IsENINetwork(ctx, pod) || ClusterInfoServiceInstance.IsGlobalRoute(ctx, false)
}

func IsENINetwork(ctx context.Context, pod *v1.Pod) bool {
	if config.Global.ClusterSupportDirect {
		return true
	}

	if utils.IsInEKSCluster() {
		return true // For EKS, all Pods IsPodDirectBackend
	}

	return IsTKERouteENI(ctx, pod) || isTKEDirectENI(ctx, pod) || isTKESubENI(ctx, pod) || isTkePODInEKS(pod)
}

// Pod 注解存在时，优先以Pod注解为准。
// Pod 注解不存在时，以集群默认配置为准。
func IsTKERouteENI(ctx context.Context, pod *v1.Pod) bool {
	// qyzhaoxun/multus-cni 参考文档：https://github.com/qyzhaoxun/multus-cni/blob/master/doc/default-delegates.md
	if network, exist := pod.Annotations[AnnoPodNetworks]; exist {
		return strings.Contains(network, AnnoNetworksENI)
	}
	return ClusterInfoServiceInstance.IsDefaultTKERouteENI(ctx, false)
}

func isTKEDirectENI(ctx context.Context, pod *v1.Pod) bool {
	if network, exist := pod.Annotations[AnnoPodNetworks]; exist {
		return strings.Contains(network, AnnoNetworksDirectENI)
	}
	return ClusterInfoServiceInstance.IsDefaultTKEDirectENI(ctx, false)
}

func isTKESubENI(ctx context.Context, pod *v1.Pod) bool {
	if network, exist := pod.Annotations[AnnoPodNetworks]; exist {
		return strings.Contains(network, AnnoNetworksSubENI)
	}
	return ClusterInfoServiceInstance.IsDefaultTKESubENI(ctx, false)
}

func isTkePODInEKS(pod *v1.Pod) bool {
	value, has := pod.Annotations["tke.cloud.tencent.com/pod-type"]
	return has && strings.ToLower(value) == "eklet"
}
