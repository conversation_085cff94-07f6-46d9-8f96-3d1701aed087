package tencent

import (
	"context"
	"time"

	loadBalancerResourcev1alpha1 "git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	"git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"

	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
)

type ContextMeta struct {
	Ingress             types.Ingress
	LoadBalancerContext *LoadBalancerContext
	IngressContext      *IngressContext
	Errors              []error
}

type SyncContext struct {
	*ContextMeta
	ctx context.Context
}

func NewSyncContext(ing types.Ingress) *SyncContext {
	return &SyncContext{
		ContextMeta: &ContextMeta{
			Ingress: ing,
		},
		ctx: context.Background(),
	}
}

func (sc SyncContext) Deadline() (deadline time.Time, ok bool) {
	return sc.ctx.Deadline()
}

func (sc SyncContext) Done() <-chan struct{} {
	return sc.ctx.Done()
}

func (sc SyncContext) Err() error {
	return sc.ctx.Err()
}

func (sc SyncContext) Value(key any) any {
	return sc.ctx.Value(key)
}

func (sc SyncContext) StartSpan(opts ...jaeger.SpanOption) (*jaeger.Span, *SyncContext) {
	dopts := []jaeger.SpanOption{jaeger.WithSkip(2)}
	span, ctx := jaeger.StartSpanFromContext(sc.ctx, append(dopts, opts...)...)
	sc.ctx = ctx
	return span, &sc
}

type LoadBalancerContext struct {
	Ingress              types.Ingress
	LoadBalancerResource *loadBalancerResourcev1alpha1.LoadBalancerResource

	LoadBalancerId   string
	Region           string
	DefaultDomain    *string
	LoadBalancer     *clb.LoadBalancer
	Rewrites         []*clb.RuleOutput
	Listeners        map[string]*clb.Listener
	ListenersBackend map[string]*clb.ListenerBackend
}

func (lbc *LoadBalancerContext) GetLoadBalancer(ctx context.Context) (*clb.LoadBalancer, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if lbc.LoadBalancer == nil {
		if err := lbc.UpdateLoadBalancer(ctx); err != nil {
			return nil, err
		}
	}
	if lbc.LoadBalancer == nil {
		return nil, types.NewError(errcode.LoadBalancerNotExistError, "", utils.IngressName(lbc.Ingress))
	}
	return lbc.LoadBalancer, nil
}

func (lbc *LoadBalancerContext) UpdateLoadBalancer(ctx context.Context) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	request := clb.NewDescribeLoadBalancersRequest()
	request.LoadBalancerIds = []*string{&lbc.LoadBalancerId}
	response, err := tencentapi.Instance.DescribeLoadBalancers(cloudctx.From(ctx, lbc.Ingress, lbc.Region), request)
	if err != nil {
		return err
	}
	if response.Response.TotalCount != nil && *response.Response.TotalCount != 0 && len(response.Response.LoadBalancerSet) != 0 {
		lbc.LoadBalancer = response.Response.LoadBalancerSet[0]

		// 负载均衡的标签不保证实时性、准确性，必须以标签服务的数据为准
		tags, err := GetLoadBalancerTags(ctx, lbc.Ingress, lbc.LoadBalancerId, lbc.Region)
		if err != nil {
			return err
		}
		lbc.LoadBalancer.Tags = utils.ConvertResourceTag(tags)
	}
	return nil
}

func (lbc *LoadBalancerContext) UpdateLoadBalancerTag(ctx context.Context) error {
	if lbc.LoadBalancer == nil {
		return lbc.UpdateLoadBalancer(context.TODO())
	}

	// 负载均衡的标签不保证实时性、准确性，必须以标签服务的数据为准
	tags, err := GetLoadBalancerTags(ctx, lbc.Ingress, lbc.LoadBalancerId, lbc.Region)
	if err != nil {
		return err
	}
	lbc.LoadBalancer.Tags = utils.ConvertResourceTag(tags)
	return nil
}

// kateway CLB 端的监听器
func (lbc *LoadBalancerContext) GetListeners(ctx context.Context) (map[string]*clb.Listener, error) {
	if lbc.Listeners == nil {
		if err := lbc.UpdateListeners(ctx); err != nil {
			return nil, err
		}
	}
	return lbc.Listeners, nil
}

func (lbc *LoadBalancerContext) UpdateListeners(ctx context.Context) error {
	listeners, err := GetLoadBalancerListener(ctx, lbc.Ingress, lbc.LoadBalancerId, lbc.Region)
	if err != nil {
		return err
	}
	lbc.Listeners = make(map[string]*clb.Listener)
	for index, listener := range listeners {
		lbc.Listeners[utils.GetListenerKey(int32(*listener.Port), *listener.Protocol)] = listeners[index]
	}
	return nil
}

func (lbc *LoadBalancerContext) GetListenersBackend(ctx context.Context) (map[string]*clb.ListenerBackend, error) {
	if lbc.ListenersBackend == nil {
		if err := lbc.UpdateListenersBackend(ctx); err != nil {
			return nil, err
		}
	}
	return lbc.ListenersBackend, nil
}

func (lbc *LoadBalancerContext) UpdateListenersBackend(ctx context.Context) error {
	listenersBackend, err := GetTargetInfo(ctx, lbc.Ingress, lbc.LoadBalancerId, lbc.Region)
	if err != nil {
		return err
	}
	lbc.ListenersBackend = make(map[string]*clb.ListenerBackend)
	for index, listenerBackend := range listenersBackend {
		lbc.ListenersBackend[utils.GetListenerKey(int32(*listenerBackend.Port), *listenerBackend.Protocol)] = listenersBackend[index]
	}
	return nil
}

func (lbc *LoadBalancerContext) GetRewrites(ctx context.Context) ([]*clb.RuleOutput, error) {
	if lbc.Rewrites == nil {
		if err := lbc.UpdateRewrites(ctx); err != nil {
			return nil, err
		}
	}
	return lbc.Rewrites, nil
}

func (lbc *LoadBalancerContext) UpdateRewrites(ctx context.Context) error {
	rewrites, err := DescribeRewrite(ctx, lbc.Ingress, lbc.LoadBalancerId, lbc.Region)
	if err != nil {
		return err
	}
	lbc.Rewrites = rewrites
	return nil
}

type IngressContext struct {
	IsRewriteSupport bool
	IngressListeners map[string]*IngressListener // kateway 期望的监听器
}

type IngressListener struct {
	Port                int32
	Protocol            string
	IngressRules        []*utils.Rule
	IngressRewriteRules []*utils.Rule
	L7ListenerConfig    *v1alpha1.L7ListenerConfig
}

func NewIngressListener(port int32, protocol string) *IngressListener {
	return &IngressListener{
		Port:                port,
		Protocol:            protocol,
		IngressRules:        make([]*utils.Rule, 0),
		IngressRewriteRules: make([]*utils.Rule, 0),
	}
}

func (this *IngressListener) Rules() []*utils.Rule {
	result := make([]*utils.Rule, len(this.IngressRules)+len(this.IngressRewriteRules))
	copy(result, this.IngressRules)
	copy(result[len(this.IngressRules):], this.IngressRewriteRules)
	return result
}
