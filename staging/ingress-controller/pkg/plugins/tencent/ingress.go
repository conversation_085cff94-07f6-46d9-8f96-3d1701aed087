package tencent

import (
	"fmt"

	v1 "k8s.io/api/core/v1"
	extensions "k8s.io/api/extensions/v1beta1"
	networking "k8s.io/api/networking/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"

	multiClusteringress "git.woa.com/kateway/multi-cluster-ingress-api/apis/multiclusteringress/v1alpha1"
	"git.woa.com/kateway/pkg/domain/ingress/ingress_wrapper"
	"git.woa.com/kateway/pkg/domain/types"

	"git.woa.com/kateway/tke-ingress-controller/pkg/service"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

func IngressGetter(ingressType types.IngressType, namespace, name string) (types.Ingress, error) {
	switch ingressType {
	case types.CoreIngress:
		return IngressGetWrapper(namespace, name)
	case types.MultiClusterIngress:
		return MultiClusterIngressGetWrapper(namespace, name)
	default:
		return nil, fmt.Errorf("ingress type %s not supported", ingressType)
	}
}

func IngressLister(ingressType types.IngressType, namespace string, selector labels.Selector) ([]types.Ingress, error) {
	switch ingressType {
	case types.CoreIngress:
		return IngressListerWrapper(namespace, selector)
	case types.MultiClusterIngress:
		return MultiClusterIngressListerWrapper(namespace, selector)
	default:
		return nil, fmt.Errorf("ingress type %s not supported", ingressType)
	}
}

func IngressGetWrapper(namespace string, name string) (types.Ingress, error) {
	if service.ClusterInfoServiceInstance.SupportExtensionsGroup() {
		ingress, err := cluster_service.Instance.ExtensionIngressLister().Ingresses(namespace).Get(name)
		if err != nil {
			klog.Infof("Ingress get error for %s/%s. %v\n", namespace, name, err)
			return ingress_wrapper.NewIngressWrapperExtensions(nil), err
		}
		return ingress_wrapper.NewIngressWrapperExtensions(ingress), nil
	} else {
		ingress, err := cluster_service.Instance.NetworkingIngressLister().Ingresses(namespace).Get(name)
		if err != nil {
			klog.Infof("Ingress get error for %s/%s. %v\n", namespace, name, err)
			return ingress_wrapper.NewIngressWrapperNetworking(nil), err
		}
		return ingress_wrapper.NewIngressWrapperNetworking(ingress), nil
	}
}

func IngressListerWrapper(namespace string, selector labels.Selector) ([]types.Ingress, error) {
	var err error
	if service.ClusterInfoServiceInstance.SupportExtensionsGroup() {
		var ingresses []*extensions.Ingress
		if namespace != v1.NamespaceAll {
			if ingresses, err = cluster_service.Instance.ExtensionIngressLister().Ingresses(namespace).List(selector); err != nil {
				klog.Infof("Ingress list error for namespace %s. %v\n", namespace, err)
				return nil, err
			}
		} else {
			if ingresses, err = cluster_service.Instance.ExtensionIngressLister().List(selector); err != nil {
				klog.Infof("Ingress list error. %v\n", err)
				return nil, err
			}
		}
		return ingress_wrapper.NewIngressWrapperExtensionsList(ingresses), nil
	} else {
		var ingresses []*networking.Ingress
		if namespace != v1.NamespaceAll {
			if ingresses, err = cluster_service.Instance.NetworkingIngressLister().Ingresses(namespace).List(selector); err != nil {
				klog.Infof("Ingress list error for namespace %s. %v\n", namespace, err)
				return nil, err
			}
		} else {
			if ingresses, err = cluster_service.Instance.NetworkingIngressLister().List(selector); err != nil {
				klog.Infof("Ingress list error. %v\n", err)
				return nil, err
			}
		}
		return ingress_wrapper.NewIngressWrapperNetworkingList(ingresses), nil
	}
}

func MultiClusterIngressGetWrapper(namespace string, name string) (types.Ingress, error) {
	ingress, err := cluster_service.Instance.MultiClusterIngressLister().MultiClusterIngresses(namespace).Get(name)
	if err != nil {
		klog.Infof("MultiClusterIngress get error for %s/%s. %v\n", namespace, name, err)
		return ingress_wrapper.NewIngressWrapperMultiCluster(nil), err
	}
	return ingress_wrapper.NewIngressWrapperMultiCluster(ingress), nil
}

func MultiClusterIngressListerWrapper(namespace string, selector labels.Selector) ([]types.Ingress, error) {
	var err error

	var ingresses []*multiClusteringress.MultiClusterIngress
	if namespace != v1.NamespaceAll {
		if ingresses, err = cluster_service.Instance.MultiClusterIngressLister().MultiClusterIngresses(namespace).List(selector); err != nil {
			klog.Infof("MultiClusterIngress list error for namespace %s. %v\n", namespace, err)
			return nil, err
		}
	} else {
		if ingresses, err = cluster_service.Instance.MultiClusterIngressLister().List(selector); err != nil {
			klog.Infof("MultiClusterIngress list error. %v\n", err)
			return nil, err
		}
	}
	return ingress_wrapper.NewIngressWrapperMultiClusterList(ingresses), nil
}
