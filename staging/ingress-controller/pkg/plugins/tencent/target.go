package tencent

import (
	"context"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	v1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
)

func EnsureLoadBalancerTargetsDeleted(sc *SyncContext, loadBalancerContext *LoadBalancerContext, ingress types.Ingress) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	loadBalancer, err := loadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		return err
	}

	listeners, err := GetCurrentIngressListeners(sc, loadBalancerContext)
	if err != nil {
		return err
	}

	listenerBackends, err := loadBalancerContext.GetListenersBackend(sc)
	if err != nil {
		return err
	}

	// 由于可能超限，所以分两批
	batchDeregiterTargets := make([]*clb.BatchTarget, 0)
	for _, listener := range listeners {
		backend := listenerBackends[utils.GetListenerKey(int32(*listener.Port), *listener.Protocol)]
		batchDeregiterTargets = append(batchDeregiterTargets, GetCurrentTarget(ingress, *backend.ListenerId, nil, backend.Targets)...)
		for _, rule := range backend.Rules {
			batchDeregiterTargets = append(batchDeregiterTargets, GetCurrentTarget(ingress, *backend.ListenerId, rule.LocationId, rule.Targets)...)
		}
	}

	// 所有解绑或绑定成功的targets，可能需要修改相应节点的保护finalizer
	targetsToEnqueue := make([]*clb.BatchTarget, 0)
	var nodesByID map[string]*v1.Node
	if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) && len(batchDeregiterTargets) > 0 {
		nodesByID, err = services.GetNodesByID(cluster_service.Instance.NodeLister())
		if err != nil {
			return err
		}
	}
	// batch deregitser targets
	if len(batchDeregiterTargets) > 0 {
		if err := BatchDeregisterTarget(sc, ingress, *loadBalancer.LoadBalancerId, loadBalancerContext.Region, batchDeregiterTargets); err != nil {
			return err
		}
		if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, batchDeregiterTargets...)
			cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueTargets(targetsToEnqueue, nodesByID)
		}
	}

	return nil
}

func GetCurrentIngressListeners(ctx context.Context, loadBalancerContext *LoadBalancerContext) ([]*clb.Listener, error) {
	ingress := loadBalancerContext.Ingress
	region := loadBalancerContext.Region
	loadBalancer, err := loadBalancerContext.GetLoadBalancer(ctx)
	if err != nil {
		return nil, err
	}
	lbId := *loadBalancer.LoadBalancerId

	managedListeners := make([]*clb.Listener, 0)
	listeners, err := GetLoadBalancerListener(ctx, ingress, lbId, region)
	if err != nil {
		klog.Errorf("Error deleting exist lb: %s listener when GetLoadBalancerListener, err: %v", lbId, err)
		return nil, err
	}
	ingressListeners := GetLoadBalancerResourceListenerByIngress(ingress, loadBalancerContext.LoadBalancerResource)
	for _, ingressListener := range ingressListeners {
		for index, listener := range listeners {
			if *listener.Port == int64(ingressListener.Port) && *listener.Protocol == ingressListener.Protocol && (listener.ListenerName != nil && *listener.ListenerName == TKEDedicatedListenerName) {
				managedListeners = append(managedListeners, listeners[index])
			}
		}
	}

	rewrites, err := DescribeRewrite(ctx, ingress, lbId, region)
	if err != nil {
		klog.Errorf("Error deleting exist lb: %s listener when DescribeRewrite, err: %v", lbId, err)
		return nil, err
	}
	if len(rewrites) != 0 {
		operatorRewrite := make([]*clb.RuleOutput, 0)
		for index, rewrite := range rewrites {
			for _, redirectListener := range managedListeners {
				if *rewrite.ListenerId == *redirectListener.ListenerId || *rewrite.RewriteTarget.TargetListenerId == *redirectListener.ListenerId {
					operatorRewrite = append(operatorRewrite, rewrites[index])
					break
				}
			}
		}
	}

	return managedListeners, nil
}

func GetCurrentTarget(ing types.Ingress, listenerId string, locationId *string, targets []*clb.Backend) []*clb.BatchTarget {
	backendManagementMode := utils.GetBackendManagementMode(ing)

	batchDeregiterTargets := make([]*clb.BatchTarget, 0)
	for _, target := range targets {
		if backendManagementMode == utils.BackendManagementModeTag && !types.ManagedTarget(target.Tag, config.Global.ClusterName) {
			continue // 开启标签管理，标签不符合预期，不在管理范围
		}
		if target.Type != nil && *target.Type == string(utils.CVM) && target.InstanceId != nil && *target.InstanceId != "" {
			batchDeregiterTargets = append(batchDeregiterTargets, &clb.BatchTarget{
				ListenerId: common.StringPtr(listenerId),
				LocationId: locationId,
				InstanceId: common.StringPtr(*target.InstanceId),
				Port:       common.Int64Ptr(*target.Port),
			})
		} else {
			for _, privateIp := range target.PrivateIpAddresses {
				batchDeregiterTargets = append(batchDeregiterTargets, &clb.BatchTarget{
					ListenerId: common.StringPtr(listenerId),
					LocationId: locationId,
					EniIp:      common.StringPtr(*privateIp),
					Port:       common.Int64Ptr(*target.Port),
				})
			}
		}
	}
	return batchDeregiterTargets
}

func checkBackendEmpty(ctx context.Context, ingress types.Ingress, loadBalancerContext *LoadBalancerContext) error {
	// MultiClusterIngress 的场景下的误删防御
	if ingress.Type() == types.MultiClusterIngress {
		listeners, err := GetCurrentIngressListeners(ctx, loadBalancerContext)
		if err != nil {
			return err
		}

		backends, err := loadBalancerContext.GetListenersBackend(ctx)
		if err != nil {
			return err
		}

		for _, listener := range listeners {
			backend := backends[utils.GetListenerKey(int32(*listener.Port), *listener.Protocol)]
			if backend.Protocol != nil {
				if utils.IsL4Protocol(*backend.Protocol) {
					if backend.Targets != nil && len(backend.Targets) != 0 {
						return types.NewError(errcode.MultiClusterDeleteWithBackend, "", utils.IngressName(ingress))
					}
				} else {
					if backend.Rules != nil && len(backend.Rules) != 0 {
						for _, rule := range backend.Rules {
							if rule.Targets != nil && len(rule.Targets) != 0 {
								return types.NewError(errcode.MultiClusterDeleteWithBackend, "", utils.IngressName(ingress))
							}
						}
					}
				}
			}
		}
	}
	return nil
}
