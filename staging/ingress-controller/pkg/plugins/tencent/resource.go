package tencent

import (
	"context"
	"fmt"
	"strings"
	"time"

	go_version "github.com/hashicorp/go-version"
	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	k8stypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/sets"

	"git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

const (
	LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE = "service"
	LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE = "ingress"
)

var WITHOUT_SUBRESOURCE *go_version.Version

func init() {
	semver, _ := go_version.NewSemver("v1.12")
	WITHOUT_SUBRESOURCE = semver
}

// GetLoadBalancerByLoadbalancerResource
// kateway: GetLoadBalancerByLoadBalancerResource 主要逻辑是尝试通过集群中已有的LBR资源来构建LoadBalancerContext：
// kateway: 基于本地LBR 构建context， 如果CLB存在但LBR不存在则创建LBR， 返回的context 缺乏 CLB 主体, todo 重命名??? GetLbr_CreateLbrIfNotReuse?
func GetLoadBalancerByLoadbalancerResource(sc *SyncContext) (*LoadBalancerContext, error) {
	span, sc := sc.StartSpan()
	defer span.Finish()

	ingress := sc.Ingress
	selector := labels.SelectorFromSet(map[string]string{ingress.UID(): LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE})
	loadBalancerResources, err := cluster_service.Instance.LoadBalancerResourceLister().List(selector)
	if err != nil {
		return nil, err
	}

	if len(loadBalancerResources) >= 1 {
		if len(loadBalancerResources) > 1 { // kateway todo 什么情况下 一个 service 会有多个 LBR
			lbIdList := make([]string, len(loadBalancerResources))
			for index, loadBalancerResource := range loadBalancerResources {
				lbIdList[index] = loadBalancerResource.Name
			}
			// TODO misakazhou 建立清理机制？
			services.UploadMetricsAndEvent(ingress, types.NewError(errcode.MultyLoadbalancer, "", utils.IngressName(ingress), strings.Join(lbIdList, ",")))
		}
		return &LoadBalancerContext{
			Ingress:              ingress,
			Region:               loadBalancerResources[0].Spec.Region,
			LoadBalancerId:       loadBalancerResources[0].Name,
			LoadBalancerResource: loadBalancerResources[0],
		}, nil
	}
	// kateway 下面是 LBR 不存在的情况

	// // 仅适用于TKE创建资源的灾难恢复
	if _, exist := utils.GetExistLbId(ingress); exist {
		return nil, nil
	}

	loadBalancerId, region, err := GetIngressLoadBalancer(sc, ingress)
	if err != nil {
		return nil, err
	}
	if loadBalancerId == "" {
		return nil, nil
	}
	loadbalancerContext := &LoadBalancerContext{
		Ingress:        ingress,
		Region:         region,
		LoadBalancerId: loadBalancerId,
	}

	loadbalancer, err := loadbalancerContext.GetLoadBalancer(sc) // kateway: 本地没有 LBR 但远程 CLB 创建好了, 这里 context 里缺乏 CLB 主体信息
	if err != nil {
		return nil, err
	}
	createdByTKE := false
	clusterIdCheck := false
	for _, tag := range loadbalancer.Tags {
		if tag.TagKey == nil || *tag.TagKey == "" || tag.TagValue == nil || *tag.TagValue == "" { // 存在空值不是TKE Service标签，略过
			continue
		}
		if *tag.TagKey == types.TagKeyClusterID.String() && *tag.TagValue == config.Global.ClusterName {
			clusterIdCheck = true
		}
		if *tag.TagKey == GetAutoCreatedTagKey(ingress).String() && *tag.TagValue == GetAutoCreatedTagValue() {
			createdByTKE = true
		}
	}
	// kateway: tag 中 clusterID 必须存在，且必须是 tke 创建的 clb，才会去创建LBR
	if !clusterIdCheck || !createdByTKE {
		return nil, nil
	}
	loadBalancerResource, err := CreateLoadBalancerResource(sc, loadBalancerId, region, createdByTKE, ingress) // kateway: LBR创建之一：非reuse情况下，本地缺LBS但远程CLB存在
	if err != nil {
		return nil, err
	}
	loadbalancerContext.LoadBalancerResource = loadBalancerResource
	return loadbalancerContext, nil
}

func EnsureLoadBalancerResource(sc *SyncContext) (err error) {
	span, sc := sc.StartSpan()
	defer span.Finish()

	ingress := sc.Ingress
	loadBalancerResource := sc.LoadBalancerContext.LoadBalancerResource
	labelValue, exist := loadBalancerResource.Labels[ingress.UID()]

	desiredListenerSets := make(sets.String)
	listeners := sc.IngressContext.IngressListeners
	for _, listener := range listeners {
		desiredListenerSets.Insert(utils.GetListenerKey(listener.Port, listener.Protocol))
	}

	currentListenerSets := make(sets.String)
	listenerByIngress := GetLoadBalancerResourceListenerByIngress(ingress, loadBalancerResource)
	for _, listener := range listenerByIngress {
		currentListenerSets.Insert(utils.GetListenerKey(listener.Port, listener.Protocol))
	}

	toAddListenerKeys := desiredListenerSets.Difference(currentListenerSets)
	toDelListenerKeys := currentListenerSets.Difference(desiredListenerSets)

	newLoadBalancerResource := loadBalancerResource
	if toAddListenerKeys.Len() != 0 || toDelListenerKeys.Len() != 0 || (!exist || labelValue != LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE) {
		newLoadBalancerResource, err = UpdateLoadBalancerResource(sc, loadBalancerResource.Name,
			toAddListenerKeys, toDelListenerKeys, ingress)
		if err != nil {
			return err
		}
	}
	newLoadBalancerResource, err = UpdateLoadBalancerResourceStatus(sc, loadBalancerResource.Name, ingress)
	if err != nil {
		return err
	}

	sc.LoadBalancerContext.LoadBalancerResource = newLoadBalancerResource
	return nil
}

func DeleteLoadBalancerResourceByIngress(ctx context.Context, loadBalancerId string, ingress types.Ingress) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	loadBalancerResource, err := deleteLoadBalancerResourceByIngress(ctx, loadBalancerId, ingress)
	if err != nil {
		return err
	}
	if loadBalancerResource == nil {
		return nil
	}
	if len(loadBalancerResource.Labels) == 0 { // CLB资源已经不再被使用
		return DeleteLoadBalancerResource(ctx, loadBalancerId)
	}

	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		if loadBalancerResource, err = cluster_service.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Update(ctx, loadBalancerResource, metav1.UpdateOptions{}); err != nil {
			if !errors.IsConflict(err) {
				return err
			}
			loadBalancerResource, err = deleteLoadBalancerResourceByIngress(ctx, loadBalancerId, ingress)
			if err != nil {
				return err
			}
			if loadBalancerResource == nil {
				return nil
			}
			if len(loadBalancerResource.Labels) == 0 { // CLB资源已经不再被使用
				return DeleteLoadBalancerResource(ctx, loadBalancerId)
			}
		} else {
			break
		}
	}

	if _, err = UpdateLoadBalancerResourceStatus(ctx, loadBalancerId, ingress); err != nil {
		return err
	}
	return nil
}

func deleteLoadBalancerResourceByIngress(ctx context.Context, loadBalancerId string, ingress types.Ingress) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerResource, err := GetLoadBalancerResource(ctx, loadBalancerId)
	if loadBalancerResource == nil {
		return loadBalancerResource, err
	}

	// DeepCopy And Update LoadBalancerResource Spec
	deepCopy := loadBalancerResource.DeepCopy()
	if value, exist := deepCopy.Labels[ingress.UID()]; exist && value == LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE {
		delete(deepCopy.Labels, ingress.UID())
	}
	deepCopy.Spec.Listeners = GetLoadBalancerResourceListenerWithoutIngress(ingress, deepCopy)

	return deepCopy, nil
}

func LoadBalancerResourceAddIngress(loadBalancerId string, region string, created bool, ingress types.Ingress) *v1alpha1.LoadBalancerResource {
	loadBalancerResource := &v1alpha1.LoadBalancerResource{
		ObjectMeta: metav1.ObjectMeta{
			Name:   loadBalancerId,
			Labels: map[string]string{},
		},
		Spec: v1alpha1.LoadBalancerResourceSpec{
			Region:    region,
			Created:   created,
			Listeners: make([]*v1alpha1.LoadBalancerResourceListener, 0),
		},
		Status: v1alpha1.LoadBalancerResourceStatus{},
	}
	if ingress != nil {
		loadBalancerResource.Labels = map[string]string{
			ingress.UID(): LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE,
		}
		listenerKeySet := GetListenerKeyByIngress(ingress)
		for listenerKey, _ := range listenerKeySet {
			port, protocol := utils.ParseListenerKey(listenerKey)
			loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(port, protocol, ingress))
		}
	}
	return loadBalancerResource
}

func GetLoadBalancerResourceListener(loadBalancerResource *v1alpha1.LoadBalancerResource) []*v1alpha1.LoadBalancerResourceListener {
	listeners := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, _ := range loadBalancerResource.Spec.Listeners {
		listeners = append(listeners, loadBalancerResource.Spec.Listeners[index])
	}
	return listeners
}

func GetLoadBalancerResourceListenerWithoutIngress(ingress types.Ingress, loadBalancerResource *v1alpha1.LoadBalancerResource) []*v1alpha1.LoadBalancerResourceListener {
	listeners := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, listener := range loadBalancerResource.Spec.Listeners {
		for _, reference := range listener.References {
			if (reference.Kind == "Ingress" || reference.Kind == "MultiClusterIngress") && reference.Namespace == ingress.Namespace() && reference.Name == ingress.Name() {
				continue
			}
			listeners = append(listeners, loadBalancerResource.Spec.Listeners[index])
		}
	}
	return listeners
}

func GetLoadBalancerResourceListenerByIngress(ingress types.Ingress, loadBalancerResource *v1alpha1.LoadBalancerResource) []*v1alpha1.LoadBalancerResourceListener {
	listeners := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, listener := range loadBalancerResource.Spec.Listeners {
		for _, reference := range listener.References {
			if (reference.Kind == "Ingress" || reference.Kind == "MultiClusterIngress") && reference.Namespace == ingress.Namespace() && reference.Name == ingress.Name() {
				listeners = append(listeners, loadBalancerResource.Spec.Listeners[index])
			}
		}
	}
	return listeners
}

func UpdateLoadBalancerResource(ctx context.Context, loadBalancerId string, toAddListener sets.String, toDelListener sets.String, ingress types.Ingress) (*v1alpha1.LoadBalancerResource, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	loadBalancerResource, err := updateLoadBalancerResource(ctx, loadBalancerId, toAddListener, toDelListener, ingress)
	if err != nil {
		return nil, err
	}

	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		if loadBalancerResource, err = cluster_service.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Update(ctx, loadBalancerResource, metav1.UpdateOptions{}); err != nil {
			if !errors.IsConflict(err) {
				return nil, err
			}
			if loadBalancerResource, err = updateLoadBalancerResource(ctx, loadBalancerId, toAddListener, toDelListener, ingress); err != nil {
				return nil, err
			}
		} else {
			break
		}
	}
	return loadBalancerResource, nil
}

func updateLoadBalancerResource(ctx context.Context, loadBalancerId string, toAddListener sets.String,
	toDelListener sets.String, ingress types.Ingress) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerResource, err := GetLoadBalancerResource(ctx, loadBalancerId)
	if err != nil { // 资源被删除，重新开始同步逻辑
		return loadBalancerResource, err
	}
	if loadBalancerResource == nil { // 资源被删除，重新开始同步逻辑
		return loadBalancerResource, err
	}

	deepCopy := loadBalancerResource.DeepCopy()
	// Update LoadBalancerResource Spec
	if value, exist := deepCopy.Labels[ingress.UID()]; !exist || value != LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE {
		if deepCopy.Labels == nil {
			deepCopy.Labels = make(map[string]string)
		}
		deepCopy.Labels[ingress.UID()] = LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE
	}

	result := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, listener := range deepCopy.Spec.Listeners {
		if _, exist := toDelListener[utils.GetListenerKey(listener.Port, listener.Protocol)]; !exist { // 非删除列表内的监听器声明
			result = append(result, deepCopy.Spec.Listeners[index])
		} else {
			references := removeByIngress(listener.References, ingress)
			// Ingress有对应监听器声明，但是还有其他资源正在使用该监听器。
			// 考虑未来的共用监听器Feature设置该逻辑。
			if len(references) != 0 {
				deepCopy.Spec.Listeners[index].References = references
				result = append(result, deepCopy.Spec.Listeners[index])
			}
		}
	}

	resourceListenerMap := make(map[string]*v1alpha1.LoadBalancerResourceListener)
	for index, listener := range result { // 注意: 这里要先处理删除，然后使用处理删除之后的监听器使用情况，去判断新增是否冲突。
		resourceListenerMap[utils.GetListenerKey(listener.Port, listener.Protocol)] = result[index]
	}
	for listener, _ := range toAddListener {
		if resourceListener, exist := resourceListenerMap[listener]; exist {
			if !onlyUsedByIngress(resourceListener.References, ingress) { // 在修改中，发现监听器被其他资源占用。
				return nil, types.NewError(errcode.ConflictListenerError, "", utils.IngressName(ingress), listener)
			}
		}
		port, protocol := utils.ParseListenerKey(listener)
		result = append(result, BuildLoadBalancerResourceListener(port, protocol, ingress))
	}
	deepCopy.Spec.Listeners = result
	return deepCopy, nil
}

func BuildLoadBalancerResourceListener(port int64, protocol string, ingress types.Ingress) *v1alpha1.LoadBalancerResourceListener {
	return &v1alpha1.LoadBalancerResourceListener{
		Port:     int32(port),
		Protocol: protocol,
		References: []*v1alpha1.ResourceObjectReference{
			ObjectReferenceFromIngress(ingress),
		},
		Domains: nil,
	}
}

func BuildLoadBalancerResourceListenerService(port int64, protocol string, service *v1.Service) *v1alpha1.LoadBalancerResourceListener {
	return &v1alpha1.LoadBalancerResourceListener{
		Port:     int32(port),
		Protocol: protocol,
		References: []*v1alpha1.ResourceObjectReference{
			ObjectReferenceFromService(service),
		},
		Domains: nil,
	}
}

func UpdateLoadBalancerResourceStatus(ctx context.Context, loadBalancerId string, ingress types.Ingress) (*v1alpha1.LoadBalancerResource, error) {
	return UpdateLoadBalancerResourceTemplate(ctx, loadBalancerId, ingress, updateLoadBalancerResourceReference)
}

func LockLoadBalancerResource(ctx context.Context, loadBalancerId string, ingress types.Ingress) bool {
	if config.Global.DryRun {
		return true
	}
	if _, err := UpdateLoadBalancerResourceTemplate(ctx, loadBalancerId, ingress, lockLoadBalancerResource); err != nil {
		return false
	}
	return true
}

func UnlockLoadBalancerResource(ctx context.Context, loadBalancerId string) error {
	if config.Global.DryRun {
		return nil
	}
	if _, err := UpdateLoadBalancerResourceTemplate(ctx, loadBalancerId, nil, unLockLoadBalancerResource); err != nil {
		return err
	}
	return nil
}

func UpdateLoadBalancerResourceTemplate(ctx context.Context, loadBalancerId string, ingress types.Ingress,
	updateFunc func(context.Context, string, types.Ingress) (*v1alpha1.LoadBalancerResource, bool, error)) (*v1alpha1.LoadBalancerResource, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	loadBalancerResource, isModify, err := updateFunc(ctx, loadBalancerId, ingress)
	if err != nil {
		return nil, err
	}
	withoutSubResources := lo.Must(cluster_service.Instance.CheckVersion("< 1.12"))
	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		if !isModify {
			break
		}
		if withoutSubResources { // 当前服务版本小于v1.12.0
			if loadBalancerResource, err = cluster_service.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Update(ctx, loadBalancerResource, metav1.UpdateOptions{}); err != nil {
				if !errors.IsConflict(err) {
					return nil, err
				}
				if loadBalancerResource, isModify, err = updateFunc(ctx, loadBalancerId, ingress); err != nil {
					return nil, err
				}
			} else {
				break
			}
		} else {
			if loadBalancerResource, err = cluster_service.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().UpdateStatus(ctx, loadBalancerResource, metav1.UpdateOptions{}); err != nil {
				if !errors.IsConflict(err) {
					return nil, err
				}
				if loadBalancerResource, isModify, err = updateFunc(ctx, loadBalancerId, ingress); err != nil {
					return nil, err
				}
			} else {
				break
			}
		}
		// 测试EKS开关内外网场景时发现，API Server会因重启而短暂不可用，可能导致LoadBalancerResource资源锁关闭失败。
		// 重试增加重试间隔后未复现
		time.Sleep(3 * time.Second)
	}
	return loadBalancerResource, nil
}

func updateLoadBalancerResourceReference(ctx context.Context, loadBalancerId string, ingress types.Ingress) (*v1alpha1.LoadBalancerResource, bool, error) {
	loadBalancerResource, err := GetLoadBalancerResource(ctx, loadBalancerId)
	if err != nil || loadBalancerResource == nil {
		return loadBalancerResource, false, err
	}

	deepCopy := loadBalancerResource.DeepCopy()
	// Ingress Get References from Spec
	referencesMap := make(map[string]*v1alpha1.ResourceObjectReference)
	for _, listener := range deepCopy.Spec.Listeners {
		for index, reference := range listener.References {
			if reference.Kind == "Ingress" || reference.Kind == "MultiClusterIngress" {
				referencesMap[ObjectReferenceNamespaceName(reference)] = listener.References[index]
			}
		}
	}

	// Compare LoadBalancerResource Status
	isIngressModify := false
	if len(referencesMap) != len(deepCopy.Status.IngressResource) {
		isIngressModify = true
	} else {
		for _, ingressResource := range deepCopy.Status.IngressResource {
			if resource, exist := referencesMap[ObjectReferenceNamespaceName(ingressResource)]; !exist || resource.UID != ingressResource.UID {
				isIngressModify = true
				break
			}
		}
	}

	if isIngressModify {
		result := make([]*v1alpha1.ResourceObjectReference, len(referencesMap))
		index := 0
		for _, reference := range referencesMap {
			result[index] = reference
			index = index + 1
		}
		deepCopy.Status.IngressResource = result
	}

	// Service Get References from Spec
	referencesMap = make(map[string]*v1alpha1.ResourceObjectReference)
	for _, listener := range deepCopy.Spec.Listeners {
		for index, reference := range listener.References {
			if reference.Kind == "Service" {
				referencesMap[ObjectReferenceNamespaceName(reference)] = listener.References[index]
			}
		}
	}

	// Compare LoadBalancerResource Status
	isServiceModify := false
	if len(referencesMap) != len(deepCopy.Status.ServiceResource) {
		isServiceModify = true
	} else {
		for _, serviceResource := range deepCopy.Status.ServiceResource {
			if resource, exist := referencesMap[ObjectReferenceNamespaceName(serviceResource)]; !exist || resource.UID != serviceResource.UID {
				isServiceModify = true
				break
			}
		}
	}

	if isServiceModify {
		result := make([]*v1alpha1.ResourceObjectReference, len(referencesMap))
		index := 0
		for _, reference := range referencesMap {
			result[index] = reference
			index = index + 1
		}
		deepCopy.Status.ServiceResource = result
	}
	return deepCopy, isIngressModify || isServiceModify, nil
}

func lockLoadBalancerResource(ctx context.Context, loadBalancerId string, ingress types.Ingress) (*v1alpha1.LoadBalancerResource, bool, error) {
	loadBalancerResource, err := GetLoadBalancerResource(ctx, loadBalancerId)
	if err != nil || loadBalancerResource == nil {
		return loadBalancerResource, false, err
	}

	deepCopy := loadBalancerResource.DeepCopy()
	// 如果Status字段不存在则初始化
	if deepCopy.Status.Lock == nil {
		deepCopy.Status.Lock = &v1alpha1.LoadBalancerResourceLock{
			Status:          "Unlock",
			UpdateTimestamp: metav1.Now(),
		}
	}

	// 已经上锁的场景下。如果上锁超过15分钟，认为是锁超时的线程丢失，强制释放锁
	lock := deepCopy.Status.Lock
	if lock.Status == "Lock" {
		if metav1.Now().Sub(lock.UpdateTimestamp.Time) >= 15*time.Minute {
			deepCopy.Status.Lock.Status = "Unlock"
		}
		if lock.Resource != nil && (lock.Resource.Kind == "Ingress" || lock.Resource.Kind == "MultiClusterIngress") && lock.Resource.Namespace == ingress.Namespace() && lock.Resource.Name == ingress.Name() {
			deepCopy.Status.Lock.Status = "Unlock"
		}
	}

	// 如果当前锁是被抢占的，不用尝试上锁
	if deepCopy.Status.Lock.Status == "Lock" {
		return deepCopy, false, types.NewError(errcode.ReuseConcurrentOperationError, "", utils.IngressName(ingress))
	}

	deepCopy.Status.Lock = &v1alpha1.LoadBalancerResourceLock{
		Status:          "Lock",
		UpdateTimestamp: metav1.Now(),
		Resource:        ObjectReferenceFromIngress(ingress),
	}
	return deepCopy, true, nil
}

func unLockLoadBalancerResource(ctx context.Context, loadBalancerId string, ingress types.Ingress) (*v1alpha1.LoadBalancerResource, bool, error) {
	loadBalancerResource, err := GetLoadBalancerResource(ctx, loadBalancerId)
	if err != nil {
		return loadBalancerResource, false, err
	}
	if loadBalancerResource == nil { // 解锁时，资源不存在了，应该保持幂等。
		return loadBalancerResource, false, nil
	}

	deepCopy := loadBalancerResource.DeepCopy()
	deepCopy.Status.Lock = &v1alpha1.LoadBalancerResourceLock{
		Status:          "Unlock",
		UpdateTimestamp: metav1.Now(),
	}
	return deepCopy, true, nil
}

func GetLoadBalancerResource(ctx context.Context, loadBalancerId string) (*v1alpha1.LoadBalancerResource, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	loadBalancerResource, err := cluster_service.Instance.LoadBalancerResourceLister().Get(loadBalancerId)
	if err != nil {
		if errors.IsNotFound(err) {
			loadBalancerResource, err = cluster_service.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().
				LoadBalancerResources().Get(ctx, loadBalancerId, metav1.GetOptions{})
			if err == nil { // BackUpdate, 创建之后立刻查询没有缓存数据，改为Client查询
				return loadBalancerResource, nil
			}
			return nil, nil
		}
		return nil, err
	}
	return loadBalancerResource, nil
}

func DeleteLoadBalancerResource(ctx context.Context, loadBalancerId string) error {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	var err error
	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		if err = cluster_service.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().
			Delete(ctx, loadBalancerId, metav1.DeleteOptions{}); err != nil {
			if !errors.IsNotFound(err) { // 幂等处理
				return err
			}
		} else {
			return nil
		}
	}
	return err
}

func CreateLoadBalancerResource(ctx context.Context, loadBalancerId string, region string, created bool,
	ingress types.Ingress) (*v1alpha1.LoadBalancerResource, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	if created {
		return createLoadBalancerResource(ctx, LoadBalancerResourceAddIngress(loadBalancerId, region, created, ingress))
	} else { // 使用已有负载均衡，资源不存在时，灾难恢复
		rebuildLoadBalancerResource, err := LoadBalancerResourceRebuild(ctx, loadBalancerId, region, created, ingress)
		if err != nil {
			return nil, err
		}
		return createLoadBalancerResource(ctx, rebuildLoadBalancerResource)
	}

}

func LoadBalancerResourceRebuild(ctx context.Context, loadBalancerId string, region string, created bool, ingress types.Ingress) (*v1alpha1.LoadBalancerResource, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	loadBalancerResource := &v1alpha1.LoadBalancerResource{
		ObjectMeta: metav1.ObjectMeta{
			Name:   loadBalancerId,
			Labels: map[string]string{},
		},
		Spec: v1alpha1.LoadBalancerResourceSpec{
			Region:    region,
			Created:   created,
			Listeners: make([]*v1alpha1.LoadBalancerResourceListener, 0),
		},
		Status: v1alpha1.LoadBalancerResourceStatus{},
	}

	loadBalancerContext := &LoadBalancerContext{
		Ingress:        ingress,
		Region:         region,
		LoadBalancerId: loadBalancerId,
	}
	// 标签被破坏的场景，对账用户声明和负载均衡监听器
	listeners, err := loadBalancerContext.GetListeners(ctx)
	if err != nil {
		return nil, err
	}

	// LoadBalancerResource 资源初始化
	services, err := cluster_service.Instance.KubeClient().CoreV1().Services(v1.NamespaceAll).
		List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}
	ingresses, err := IngressLister(types.CoreIngress, v1.NamespaceAll, labels.Everything())
	if err != nil {
		return nil, err
	}

	// multiClusterIngressList 资源初始化
	var multiClusterIngressList []types.Ingress
	if config.Global.EnableMultiClusterIngress {
		multiClusterIngressList, err = IngressLister(types.MultiClusterIngress, v1.NamespaceAll, labels.Everything())
		if err != nil {
			return nil, err
		}
	}

	alreadyRecordListener := make(map[string]bool)

	// 当前同步资源优先处理
	listenerKeySet := GetListenerKeyByIngress(ingress)
	for listenerKey, _ := range listenerKeySet {
		_, alreadyRecord := alreadyRecordListener[listenerKey]
		listener, listenerExist := listeners[listenerKey]
		if listenerExist && !alreadyRecord && listener.ListenerName != nil && *listener.ListenerName == TKEDedicatedListenerName {
			loadBalancerResource.Labels[ingress.UID()] = LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE
			alreadyRecordListener[utils.GetListenerKey((int32)(*listener.Port), *listener.Protocol)] = true
			loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(*listener.Port, *listener.Protocol, ingress))
		}
	}

	for _, service := range services.Items {
		if existLB, exist := utils.GetServiceExistLB(&service); exist && existLB == loadBalancerId {
			listenerKeyByService := GetListenerKeyByService(&service)
			for listenerKey, _ := range listenerKeyByService {
				_, alreadyRecord := alreadyRecordListener[listenerKey]
				listener, listenerExist := listeners[listenerKey]
				if listenerExist && !alreadyRecord && listener.ListenerName != nil && *listener.ListenerName == TKEDedicatedListenerName {
					loadBalancerResource.Labels[string(service.GetUID())] = LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE
					alreadyRecordListener[utils.GetListenerKey((int32)(*listener.Port), *listener.Protocol)] = true
					loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListenerService(*listener.Port, *listener.Protocol, &service))
				}
			}
		}
	}

	for _, ingress := range multiClusterIngressList {
		if existLB, exist := utils.GetExistLbId(ingress); exist && existLB == loadBalancerId {
			listenerKeySet := GetListenerKeyByIngress(ingress)
			for listenerKey, _ := range listenerKeySet {
				_, alreadyRecord := alreadyRecordListener[listenerKey]
				listener, listenerExist := listeners[listenerKey]
				if listenerExist && !alreadyRecord && listener.ListenerName != nil && *listener.ListenerName == TKEDedicatedListenerName {
					loadBalancerResource.Labels[ingress.UID()] = LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE
					alreadyRecordListener[utils.GetListenerKey((int32)(*listener.Port), *listener.Protocol)] = true
					loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(*listener.Port, *listener.Protocol, ingress))
				}
			}
		}
	}

	for _, ingress := range ingresses {
		if existLB, exist := utils.GetExistLbId(ingress); exist && existLB == loadBalancerId {
			listenerKeySet := GetListenerKeyByIngress(ingress)
			for listenerKey, _ := range listenerKeySet {
				_, alreadyRecord := alreadyRecordListener[listenerKey]
				listener, listenerExist := listeners[listenerKey]
				if listenerExist && !alreadyRecord && listener.ListenerName != nil && *listener.ListenerName == TKEDedicatedListenerName {
					loadBalancerResource.Labels[ingress.UID()] = LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE
					alreadyRecordListener[utils.GetListenerKey((int32)(*listener.Port), *listener.Protocol)] = true
					loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(*listener.Port, *listener.Protocol, ingress))
				}
			}
		}
	}
	return loadBalancerResource, nil
}

func createLoadBalancerResource(ctx context.Context, loadBalancerResource *v1alpha1.LoadBalancerResource) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerId := loadBalancerResource.Name
	loadBalancerResource, err := cluster_service.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().
		LoadBalancerResources().Create(ctx, loadBalancerResource, metav1.CreateOptions{})
	if err != nil {
		if errors.IsAlreadyExists(err) {
			loadBalancerResource, err = cluster_service.Instance.LoadBalancerResourceLister().Get(loadBalancerId)
			if err != nil {
				return nil, err
			}
			return loadBalancerResource, nil
		}
		return nil, err
	}
	return loadBalancerResource, nil
}

func ObjectReferenceFromService(service *v1.Service) *v1alpha1.ResourceObjectReference {
	return &v1alpha1.ResourceObjectReference{
		ObjectReference: v1.ObjectReference{
			APIVersion: "v1",
			Kind:       "Service",
			Namespace:  service.Namespace,
			Name:       service.Name,
			UID:        service.UID,
		},
	}
}

func ObjectReferenceFromIngress(ingress types.Ingress) *v1alpha1.ResourceObjectReference {
	if ingress.Type() == types.MultiClusterIngress {
		return &v1alpha1.ResourceObjectReference{
			ObjectReference: v1.ObjectReference{
				Kind:      "MultiClusterIngress",
				Namespace: ingress.Namespace(),
				Name:      ingress.Name(),
				UID:       k8stypes.UID(ingress.UID()),
			},
		}
	}
	return &v1alpha1.ResourceObjectReference{
		ObjectReference: v1.ObjectReference{
			Kind:      "Ingress",
			Namespace: ingress.Namespace(),
			Name:      ingress.Name(),
			UID:       k8stypes.UID(ingress.UID()),
		},
	}
}

func ObjectReferenceNamespaceName(objectReference *v1alpha1.ResourceObjectReference) string {
	return fmt.Sprintf("%s/%s", objectReference.Namespace, objectReference.Name)
}

// func ConvertTagToLoadbalancerResource(ingressList []types.Ingress) {
//	normalIngress := make([]types.Ingress, 0)
//	for index, ingress := range ingressList {
//		if !utils.IsQCLOUDIngress(ingress) {
//			continue
//		}
//		normalIngress = append(normalIngress, ingressList[index])
//	}
//
//	convertNormalServiceList(normalIngress)
// }
//
// func convertNormalServiceList(ingressList []types.Ingress) {
//	for _, ingress := range ingressList {
//		loadBalancerId, region, err := GetIngressLoadBalancer(ingress)
//		if err != nil {
//			continue
//		}
//		if loadBalancerId == "" {
//			continue
//		}
//		loadBalancerResource, err := GetLoadBalancerResource(loadBalancerId)
//		if loadBalancerResource != nil { // CRD资源已存在
//			continue
//		}
//
//		loadbalancerContext := &LoadBalancerContext{
//			Ingress:        ingress,
//			Region:         region,
//			LoadBalancerId: loadBalancerId,
//		}
//
//		loadbalancer, err := loadbalancerContext.GetLoadBalancer()
//		if err != nil {
//			continue
//		}
//		createdByTKE := false
//		clusterIdCheck := false
//		for _, tag := range loadbalancer.Tags {
//			if tag.TagKey == nil || *tag.TagKey == "" || tag.TagValue == nil || *tag.TagValue == "" { // 存在空值不是TKE Service标签，略过
//				continue
//			}
//			if *tag.TagKey == types.TagKeyClusterID && *tag.TagValue == config.GlobalConfigs.ClusterName {
//				clusterIdCheck = true
//			}
//			if *tag.TagKey == CreateByTKETag() && *tag.TagValue == CreateByTKEValue() {
//				createdByTKE = true
//			}
//		}
//		if !clusterIdCheck {
//			continue
//		}
//
//		loadBalancerResource = &v1alpha1.LoadBalancerResource{
//			ObjectMeta: v12.ObjectMeta{
//				Name: loadBalancerId,
//				Labels: map[string]string{
//					ingress.UID(): LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE,
//				},
//			},
//			Spec: v1alpha1.LoadBalancerResourceSpec{
//				Region:    region,
//				Created:   createdByTKE,
//				Listeners: make([]*v1alpha1.LoadBalancerResourceListener, 0),
//			},
//			Status: v1alpha1.LoadBalancerResourceStatus{},
//		}
//
//		// 标签被破坏的场景，对账用户声明和负载均衡监听器
//		listeners, err := loadbalancerContext.GetListeners()
//		if err != nil {
//			klog.Errorf("convert error Loadbalancer get error. %v", err.Error())
//			continue
//		}
//		listenerKeySet := GetListenerKeyByIngress(ingress)
//		for listenerKey, _ := range listenerKeySet {
//			listener, listenerExist := listeners[listenerKey]
//			if listenerExist && listener.ListenerName != nil && *listener.ListenerName == TKEDedicatedListenerName {
//				loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(*listener.Port, *listener.Protocol, ingress))
//			}
//		}
//
//		loadBalancerResource, err = createLoadBalancerResource(loadBalancerResource)
//		if err != nil {
//			continue
//		}
//		loadbalancerContext.LoadBalancerResource = loadBalancerResource
//		_ = EnsureLoadBalancerTags(loadbalancerContext)
//		_, _ = UpdateLoadBalancerResourceStatus(loadBalancerId, ingress)
//	}
// }

// 引用列表中是否只有指定的一个Ingress资源
func onlyUsedByIngress(references []*v1alpha1.ResourceObjectReference, ingress types.Ingress) bool {
	onlyUsed := true
	for _, reference := range references {
		if (reference.Kind == "Ingress" || reference.Kind == "MultiClusterIngress") && reference.Namespace == ingress.Namespace() && reference.Name == ingress.Name() {
			continue
		} else {
			onlyUsed = false
			break
		}
	}
	return onlyUsed
}

// 在引用列表中删除一个Ingress资源
func removeByIngress(references []*v1alpha1.ResourceObjectReference, ingress types.Ingress) []*v1alpha1.ResourceObjectReference {
	result := make([]*v1alpha1.ResourceObjectReference, 0)
	for index, reference := range references {
		if (reference.Kind == "Ingress" || reference.Kind == "MultiClusterIngress") && reference.Namespace == ingress.Namespace() && reference.Name == ingress.Name() {
			continue
		} else {
			result = append(result, references[index])
		}
	}
	return result
}
