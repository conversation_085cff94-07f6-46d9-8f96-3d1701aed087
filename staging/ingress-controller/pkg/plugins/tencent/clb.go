package tencent

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	"github.com/google/uuid"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/event"
	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	clbInner "git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	skderrors "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/crossregion"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

const (
	QUERY_LIMIT = 3

	TaskCheckInterval = time.Second * 3

	InternetChargeType      = "kubernetes.io/ingress.internetChargeType"
	InternetMaxBandwidthOut = "kubernetes.io/ingress.internetMaxBandwidthOut"
	AnnoExtensiveParameters = "kubernetes.io/ingress.extensiveParameters"
	IngressSubnetId         = "kubernetes.io/ingress.subnetId"
	IngressLoadBalancerType = "kubernetes.io/ingress.loadbalancerType"
	AnnoExistedLBID         = "kubernetes.io/ingress.existLbId"

	TKEDedicatedListenerName = "TKE-DEDICATED-LISTENER"
	MaxRulePerRequest        = 20

	FORWARDTYPE             = 1
	LOADBALANCETYPEOPEN     = "OPEN"
	LOADBALANCETYPEINTERNAL = "INTERNAL"
	LOADBALANCESTATUSNORMAL = 1

	BATCH_LOADBALANCES_LIMIT = 20
	BATCH_RULE_LIMIT         = 20
	BATCH_REWRITE_LIMIT      = 20
	TAGSPERPAGE              = 50
	NEW_BATCH_TARGET_LIMIT   = 500

	TEMPMAXBATCHMODIFYREQUEST = 100
	MAXBATCHTARGETPERREQUEST  = 500

	DESCRIBE_INSTANCES_LIMIT              = 100
	DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT = 5
)

// func SetNormToTag() error {
//	request := norm.NormGetClusterLBInfoReq{ClusterID: utils.Global.ClusterName}
//	response, err := norm.NormGetClusterLBInfo(request)
//	if err != nil {
//		return err
//	}
//
//	for _, service := range response.LBInfo {
//		//if this service's lb info has been updated in tag, we need not set it again
//		//first check lb's type, only focus on lb of forward type
//		lbInfo, err := GetLoadBalancerByLBId(nil, service.LBID)
//		if lbInfo == nil || err != nil {
//			continue
//		}
//		if *lbInfo.Forward == 0 {
//			continue
//		}
//
//		lbID, err := getLbIDByIngressUUID(service.ServiceUUID)
//		if err != nil {
//			return err
//		}
//		if lbID == "" {
//			err = setLBTag(service.ServiceUUID, service.LBID)
//			if err != nil {
//				return err
//			}
//		}
//	}
//	return nil
// }

// EnsureCreateLoadBalancer给ingress创建出对应的LB；
// 并不会创建监听器，更不会绑定后端；
// kateway 确保 clb 和 lbr 创建好，但不会创建监听器，更不会绑定后端；
func EnsureCreateLoadBalancer(sc *SyncContext) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	ingress := sc.Ingress
	currentContext, err := GetLoadBalancerByLoadbalancerResource(sc)
	if err != nil {
		return err
	}

	if currentContext == nil { // 可能是 reuse 情况，也可能是第一次进来 clb 还没创建过。
		existLbId, hasExistLbID := utils.GetExistLbId(ingress)
		if hasExistLbID { // 使用已有 CLB
			region := utils.GetIngressRegion(ingress)
			currentContext = &LoadBalancerContext{
				Ingress:        ingress,
				Region:         region,
				LoadBalancerId: existLbId,
			}
			if _, err := currentContext.GetLoadBalancer(sc); err != nil {
				if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
					if sdkError.Code == "InvalidParameter.FormatError" || sdkError.Code == "InvalidParameterValue.Length" { // Service注解中的内容不正确
						return types.NewError(errcode.ReuseFormatInvalidError, sdkError.Error(), utils.IngressName(ingress))
					}
				} else if svcError, ok := lo.ErrorsAs[*types.Error](err); ok {
					if svcError.ErrorCode == errcode.LoadBalancerNotExistError { // Service注解中的LoadBalancer不存在
						return types.NewError(errcode.ReuseExistLoadBalancerNotExistError, "", utils.IngressName(ingress), existLbId)
					}
				}
				return err
			}
			loadBalancerResource, err := GetLoadBalancerResource(sc, existLbId)
			if err != nil {
				return err
			}
			if loadBalancerResource != nil {
				currentContext.LoadBalancerResource = loadBalancerResource
			}

			// 当ingress是复用已经存在的LB时，要经过这个检验
			if err = validaCreateLoadbBalancerConfig(ingress, currentContext); err != nil {
				return err
			}

			if loadBalancerResource == nil {
				if loadBalancerResource, err = CreateLoadBalancerResource(sc, existLbId, region, false, ingress); err != nil {
					return err
				}
				currentContext.LoadBalancerResource = loadBalancerResource
			}
			sc.LoadBalancerContext = currentContext
		} else { // 自动创建 CLB
			if err = PreCheckBeforeCreateLb(ingress); err != nil {
				return err
			}
			// 计费类型注解校验
			if _, _, err := utils.GetInternetChargeType(ingress); err != nil {
				return err
			}
			// 计费配置注解校验
			if _, _, err := utils.GetInternetMaxBandwidthOut(ingress); err != nil {
				return err
			}
			// create a new lb
			loadBalancerId, err := createLoadBalancer(sc, ingress) // kateway 真实创建 clb
			if err != nil {
				return err
			}
			region := utils.GetIngressRegion(ingress)
			loadBalancerResource, err := CreateLoadBalancerResource(sc, loadBalancerId, region, true, ingress)
			if err != nil {
				klog.Errorf("CreateLoadBalancerResource %s for ingress %s error: %v", loadBalancerId, utils.IngressName(ingress), err)
				// LB不做回滚，后续会重试
				return err
			}
			// 检查标签资源落地情况
			// 标签资源在一分钟之内落地失败，则回收创建的负载均衡资源。
			// if err := checkNewLoadBalancerTag(ingress, loadBalancerId); err != nil {
			//	klog.Errorf("Remove the loadBalancer. Tag not ready. lb: %s for ingress %s", loadBalancerId, utils.IngressName(ingress))
			//	request := clb.NewDeleteLoadBalancerRequest()
			//	request.LoadBalancerIds = []*string{&loadBalancerId}
			//	if _, err := tencentapi.Instance.DeleteLoadBalancer(ingress, region, request); err != nil {
			//		klog.Errorf("Remove the loadBalancer error. Tag not ready. lb: %s for ingress %s. err: %v", loadBalancerId, utils.IngressName(ingress), err)
			//	}
			//	return err
			// }
			sc.LoadBalancerContext = &LoadBalancerContext{
				Ingress:              ingress,
				LoadBalancerId:       loadBalancerId,
				LoadBalancerResource: loadBalancerResource,
				Region:               loadBalancerResource.Spec.Region,
			}
		}
	} else {
		sc.LoadBalancerContext = currentContext
		if err = validateLoadBalancerClusterTag(sc); err != nil {
			return err
		}
	}

	loadBalancer, err := sc.LoadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		return err
	}
	if !utils.BackendOnlyIngress(ingress) {
		if err = EnsureLoadBalancerTags(sc, sc.LoadBalancerContext); err != nil {
			return err
		}
	}

	vips, domains := utils.GetVipAndDomain(loadBalancer)
	if err := UpdateIngressEntry(sc, ingress, vips, domains); err != nil {
		return err
	}
	if *loadBalancer.Forward == FORWARDTYPE {
		if loadBalancer.Domain != nil && *loadBalancer.Domain != "" {
			sc.LoadBalancerContext.DefaultDomain = common.StringPtr(*loadBalancer.Domain)
		} else if len(loadBalancer.LoadBalancerVips) != 0 {
			sc.LoadBalancerContext.DefaultDomain = loadBalancer.LoadBalancerVips[0]
		}
	} else {
		if len(loadBalancer.LoadBalancerVips) != 0 {
			sc.LoadBalancerContext.DefaultDomain = loadBalancer.LoadBalancerVips[0]
		}
	}
	return nil
}

func diffSnatIps(snatProInfo *utils.SnatProInfo, snatIps []*clb.SnatIp) ([]*clb.SnatIp, []*clb.SnatIp, []*string, []*string) {
	subnetMap := make(map[string]bool)
	ipMap := make(map[string]map[string]bool)
	countMap := make(map[string]int)
	for _, snatIp := range snatProInfo.SnatIPs {
		subnetMap[snatIp.SubnetId] = true
		if snatIp.IP == nil {
			if _, exist := countMap[snatIp.SubnetId]; !exist {
				countMap[snatIp.SubnetId] = 0
			}
			countMap[snatIp.SubnetId] = countMap[snatIp.SubnetId] + 1
		} else {
			if _, exist := ipMap[snatIp.SubnetId]; !exist {
				ipMap[snatIp.SubnetId] = make(map[string]bool)
			}
			ipMap[snatIp.SubnetId][*snatIp.IP] = true
		}
	}

	addSnatIp := make([]*clb.SnatIp, 0)
	deleteSnatIp := make([]*string, 0)
	if snatIps != nil {
		for _, snatIp := range snatIps {
			if snatIp.SubnetId != nil && snatIp.Ip != nil {
				if _, exist := subnetMap[*snatIp.SubnetId]; !exist {
					deleteSnatIp = append(deleteSnatIp, common.StringPtr(*snatIp.Ip))
				}
			}
		}
	}

	for subnet, _ := range subnetMap {
		if snatIps != nil {
			for _, snatIp := range snatIps {
				if snatIp.SubnetId != nil && *snatIp.SubnetId == subnet && snatIp.Ip != nil {
					if snatIp.Ip != nil {
						if _, exist := ipMap[subnet]; exist {
							if _, exist := ipMap[subnet][*snatIp.Ip]; exist {
								delete(ipMap[subnet], *snatIp.Ip)
								continue
							}
						}
					}
					if count, exist := countMap[subnet]; exist {
						if count > 0 {
							countMap[subnet] = countMap[subnet] - 1
							continue
						}
					}
					deleteSnatIp = append(deleteSnatIp, common.StringPtr(*snatIp.Ip))
				}
			}
		}

		if _, exist := ipMap[subnet]; exist {
			for ip, _ := range ipMap[subnet] {
				addSnatIp = append(addSnatIp, &clb.SnatIp{SubnetId: common.StringPtr(subnet), Ip: common.StringPtr(ip)})
			}
		}

		if _, exist := countMap[subnet]; exist {
			for i := 0; i < countMap[subnet]; i++ {
				addSnatIp = append(addSnatIp, &clb.SnatIp{SubnetId: common.StringPtr(subnet)})
			}
		}
	}

	// 删除和添加的限制
	// 1. 删除SnatIP的时候，不能将全部SnatIP都删光。
	// 2. 添加SnatIP的时候，不能超过10个的数量限制。
	deleteFirstSnatIp := deleteSnatIp
	deleteSecondSnatIp := make([]*string, 0)
	if len(deleteSnatIp) != 0 && len(snatIps) == len(deleteSnatIp) {
		if len(deleteSnatIp) == 1 {
			deleteFirstSnatIp = make([]*string, 0)
		} else {
			deleteFirstSnatIp = deleteSnatIp[1:]
		}
		deleteSecondSnatIp = deleteSnatIp[0:1]
	}

	addFirstSnatIp := addSnatIp
	addSecondSnatIp := make([]*clb.SnatIp, 0)
	if len(addSnatIp) == 10 {
		addFirstSnatIp = addSnatIp[1:]
		addSecondSnatIp = addSnatIp[0:1]
	}

	return addFirstSnatIp, addSecondSnatIp, deleteFirstSnatIp, deleteSecondSnatIp
}

// kateway: 修改 Clb 各种网络配置
func SyncLoadbalancerNetworks(sc *SyncContext) error {
	ing := sc.Ingress
	loadBalancer, err := sc.LoadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		return err
	}
	region := sc.LoadBalancerContext.LoadBalancerResource.Spec.Region
	crossRegionId, crossRegionIdExist := utils.GetCrossRegionId(sc.Ingress)
	crossVPCId, crossVPCIdExist := utils.GetCrossVPCId(sc.Ingress)
	crossType := utils.GetCrossType(sc.Ingress)
	hybridType := utils.GetHybridType(sc.Ingress)
	snatProInfo, _ := utils.GetSnatProInfo(sc.Ingress)

	// 存量负载均衡在这里开启SnatPro，以支持GlobalRoute直连的能力
	modifyed := false
	addSnatIp := make([]*clb.SnatIp, 0)
	addSecondSnatIp := make([]*clb.SnatIp, 0)
	deleteSnatIp := make([]*string, 0)
	deleteSecondSnatIp := make([]*string, 0)
	request := clbInner.NewModifyLoadBalancerAttributesRequest()
	request.LoadBalancerId = loadBalancer.LoadBalancerId
	if crossType == utils.CrossType2_0 {
		if (crossRegionIdExist && crossRegionId != config.Global.Region) || (crossVPCIdExist && crossVPCId != config.Global.VPCID) {
			if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro == false {
				request.SnatPro = common.BoolPtr(true)
				modifyed = true
			}
		}
	}
	if crossType == utils.CrossType1_2 {
		if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro == false {
			request.SnatPro = common.BoolPtr(true)
			request.NoLBNat = common.BoolPtr(true)
			modifyed = true
		}
	}
	if hybridType == utils.HybridType_PVGW || hybridType == utils.HybridType_CCN || crossType == utils.CrossType1_1 {
		if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro == false {
			request.SnatPro = common.BoolPtr(true)
			modifyed = true
		}
		if snatProInfo == nil || snatProInfo.SnatIPs == nil || len(snatProInfo.SnatIPs) == 0 {
			if ing.Type() == types.CoreIngress {
				if hybridType == utils.HybridType_PVGW || hybridType == utils.HybridType_CCN {
					return types.NewError(errcode.HybridCloudWithoutSnatProSetting, "", utils.IngressName(ing))
				} else {
					return types.NewError(errcode.CrossRegionWithoutSnatProSetting, "", utils.IngressName(ing))
				}
			}
		} else {
			addSnatIp, addSecondSnatIp, deleteSnatIp, deleteSecondSnatIp = diffSnatIps(snatProInfo, loadBalancer.SnatIps)
		}
	}
	// 负载均衡需要通过跨域绑定1.0的能力，进行跨域绑定。SNAT Pro和该能力冲突
	if crossType == utils.CrossType1_0 {
		targetCrossRegionID := config.Global.Region
		if targetRegionId, exist := utils.GetTargetCrossRegionId(ing); exist {
			targetCrossRegionID = targetRegionId
		}
		targetCrossVPCId := config.Global.VPCID // kateway 这是单集群的默认值
		if targetVpcId, exist := utils.GetTargetCrossVPCId(ing); exist {
			targetCrossVPCId = targetVpcId
		}
		if loadBalancer.TargetRegionInfo == nil ||
			loadBalancer.TargetRegionInfo.VpcId == nil || *loadBalancer.TargetRegionInfo.VpcId != config.Global.VPCID ||
			loadBalancer.TargetRegionInfo.Region == nil || *loadBalancer.TargetRegionInfo.Region != config.Global.Region {
			request.TargetRegionInfo = &clb.TargetRegionInfo{
				Region: common.StringPtr(targetCrossRegionID),
				VpcId:  common.StringPtr(targetCrossVPCId),
			}
			modifyed = true
			if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro == true { // 考虑SNAT Pro对变更的影响
				innerRequest := clbInner.NewModifyLoadBalancerAttributesRequest()
				innerRequest.LoadBalancerId = loadBalancer.LoadBalancerId
				innerRequest.SnatPro = common.BoolPtr(false)
				if _, err := tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.From(sc, ing, region), innerRequest); err != nil {
					return types.NewError(errcode.SnatProNotSupportError, err.Error(), utils.IngressName(ing))
				}
			}
		}
	}
	if modifyed {
		// IPv6FullChain 资源开启SNATPro的同时，必须开启混绑能力。
		// Tips: "Code":"InvalidParameterValue","Message":"FullChain IPv6 CLB does not support to enable SnatPro before enable MixIpTarget","RequestId":"01c4db93-30bb-4114-872e-47f4d1bfafbb"
		if request.SnatPro != nil && *request.SnatPro == true {
			if loadBalancer.AddressIPVersion != nil && *loadBalancer.AddressIPVersion == "IPv6FullChain" && loadBalancer.MixIpTarget != nil && *loadBalancer.MixIpTarget == false {
				innerRequest := clb.NewModifyLoadBalancerMixIpTargetRequest()
				innerRequest.LoadBalancerIds = []*string{loadBalancer.LoadBalancerId}
				innerRequest.MixIpTarget = common.BoolPtr(true)
				if _, err := tencentapi.Instance.ModifyLoadBalancerMixIpTarget(cloudctx.From(sc, ing, region), innerRequest); err != nil {
					return types.NewError(errcode.MixIpTargetError, err.Error(), utils.IngressName(ing))
				}
			}
		}
		if _, err := tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.From(sc, ing, region), request); err != nil {
			if sdkError, ok := err.(*skderrors.TencentCloudSDKError); ok { // Code=FailedOperation, Message=cross LoadBalancer is not supported snatPro
				if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "LoadBalancer is not supported snatPro") {
					return types.NewError(errcode.SnatProNotSupportForLoadbalancerError, sdkError.Error(), utils.IngressName(ing))
				}
			}
			return types.NewError(errcode.SnatProNotSupportError, err.Error(), utils.IngressName(ing))
		}
		if err := sc.LoadBalancerContext.UpdateLoadBalancer(sc); err != nil {
			return err
		}
	}

	if deleteSnatIp != nil && len(deleteSnatIp) != 0 {
		request := clb.NewDeleteLoadBalancerSnatIpsRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		request.Ips = deleteSnatIp
		if _, err := tencentapi.Instance.DeleteLoadBalancerSnatIps(cloudctx.From(sc, ing, region), request); err != nil {
			return err
		}
	}

	if addSnatIp != nil && len(addSnatIp) != 0 {
		request := clb.NewCreateLoadBalancerSnatIpsRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		request.SnatIps = addSnatIp
		if _, err := tencentapi.Instance.CreateLoadBalancerSnatIps(cloudctx.From(sc, ing, region), request); err != nil {
			return err
		}
	}

	if deleteSecondSnatIp != nil && len(deleteSecondSnatIp) != 0 {
		request := clb.NewDeleteLoadBalancerSnatIpsRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		request.Ips = deleteSecondSnatIp
		if _, err := tencentapi.Instance.DeleteLoadBalancerSnatIps(cloudctx.From(sc, ing, region), request); err != nil {
			return err
		}
	}

	if addSecondSnatIp != nil && len(addSecondSnatIp) != 0 {
		request := clb.NewCreateLoadBalancerSnatIpsRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		request.SnatIps = addSecondSnatIp
		if _, err := tencentapi.Instance.CreateLoadBalancerSnatIps(cloudctx.From(sc, ing, region), request); err != nil {
			return err
		}
	}
	return nil
}

func syncSnatProConfig(sc *SyncContext) error {
	ing := sc.Ingress
	hybridType := utils.GetHybridType(ing)
	crossType := utils.GetCrossType(sc.Ingress)
	if hybridType == utils.HybridType_PVGW || hybridType == utils.HybridType_CCN || crossType == utils.CrossType1_1 {
		loadBalancer, err := sc.LoadBalancerContext.GetLoadBalancer(sc)
		if err != nil {
			return err
		}
		snatProInfo, err := utils.GetSnatProInfo(ing)
		if err != nil {
			return err
		}
		modifyed := false
		addSnatIp := make([]*clb.SnatIp, 0)
		addSecondSnatIp := make([]*clb.SnatIp, 0)
		deleteSnatIp := make([]*string, 0)
		deleteSecondSnatIp := make([]*string, 0)
		request := clbInner.NewModifyLoadBalancerAttributesRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro == false {
			request.SnatPro = common.BoolPtr(true)
			modifyed = true
		}
		if snatProInfo == nil || snatProInfo.SnatIPs == nil || len(snatProInfo.SnatIPs) == 0 {
			if hybridType == utils.HybridType_PVGW || hybridType == utils.HybridType_CCN {
				return types.NewError(errcode.HybridCloudWithoutSnatProSetting, "", utils.IngressName(ing))
			} else {
				return types.NewError(errcode.CrossRegionWithoutSnatProSetting, "", utils.IngressName(ing))
			}
		}
		addSnatIp, addSecondSnatIp, deleteSnatIp, deleteSecondSnatIp = diffSnatIps(snatProInfo, loadBalancer.SnatIps)
		if modifyed {
			// IPv6FullChain 资源开启SNATPro的同时，必须开启混绑能力。
			// Tips: "Code":"InvalidParameterValue","Message":"FullChain IPv6 CLB does not support to enable SnatPro before enable MixIpTarget","RequestId":"01c4db93-30bb-4114-872e-47f4d1bfafbb"
			if request.SnatPro != nil && *request.SnatPro == true {
				if loadBalancer.AddressIPVersion != nil && *loadBalancer.AddressIPVersion == "IPv6FullChain" && loadBalancer.MixIpTarget != nil && *loadBalancer.MixIpTarget == false {
					innerRequest := clb.NewModifyLoadBalancerMixIpTargetRequest()
					innerRequest.LoadBalancerIds = []*string{loadBalancer.LoadBalancerId}
					innerRequest.MixIpTarget = common.BoolPtr(true)
					if _, err := tencentapi.Instance.ModifyLoadBalancerMixIpTarget(cloudctx.From(sc, ing, sc.LoadBalancerContext.Region), innerRequest); err != nil {
						return types.NewError(errcode.MixIpTargetError, err.Error(), utils.IngressName(ing))
					}
				}
			}
			if _, err := tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.From(sc, ing, sc.LoadBalancerContext.Region), request); err != nil {
				if sdkError, ok := err.(*skderrors.TencentCloudSDKError); ok {
					if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "LoadBalancer is not supported snatPro") {
						return types.NewError(errcode.SnatProNotSupportForLoadbalancerError, sdkError.Error(), utils.IngressName(ing))
					}
				}
				return types.NewError(errcode.SnatProNotSupportError, err.Error(), utils.IngressName(ing))
			}
			if err := sc.LoadBalancerContext.UpdateLoadBalancer(sc); err != nil {
				return err
			}
		}

		if deleteSnatIp != nil && len(deleteSnatIp) != 0 {
			request := clb.NewDeleteLoadBalancerSnatIpsRequest()
			request.LoadBalancerId = loadBalancer.LoadBalancerId
			request.Ips = deleteSnatIp
			if _, err := tencentapi.Instance.DeleteLoadBalancerSnatIps(cloudctx.From(sc, ing, sc.LoadBalancerContext.Region), request); err != nil {
				return err
			}
		}

		if addSnatIp != nil && len(addSnatIp) != 0 {
			request := clb.NewCreateLoadBalancerSnatIpsRequest()
			request.LoadBalancerId = loadBalancer.LoadBalancerId
			request.SnatIps = addSnatIp
			if _, err := tencentapi.Instance.CreateLoadBalancerSnatIps(cloudctx.From(sc, ing, sc.LoadBalancerContext.Region), request); err != nil {
				return err
			}
		}

		if deleteSecondSnatIp != nil && len(deleteSecondSnatIp) != 0 {
			request := clb.NewDeleteLoadBalancerSnatIpsRequest()
			request.LoadBalancerId = loadBalancer.LoadBalancerId
			request.Ips = deleteSecondSnatIp
			if _, err := tencentapi.Instance.DeleteLoadBalancerSnatIps(cloudctx.From(sc, ing, sc.LoadBalancerContext.Region), request); err != nil {
				return err
			}
		}

		if addSecondSnatIp != nil && len(addSecondSnatIp) != 0 {
			request := clb.NewCreateLoadBalancerSnatIpsRequest()
			request.LoadBalancerId = loadBalancer.LoadBalancerId
			request.SnatIps = addSecondSnatIp
			if _, err := tencentapi.Instance.CreateLoadBalancerSnatIps(cloudctx.From(sc, ing, sc.LoadBalancerContext.Region), request); err != nil {
				return err
			}
		}
	}
	return nil
}

func PreCheckBeforeCreateLb(ingress types.Ingress) error {
	// "kubernetes.io/ingress.subnetId"  Annotation Check.
	if subnetId, exist := ingress.Annotations()[IngressSubnetId]; exist {
		if subnetId == "" {
			return types.NewError(errcode.LoadBalancerSubnetInvalidError, "subnetId in the annotation of ingress is empty", utils.IngressName(ingress))
		}
	}

	// "kubernetes.io/ingress.loadbalancerType"  Annotation Check.
	// 负载均衡实例的网络类型：
	// OPEN：公网属性， INTERNAL：内网属性。
	if loadBalancerType, exist := ingress.Annotations()[IngressLoadBalancerType]; exist {
		if loadBalancerType != "OPEN" && loadBalancerType != "INTERNAL" {
			return types.NewError(errcode.LoadBalancerParamTypeInvalidError, "loadBalancerType in the annotation is invalid.", utils.IngressName(ingress))
		}
	}

	// 检查跨地域属性配置的正确性
	if ok := crossregion.CrossRegionServiceInstance.CheckIngressRegion(ingress); !ok {
		regionId, _ := utils.GetCrossRegionId(ingress)
		return types.NewError(errcode.CrossRegionInvalidReginError, "", utils.IngressName(ingress), regionId)
	}

	// 检查跨地域配置
	crossRegionId, crossRegionIdExist := utils.GetCrossRegionId(ingress)
	crossVPCId, crossVPCIdExist := utils.GetCrossVPCId(ingress)
	crossType := utils.GetCrossType(ingress)
	if crossRegionIdExist {
		if !crossVPCIdExist { // 指定跨地域未指定VPCId或已有负载均衡Id
			return types.NewError(errcode.CrossRegionConfigError, "", utils.IngressName(ingress))
		}
	}
	if crossVPCIdExist {
		checkRegion := config.Global.Region
		if crossRegionIdExist {
			checkRegion = crossRegionId
		}
		if crossType == utils.CrossType2_0 {
			if ok := crossregion.CrossRegionServiceInstance.CheckCCNInstance(checkRegion, crossVPCId); !ok { // 其他地域VPC与集群VPC不在同一个云联网
				return types.NewError(errcode.CrossRegionCNNConfigError, "", utils.IngressName(ingress))
			}
		}
	}

	if utils.BackendOnlyIngress(ingress) {
		return types.NewError(errcode.BackendManageOnlyMustUseExistLBError, "", utils.IngressName(ingress))
	}

	return nil
}

// deleteLoadBalancer delete lb for service
func DeleteLoadBalancer(ingress types.Ingress, lbID string, region string) error {
	request := clb.NewDeleteLoadBalancerRequest()
	request.LoadBalancerIds = []*string{&lbID}
	request.ForceDelete = lo.ToPtr(ingress.ForceDelete())
	if _, err := tencentapi.Instance.DeleteLoadBalancer(cloudctx.New(ingress, region), request); err != nil {
		klog.Errorf("DeleteLoadBalancer Error, %v", err.Error())
		return err
	}
	return nil
}

func UpdateIngressEntry(ctx context.Context, ing types.Ingress, vips []*string, domain *string) error {
	originIngress := ing.StatusLoadBalancer().Ingress
	ingress := make([]v1.LoadBalancerIngress, 0)
	if vips != nil && len(vips) != 0 {
		for _, v := range vips { // IPv4、IPv6
			ingress = append(ingress, v1.LoadBalancerIngress{IP: *v})
		}
		if domain != nil {
			for index, _ := range ingress {
				ingress[index].Hostname = *domain
			}
		}
	} else if domain != nil {
		ingress = append(ingress, v1.LoadBalancerIngress{Hostname: *domain})
	}
	if len(originIngress) == 0 || ingressStatusChange(originIngress, ingress) {
		err := ing.UpdateLoadBalancerStatus(ctx, v1.LoadBalancerStatus{
			Ingress: ingress,
		})
		if err != nil {
			klog.Infof("Ingress update error for %s. %v\n", utils.IngressName(ing), err)
			return err
		}
	}
	return nil
}

func ingressStatusChange(a, b []v1.LoadBalancerIngress) bool {
	if len(a) != len(b) {
		return true
	}
	for i, v := range a {
		if v.IP != b[i].IP || v.Hostname != b[i].Hostname {
			return true
		}
	}
	return false
}

func validateLoadBalancerClusterTag(sc *SyncContext) error {
	ingress := sc.Ingress
	lbc := sc.LoadBalancerContext
	loadBalancer, err := lbc.GetLoadBalancer(sc)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == "InvalidParameter.FormatError" || sdkError.Code == "InvalidParameterValue.Length" {
				return types.NewError(errcode.LoadBalancerLocatedError, sdkError.Error(), utils.IngressName(ingress))
			}
		}
		klog.Errorf("ingress (%s) wants to use existed lbId (%s), but get error: %v", ingress.Name(), lbc.LoadBalancerId, err)
		return err
	}
	if loadBalancer == nil {
		return types.NewError(errcode.ReuseExistLoadBalancerNotExistError, "", ingress.Name(), loadBalancer.LoadBalancerId)
	}
	// 校验 CLB clusterId tag 的合法性
	clusterId := getCLusterIdByTags(loadBalancer.Tags)
	fromOtherCluster, _ := utils.GetFromOtherCluster(ingress)
	if fromOtherCluster != "" {
		if clusterId != fromOtherCluster {
			return types.NewError(errcode.NotBelongToSpecifiedClusterError, "", utils.IngressName(ingress), fromOtherCluster)
		}
	} else {
		if clusterId != "" && clusterId != config.Global.ClusterName {
			return types.NewError(errcode.ReuseBelongToAnotherClusterError, "", utils.IngressName(ingress))
		}
	}
	return nil
}

func validaCreateLoadbBalancerConfig(ingress types.Ingress, exist *LoadBalancerContext) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	// verify exist lb
	existLbID := exist.LoadBalancerId
	existLbInfo, err := exist.GetLoadBalancer(context.TODO())
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == "InvalidParameter.FormatError" || sdkError.Code == "InvalidParameterValue.Length" {
				return types.NewError(errcode.LoadBalancerLocatedError, sdkError.Error(), utils.IngressName(ingress))
			}
		}
		klog.Errorf("ingress (%s) wants to use existed lbId (%s), but get error: %v", ingress.Name(), existLbID, err)
		return err
	}
	// check exist
	if existLbInfo == nil {
		return types.NewError(errcode.ReuseExistLoadBalancerNotExistError, "", ingress.Name(), existLbID)
	}
	// check lb status
	if existLbInfo.Status == nil || *existLbInfo.Status != LOADBALANCESTATUSNORMAL {
		return fmt.Errorf("Lb: %s status is not running when using exist lb", existLbID)
	}
	// check type
	if *existLbInfo.Forward != FORWARDTYPE {
		return fmt.Errorf("Lb:%s type %d not %d", existLbID, existLbInfo.Forward, FORWARDTYPE)
	}

	if !utils.BackendOnlyIngress(ingress) {
		// check if exist lb created by tke
		if exist.LoadBalancerResource != nil {
			createByTke := checkCreatedByTKENew(exist.LoadBalancerResource)
			// 由TKE创建的LB是不能被复用的
			if createByTke {
				return types.NewError(errcode.ReuseExistLoadBalancerConflictTKEError, "", utils.IngressName(ingress), existLbID)
			}
		}

		// not allow use exist loadBalancer for multi cluster
		if lbCluster := getCLusterIdByTags(existLbInfo.Tags); lbCluster != "" && lbCluster != config.Global.ClusterName {
			return types.NewError(errcode.ReuseBelongToAnotherClusterError, "", utils.IngressName(ingress))
		}
	}

	// 检查跨地域配置
	crossRegionId, crossRegionIdExist := utils.GetCrossRegionId(ingress)
	crossType := utils.GetCrossType(ingress)
	if existLbInfo.VpcId == nil {
		return types.NewError(errcode.LoadBalancerDoNotSupportCNNError, "", utils.IngressName(ingress))
	}
	if *existLbInfo.VpcId != config.Global.VPCID {
		checkRegion := config.Global.Region
		if crossRegionIdExist {
			checkRegion = crossRegionId
		}
		if crossType == utils.CrossType2_0 {
			if ok := crossregion.CrossRegionServiceInstance.CheckCCNInstance(checkRegion, *existLbInfo.VpcId); !ok { // 其他地域VPC与集群VPC不在同一个云联网
				return types.NewError(errcode.CrossRegionCNNConfigError, "", utils.IngressName(ingress))
			}
		}
	}

	// check if used by other svc
	// usedIngressUUID, err := getIngressUUIDByLBID(ingress, existLbID, exist.Region)
	// if err != nil {
	//	klog.Errorf("Error when get lb: %s used ingress, err: %v", existLbID, err)
	//	return err
	// }
	// if usedIngressUUID != "" && usedIngressUUID != ingress.UID() {
	//	return types.NewError(ingressError.ReuseExistLoadBalancerConflictIngressError, "", utils.IngressName(ingress), existLbID, usedIngressUUID)
	// }
	// none => exist check if listener is empty
	// if current == nil || current.LoadBalancerId == "" {
	//	listeners, err := exist.GetListeners()
	//	if err != nil {
	//		klog.Errorf("Error check exist lb: %s listener if used when GetLoadBalancerListener, err: %v", existLbID, err)
	//		return err
	//	}
	//	if len(listeners) != 0 {
	//		return types.NewError(ingressError.ReuseExistLoadBalancerNotEmptyError, "", utils.IngressName(ingress), existLbID)
	//	}
	// }
	return nil
}

func getCLusterIdByTags(lbTags []*clb.TagInfo) string {
	for _, lbTag := range lbTags {
		if lbTag.TagKey != nil && *lbTag.TagKey == types.TagKeyClusterID.String() && lbTag.TagValue != nil {
			return *lbTag.TagValue
		}
	}
	return ""
}

// 不能轻易修改，和域名相关，集群入口强关联
func makeLbName(clusterId, resourceName string) (string, string) {
	// create
	lbName := resourceName
	// regularize lb name: ccs_[cluster_id]_[service_name]
	lbName = fmt.Sprintf("ccs_%s_%s", clusterId, lbName)

	if len(lbName) > 40 {
		lbName = lbName[0:40]
	}
	lbDomainPrefix := strings.Replace(lbName, "_", "-", -1)
	if len(lbDomainPrefix) > 20 {
		lbDomainPrefix = lbDomainPrefix[0:19]
	}
	return lbName, lbDomainPrefix
}

func createLoadBalancer(ctx context.Context, ingress types.Ingress) (string, error) {
	lbName, _ := makeLbName(config.Global.ClusterName, ingress.Name())

	var err error
	clientToken := ""
	if ingress.Annotations() != nil && ingress.Annotations()[utils.ClientTokenAnnotation] != "" {
		clientToken = ingress.Annotations()[utils.ClientTokenAnnotation]
	} else {
		clientToken = uuid.New().String()
		ingress, err = ingress.UpdateAnnotation(ctx, map[string]string{
			utils.ClientTokenAnnotation: clientToken,
		}, nil)
		if err != nil {
			return "", err
		}
	}

	// 装配 CreateLoadBalancerRequest
	request := tencentapi.NewCreateLoadBalancerRequest()
	request.LoadBalancerName = &lbName
	request.Forward = common.Int64Ptr(FORWARDTYPE)
	request.VpcId = &config.Global.VPCID
	request.ClientToken = &clientToken

	if config.Global.ProjectID > 0 {
		request.ProjectId = common.Int64Ptr(config.Global.ProjectID)
	}

	// 处理集群外VPC接入的场景
	if vpcId, exist := utils.GetCrossVPCId(ingress); exist {
		if vpcId != config.Global.VPCID {
			request.VpcId = common.StringPtr(vpcId)
			if utils.GetCrossType(ingress) == utils.CrossType2_0 {
				request.SnatPro = common.BoolPtr(true)
			}
		}
	}

	// 处理跨地域接入，后端绑定默认开启 SnatPro 功能
	region := config.Global.Region
	if crossRegionId, exist := utils.GetCrossRegionId(ingress); exist {
		if crossRegionId != config.Global.Region {
			region = crossRegionId
		}
	}

	// 1. 设置 CLB带宽配置
	internetChargeType, _ := ingress.Annotations()[InternetChargeType]
	internetMaxBandwidthOut, _ := ingress.Annotations()[InternetMaxBandwidthOut]
	if internetChargeType != "" && internetMaxBandwidthOut != "" {
		band, err := strconv.ParseInt(internetMaxBandwidthOut, 10, 64)
		if err == nil {
			internetAccessible := clb.InternetAccessible{
				InternetChargeType:      &internetChargeType,
				InternetMaxBandwidthOut: &band,
			}
			request.InternetAccessible = &internetAccessible
		}
	}

	// 2. 设置 CLB网络类型
	// IPv6类型的负载均衡需要指定子网，但是是公网类型。子网参数与负载均衡类型的绑定关系不再成立
	if subnetId, exist := ingress.Annotations()[IngressSubnetId]; exist {
		request.SubnetId = &subnetId
		request.LoadBalancerType = common.StringPtr(LOADBALANCETYPEINTERNAL)
	} else {
		request.LoadBalancerType = common.StringPtr(LOADBALANCETYPEOPEN)
	}

	if loadBalancerType, exist := ingress.Annotations()[IngressLoadBalancerType]; exist {
		request.LoadBalancerType = common.StringPtr(loadBalancerType)
	}

	// 3. 同步集群标签到CLB
	tagResponses, err := GetCCSResourceTags(ctx, config.Global.ClusterName)
	if err != nil {
		return "", err
	}
	tagsMap := lo.SliceToMap(tagResponses, func(tr *tag.TagResource) (string, *clb.TagInfo) {
		return *tr.TagKey, types.TagKey(*tr.TagKey).ToCLBTagInfo(*tr.TagValue)
	})

	// 4. 设置用户自定义CLB参数
	if err = getLbParamFromAnnotation(ingress, request); err != nil {
		return "", err
	}
	request.Number = common.Uint64Ptr(1) // 危险, 参数没有限制, 固定创建的负载均衡数量不能被用户入参修改。
	for _, tagInfo := range request.Tags {
		tagsMap[*tagInfo.TagKey] = tagInfo
	}

	// 5. 设置Ingress生命周期标签
	// 系统标签优先，用户自定义标签其次，用户集群标签优先级最低
	defaultTags := []*clb.TagInfo{
		types.TagKeyClusterID.ToCLBTagInfo(config.Global.ClusterName),
		types.TagKey(GetAutoCreatedTagKey(ingress)).ToCLBTagInfo(GetAutoCreatedTagValue()),
		types.TagKeyLifecycleOwner.ToCLBTagInfo(types.TagValueLifecycleOwnedByTKE),
	}

	if ingress.Type() == types.CoreIngress {
		if utils.IsInEKSCluster() {
			defaultTags = append(defaultTags,
				types.TagKeyObjectKind.ToCLBTagInfo("ingress"),
				types.TagKeyObjectName.ToCLBTagInfo(fmt.Sprintf("%s/%s", ingress.Namespace(), ingress.Name())),
			)
		} else {
			defaultTags = append(defaultTags,
				types.TagKeyIngressUUID.ToCLBTagInfo(ingress.UID()),
			)
		}
	} else if ingress.Type() == types.MultiClusterIngress {
		defaultTags = append(defaultTags,
			types.TagKeyMultiClusterIngressUUID.ToCLBTagInfo(ingress.UID()),
		)
	}

	for _, t := range defaultTags {
		tagsMap[*t.TagKey] = t
	}

	request.Tags = lo.Values(tagsMap)

	response, err := tencentapi.Instance.ExpandCreateLoadBalancer(cloudctx.From(ctx, ingress, region), request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == "LimitExceeded" {
				return "", types.NewError(errcode.LoadBalancerLimitExceeded, sdkError.Error())
			} else if sdkError.Code == "InvalidParameterValue" {
				if strings.Contains(sdkError.Message, "AddressIPVersion") { // The value of parameter AddressIPVersion must be IPV4 or IPV6.
					return "", types.NewError(errcode.LoadBalancerParamAddressIPVersinError, sdkError.Error(), utils.IngressName(ingress), *request.AddressIPVersion)
				} else if strings.Contains(sdkError.Message, "SubnetId") || strings.Contains(sdkError.Message, "subnet not exists") {
					// [TencentCloudSDKError] Code=InvalidParameterValue, Message=Invoke vpc failed: subnet not exists, RequestId=b5f6053b-f0d7-42f9-bb4a-f5da09339986
					// [TencentCloudSDKError] Code=InvalidParameterValue, Message=参数`SubnetId`中`sub`不合规范
					return "", types.NewError(errcode.LoadBalancerParamSubnetNotExistError, sdkError.Error(), utils.IngressName(ingress), *request.SubnetId)
				}
			} else if sdkError.Code == "ResourceInsufficient" {
				if strings.Contains(sdkError.Message, "The number of IP") { // 子网IP不足，导致无法创建内网负载均衡。Code=ResourceInsufficient, Message=The number of IP in subnet subnet-963gypx4 is not enough., RequestId=f8f0eea6-32fc-48a1-9f59-e4af557d79ee
					return "", types.NewError(errcode.LoadBalancerSubnetIPInsufficientError, sdkError.Error(), *request.SubnetId)
				}
				return "", types.NewError(errcode.LoadBalancerResourceInsufficientError, sdkError.Error(), utils.IngressName(ingress)) // CLB资源不足，导致无法创建负载均衡。
			} else if sdkError.Code == "FailedOperation" { // Code=FailedOperation, Message=Order parameter checking failed: The name of loadbalancer is invalid, RequestId=1e553d08-3b04-45e5-9fb8-aac82d6eff33
				if strings.Contains(sdkError.Message, "Insufficient account balance") {
					return "", types.NewError(errcode.InsufficientAccountBalanceError, sdkError.Error())
				} else if strings.Contains(sdkError.Message, "do not support create IPv6") { // Region ap-hangzhou-ec do not support create IPv6 Nat loadbalancer
					return "", types.NewError(errcode.LoadBalancerAddressIPVersinSupportError, sdkError.Error(), utils.IngressName(ingress))
				} else if strings.Contains(sdkError.Message, "The name of loadbalancer is invalid") { // Order parameter checking failed: The name of loadbalancer is invalid
					return "", types.NewError(errcode.LoadBalancerNameFormateError, sdkError.Error(), utils.IngressName(ingress), *request.LoadBalancerName)
				}
			} else if sdkError.Code == "InvalidParameter.ClientTokenLimitExceeded" { // 幂等方式创建 CLB 不会成功了。Code=InvalidParameter.ClientTokenLimitExceeded, Message=Failed to create by ClientToken `system-XXX` limit exceeded
				// 需要清理已过时失效的 ClientToken
				if ingress.Annotations() != nil && ingress.Annotations()[utils.ClientTokenAnnotation] != "" {
					ingress, err = ingress.UpdateAnnotation(ctx, map[string]string{utils.ClientTokenAnnotation: ""}, nil)
					if err != nil {
						return "", err
					}
				}
				return "", types.NewError(errcode.LoadBalancerClientTokenHasExpiredError, sdkError.Error(), utils.IngressName(ingress))
			}

		}
		return "", err
	}
	if len(response.Response.LoadBalancerIds) == 0 {
		return "", fmt.Errorf("Error empty LoadBalancerId after CreateLoadBalancer")
	}
	return *response.Response.LoadBalancerIds[0], nil
}

func getLbParamFromAnnotation(ingress types.Ingress, createDetail *tencentapi.ExpandCreateLoadBalancerRequest) error {
	if param, exist := ingress.Annotations()[AnnoExtensiveParameters]; exist {
		if err := json.Unmarshal([]byte(param), createDetail); err != nil {
			return types.NewError(errcode.LoadBalancerExtensiveParamError, err.Error(), utils.IngressName(ingress))
		}
	}
	return nil
}

// func  GetLoadBalancerByIngressId(ingress *utils.IngressWrapper) (*clb.LoadBalancer, error) {
//	lbId, err := getIngressLoadBalancer(ingress)
//	if err != nil {
//		return nil, err
//	}
//	if lbId != "" {
//		lbInfo, err := GetLoadBalancerByLBId(ingress, lbId)
//		if err != nil {
//			return nil, err
//		}
//		if lbInfo != nil {
//			return lbInfo, nil
//		} else {
//			return nil, fmt.Errorf("lb is nil")
//		}
//	}
//	return nil, fmt.Errorf("lb does not exist")
// }

func EnsureDeleteLoadBalancer(sc *SyncContext) error {
	span, sc := sc.StartSpan()
	defer span.Finish()
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	ingress := sc.Ingress
	loadBalancerContext, err := GetLoadBalancerByLoadbalancerResource(sc)
	if err != nil {
		return err
	}
	if loadBalancerContext == nil {
		klog.Infof("lb does not exist: ingress: %s", utils.IngressName(ingress))
		return nil
	}

	loadBalancer, err := loadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		if serviceError, ok := lo.ErrorsAs[*types.Error](err); ok && serviceError.ErrorCode.Code == errcode.LoadBalancerNotExistError.Code { // 幂等处理
			if err := DeleteLoadBalancerResource(sc, loadBalancerContext.LoadBalancerId); err != nil {
				return err
			}
			return nil
		}
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == "InvalidParameter.FormatError" || sdkError.Code == "InvalidParameterValue.Length" { // ???
				return types.NewError(errcode.LoadBalancerLocatedError, sdkError.Error(), ingress.Name())
			}
		}
		return err
	}

	if loadBalancer == nil {
		klog.Infof("lb has been deleted: loadBalancerContext: %s ingress: %s", loadBalancerContext, utils.IngressName(ingress))
		return nil
	}

	if canProcess := LockLoadBalancerResource(sc, *loadBalancer.LoadBalancerId, ingress); !canProcess {
		return types.NewError(errcode.ReuseConcurrentOperationError, "", utils.IngressName(ingress))
	}
	defer func(loadBalancerId string) {
		if err := UnlockLoadBalancerResource(sc, loadBalancerId); err != nil {
			klog.Errorf("LoadBalancerResource unlock failed for lbid: %s, err: %v", loadBalancerId, err)
		}
	}(*loadBalancer.LoadBalancerId)

	lbId := loadBalancerContext.LoadBalancerId
	region := loadBalancerContext.Region
	createByTke := checkCreatedByTKENew(loadBalancerContext.LoadBalancerResource)
	clusterId := getCLusterIdByTags(loadBalancer.Tags)

	lb := types.NewLB(loadBalancer, createByTke)
	if clusterId == config.Global.ClusterName && !utils.BackendOnlyIngress(ingress) {
		if err := checkBackendEmpty(sc, ingress, loadBalancerContext); err != nil {
			return err
		}

		if lb.IsDeletable() {
			klog.Infof("Deleting the loadbalancer %q created by TKE of ingress %s, deletion protection status: %s",
				lbId, utils.IngressName(ingress), lo.Ternary(lb.IsDeletionProtectionEnabled(), "enabled", "disabled"))

			// clb或者监听器的删除也可能导致node与clb的解绑，所以优雅删除开关打开时需要入队处理node的finalizer
			var nodesByID map[string]*v1.Node
			var listenersBackendsByKey map[string]*clb.ListenerBackend
			if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
				nodesByID, err = services.GetNodesByID(cluster_service.Instance.NodeLister())
				if err != nil {
					return err
				}
				listenersBackendsByKey, err = loadBalancerContext.GetListenersBackend(sc)
				if err != nil {
					return err
				}
			}

			if err := DeleteLoadBalancer(ingress, lbId, region); err != nil {
				if sdkError, ok := lo.ErrorsAs[*errors.TencentCloudSDKError](err); ok {
					if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "deletion protection enabled") {
						return types.NewError(errcode.DeletionProtectionError, sdkError.Message, utils.IngressName(ingress))
					}
					if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "be deleted") {
						return types.NewError(errcode.ForbiddenDeletionError, sdkError.Message, utils.IngressName(ingress))
					}
				}
				return err
			} else if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
				nodes := services.GetNodesFromListenersBackendsByKey(listenersBackendsByKey, nodesByID)
				for node := range nodes {
					cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueNode(node)
				}
			}

			tagResourceList := utils.ConvertTagResourceList(loadBalancer.Tags)
			RecycleLoadbalancerTags(sc, ingress, loadBalancerContext.Region, tagResourceList)
		} else {
			cleanListeners := make([]*clb.Listener, 0)
			listeners, err := GetLoadBalancerListener(sc, ingress, lbId, region)
			if err != nil {
				klog.Errorf("Error deleting exist lb: %s listener when GetLoadBalancerListener, err: %v", lbId, err)
				return err
			}
			ingressListeners := GetLoadBalancerResourceListenerByIngress(ingress, loadBalancerContext.LoadBalancerResource)
			listenerByID := make(map[string]*clb.Listener)
			for _, ingressListener := range ingressListeners {
				for index, listener := range listeners {
					if *listener.Port == int64(ingressListener.Port) && *listener.Protocol == ingressListener.Protocol && (listener.ListenerName != nil && *listener.ListenerName == TKEDedicatedListenerName) {
						curListener := listeners[index]
						cleanListeners = append(cleanListeners, curListener)
						listenerByID[*curListener.ListenerId] = listeners[index]
					}
				}
			}

			rewrites, err := DescribeRewrite(sc, ingress, lbId, region)
			if err != nil {
				klog.Errorf("Error deleting exist lb: %s listener when DescribeRewrite, err: %v", lbId, err)
				return err
			}
			if len(rewrites) != 0 {
				operatorRewrite := make([]*clb.RuleOutput, 0)
				for index, rewrite := range rewrites {
					for _, cleanListener := range cleanListeners {
						if *rewrite.ListenerId == *cleanListener.ListenerId || *rewrite.RewriteTarget.TargetListenerId == *cleanListener.ListenerId {
							operatorRewrite = append(operatorRewrite, rewrites[index])
							break
						}
					}
				}
				if err := DeleteRewrites(sc, ingress, loadBalancer, region, ConvertToRewriteRules(operatorRewrite)); err != nil {
					return err
				}
			}

			// 监听器的删除也可能导致node与clb的解绑，所以优雅删除开关打开时需要入队处理node的finalizer
			// 这段逻辑需要在调用api之前，避免调用api成功但入队失败
			var nodesByID map[string]*v1.Node
			var listenersBackendsByKey map[string]*clb.ListenerBackend
			if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
				nodesByID, err = services.GetNodesByID(cluster_service.Instance.NodeLister())
				if err != nil {
					return err
				}
				listenersBackendsByKey, err = loadBalancerContext.GetListenersBackend(sc)
				if err != nil {
					return err
				}
			}
			for _, listener := range cleanListeners {
				if err := DeleteLoadBalancerListener(sc, ingress, lbId, region, *listener.ListenerId); err != nil {
					if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
						if sdkError.Code == "InvalidParameter" && strings.Contains(sdkError.Message, "Redirection config on the listener") { // Message=Redirection config on the listener lbl-de6ynvxl ,relieve redirection before delete
							return types.NewError(errcode.ListenerRedirectionError, "Redirection config on the listener", sdkError.Error())
						}
					}
					klog.Errorf("Error deleting exist lb: %s listener %s when waitUntilTaskDone, err: %v", lbId, *listener.ListenerId, err)
					return err
				} else if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
					listenerBackends, exist := listenersBackendsByKey[utils.GetListenerKey(int32(*(listenerByID[*listener.ListenerId].Port)), *(listenerByID[*listener.ListenerId].Protocol))]
					if exist {
						nodes := services.GetNodesFromListenerBackends(listenerBackends, nodesByID)
						for node := range nodes {
							cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueNode(node)
						}
					}
				}
			}
			if lb.IsAutoCreatedByTKE() {
				event.Instance.EventError(ingress, types.NewError(errcode.ExplicitCLBResourceLeak, "", utils.IngressName(ingress), lbId))
			}
			err = EnsureLoadBalancerTagsDeleted(sc, loadBalancerContext)
			if err != nil {
				klog.Errorf("Error deleting clear exist lb: %s tags, err: %v", lbId, err)
				return err
			}
		}
	} else if !lb.IsAutoCreatedByTKE() {
		fromOtherCluster, _ := utils.GetFromOtherCluster(ingress)
		if clusterId != "" && clusterId == fromOtherCluster {
			if utils.BackendOnlyIngress(ingress) {
				if err := EnsureLoadBalancerTargetsDeleted(sc, loadBalancerContext, ingress); err != nil {
					return err
				}
			} else {
				return types.NewError(errcode.ChildIngressDeleteWithoutBackendOnly, "Trying to delete not backendManageOnly child ingress", ingress.Name())
			}
		}
	}

	if lb.IsAutoCreatedByTKE() {
		if err := DeleteLoadBalancerResource(sc, lbId); err != nil {
			return err
		}
	} else {
		if err := DeleteLoadBalancerResourceByIngress(sc, lbId, ingress); err != nil {
			return err
		}
	}

	if err := cluster_service.Instance.TkeServiceConfigClient().CloudV1alpha1().TkeServiceConfigs(ingress.Namespace()).Delete(sc, utils.IngressAutoServiceConfigName(ingress), metav1.DeleteOptions{}); err != nil {
		if !apierrors.IsNotFound(err) {
			klog.Errorf("Error deleting TkeServiceConfigs for ingress %s %v", utils.IngressName(ingress), err)
		}
	}

	return nil
}

func GetLoadBalancerByLBId(ctx context.Context, ingress types.Ingress, lbId string, region string) (*clb.LoadBalancer, error) {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return nil, err
	// }

	request := clb.NewDescribeLoadBalancersRequest()
	request.LoadBalancerIds = []*string{&lbId}

	response, err := tencentapi.Instance.DescribeLoadBalancers(cloudctx.From(ctx, ingress, region), request)
	if err != nil {
		return nil, err
	}

	if response.Response.TotalCount != nil {
		if *response.Response.TotalCount == 0 || len(response.Response.LoadBalancerSet) == 0 {
			klog.Infof("cannot find the specific lb")
			return nil, nil
		} else {
			return response.Response.LoadBalancerSet[0], nil
		}
	}
	return nil, fmt.Errorf("get lb by lbId error")
}

func CreateLoadBalancerListener(ctx context.Context, ingress types.Ingress, lbId string, region string, port int64, protocol string) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	request := clbInner.NewCreateListenerRequest()
	request.LoadBalancerId = &lbId
	request.Ports = []*int64{&port}
	request.Protocol = &protocol
	request.ListenerNames = common.StringPtrs([]string{TKEDedicatedListenerName})
	if protocol == "HTTPS" {
		request.SniSwitch = common.Int64Ptr(1)
	}

	if _, err := tencentapi.Instance.CreateListener(cloudctx.From(ctx, ingress, region), request); err != nil {
		return err
	}
	return nil
}

func DeleteLoadBalancerListener(ctx context.Context, ingress types.Ingress, lbId string, region string, listenerId string) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	request := clb.NewDeleteListenerRequest()
	request.LoadBalancerId = &lbId
	request.ListenerId = &listenerId

	if _, err := tencentapi.Instance.DeleteListener(cloudctx.From(ctx, ingress, region), request); err != nil {
		return err
	}
	return nil
}

func GetLoadBalancerListener(ctx context.Context, ingress types.Ingress, lbId string, region string) ([]*clb.Listener, error) {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return nil, err
	// }

	request := clb.NewDescribeListenersRequest()
	request.LoadBalancerId = &lbId

	response, err := tencentapi.Instance.DescribeListeners(cloudctx.From(ctx, ingress, region), request)
	if err != nil {
		return nil, err
	}
	return response.Response.Listeners, nil
}

func GetListenerKeyByIngress(ingress types.Ingress) map[string]bool {
	result := make(map[string]bool)
	result[utils.GetListenerKey(80, "HTTP")] = true
	tls := ingress.TLS()
	if tls != nil && len(tls) != 0 {
		result[utils.GetListenerKey(443, "HTTPS")] = true
	}
	return result
}

func GetListenerKeyByService(service *v1.Service) map[string]bool {
	specifyProtocolMap, _ := utils.GetSpecifyProtocol(service)
	result := make(map[string]bool)
	for _, port := range service.Spec.Ports {
		protocolList := []string{strings.ToUpper(string(port.Protocol))}
		specifyProtocol, specifyProtocolExist := (*specifyProtocolMap)[port.Port]
		if specifyProtocolExist {
			protocolList = specifyProtocol.Protocol
		}

		for _, protocol := range protocolList {
			result[utils.GetListenerKey(port.Port, protocol)] = true
		}
	}
	return result
}

func DescribeRewrite(ctx context.Context, ingress types.Ingress, lbID string, region string) ([]*clb.RuleOutput, error) {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return nil, err
	// }

	request := clb.NewDescribeRewriteRequest()
	request.LoadBalancerId = &lbID

	response, err := tencentapi.Instance.DescribeRewrite(cloudctx.From(ctx, ingress, region), request)
	if err != nil {
		return nil, err
	}

	return response.Response.RewriteSet, nil
}

// func  GetLoadBalancerListenerByListenerId(lbId string, listenerId string) (*clb.Listener, error) {
//	request := clb.NewDescribeListenersRequest()
//	request.LoadBalancerId = &lbId
//	request.ListenerIds = []*string{&listenerId}
//
//	response, err := tencentapi.Instance.DescribeListeners(nil, region, request)
//	if err != nil {
//		return nil, err
//	}
//	if len(response.Response.Listeners) == 0 {
//		return nil, fmt.Errorf("lb: %s listener: %s DescribeListeners empty response", lbId, listenerId)
//	}
//	return response.Response.Listeners[0], nil
// }

func CreateRules(ctx context.Context, ingress types.Ingress, lbId string, region string, listenerId string, rules []*clb.RuleInput) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	request := clb.NewCreateRuleRequest()
	request.ListenerId = &listenerId
	request.LoadBalancerId = &lbId

	for i := 0; i < len(rules); i += BATCH_RULE_LIMIT {
		request.Rules = rules[i:min(i+BATCH_RULE_LIMIT, len(rules))]
		if _, err := tencentapi.Instance.CreateRule(cloudctx.From(ctx, ingress, region), request); err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				if sdkError.Code == "LimitExceeded" {
					return types.NewError(errcode.RuleLimitExceeded, sdkError.Error())
				} else if sdkError.Code == "InvalidParameterValue" {
					if strings.Contains(sdkError.Message, "Invalid url:") { // Message=Invalid url: /api/sns/v1/comment/{id:[a-z0-9]{24}}/detail
						return types.NewError(errcode.RuleFormatError, sdkError.Error(), utils.IngressName(ingress))
					} else if strings.Contains(sdkError.Message, "This interface only support HTTP/HTTPS listener") { // Message=This interface only support HTTP/HTTPS listener.
						return types.NewError(errcode.RuleProtocolError, sdkError.Error(), utils.IngressName(ingress))
					} else if strings.Contains(sdkError.Message, "can't be regular expression or wildcards") {
						return types.NewError(errcode.InvalidHealthCheckDomainError, sdkError.Error(), ingress.String())
					}
				} else if sdkError.Code == "InvalidParameterValue.Length" {
					if strings.Contains(sdkError.Message, "The length of CertificateId") {
						return types.NewError(errcode.CertificateFormatError, sdkError.Error(), ingress.String())
					}
				} else if sdkError.Code == "FailedOperation.CertificateNotFound" || (sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "Query certificate")) {
					return types.NewError(errcode.CertificateNotFoundError, sdkError.Error(), utils.IngressName(ingress))
				}
			}
			return err
		}
	}
	return nil
}

func DeleteRules(ctx context.Context, ingress types.Ingress, lbId string, region string, listenerId string, locationIds []string) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	locations := make([]*string, len(locationIds))
	for index, location := range locationIds {
		locationId := location
		locations[index] = &locationId
	}

	request := clb.NewDeleteRuleRequest()
	request.LoadBalancerId = &lbId
	request.ListenerId = &listenerId

	for i := 0; i < len(locations); i += BATCH_RULE_LIMIT {
		request.LocationIds = locations[i:min(i+BATCH_RULE_LIMIT, len(locations))]
		if _, err := tencentapi.Instance.DeleteRule(cloudctx.From(ctx, ingress, region), request); err != nil {
			return err
		}
	}
	return nil
}

func GetTargetInfo(ctx context.Context, ingress types.Ingress, lbId string, region string) ([]*clb.ListenerBackend, error) {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return nil, err
	// }

	request := clb.NewDescribeTargetsRequest()
	request.LoadBalancerId = &lbId

	response, err := tencentapi.Instance.DescribeTargets(cloudctx.From(ctx, ingress, region), request)
	if err != nil {
		return nil, err
	}
	return response.Response.Listeners, nil
}

func BatchRegisterTarget(ctx context.Context, ingress types.Ingress, lbId string, region string, targets []*clb.BatchTarget) error {
	if targets == nil || len(targets) == 0 {
		return nil
	}

	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	request := clb.NewBatchRegisterTargetsRequest()
	request.LoadBalancerId = &lbId

	// Can only register 500 target a time
	for index := 0; index < len(targets); index += NEW_BATCH_TARGET_LIMIT {
		request.Targets = targets[index:min(index+NEW_BATCH_TARGET_LIMIT, len(targets))]

		response, err := tencentapi.Instance.BatchRegisterTargets(cloudctx.From(ctx, ingress, region), request)
		if err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				if sdkError.Code == "LimitExceeded" && strings.Contains(sdkError.Message, "max instances registed") { // [TencentCloudSDKError] Code=LimitExceeded, Message=Over the limit of max instances registed on one rule
					return types.NewError(errcode.BackendOverLimitError, sdkError.Error(), utils.IngressName(ingress), lbId)
				} else if sdkError.Code == "InvalidParameter" && strings.Contains(sdkError.Message, "cvm param error") {
					return types.NewError(errcode.NodeInsufficient, sdkError.Error(), utils.IngressName(ingress))
				} else if sdkError.Code == "FailedOperation" {
					if strings.Contains(sdkError.Message, "do not support to bind other ip") { // [TencentCloudSDKError] Code=FailedOperation, Message=Account ********** do not support to bind other ip ['**********', '**********'], RequestId=552e0022-3c75-46d8-b26c-9c863e3264a9.
						return types.NewError(errcode.NetworkNotRegisterToCNNError, sdkError.Error(), utils.IngressName(ingress))
					} else if strings.Contains(sdkError.Message, "eks") && strings.Contains(sdkError.Message, "only support \"normal\" status") {
						// [TencentCloudSDKError] Code=FailedOperation, Message=eks-oljjepj0(cls-4g9b5b18) is "deleting", only support "normal" status, RequestId=2a5de623-a64a-46e4-ae91-e3e1f1ff0f4f
						// [TencentCloudSDKError] Code=FailedOperation, Message=eks-6ibc5eb6(cls-5vzjheeo) is "creating", only support "normal" status, RequestId=2879a6f1-6ac9-45ac-99a3-eaebd6a3fe34
						return types.NewError(errcode.EVMUpdatingError, sdkError.Error(), utils.IngressName(ingress))
					} else if strings.Contains(sdkError.Message, "in ENI is not normal state") {
						// [TencentCloudSDKError] Code=FailedOperation, Message=IP ['************'] in ENI is not normal state, not support to register, RequestId=f1fab6a4-6065-4a9e-8080-8321afeb7c0f
						return types.NewError(errcode.EVMUpdatingError, sdkError.Error(), utils.IngressName(ingress))
					}
				}
			} // Code=FailedOperation, Message=Not support to register other ip ['**********'] before enable SnatPro
			return err
		}
		if response != nil && response.Response != nil && response.Response.FailListenerIdSet != nil && len(response.Response.FailListenerIdSet) != 0 {
			klog.Infof("BatchRegisterTargets error: %s, %s", utils.JsonWrapper(response.Response.FailListenerIdSet), *response.Response.RequestId)
			return types.NewError(errcode.CLBUnknownError, utils.JsonWrapper(response), tencentapi.BatchRegisterTargets.Name, utils.JsonWrapper(request))
		}
	}
	return nil
}

func BatchDeregisterTarget(ctx context.Context, ingress types.Ingress, lbId string, region string, targets []*clb.BatchTarget) error {
	if targets == nil || len(targets) == 0 {
		return nil
	}

	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	request := clb.NewBatchDeregisterTargetsRequest()
	request.LoadBalancerId = &lbId

	// Can only register 500 target a time
	for index := 0; index < len(targets); index += NEW_BATCH_TARGET_LIMIT {
		request.Targets = targets[index:min(index+NEW_BATCH_TARGET_LIMIT, len(targets))]

		response, err := tencentapi.Instance.BatchDeregisterTargets(cloudctx.From(ctx, ingress, region), request)
		if err != nil {
			if sdkError, ok := err.(*skderrors.TencentCloudSDKError); ok {
				// [TencentCloudSDKError] Code=FailedOperation, Message=eks-oljjepj0(cls-4g9b5b18) is "deleting", only support "normal" status, RequestId=2a5de623-a64a-46e4-ae91-e3e1f1ff0f4f
				// [TencentCloudSDKError] Code=FailedOperation, Message=eks-6ibc5eb6(cls-5vzjheeo) is "creating", only support "normal" status, RequestId=2879a6f1-6ac9-45ac-99a3-eaebd6a3fe34
				if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "eks") && strings.Contains(sdkError.Message, "only support \"normal\" status") {
					return types.NewError(errcode.EVMUpdatingError, sdkError.Error())
				}
			}
			return err
		}
		if response != nil && response.Response != nil && response.Response.FailListenerIdSet != nil && len(response.Response.FailListenerIdSet) != 0 {
			klog.Infof("BatchDeregisterTargets error: %s, %s", utils.JsonWrapper(response.Response.FailListenerIdSet), *response.Response.RequestId)
			return types.NewError(errcode.CLBUnknownError, utils.JsonWrapper(response), tencentapi.BatchDeregisterTargets.Name, utils.JsonWrapper(request))
		}
	}
	return nil
}

func ModifyListenerCertId(ctx context.Context, ingress types.Ingress, lbId string, region string, listenerId string, certId string, caCertId *string) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	request := clbInner.NewModifyListenerRequest()
	request.LoadBalancerId = &lbId
	request.ListenerId = &listenerId
	if caCertId == nil {
		request.Certificate = &clb.CertificateInput{CertId: common.StringPtr(certId), SSLMode: common.StringPtr("UNIDIRECTIONAL")}
	} else {
		request.Certificate = &clb.CertificateInput{CertId: common.StringPtr(certId), CertCaId: caCertId, SSLMode: common.StringPtr("MUTUAL")}
	}

	if _, err := tencentapi.Instance.ModifyListener(cloudctx.From(ctx, ingress, region), request); err != nil {
		return err
	}
	return nil
}

func GetIngressLoadBalancer(ctx context.Context, ingress types.Ingress) (string, string, error) {
	resourceTags, err := GetResourceTagsByIngress(ctx, ingress)
	if err != nil {
		return "", "", err
	}
	if len(resourceTags) == 1 {
		return *resourceTags[0].ResourceId, *resourceTags[0].ResourceRegion, nil
	}
	if len(resourceTags) > 1 {
		lbIds := make([]string, len(resourceTags))
		for index, row := range resourceTags {
			lbIds[index] = *row.ResourceId
		}
		services.UploadMetricsAndEvent(ingress, types.NewError(errcode.MultyLoadbalancer, "", utils.IngressName(ingress), strings.Join(lbIds, ",")))
		return *resourceTags[0].ResourceId, *resourceTags[0].ResourceRegion, nil
	}
	return "", "", nil
}

func GetResourceTagsByIngress(ctx context.Context, ingress types.Ingress) ([]*tag.ResourceTag, error) {
	ingressUUID := ingress.UID()

	switch ingress.Type() {
	case types.CoreIngress:
		if utils.IsInEKSCluster() {
			return GetLoadBalancerByTagFilterInEKS(ctx, ingress, ingressUUID)
		} else {
			return GetLoadBalancerByTagFilter(ctx, ingress, types.TagKeyIngressUUID.String(), ingressUUID)
		}
	case types.MultiClusterIngress:
		return GetLoadBalancerByTagFilter(ctx, ingress, types.TagKeyMultiClusterIngressUUID.String(), ingressUUID)
	}
	return []*tag.ResourceTag{}, nil
}

func GetLoadBalancerByTagFilterInEKS(ctx context.Context, ingress types.Ingress, ingressUUID string) ([]*tag.ResourceTag, error) {
	request := tag.NewDescribeResourcesByTagsRequest()
	request.ResourcePrefix = common.StringPtr("clb")
	request.ServiceType = common.StringPtr("clb")
	request.Offset = common.Uint64Ptr(0)
	request.TagFilters = []*tag.TagFilter{
		GetAutoCreatedTagKey(ingress).ToTagFilter(GetAutoCreatedTagValue()),
		types.TagKeyClusterID.ToTagFilter(config.Global.ClusterName),
		types.TagKeyObjectKind.ToTagFilter("ingress"),
		types.TagKeyObjectName.ToTagFilter(fmt.Sprintf("%s/%s", ingress.Namespace(), ingress.Name())),
	}
	response, err := tencentapi.Instance.DescribeResourcesByTags(cloudctx.From(ctx, ingress, config.Global.Region), request)
	if err != nil {
		return nil, err
	}
	if *response.Response.TotalCount > 0 {
		return response.Response.Rows, nil
	}

	request.TagFilters = []*tag.TagFilter{
		newTagFilter(ingressUUID, []*string{common.StringPtr(types.TagKeyClusterIDPrefix + config.Global.ClusterName)}),
	}
	response, err = tencentapi.Instance.DescribeResourcesByTags(cloudctx.From(ctx, ingress, config.Global.Region), request)
	if err != nil {
		return nil, err
	}
	if *response.Response.TotalCount > 0 {
		return response.Response.Rows, nil
	}
	return []*tag.ResourceTag{}, nil
}

func GetLoadBalancerByTagFilter(ctx context.Context, ingress types.Ingress, tagKey string, tagValue string) ([]*tag.ResourceTag, error) {
	request := tag.NewDescribeResourcesByTagsRequest()
	request.ResourcePrefix = common.StringPtr("clb")
	request.ServiceType = common.StringPtr("clb")
	request.Offset = common.Uint64Ptr(0)
	request.TagFilters = []*tag.TagFilter{
		newTagFilter(tagKey, []*string{&tagValue}),
	}

	response, err := tencentapi.Instance.DescribeResourcesByTags(cloudctx.From(ctx, ingress, config.Global.Region), request)
	if err != nil {
		return nil, err
	}

	if *response.Response.TotalCount > 0 {
		return response.Response.Rows, nil
	}
	return []*tag.ResourceTag{}, nil
}

// type TaskError struct{}
//
// func (e TaskError) Error() string {
//	return fmt.Sprint("CLB Task Error.")
// }

/*
 * 根据后端`private-ip-address`查询，批量数量限制过窄(5个)
 * 	 1. 查询VPC下的CVM列表，批量100个
 *   2. 查询VPC下指定`ip`的CVM列表，批量5个
 * 考虑优先获取VPC下的机器数量，哪种查询方式的请求次数少，就用哪种方式查询
 */
func GetBackendIDs(ctx context.Context, ingress types.Ingress, region string, ipList []string) (map[string]string, error) {
	if len(ipList) <= 0 {
		return map[string]string{}, nil
	}

	vpcIdList := &[]string{config.Global.VPCID}
	response, err := GetInstancesByVpcList(ctx, ingress, region, vpcIdList, 0, DESCRIBE_INSTANCES_LIMIT)
	if err != nil {
		klog.Errorf("Get cvm (vpcId: %v) info error: %v", config.Global.VPCID, err)
		return map[string]string{}, err
	}

	result := make(map[string]string)
	backendMap := make(map[string]bool) // instanceId to Port
	for _, backend := range ipList {
		backendMap[backend] = true
	}

	totalCount := *response.Response.TotalCount
	pageVPCQuery := int((totalCount + DESCRIBE_INSTANCES_LIMIT - 1) / DESCRIBE_INSTANCES_LIMIT)                          // 在VPC下查询所有实例需要的次数（已经执行一次）
	pageFilterQuery := (len(ipList) + DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT - 1) / DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT // 在VPC下根据IP筛选查询需要的次数
	if pageVPCQuery-1 < pageFilterQuery {                                                                                // 直接通过VPC下所有CVM查询
		// 根据查询结果，筛选后端实例到result中
		filterRecord := func(instanceSet *[]*cvm.Instance, backendMap *map[string]bool, result *map[string]string) {
			for _, instance := range *instanceSet {
				for _, ip := range instance.PrivateIpAddresses {
					if _, ok := (*backendMap)[*ip]; ok {
						(*result)[*ip] = *instance.InstanceId
						break
					}
				}
			}
		}

		filterRecord(&response.Response.InstanceSet, &backendMap, &result)
		for i := 1; i < pageVPCQuery; i++ {
			response, err := GetInstancesByVpcList(ctx, ingress, region, vpcIdList, int64(i*DESCRIBE_INSTANCES_LIMIT), DESCRIBE_INSTANCES_LIMIT)
			if err != nil {
				klog.Errorf("Get cvm (vpcId: %v) info error: %v", config.Global.VPCID, err)
				return map[string]string{}, err
			}
			filterRecord(&response.Response.InstanceSet, &backendMap, &result)
		}
	} else { // 通过`private-ip-address`筛选查询，每次最多筛选出五个后端实例的信息
		for i := 0; i < len(ipList); i += DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT {
			backend := ipList[i:min(i+DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT, len(ipList))]

			response, err := GetInstancesByBackendAndVpcList(ctx, ingress, region, &backend, vpcIdList)
			if err != nil {
				klog.Errorf("Get cvm (lanIP: %v) info error: %v", backend, err)
				return map[string]string{}, err
			}

			instanceSet := response.Response.InstanceSet
			for _, instance := range instanceSet {
				for _, ip := range instance.PrivateIpAddresses {
					if _, ok := backendMap[*ip]; ok {
						result[*ip] = *instance.InstanceId
						break
					}
				}
			}
		}
	}

	if len(ipList) != len(result) {
		instanceList := make([]string, len(ipList)-len(result))
		index := 0
		for _, ip := range ipList {
			if _, exist := result[ip]; !exist {
				instanceList[index] = ip
				index++
			}
		}
		klog.Errorf("meet error: some cvms cannot be found in cvm list (result list: %v, check list: %v)", instanceList, ipList)
	}

	return result, nil
}

func GetInstancesByVpcList(ctx context.Context, ingress types.Ingress, region string, vpcIdList *[]string, offset int64, limit int64) (*cvm.DescribeInstancesResponse, error) {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return nil, err
	// }

	request := cvm.NewDescribeInstancesRequest()
	request.Offset = &offset
	request.Limit = &limit
	request.Filters = []*cvm.Filter{
		{Name: common.StringPtr("vpc-id"), Values: common.StringPtrs(*vpcIdList)},
	}

	response, err := tencentapi.Instance.DescribeInstances(cloudctx.From(ctx, ingress, region), request)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func GetInstancesByBackendAndVpcList(ctx context.Context, ingress types.Ingress, region string, backend *[]string, vpcIdList *[]string) (*cvm.DescribeInstancesResponse, error) {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return nil, err
	// }

	request := cvm.NewDescribeInstancesRequest()
	request.Offset = common.Int64Ptr(0)
	request.Limit = common.Int64Ptr(100)
	request.Filters = []*cvm.Filter{
		{Name: common.StringPtr("vpc-id"), Values: common.StringPtrs(*vpcIdList)},
		{Name: common.StringPtr("private-ip-address"), Values: common.StringPtrs(*backend)},
	}

	response, err := tencentapi.Instance.DescribeInstances(cloudctx.From(ctx, ingress, region), request)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// func  setLBTag(ingressUUID string, lbID string) error {
//	addTags := []*tag.Tag{
//		newTag(types.TagKeyIngressUUID, ingressUUID),      // ServiceTag
//		newTag(CreateByTKETag, CreateByTKEValue), // TKETag
//		newTag(types.TagKeyClusterID, utils.Global.ClusterName), // ClusterTag
//	}
//	return addResourceTags(lbID, &addTags)
// }

// clear tags associated with service, only call when delete exist type lb
func EnsureLoadBalancerTagsDeleted(ctx context.Context, loadBalancerContext *LoadBalancerContext) error {
	ingress := loadBalancerContext.Ingress
	loadBalancer, err := loadBalancerContext.GetLoadBalancer(context.TODO())
	if err != nil {
		return err
	}

	ingressUUID := ingress.UID()
	deleteTagKey := make(sets.String)
	if ingress.Type() == types.CoreIngress {
		if utils.IsInEKSCluster() {
			deleteTagKey.Insert(ingressUUID)
		} else {
			deleteTagKey.Insert(types.TagKeyIngressUUID.String())
		}
	} else {
		deleteTagKey.Insert(types.TagKeyMultiClusterIngressUUID.String())
	}

	_, exclusiveUsed := getLBUsedOtherResource(ingress, loadBalancerContext.LoadBalancerResource)

	deleteTags := make([]*tag.TagResource, 0)
	for _, lbTag := range loadBalancer.Tags {
		if lbTag.TagKey != nil && deleteTagKey.Has(*lbTag.TagKey) {
			deleteTags = append(deleteTags, newTagResource(*lbTag.TagKey, *lbTag.TagValue))
		}
		if exclusiveUsed {
			if *lbTag.TagKey == types.TagKeyClusterID.String() && *lbTag.TagValue == config.Global.ClusterName {
				deleteTags = append(deleteTags, types.TagKeyClusterID.ToTagResource(*lbTag.TagValue))
			}
			if *lbTag.TagKey == types.TagKeyLifecycleOwner.String() {
				deleteTags = append(deleteTags, types.TagKeyLifecycleOwner.ToTagResource(*lbTag.TagValue))
			}
			if *lbTag.TagKey == types.TagKeyAutoCreated.String() || *lbTag.TagKey == types.TagKeyAutoCreatedInEKS.String() {
				deleteTags = append(deleteTags, newTagResource(*lbTag.TagKey, *lbTag.TagValue))
			}
		}
	}
	if len(deleteTags) == 0 {
		return nil
	}
	return ModifyAndRecycleLoadBalancerTags(ctx, ingress, *loadBalancer.LoadBalancerId, loadBalancerContext.Region, nil, deleteTags)
}

// getLBUsedServiceUUIDSource
func getLBUsedOtherResource(ingress types.Ingress, resource *v1alpha1.LoadBalancerResource) (UIDs []string, exclusiveUsed bool) {
	usedResourceUUIDs := make(sets.String)
	for _, listener := range resource.Spec.Listeners {
		for _, reference := range listener.References {
			if reference.Kind == string(ingress.Type()) && reference.Namespace == ingress.Namespace() && reference.Name == ingress.Name() {
				continue
			}
			usedResourceUUIDs.Insert(string(reference.UID))
		}
	}
	return usedResourceUUIDs.List(), usedResourceUUIDs.Len() == 0
}

func EnsureLoadBalancerTags(ctx context.Context, loadBalancerContext *LoadBalancerContext) error {
	ingress := loadBalancerContext.Ingress
	balancer, err := loadBalancerContext.GetLoadBalancer(context.TODO())
	if err != nil {
		return err
	}

	desiredLbTags := getDesiredTKEIngressTags(ingress, loadBalancerContext.LoadBalancerResource)
	currentLbTags := getCurrentTKEIngressTags(ingress, balancer)
	v := services.LBService{}.DetermineLifecycleOwnerValue(context.TODO(), ingress.RuntimeObject().(metav1.Object),
		types.NewLB(balancer, loadBalancerContext.LoadBalancerResource.Spec.Created))
	desiredLbTags[types.TagKeyLifecycleOwner.String()] = v

	toAddTags, toDelTags := tagDifference(currentLbTags, desiredLbTags)
	if len(toAddTags) != 0 || len(toDelTags) != 0 {
		klog.Infof("EnsureLoadBalancerTags for ingress: %s exist: %s target: %s add: %s del: %s", utils.IngressName(ingress), utils.JsonWrapper(currentLbTags), utils.JsonWrapper(desiredLbTags), utils.JsonWrapper(toAddTags), utils.JsonWrapper(toDelTags))
		if err := ModifyAndRecycleLoadBalancerTags(ctx, ingress, *balancer.LoadBalancerId, loadBalancerContext.Region, toAddTags, toDelTags); err != nil {
			klog.Errorf("ensureLoadBalancerTags for ingress: %s err: %v", utils.IngressName(ingress), err)
			return err
		}

		if err := loadBalancerContext.UpdateLoadBalancerTag(ctx); err != nil {
			return err
		}
	}
	return nil
}

func getDesiredTKEIngressTags(ingress types.Ingress, lb *v1alpha1.LoadBalancerResource) map[string]string {
	tags := make(map[string]string)
	tags[types.TagKeyClusterID.String()] = config.Global.ClusterName
	if lb.Spec.Created {
		if ingress.Type() == types.CoreIngress {
			if utils.IsInEKSCluster() {
				tags[GetAutoCreatedTagKey(ingress).String()] = GetAutoCreatedTagValue()
				tags[types.TagKeyObjectKind.String()] = "ingress"
				tags[types.TagKeyObjectName.String()] = fmt.Sprintf("%s/%s", ingress.Namespace(), ingress.Name())
			} else {
				tags[GetAutoCreatedTagKey(ingress).String()] = GetAutoCreatedTagValue()
				tags[types.TagKeyIngressUUID.String()] = ingress.UID()
			}
		} else if ingress.Type() == types.MultiClusterIngress {
			tags[GetAutoCreatedTagKey(ingress).String()] = GetAutoCreatedTagValue()
			tags[types.TagKeyMultiClusterIngressUUID.String()] = ingress.UID()
		}
	}

	return tags
}

func getCurrentTKEIngressTags(ingress types.Ingress, loadBalancer *clb.LoadBalancer) map[string]string {
	ingressUID := ingress.UID()
	tkeTags := make(map[string]string)

	for _, lbTag := range loadBalancer.Tags {
		if lbTag.TagKey == nil || *lbTag.TagKey == "" || lbTag.TagValue == nil || *lbTag.TagValue == "" { // 存在空值不是TKE Service标签，略过
			continue
		}

		key := *lbTag.TagKey
		value := *lbTag.TagValue

		if key == GetAutoCreatedTagKey(ingress).String() || key == types.TagKeyLifecycleOwner.String() {
			tkeTags[key] = value
		}
		if ingress.Type() == types.CoreIngress {
			// need to only add tke related tags
			if utils.IsInEKSCluster() {
				if key == types.TagKeyClusterID.String() || key == types.TagKeyObjectKind.String() || key == types.TagKeyObjectName.String() {
					tkeTags[key] = value
				} else {
					if strings.HasPrefix(*lbTag.TagValue, types.TagKeyClusterIDPrefix) {
						if *lbTag.TagKey == ingressUID {
							tkeTags[key] = value
						}
					}
				}
			} else {
				if key == types.TagKeyClusterID.String() || key == types.TagKeyIngressUUID.String() {
					tkeTags[key] = value
				}
			}
		} else if ingress.Type() == types.MultiClusterIngress {
			if key == types.TagKeyClusterID.String() || key == types.TagKeyMultiClusterIngressUUID.String() {
				tkeTags[key] = value
			}
		}
	}
	return tkeTags
}

func tagDifference(fromMap, toMap map[string]string) ([]*tag.TagResource, []*tag.TagResource) {
	fromSet := sets.StringKeySet(fromMap)
	toSet := sets.StringKeySet(toMap)

	toDel := fromSet.Difference(toSet)
	toAdd := toSet.Difference(fromSet)

	toAddItems := make([]*tag.TagResource, 0)
	toDelItems := make([]*tag.TagResource, 0)

	for _, key := range toAdd.List() {
		toAddItems = append(toAddItems, newTagResource(key, toMap[key]))
	}

	for _, key := range toDel.List() {
		toDelItems = append(toDelItems, newTagResource(key, fromMap[key]))
	}

	return toAddItems, toDelItems
}

func GetTagSetAndMap(items []*tag.TagResource) (sets.String, map[string]*tag.TagResource) {
	itemSet := make(sets.String)
	itemMap := make(map[string]*tag.TagResource)
	for _, v := range items {
		item := *v
		if item.TagKey == nil {
			continue
		}
		itemKey := fmt.Sprintf("%s_%s", *item.TagKey, *item.TagValue)
		// itemKey := *item.TagKey
		itemSet.Insert(itemKey)
		itemMap[itemKey] = &item
	}
	return itemSet, itemMap
}

func ModifyAndRecycleLoadBalancerTags(ctx context.Context, ingress types.Ingress, loadbalancerId string, region string, replaceTagResources []*tag.TagResource, deleteTagResources []*tag.TagResource) error {
	replaceTags := ConvertTagList(replaceTagResources)
	deleteTags := ConvertTagKeyObjectList(deleteTagResources)
	if err := ModifyLoadBalancerTags(ctx, ingress, loadbalancerId, region, replaceTags, deleteTags); err != nil {
		return err
	}
	if len(deleteTagResources) > 0 {
		RecycleLoadbalancerTags(ctx, ingress, region, deleteTagResources)
	}
	return nil
}

func ModifyLoadBalancerTags(ctx context.Context, ingress types.Ingress, lbId string, region string, replaceTags []*tag.Tag, deleteTags []*tag.TagKeyObject) error {
	if len(replaceTags) == 0 && len(deleteTags) == 0 {
		return nil
	}

	request := tag.NewModifyResourceTagsRequest()
	request.Resource = common.StringPtr(fmt.Sprintf("qcs::clb:%s:uin/%d:clb/%s", region, config.Global.OwnerUin, lbId))
	if len(replaceTags) != 0 {
		request.ReplaceTags = replaceTags
	}
	if len(deleteTags) != 0 {
		request.DeleteTags = deleteTags
	}

	if _, err := tencentapi.Instance.ModifyResourceTags(cloudctx.From(ctx, ingress, region), request); err != nil {
		return err
	}
	return nil
}

// recycleLoadbalancerTags delete tags after lb deleted
func RecycleLoadbalancerTags(ctx context.Context, ingress types.Ingress, region string, tags []*tag.TagResource) {
	for _, lbTag := range tags {
		if lbTag.TagKey == nil || *lbTag.TagKey == "" || lbTag.TagValue == nil || *lbTag.TagValue == "" {
			continue
		}

		if *lbTag.TagKey == GetAutoCreatedTagKey(ingress).String() || *lbTag.TagKey == types.TagKeyClusterID.String() ||
			*lbTag.TagKey == types.TagKeyObjectKind.String() {
			continue
		}

		if err := DeleteTags(ctx, ingress, region, *lbTag.TagKey, *lbTag.TagValue); err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				if sdkError.Code == "FailedOperation.TagAttachedResource" {
					continue
				}
			}
			klog.Errorf("recycleLoadbalancerTags deleteTag for ingress: %s key: %s value: %s err: %v", utils.IngressName(ingress), *lbTag.TagKey, *lbTag.TagValue, err)
		}
	}
}

// deleteTag delete tag
func DeleteTags(ctx context.Context, ingress types.Ingress, region string, tagKey string, tagValue string) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	request := tag.NewDeleteTagRequest()
	request.TagKey = &tagKey
	request.TagValue = &tagValue

	response, err := tencentapi.Instance.DeleteTag(cloudctx.From(ctx, ingress, region), request)
	if err != nil {
		return nil
	}

	klog.Infof("Ingress: %s DeleteTag request: %s response: %s", utils.IngressName(ingress), utils.JsonWrapper(request), utils.JsonWrapper(response))
	return nil
}

func ConvertTagList(tagResources []*tag.TagResource) []*tag.Tag {
	tags := make([]*tag.Tag, len(tagResources))
	for i, tagResource := range tagResources {
		tags[i] = newTag(*tagResource.TagKey, *tagResource.TagValue)
	}
	return tags
}

func ConvertTagKeyObjectList(tagResources []*tag.TagResource) []*tag.TagKeyObject {
	tagKeyObjects := make([]*tag.TagKeyObject, len(tagResources))
	for i, tagResource := range tagResources {
		tagKeyObjects[i] = newTagKeyObject(*tagResource.TagKey)
	}
	return tagKeyObjects
}

// func setExistLBTag(ingress types.Ingress, lbID string) error {
//	ingressUUID := ingress.UID()
//	var addTags []*tag.Tag
//	if utils.IsInEKSCluster() {
//		addTags = []*tag.Tag{
//			newTag(ingressUUID, types.TagKeyClusterIDPrefix+utils.Global.ClusterName), // ClusterTag
//		}
//	} else {
//		addTags = []*tag.Tag{
//			newTag(types.TagKeyIngressUUID, ingressUUID),                   // ServiceTag
//			newTag(types.TagKeyClusterID, utils.Global.ClusterName), // ClusterTag
//		}
//	}
//	return addResourceTags(ingress, lbID, region,addTags)
// }

// func CheckLBCreateByTKE(lbID *clbInner.LoadBalancer) bool {
//	for _, resourceTag := range lbID.Tags {
//		if *resourceTag.TagKey == CreateByTKETag() && *resourceTag.TagValue == CreateByTKEValue() {
//			return true
//		}
//	}
//	return false
// }

func checkCreatedByTKENew(resource *v1alpha1.LoadBalancerResource) bool {
	return resource.Spec.Created
}

// get ingress uuid which used this lbid
// func getIngressUUIDByLBID(ingress types.Ingress, lbID string, region string) (string, error) {
//	tagResources, err := GetLoadBalancerTags(ingress, lbID, region)
//	if err != nil {
//		return "", err
//	}
//	if utils.IsInEKSCluster() {
//		for _, resourceTag := range tagResources {
//			if strings.HasPrefix(*resourceTag.TagValue, types.TagKeyClusterIDPrefix) && *resourceTag.TagKey != ingress.UID() {
//				return *resourceTag.TagKey, nil
//			}
//		}
//	} else {
//		for _, resourceTag := range tagResources {
//			if *resourceTag.TagKey == types.TagKeyIngressUUID {
//				return *resourceTag.TagValue, nil
//			}
//		}
//	}
//	return "", nil
// }

// func clearExistLBTag(ingress types.Ingress, region string, lbID string) error {
//	ingressUUID := ingress.UID()
//	tagResources, err := GetLoadBalancerTags(ingress, lbID, region)
//	if err != nil {
//		return err
//	}
//	delTags := make([]*tag.TagKeyObject, 0)
//	for _, resourceTag := range tagResources {
//		if utils.IsInEKSCluster() {
//			if strings.HasPrefix(*resourceTag.TagValue, types.TagKeyClusterIDPrefix) && *resourceTag.TagKey == ingressUUID {
//				delTags = append(delTags, newTagKeyObject(*resourceTag.TagKey))
//			}
//		} else {
//			if (*resourceTag.TagKey == types.TagKeyIngressUUID && *resourceTag.TagValue == ingressUUID) || (*resourceTag.TagKey == types.TagKeyClusterID && *resourceTag.TagValue == utils.Global.ClusterName) {
//				delTags = append(delTags, newTagKeyObject(*resourceTag.TagKey))
//			}
//		}
//	}
//	// no tag need to del
//	if len(delTags) != 0 {
//		return deleteResourceTags(ingress, lbID, region, delTags)
//	}
//
//	return nil
// }

func GetCCSResourceTags(ctx context.Context, clusterId string) ([]*tag.TagResource, error) {
	request := tag.NewDescribeResourceTagsByResourceIdsRequest()
	request.Limit = common.Uint64Ptr(TAGSPERPAGE)
	request.Offset = common.Uint64Ptr(0)
	request.ServiceType = common.StringPtr("ccs")
	request.ResourcePrefix = common.StringPtr("cluster")
	request.ResourceIds = []*string{&clusterId}
	request.ResourceRegion = &config.Global.Region

	tagResource := make([]*tag.TagResource, 0)
	totalCount := uint64(TAGSPERPAGE)
	for *request.Offset < totalCount {
		response, err := tencentapi.Instance.DescribeResourceTagsByResourceIds(cloudctx.From(ctx, nil, config.Global.Region), request)
		if err != nil {
			return nil, err
		}
		if response.Response.Tags != nil && len(response.Response.Tags) > 0 {
			tagResource = append(tagResource, response.Response.Tags...)
		}
		totalCount = *response.Response.TotalCount
		request.Offset = common.Uint64Ptr(*request.Offset + uint64(len(response.Response.Tags)))
	}
	return tagResource, nil
}

func GetLoadBalancerTags(ctx context.Context, ingress types.Ingress, lbId string, region string) ([]*tag.TagResource, error) {
	request := tag.NewDescribeResourceTagsByResourceIdsRequest()
	request.Limit = common.Uint64Ptr(TAGSPERPAGE)
	request.Offset = common.Uint64Ptr(0)
	request.ServiceType = common.StringPtr("clb")
	request.ResourcePrefix = common.StringPtr("clb")
	request.ResourceIds = []*string{&lbId}
	request.ResourceRegion = &region

	tagResource := make([]*tag.TagResource, 0)
	totalCount := uint64(TAGSPERPAGE)
	for *request.Offset < totalCount {
		response, err := tencentapi.Instance.DescribeResourceTagsByResourceIds(cloudctx.From(ctx, ingress, region), request)
		if err != nil {
			return nil, err
		}
		if response.Response.Tags != nil && len(response.Response.Tags) > 0 {
			tagResource = append(tagResource, response.Response.Tags...)
		}
		totalCount = *response.Response.TotalCount
		request.Offset = common.Uint64Ptr(*request.Offset + uint64(len(response.Response.Tags)))
	}
	return tagResource, nil
}

// func addResourceTags(ingress types.Ingress, lbID string, region string, addTags []*tag.Tag) error {
//	//qcs::clb:ap-chongqing:uin/**********:clb/lb-8hl075yn
//	resource := fmt.Sprintf("qcs::clb:%s:uin/%d:clb/%s", region, utils.Global.OwnerUin, lbID)
//	request := tag.NewModifyResourceTagsRequest()
//	request.ReplaceTags = addTags
//	request.Resource = &resource
//
//	if _, err := tencentapi.Instance.ModifyResourceTags(ingress, region, request); err != nil {
//		return err
//	}
//	return nil
// }

// func deleteResourceTags(ingress types.Ingress, lbID string, region string, delTags []*tag.TagKeyObject) error {
//	//region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
//	//if err != nil {
//	//	return err
//	//}
//
//	//qcs::clb:ap-chongqing:uin/**********:clb/lb-8hl075yn
//	resource := fmt.Sprintf("qcs::clb:%s:uin/%d:clb/%s", region, utils.Global.OwnerUin, lbID)
//	request := tag.NewModifyResourceTagsRequest()
//	request.DeleteTags = delTags
//	request.Resource = &resource
//
//	if _, err := tencentapi.Instance.ModifyResourceTags(ingress, region, request); err != nil {
//		return err
//	}
//	return nil
// }

func DescribeQuota() (*config.BackendQuota, error) {
	request := clbInner.NewDescribeQuotaRequest()
	request.ResourceQuota = common.BoolPtr(true)
	if rsp, err := tencentapi.Instance.DescribeQuota(cloudctx.From(context.TODO(), nil, config.Global.Region), request); err != nil {
		klog.Errorf("DescribeQuota Error, %v", err)
		return nil, err
	} else {
		quota := config.NewBackendQuota(-1, config.Global.BackendQuota)
		for _, item := range rsp.Response.QuotaSet {
			if *item.QuotaId == "TOTAL_TARGET_BIND_QUOTA" {
				if *item.ResourceId == "default" {
					quota.LBUserQuota = int(*item.QuotaLimit)
				} else {
					quota.LBInstanceQuota[*item.ResourceId] = int(*item.QuotaLimit)
				}
			}
		}
		if quota.LBUserQuota == -1 {
			return nil, fmt.Errorf("can't find UserQuota in response")
		}
		return quota, nil
	}
}

type RewriteRule struct {
	listenerId       string
	locationId       string
	targetListenerId string
	targetLocationId string
}

func ChangeToRewriteRule(key string) *RewriteRule {
	split := strings.Split(key, "_")
	return &RewriteRule{
		listenerId:       split[0],
		locationId:       split[1],
		targetListenerId: split[2],
		targetLocationId: split[3],
	}
}

func ChangeToRewriteRules(keySet sets.String) []*RewriteRule {
	rules := make([]*RewriteRule, 0)
	for key, _ := range keySet {
		rules = append(rules, ChangeToRewriteRule(key))
	}
	return rules
}

func ConvertToRewriteRules(rewriteRules []*clb.RuleOutput) []*RewriteRule {
	rules := make([]*RewriteRule, 0)
	for _, rewriteRule := range rewriteRules {
		rules = append(rules, &RewriteRule{
			listenerId:       *rewriteRule.ListenerId,
			locationId:       *rewriteRule.LocationId,
			targetListenerId: *rewriteRule.RewriteTarget.TargetListenerId,
			targetLocationId: *rewriteRule.RewriteTarget.TargetLocationId,
		})
	}
	return rules
}

func GroupByRewriteRule(deleteRewrite []*RewriteRule) map[string]map[string][]*RewriteRule {
	deleteRewriteMap := make(map[string]map[string][]*RewriteRule)
	for index, rewrite := range deleteRewrite {
		if _, exist := deleteRewriteMap[rewrite.listenerId]; !exist {
			deleteRewriteMap[rewrite.listenerId] = make(map[string][]*RewriteRule)
			if _, exist := deleteRewriteMap[rewrite.listenerId][rewrite.targetListenerId]; !exist {
				deleteRewriteMap[rewrite.listenerId][rewrite.targetListenerId] = make([]*RewriteRule, 0)
			}
		}
		deleteRewriteMap[rewrite.listenerId][rewrite.targetListenerId] = append(deleteRewriteMap[rewrite.listenerId][rewrite.targetListenerId], deleteRewrite[index])
	}
	return deleteRewriteMap
}

func DeleteRewrites(ctx context.Context, ingress types.Ingress, loadBalancer *clb.LoadBalancer, region string, deleteRewrite []*RewriteRule) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	for sourceListenerId, rewriteMap := range GroupByRewriteRule(deleteRewrite) {
		for targetListenerId, rewriteList := range rewriteMap {
			rewriteInfos := make([]*clb.RewriteLocationMap, 0)
			for _, rewrite := range rewriteList {
				rewriteInfos = append(rewriteInfos, &clb.RewriteLocationMap{
					SourceLocationId: common.StringPtr(rewrite.locationId),
					TargetLocationId: common.StringPtr(rewrite.targetLocationId),
				})
			}

			request := clb.NewDeleteRewriteRequest()
			request.LoadBalancerId = loadBalancer.LoadBalancerId
			request.SourceListenerId = common.StringPtr(sourceListenerId)
			request.TargetListenerId = common.StringPtr(targetListenerId)
			for index := 0; index < len(rewriteInfos); index += BATCH_REWRITE_LIMIT {
				request.RewriteInfos = rewriteInfos[index:min(index+BATCH_REWRITE_LIMIT, len(rewriteInfos))]
				if _, err := tencentapi.Instance.DeleteRewrite(cloudctx.From(ctx, ingress, region), request); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func CreateRewrites(ctx context.Context, ingress types.Ingress, loadBalancer *clb.LoadBalancer, region string, addRewrite []*RewriteRule) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	for sourceListenerId, rewriteMap := range GroupByRewriteRule(addRewrite) {
		for targetListenerId, rewriteList := range rewriteMap {
			rewriteInfos := make([]*clb.RewriteLocationMap, 0)
			for _, rewrite := range rewriteList {
				rewriteInfos = append(rewriteInfos, &clb.RewriteLocationMap{
					SourceLocationId: common.StringPtr(rewrite.locationId),
					TargetLocationId: common.StringPtr(rewrite.targetLocationId),
				})
			}

			request := clb.NewManualRewriteRequest()
			request.LoadBalancerId = loadBalancer.LoadBalancerId
			request.SourceListenerId = common.StringPtr(sourceListenerId)
			request.TargetListenerId = common.StringPtr(targetListenerId)
			for index := 0; index < len(rewriteInfos); index += BATCH_REWRITE_LIMIT {
				request.RewriteInfos = rewriteInfos[index:min(index+BATCH_REWRITE_LIMIT, len(rewriteInfos))]
				if _, err := tencentapi.Instance.ManualRewrite(cloudctx.From(ctx, ingress, region), request); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func newTag(tagKey string, tagValue string) *tag.Tag {
	return &tag.Tag{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}

func newTagResource(tagKey string, tagValue string) *tag.TagResource {
	return &tag.TagResource{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}

func newTagKeyObject(tagKey string) *tag.TagKeyObject {
	return &tag.TagKeyObject{
		TagKey: &tagKey,
	}
}

func newTagFilter(serviceKey string, serviceValue []*string) *tag.TagFilter {
	return &tag.TagFilter{
		TagKey:   &serviceKey,
		TagValue: serviceValue,
	}
}

func newTagInfo(tagKey string, tagValue string) *clb.TagInfo {
	return &clb.TagInfo{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}

// min return the min of two int
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func GetAutoCreatedTagKey(ingress types.Ingress) types.TagKey {
	if ingress.Type() == types.CoreIngress && utils.IsInEKSCluster() {
		return types.TagKeyAutoCreatedInEKS
	} else {
		return types.TagKeyAutoCreated
	}
}

func GetAutoCreatedTagValue() string {
	return types.TagValueAutoCreated
}

func EnsureDeletionProtection(ctx context.Context, lbc *LoadBalancerContext) error {
	if !lbc.LoadBalancerResource.Spec.Created {
		return nil
	}
	enabled := lbc.Ingress.DeletionProtection()
	if enabled == nil {
		return nil
	}

	return modifyDeletionProtection(ctx, lbc, *enabled)
}

func modifyDeletionProtection(ctx context.Context, lbc *LoadBalancerContext, enable bool) error {
	loadbalancer, err := lbc.GetLoadBalancer(ctx)
	if err != nil {
		return err
	}
	lb := types.NewLB(loadbalancer, lbc.LoadBalancerResource.Spec.Created)
	if lb.IsDeletionProtectionEnabled() == enable {
		return nil
	}
	req := clbInner.NewModifyLoadBalancerAttributesRequest()
	req.LoadBalancerId = lb.LoadBalancerId
	req.DeleteProtect = &enable
	_, err = tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.From(ctx, lbc.Ingress, lbc.Region), req)
	if err != nil {
		return err
	}
	return lbc.UpdateLoadBalancer(ctx)
}
