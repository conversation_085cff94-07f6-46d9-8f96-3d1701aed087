package utils

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
)

type BackendManagementModeType string

const (
	RuleMix    = "kubernetes.io/ingress.rule-mix"
	HttpRules  = "kubernetes.io/ingress.http-rules"
	HttpsRules = "kubernetes.io/ingress.https-rules"

	InternetChargeType      = "kubernetes.io/ingress.internetChargeType"
	InternetMaxBandwidthOut = "kubernetes.io/ingress.internetMaxBandwidthOut"

	AnnoExistedLBID                   = "kubernetes.io/ingress.existLbId"
	IngressSubnetId                   = "kubernetes.io/ingress.subnetId"
	IngressDirectAccessAnnotation     = "ingress.cloud.tencent.com/direct-access"
	IngressEnableGraceShutdownForTKEx = "ingress.cloud.tencent.com/enable-grace-shutdown-tkex"
	ServiceEnableGraceShutdownForTKEx = "service.cloud.tencent.com/enable-grace-shutdown-tkex"
	IngressEnablePublicGraceShutdown  = "ingress.cloud.tencent.com/enable-grace-shutdown"
	ServiceEnablePublicGraceShutdown  = "service.cloud.tencent.com/enable-grace-shutdown"
	TkeServiceConfigAnnontation       = "ingress.cloud.tencent.com/tke-service-config"
	TkeServiceConfigAutoAnnontation   = "ingress.cloud.tencent.com/tke-service-config-auto"
	LoadBalancerNatIPv6Annontation    = "ingress.cloud.tencent.com/loadbalance-nat-ipv6"
	LoadBalancerIPv6Annontation       = "ingress.cloud.tencent.com/loadbalance-ipv6"
	RewriteSupportAnnontation         = "ingress.cloud.tencent.com/rewrite-support"
	AutoRewriteAnnontation            = "ingress.cloud.tencent.com/auto-rewrite"
	IngressEnableCustomizedWeight     = "ingress.cloud.tencent.com/lb-rs-weight"
	CrossRegionIdAnnontation          = "ingress.cloud.tencent.com/cross-region-id"
	CrossVPCIdAnnontation             = "ingress.cloud.tencent.com/cross-vpc-id"
	CrossTypeAnnontation              = "ingress.cloud.tencent.com/cross-type"
	TargetCrossRegionIdAnnontation    = "ingress.cloud.tencent.com/target-cross-region-id"
	TargetCrossVPCIdAnnontation       = "ingress.cloud.tencent.com/target-cross-vpc-id"
	HybridTypeAnnontation             = "ingress.cloud.tencent.com/hybrid-type"
	SnatProInfoAnnotation             = "ingress.cloud.tencent.com/snat-pro-info"
	ModificationProtectionAnnontation = "ingress.cloud.tencent.com/modification-protection"
	PassToTargetAnnontation           = "ingress.cloud.tencent.com/pass-to-target"
	SecurityGroupsAnnontation         = "ingress.cloud.tencent.com/security-groups"
	ClientTokenAnnotation             = "ingress.cloud.tencent.com/client-token"

	BackendManagementModeAnnotation = "ingress.cloud.tencent.com/backend-management-mode"
	BackendManageOnlyAnnotation     = "ingress.cloud.tencent.com/backend-manage-only"

	FromOtherClusterAnnotation = "ingress.cloud.tencent.com/from-other-cluster"

	BackendManagementModeAll         BackendManagementModeType = "all"
	BackendManagementModeTag         BackendManagementModeType = "tag"
	BackendManagementModeTargetGroup BackendManagementModeType = "target-group"

	ServiceCondition   = "service.cloud.tencent.com/status.conditions"
	IngressCondition   = "ingress.cloud.tencent.com/status.conditions"
	IngressClassKey    = "kubernetes.io/ingress.class"
	QcloudIngressClass = "qcloud"

	CrossType0_0 = "ManagerOnly"
	CrossType1_0 = "CrossTarget"
	CrossType2_0 = "CCN"
	CrossType1_1 = "PVGW"

	CrossType1_2 = "PVGW-PRO"

	HybridType_NONE = "NONE" // 未使用混合云
	HybridType_PVGW = "PVGW"
	HybridType_CCN  = "CCN"

	IngressFinalizer = "ingress.k8s.tencent/resources"
)

type TkePathType string

const ( // 参考：https://cloud.tencent.com/document/product/214/9032#.E8.BD.AC.E5.8F.91.E5.9F.9F.E5.90.8D.E9.85.8D.E7.BD.AE.E8.A7.84.E5.88.99
	// 负载均衡默认路径格式，已经是前缀匹配的含义，无需支持。

	// Prefix          TkePathType = "Prefix"          // ^~ 开头表示 URL 以某个常规字符串开头，不是正则匹配。
	Exact           TkePathType = "Exact"           // =  开头表示精确匹配。
	Regex           TkePathType = "Regex"           // ~  开头表示区分大小写的正则匹配。
	RegexIgnoreCase TkePathType = "RegexIgnoreCase" // ~* 开头表示不区分大小写的正则匹配。

	NonAbsolutePath TkePathType = "NonAbsolutePath" // 转发路径不要以绝对路径开头
)

type SnatProInfo struct {
	SnatIPs []SnatIP `json:"snatIPs"`
}

type SnatIP struct {
	SubnetId string  `json:"subnetId"`
	IP       *string `json:"ip"`
}

type Rewrite struct {
	Port       int32  `json:"port,omitempty"`
	Host       string `json:"host,omitempty"`
	Path       string `json:"path,omitempty"`
	PathType   []TkePathType
	RealPath   string
	AutoCreate bool
}

type Backend struct {
	Name string             `json:"serviceName,omitempty"`
	Port intstr.IntOrString `json:"servicePort,omitempty"`
}

func (this *Backend) ServiceName() string {
	return this.Name
}

func (this *Backend) ServicePort() intstr.IntOrString {
	return this.Port
}

type Rule struct {
	types.Domain
	Path     string        `json:"path,omitempty"`
	PathType []TkePathType `json:"pathType,omitempty"`
	Backend  *Backend      `json:"backend,omitempty"`
	Rewrite  *Rewrite      `json:"rewrite,omitempty"`

	RealPath     string
	Secret       *string
	TargetPort   *intstr.IntOrString
	L7RuleConfig *v1alpha1.L7RuleConfig
}

func (r Rule) GetHostPath() string {
	return fmt.Sprintf("%s%s", r.Host, r.Path)
}

func NewRule(host string, path string, pathType string, secret *string, backend types.IngressBackend) *Rule {
	rule := &Rule{
		Domain:   types.Domain{Host: host},
		Path:     path,
		PathType: ConvertPathType(&pathType),
		Secret:   secret,
		Backend: &Backend{
			Name: backend.ServiceName,
			Port: backend.ServicePort,
		},
	}
	return rule
}

func ConvertPathType(pathType *string) []TkePathType {
	if pathType != nil {
		if *pathType == "Exact" {
			return []TkePathType{Exact}
		}
	}
	return nil
}

func IngressName(ingress types.Ingress) string {
	return fmt.Sprintf("%s/%s", ingress.Namespace(), ingress.Name())
}

func IsQCLOUDIngress(ingress types.Ingress) bool {
	if config, exist := ingress.Annotations()[IngressClassKey]; exist {
		return config == "" || config == QcloudIngressClass
	}
	ingressClassName := ingress.IngressClassName()
	if ingressClassName != nil && *ingressClassName != QcloudIngressClass {
		return false
	}
	return true
}

func HasAutoRewrite(ingress types.Ingress) bool {
	if _, exist := ingress.Annotations()[AutoRewriteAnnontation]; exist {
		return true
	}
	return false
}

func IsAutoRewrite(ingress types.Ingress) (bool, error) {
	if autoRewriteAnnontation, exist := ingress.Annotations()[AutoRewriteAnnontation]; exist {
		autoRewrite, err := ParseBool(autoRewriteAnnontation)
		if err != nil {
			return false, types.NewError(errcode.AutoRewriteAnnotationError, "", IngressName(ingress))
		}
		return autoRewrite, nil
	}
	return false, nil
}

func IsRewriteSupport(ingress types.Ingress) (bool, error) {
	if rewriteSupportAnnotation, exist := ingress.Annotations()[RewriteSupportAnnontation]; exist {
		rewriteSupport, err := ParseBool(rewriteSupportAnnotation)
		if err != nil {
			return false, types.NewError(errcode.RewriteSupportAnnotationError, "", IngressName(ingress))
		}
		return rewriteSupport, nil
	}
	return false, nil
}

func IsRuleMixed(ingress types.Ingress) (bool, error) {
	if ruleMixedAnnotation, exist := ingress.Annotations()[RuleMix]; exist {
		ruleMixed, err := ParseBool(ruleMixedAnnotation)
		if err != nil {
			return false, types.NewError(errcode.RuleMixedAnnotationError, "", IngressName(ingress))
		}
		return ruleMixed, nil
	}
	return false, nil
}

func GetExistLbId(ingress types.Ingress) (string, bool) {
	if existLbId, exist := ingress.Annotations()[AnnoExistedLBID]; exist {
		return existLbId, true
	}
	return "", false
}

func GetSubnetId(ingress types.Ingress) (string, bool) {
	if subnetId, exist := ingress.Annotations()[IngressSubnetId]; exist {
		return subnetId, true
	}
	return "", false
}

func GetHttpRules(ingress types.Ingress) (string, bool) {
	if httpRule, exist := ingress.Annotations()[HttpRules]; exist {
		return httpRule, true
	}
	return "", false
}

func GetTargetCrossRegionId(ingress types.Ingress) (string, bool) {
	crossRegionId, exist := ingress.Annotations()[TargetCrossRegionIdAnnontation]
	return crossRegionId, exist
}

func GetTargetCrossVPCId(ingress types.Ingress) (string, bool) {
	crossVPCId, exist := ingress.Annotations()[TargetCrossVPCIdAnnontation]
	return crossVPCId, exist
}

func GetBackendManagementMode(ingress types.Ingress) BackendManagementModeType {
	if mode, exist := ingress.Annotations()[BackendManagementModeAnnotation]; exist {
		if mode == string(BackendManagementModeTag) {
			return BackendManagementModeTag
		} else if mode == string(BackendManagementModeTargetGroup) {
			return BackendManagementModeTargetGroup
		}
	}
	return BackendManagementModeAll
}

func GetFromOtherCluster(ingress types.Ingress) (string, bool) {
	fromOtherCluster, exist := ingress.Annotations()[FromOtherClusterAnnotation]
	if !exist {
		return "", false
	}
	return fromOtherCluster, true
}

func GetHttpRulesList(ingress types.Ingress) ([]*Rule, error) {
	val, ok := GetHttpRules(ingress)
	if !ok {
		return []*Rule{}, nil
	}

	rules, err := toRuleList(val)
	if err != nil {
		return nil, types.NewError(errcode.RuleHttpAnnotationError, err.Error(), IngressName(ingress))
	}
	return rules, nil
}

func GetHttpsRules(ingress types.Ingress) (string, bool) {
	if httpsRule, exist := ingress.Annotations()[HttpsRules]; exist {
		return httpsRule, true
	}
	return "", false
}

func GetHttpsRulesList(ingress types.Ingress) ([]*Rule, error) {
	val, ok := GetHttpsRules(ingress)
	if !ok {
		return []*Rule{}, nil
	}

	rules, err := toRuleList(val)
	if err != nil {
		return nil, types.NewError(errcode.RuleHttpsAnnotationError, err.Error(), IngressName(ingress))
	}

	for _, rule := range rules {
		mainType := false // Exact/Prefix/Regex/RegexIgnoreCase
		subType := false  // NonAbsolutePath
		for _, pathType := range rule.PathType {
			if pathType == Exact || pathType == Regex || pathType == RegexIgnoreCase {
				if mainType == false {
					mainType = true
				} else {
					return nil, types.NewError(errcode.ConflictPathType, "", IngressName(ingress), JsonWrapper(rule.PathType))
				}
			} else if pathType == NonAbsolutePath {
				if subType == false {
					subType = true
				} else {
					return nil, types.NewError(errcode.ConflictPathType, "", IngressName(ingress), JsonWrapper(rule.PathType))
				}
			} else {
				return nil, types.NewError(errcode.UnsupportedPathType, "", IngressName(ingress), JsonWrapper(rule.PathType))
			}
		}
	}
	return rules, nil
}

func toRuleList(rule string) ([]*Rule, error) {
	rules := make([]*Rule, 0)
	if err := json.Unmarshal([]byte(rule), &rules); err != nil {
		return nil, err
	}
	return rules, nil
}

// func ruleToString(r Rule) string {
//	return fmt.Sprintf("%s_%s_%s_%s", r.Host, r.Path, r.Backend.ServiceName(), r.Backend.ServicePort().String())
// }

func GetTkeServiceConfig(ingress types.Ingress) (string, error) {
	tkeServiceConfigAuto, err := IsTkeServiceConfigAuto(ingress)
	if err != nil {
		return "", err
	}
	// 自动化的TkeServiceConfig
	if tkeServiceConfigAuto {
		return IngressAutoServiceConfigName(ingress), nil
	}
	// 自定义的TkeServiceConfig
	if config, exist := ingress.Annotations()[TkeServiceConfigAnnontation]; exist {
		if strings.HasSuffix(config, "-auto-ingress-config") || strings.HasSuffix(config, "-auto-service-config") {
			return "", types.NewError(errcode.TkeServiceConfigConflictError, "", IngressName(ingress))
		}
		return config, nil
	}
	return "", nil
}

func IsTkeServiceConfigAuto(ingress types.Ingress) (bool, error) {
	if tkeServiceConfigAutoAnnontation, exist := ingress.Annotations()[TkeServiceConfigAutoAnnontation]; exist {
		tkeServiceConfigAuto, err := ParseBool(tkeServiceConfigAutoAnnontation)
		if err != nil {
			return false, types.NewError(errcode.TkeServiceConfigAutoAnnontationError, "", IngressName(ingress))
		}
		return tkeServiceConfigAuto, nil
	}
	return false, nil
}

func IngressAutoServiceConfigName(ingress types.Ingress) string {
	if ingress.Type() == types.CoreIngress {
		return fmt.Sprintf("%s-auto-ingress-config", ingress.Name())
	} else if ingress.Type() == types.MultiClusterIngress {
		return fmt.Sprintf("%s-auto-multiclusteringress-config", ingress.Name())
	}
	panic("unexpected ingress type.")
}

func ServiceAutoServiceConfigName(ingress types.Ingress) string {
	return fmt.Sprintf("%s-auto-service-config", ingress.Name())
}

func GetLoadBalancerNatIPv6(ingress types.Ingress) string {
	if config, exist := ingress.Annotations()[LoadBalancerNatIPv6Annontation]; exist {
		return config
	}
	return ""
}

func GetLoadBalancerIPv6(ingress types.Ingress) string {
	if config, exist := ingress.Annotations()[LoadBalancerIPv6Annontation]; exist {
		return config
	}
	return ""
}

func GetInternetChargeType(ingress types.Ingress) (string, bool, error) {
	if internetChargeType, exist := ingress.Annotations()[InternetChargeType]; exist {
		for _, chargeType := range []string{"TRAFFIC_POSTPAID_BY_HOUR", "BANDWIDTH_POSTPAID_BY_HOUR", "BANDWIDTH_PACKAGE"} {
			if chargeType == internetChargeType {
				return internetChargeType, true, nil
			}
		}
		return "", true, types.NewError(errcode.InternetChargeTypeAnnotationError, "", IngressName(ingress))
	}
	return "", false, nil
}

func GetInternetMaxBandwidthOut(ingress types.Ingress) (int, bool, error) {
	if internetMaxBandwidthOutAnnotation, exist := ingress.Annotations()[InternetMaxBandwidthOut]; exist {
		internetMaxBandwidthOut, err := strconv.Atoi(internetMaxBandwidthOutAnnotation)
		if err != nil {
			return 0, true, types.NewError(errcode.InternetMaxBandwidthOutAnnotationError, "", IngressName(ingress))
		}
		if internetMaxBandwidthOut < 0 || internetMaxBandwidthOut > 2048 {
			return internetMaxBandwidthOut, true, types.NewError(errcode.InternetMaxBandwidthOutAnnotationError, "", IngressName(ingress))
		}
		return internetMaxBandwidthOut, true, nil
	}
	return 0, false, nil
}

func HasDirectAccessIngress(ingress types.Ingress) bool {
	if _, exist := ingress.Annotations()[IngressDirectAccessAnnotation]; exist { // ENI直绑
		return true
	}
	return false
}

func IsDirectAccessIngress(ingress types.Ingress) (bool, error) {
	if directAccessAnnotation, exist := ingress.Annotations()[IngressDirectAccessAnnotation]; exist { // ENI直绑
		directAccess, err := ParseBool(directAccessAnnotation)
		if err != nil {
			return false, types.NewError(errcode.DirectAccessAnnotationError, "", IngressName(ingress))
		}
		return directAccess, nil
	}
	return false, nil
}

// 当用户在TLS中指定域名时，监听器需要开启SNI
func IsNeedSNI(ingress types.Ingress) bool {
	ingressTLSWrappers := ingress.TLS()
	if ingressTLSWrappers != nil {
		for _, tls := range ingressTLSWrappers {
			if tls.Hosts != nil && len(tls.Hosts) != 0 {
				return true
			}
		}
	}
	return false
}

func GetIngressRegion(ingress types.Ingress) string {
	if regionId, exist := GetCrossRegionId(ingress); exist {
		return regionId
	}
	return config.Global.Region
}

func GetCrossRegionId(ingress types.Ingress) (string, bool) {
	crossRegionId, exist := ingress.Annotations()[CrossRegionIdAnnontation]
	return crossRegionId, exist
}

func GetCrossVPCId(ingress types.Ingress) (string, bool) {
	crossVPCId, exist := ingress.Annotations()[CrossVPCIdAnnontation]
	return crossVPCId, exist
}

func GetCrossType(ingress types.Ingress) string {
	crossType, exist := ingress.Annotations()[CrossTypeAnnontation]
	if !exist {
		return CrossType2_0
	}
	if crossType != CrossType2_0 && crossType != CrossType1_0 && crossType != CrossType1_1 && crossType != CrossType0_0 && crossType != CrossType1_2 { // TODO
		return "err"
	}
	return crossType
}

func GetHybridType(ingress types.Ingress) string { // 混合云类型
	crossType, exist := ingress.Annotations()[HybridTypeAnnontation]
	if !exist {
		return HybridType_NONE
	}
	if crossType != HybridType_NONE && crossType != HybridType_PVGW && crossType != HybridType_CCN { // TODO
		return HybridType_NONE
	}
	return crossType
}

func GetSnatProInfo(ingress types.Ingress) (*SnatProInfo, error) {
	SNATProInfoString, existAnnotation := ingress.Annotations()[SnatProInfoAnnotation]
	if !existAnnotation {
		return nil, nil
	}

	SnatProInfo := &SnatProInfo{}
	if err := json.Unmarshal([]byte(SNATProInfoString), &SnatProInfo); err != nil {
		return nil, types.NewError(errcode.SnatIPAnnotationFormatError, "", IngressName(ingress))
	}

	if len(SnatProInfo.SnatIPs) > 10 {
		SnatProInfo.SnatIPs = SnatProInfo.SnatIPs[0:10]
		return SnatProInfo, types.NewError(errcode.SnatIPLimitExceeded, "", IngressName(ingress))
	}
	return SnatProInfo, nil
}

// 因为配置修改保护的注解比较特殊，如果由组件开启必须由组件关闭，所以这个配置不存在也认为是false并关闭资源配置保护
func IsModificationProtection(ingress types.Ingress) (bool, error) {
	modificationProtectionAnnotation, exist := ingress.Annotations()[ModificationProtectionAnnontation]
	if !exist {
		return false, nil
	}

	modificationProtection, err := ParseBool(modificationProtectionAnnotation)
	if err != nil {
		return false, types.NewError(errcode.ModificationProtectionAnnontationError, err.Error(), IngressName(ingress))
	}
	return modificationProtection, nil
}

func GetServiceSecurityGroups(ingress types.Ingress) []string {
	result := make([]string, 0)
	if value, exist := ingress.Annotations()[SecurityGroupsAnnontation]; exist { // ENI直绑
		if value == "" {
			return []string{}
		}
		return strings.Split(value, ",")
	}
	return result
}

func HasServiceSecurityGroups(ingress types.Ingress) (string, bool) {
	if value, exist := ingress.Annotations()[SecurityGroupsAnnontation]; exist { // ENI直绑
		return value, true
	}
	return "", false
}

func IsServicePassToTarget(ingress types.Ingress) (bool, error) {
	if passToTargetAnnotation, exist := ingress.Annotations()[PassToTargetAnnontation]; exist { // ENI直绑
		passToTarget, err := ParseBool(passToTargetAnnotation)
		if err != nil {
			return false, types.NewError(errcode.PassToTargetAnnotationError, "", IngressName(ingress))
		}
		return passToTarget, nil
	}
	return false, nil
}

func HasServicePassToTarget(service types.Ingress) (string, bool) {
	if value, exist := service.Annotations()[PassToTargetAnnontation]; exist { // ENI直绑
		return value, true
	}
	return "", false
}

func GetClientToken(ingress types.Ingress) string {
	if ingress.Annotations() == nil {
		return ""
	}
	return ingress.Annotations()[ClientTokenAnnotation]
}

func BackendOnlyIngress(ingress types.Ingress) bool {
	backendManageOnlyAnnotation, exist := ingress.Annotations()[BackendManageOnlyAnnotation]
	if !exist {
		return false
	}

	backendManageOnly, err := ParseBool(backendManageOnlyAnnotation)
	if err != nil {
		return false
	}
	return backendManageOnly
}

func NeedPatchRSTags(ingress types.Ingress) bool {
	patchRSTagAnnotation, exist := ingress.Annotations()[types.AnnotationPatchRSTags]
	if !exist {
		return false
	}

	needPatchTag, err := ParseBool(patchRSTagAnnotation)
	if err != nil {
		return false
	}
	return needPatchTag
}
