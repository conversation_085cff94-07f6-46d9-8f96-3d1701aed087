package utils

import (
	"encoding/json"
	"strconv"
	"strings"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	v1 "k8s.io/api/core/v1"

	"git.woa.com/kateway/pkg/domain/types"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
)

const (
	WeightDefault          = 10
	WeightDefaultLocal     = 1
	WeightDefaultProtect   = 8
	WeightGracefulShutdown = 0
)

type CustomizedWeight struct {
	DefaultWeight *int               `json:"defaultWeight"`
	Groups        []WeightConf       `json:"groups"`
	Zones         []types.ZoneWeight `json:"zones"`
}

type WeightConf struct {
	Key         Key           `json:"key"`
	StatefulSet []StatefulSet `json:"statefulSets"`
}

type Key struct {
	ClusterID string `json:"clusterID"`
	Proto     string `json:"proto"`
	Port      int    `json:"port"`
	Host      string `json:"host"`
	Path      string `json:"path"`
}

type StatefulSet struct {
	Name       string   `json:"name"`
	PodWeights []Detail `json:"weights"`
}

type Detail struct {
	Weight   int   `json:"weight"`
	PodIndex []int `json:"podIndexes"`
}

func IsEnableCustomizedWeight(ingress types.Ingress) (*CustomizedWeight, error) {
	if CustomizedWeightStr, exist := ingress.Annotations()[IngressEnableCustomizedWeight]; exist {
		customizedWeight := CustomizedWeight{}
		err := json.Unmarshal([]byte(CustomizedWeightStr), &customizedWeight)
		if err != nil {
			return nil, err
		}
		return &customizedWeight, nil
	}
	return nil, nil
}

func DeterminePodCustomizedWeight(protocol string, path string, host string, pod *v1.Pod, cus *CustomizedWeight) *int {
	for _, owner := range pod.OwnerReferences {
		if owner.Kind == "StatefulSet" || owner.Kind == "StatefulSetPlus" {
			split := strings.Split(pod.Name, "-")
			podIndexStr := split[len(split)-1]
			podIndex, err := strconv.Atoi(podIndexStr)
			if err != nil {
				return nil
			}
			for _, group := range cus.Groups {
				if group.Key.ClusterID == config.Global.ClusterName || group.Key.ClusterID == "" {
					for _, sts := range group.StatefulSet {
						if sts.Name == owner.Name {
							if strings.ToUpper(protocol) == strings.ToUpper(group.Key.Proto) && group.Key.Host == host && group.Key.Path == path {
								for _, weight := range sts.PodWeights {
									for _, index := range weight.PodIndex {
										if index == podIndex {
											return common.IntPtr(weight.Weight)
										}
									}
								}
								return nil
							}
						}
					}
				}
			}
		}
	}
	return nil
}

func IsHealthyWeight(weight int64) bool {
	return weight > 0 && weight != WeightDefaultProtect
}
