package cluster_service

import (
	"sync"

	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/taskqueue"
)

var (
	QueueServiceInstanceLock              = new(sync.Mutex)
	QueueServiceInstance     QueueService = nil
)

type QueueService interface {
	IngressQueue() *taskqueue.TaskQueue
	MultiClusterIngressQueue() *taskqueue.TaskQueue
	NodeGracefulDeletionManager() *services.NodeGracefulDeletionManager
	SecretQueue() *taskqueue.TaskQueue
}
