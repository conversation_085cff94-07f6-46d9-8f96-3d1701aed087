package utils

import (
	"encoding/json"
	"fmt"
	v1 "k8s.io/api/core/v1"
)

const (
	ServiceDirectAccessAnnotation       = "service.cloud.tencent.com/direct-access"
	TkeManagementAnnontation            = "service.cloud.tencent.com/tke-management"
	AnnoBackendsListLabelAnnotation     = "service.kubernetes.io/qcloud-loadbalancer-backends-label"
	LocalSvcWeightedBalancerAnnontation = "service.cloud.tencent.com/local-svc-weighted-balance"
	SpecifyProtocolAnnontation          = "service.cloud.tencent.com/specify-protocol"
	ServiceExistedLBAnnotation          = "service.kubernetes.io/tke-existed-lbid"
	LocalSvcWeightedBalancerTrue        = "true"
)

type SpecifyProtocolAnnotation map[int32]SpecifyProtocol

type SpecifyProtocol struct {
	Protocol []string                        `json:"protocol,omitempty"`
	Tls      *string                         `json:"tls,omitempty"`
	Hosts    map[string]*SpecifyProtocolHost `json:"hosts,omitempty"`
}

type SpecifyProtocolHost struct {
	Tls *string `json:"tls,omitempty"`
}

func ServiceName(service *v1.Service) string {
	return fmt.Sprintf("%s/%s", service.Namespace, service.Name)
}

func IsDirectAccessService(service *v1.Service) bool {
	if IsInEKSCluster() {
		return true
	}
	if directAccessAnnotation, exist := service.Annotations[ServiceDirectAccessAnnotation]; exist { // ENI直绑
		directAccess, _ := ParseBool(directAccessAnnotation)
		return directAccess
	}
	return false
}

func PortSlicesEqualForLB(x, y []v1.ServicePort) bool {
	if len(x) != len(y) {
		return false
	}

	for i := range x {
		if !portEqualForLB(&x[i], &y[i]) {
			return false
		}
	}
	return true
}

func portEqualForLB(x, y *v1.ServicePort) bool {
	if x.Name != y.Name || x.Protocol != y.Protocol || x.Port != y.Port || x.TargetPort != y.TargetPort || x.NodePort != y.NodePort {
		return false
	}
	return true
}

//func GetLocalSvcWeightedBalancer(service *v1.Service) bool {
//	if localSvcWeightedAnnotation, exist := service.Annotations[LocalSvcWeightedBalancerAnnontation]; exist {
//		if localSvcWeighted, err := ParseBool(localSvcWeightedAnnotation); err == nil { // 填错的场景，忽略注解的作用
//			return localSvcWeighted
//		}
//	}
//	return false
//}

func GetBackendsListLabel(service *v1.Service) (string, bool) {
	if labelSelector, exist := service.Annotations[AnnoBackendsListLabelAnnotation]; exist { // ENI直绑
		return labelSelector, true
	}
	return "", false
}

func GetServiceExistLB(service *v1.Service) (string, bool) {
	if existedLB, exist := service.Annotations[ServiceExistedLBAnnotation]; exist {
		return existedLB, true
	}
	return "", false
}

func GetSpecifyProtocol(service *v1.Service) (*SpecifyProtocolAnnotation, bool) {
	specifyProtocol := &SpecifyProtocolAnnotation{}
	data, exist := service.Annotations[SpecifyProtocolAnnontation]
	if !exist {
		return specifyProtocol, false
	}

	if err := json.Unmarshal([]byte(data), specifyProtocol); err != nil {
		return specifyProtocol, false
	}
	return specifyProtocol, exist
}
