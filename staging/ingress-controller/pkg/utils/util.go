package utils

import (
	"context"
	"errors"
	"fmt"
	"os"
	"sort"
	"strconv"
	"strings"

	go_version "github.com/hashicorp/go-version"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/json"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/ingress"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
)

/*
 * https://cloud.tencent.com/document/api/214/30694#Backend 后端服务的类型
 */
type BackendType string

const (
	CLBRsProtection = "tke.cloud.tencent.com/protected-by-ingress-controller" // Node优雅删除：Finalizer 保护机制
)

var (
	ENI         BackendType = "ENI"
	CVM         BackendType = "CVM"
	PVGW        BackendType = "PVGW"
	NAT         BackendType = "NAT" // SNAT Pro，PVGW网络跨地域后端、PVGW网络混合云后端
	EVM         BackendType = "EVM"
	CCN         BackendType = "CCN" // 云联网跨地域绑定类型后端
	GLOBALROUTE BackendType = "GLOBALROUTE"
	IPDC        BackendType = "IPDC"

	PROTOCOL_TCP     = "TCP"
	PROTOCOL_UDP     = "UDP"
	PROTOCOL_TCP_SSL = "TCP_SSL"
	PROTOCOL_QUIC    = "QUIC"
	PROTOCOL_HTTP    = "HTTP"
	PROTOCOL_HTTPS   = "HTTPS"

	InstanceTypeLabelKey = "node.kubernetes.io/instance-type"
	InstanceTypeEKS      = "EKLET"
	InstanceTypeIDC      = "EXTERNAL"
)

var (
	IngressConditionType = "Ready"
)

func IsENILikeType(backendType string) bool {
	return backendType == string(ENI) || backendType == string(EVM) || backendType == string(GLOBALROUTE) || backendType == string(CCN) || backendType == string(NAT) || backendType == string(PVGW) || backendType == string(IPDC)
}

func IsCVMLikeType(backendType string) bool {
	return backendType == string(CVM)
}

func IsEKSNode(node *v1.Node) bool {
	if value, has := node.Labels[InstanceTypeLabelKey]; has && strings.ToUpper(value) == InstanceTypeEKS {
		return true
	}
	return false
}

func IsIDCNode(node *v1.Node) bool {
	if value, has := node.Labels[InstanceTypeLabelKey]; has && strings.ToUpper(value) == InstanceTypeIDC {
		return true
	}
	return false
}

func IsCXMNode(node *v1.Node) bool {
	providerId := node.Spec.ProviderID
	if strings.HasPrefix(providerId, "tencentcloud:") {
		instanceId := providerId[strings.LastIndex(providerId, "/")+1:]
		if strings.HasPrefix(instanceId, "kn-") {
			return true
		}
	}
	return false
}

func IsTencentCloudCVMNode(node *v1.Node) bool {
	providerId := node.Spec.ProviderID
	if strings.HasPrefix(providerId, "tencentcloud:") {
		instanceId := providerId[strings.LastIndex(providerId, "/")+1:]
		if strings.HasPrefix(instanceId, "ins-") {
			return true
		}
	}
	return false
}

// Ingress的直通注解优先
// 没有Ingress的直通注解，默认继承Service的直通注解（Service注解内容出现错误时，认为是非直连）
func IsDirectAccessIngressCascade(ingress types.Ingress, service *v1.Service) bool {
	if IsInEKSCluster() {
		return true
	}
	if HasDirectAccessIngress(ingress) {
		ingressDirectAccess, _ := IsDirectAccessIngress(ingress)
		return ingressDirectAccess
	} else {
		return IsDirectAccessService(service)
	}
}

// func IsEnableTKExGraceShutdown(ingress types.Ingress, service *v1.Service) bool {
//	if HasEnableTKExGraceUpdateIngress(ingress) {
//		ingressEnableGraceUpdate, _ := IsIngressEnableTKExGraceShutdown(ingress)
//		return ingressEnableGraceUpdate
//	} else {
//		return IsServiceEnableTKExGraceShutdown(service)
//	}
// }
//
// func HasEnableTKExGraceUpdateIngress(ingress types.Ingress) bool {
//	if _, exist := ingress.Annotations()[IngressEnableGraceShutdownForTKEx]; exist {
//		return true
//	}
//	return false
// }
//
// func IsIngressEnableTKExGraceShutdown(ingress types.Ingress) (bool, error) {
//	if directAccessAnnotation, exist := ingress.Annotations()[IngressEnableGraceShutdownForTKEx]; exist {
//		directAccess, err := ParseBool(directAccessAnnotation)
//		if err != nil {
//			return false, types.NewError(ingressError.TKExGraceShutdownAnnotationError, "", IngressName(ingress))
//		}
//		return directAccess, nil
//	}
//	return false, nil
// }
//
// func IsServiceEnableTKExGraceShutdown(service *v1.Service) bool {
//	if directAccessAnnotation, exist := service.Annotations[ServiceEnableGraceShutdownForTKEx]; exist {
//		directAccess, _ := ParseBool(directAccessAnnotation)
//		return directAccess
//	}
//	return false
// }
//
// func IsEnablePublicGraceShutdown(ingress types.Ingress, service *v1.Service) bool {
//	if HasEnablePublicGraceUpdateIngress(ingress) {
//		ingressEnableGraceUpdate, _ := IsIngressEnablePublicGraceShutdown(ingress)
//		return ingressEnableGraceUpdate
//	} else {
//		return IsServiceEnablePublicGraceShutdown(service)
//	}
// }
//
// func HasEnablePublicGraceUpdateIngress(ingress types.Ingress) bool {
//	if _, exist := ingress.Annotations()[IngressEnablePublicGraceShutdown]; exist {
//		return true
//	}
//	return false
// }
//
// func IsIngressEnablePublicGraceShutdown(ingress types.Ingress) (bool, error) {
//	if directAccessAnnotation, exist := ingress.Annotations()[IngressEnablePublicGraceShutdown]; exist {
//		directAccess, err := ParseBool(directAccessAnnotation)
//		if err != nil {
//			return false, types.NewError(ingressError.PublicGraceShutdownAnnotationError, "", IngressName(ingress))
//		}
//		return directAccess, nil
//	}
//	return false, nil
// }
//
// func IsServiceEnablePublicGraceShutdown(service *v1.Service) bool {
//	if directAccessAnnotation, exist := service.Annotations[ServiceEnablePublicGraceShutdown]; exist {
//		directAccess, _ := ParseBool(directAccessAnnotation)
//		return directAccess
//	}
//	return false
// }

func ConvertResourceTag(tagResources []*tag.TagResource) []*clb.TagInfo {
	tags := make([]*clb.TagInfo, len(tagResources))
	for i, tagResource := range tagResources {
		tags[i] = NewTagInfo(*tagResource.TagKey, *tagResource.TagValue)
	}
	return tags
}

func NewTagInfo(tagKey string, tagValue string) *clb.TagInfo {
	return &clb.TagInfo{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}

func NodeName(node *v1.Node) string {
	return fmt.Sprintf("%s/%s", node.Namespace, node.Name)
}

func EndpointsName(endpoints *v1.Endpoints) string {
	return fmt.Sprintf("%s/%s", endpoints.Namespace, endpoints.Name)
}

func SecretName(secret *v1.Secret) string {
	return fmt.Sprintf("%s/%s", secret.Namespace, secret.Name)
}

func ConvertTagResourceList(tagResources []*clb.TagInfo) []*tag.TagResource {
	tags := make([]*tag.TagResource, len(tagResources))
	for i, tagResource := range tagResources {
		tags[i] = NewTagResource(*tagResource.TagKey, *tagResource.TagValue)
	}
	return tags
}

func NewTagResource(tagKey string, tagValue string) *tag.TagResource {
	return &tag.TagResource{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}

func JsonWrapper(obj interface{}) string {
	if jsonStr, jsonErr := json.Marshal(obj); jsonErr == nil {
		return string(jsonStr)
	}
	return "json_format_error"
}

func ParseBool(str string) (bool, error) {
	switch str {
	case "true", "TRUE", "True":
		return true, nil
	case "false", "FALSE", "False":
		return false, nil
	}
	return false, errors.New("ParseBool")
}

// Min return the min of two int
func Min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func Int32Ptr(v int32) *int32 {
	return &v
}

func Int64Ptr(v int) *int64 {
	a := int64(v)
	return &a
}

func IsInEKSCluster() bool {
	_, has := os.LookupEnv("TKE_ENV_FOR_EKS_CLUSTER")
	return has
}

// 后端协议逻辑
// ipv4 -> ipv4
// ipv6(NAT) -> ipv4
// ipv6(FullChain) -> ipv6
//
// Special For L7 Listener : ipv6(FullChain) with MixIpTarget -> ipv4 or ipv6
func GetBackendType(loadBalancer *clb.LoadBalancer) string {
	addressIPVersion := strings.ToLower(*loadBalancer.AddressIPVersion)
	if addressIPVersion == "ipv6" { // IPv6
		if *loadBalancer.IPv6Mode == "IPv6FullChain" { // IPv6FullChain
			if *loadBalancer.MixIpTarget == true {
				return "mixed"
			}
			return "ipv6"
		} else { // IPv6Nat64
			return "ipv4"
		}
	} else { // IPv4
		return "ipv4"
	}
}

func GetVipAndDomain(loadBalancer *clb.LoadBalancer) ([]*string, *string) {
	vips := make([]*string, 0)
	if loadBalancer != nil && len(loadBalancer.LoadBalancerVips) != 0 {
		vips = append(vips, loadBalancer.LoadBalancerVips...)
	}
	if loadBalancer != nil && loadBalancer.AddressIPv6 != nil {
		vips = append(vips, loadBalancer.AddressIPv6)
	}

	if loadBalancer.Forward != nil && *loadBalancer.Forward == 1 && loadBalancer.Domain != nil && *loadBalancer.Domain != "" {
		// 域名化改造账户，应用型资源有域名的场景
		// 1. 有域名返回域名，有VIP返回VIP，两个都有都返回
		return vips, common.StringPtr(*loadBalancer.Domain)
	}
	return vips, nil
}

func FilterPodsByBackendType(pods []*v1.Pod, backendType string) []*v1.Pod {
	if backendType == "mixed" {
		return pods
	}

	result := make([]*v1.Pod, 0)
	for index, pod := range pods {
		if pod.Status.PodIPs != nil && len(pod.Status.PodIPs) != 0 {
			for _, podIP := range pod.Status.PodIPs {
				if CheckBackendType(podIP.IP, backendType) {
					result = append(result, pods[index])
					break
				}
			}
		} else {
			if CheckBackendType(pod.Status.PodIP, backendType) {
				result = append(result, pods[index])
			}
		}
	}
	return result
}

func FilterNodesByBackendType(nodes []*v1.Node, backendType string) []*v1.Node {
	if backendType == "mixed" {
		return nodes
	}

	result := make([]*v1.Node, 0)
	for index, node := range nodes {
		if node.Status.Addresses != nil && len(node.Status.Addresses) != 0 {
			for _, address := range node.Status.Addresses {
				if address.Type == v1.NodeInternalIP && CheckBackendType(address.Address, backendType) {
					result = append(result, nodes[index])
					break
				}
			}
		}
	}
	return result
}

func CheckBackendType(ip string, backendType string) bool {
	if backendType == "ipv4" {
		return strings.Contains(ip, ".")
	} else if backendType == "ipv6" {
		return strings.Contains(ip, ":")
	}
	return true // mixed ?
}

func GetNodeBackend(node *v1.Node, backendType string) (string, bool) {
	if backendType == "ipv6" || backendType == "ipv4" {
		return GetNodeIpv4Ipv6Backend(node, backendType)
	} else {
		if backend, exist := GetNodeIpv4Ipv6Backend(node, "ipv6"); exist {
			return backend, exist
		}
		return GetNodeIpv4Ipv6Backend(node, "ipv4")
	}
}

func GetNodeIpv4Ipv6Backend(node *v1.Node, backendType string) (string, bool) {
	if node.Status.Addresses != nil && len(node.Status.Addresses) != 0 {
		for _, address := range node.Status.Addresses {
			if address.Type == v1.NodeInternalIP && CheckBackendType(address.Address, backendType) {
				return address.Address, true
			}
		}
	}

	return "", false
}

func IsPodUnableBind(pod *v1.Pod) bool {
	if pod.Status.PodIP == "" { // ENI IP 还未分配的话，跳过该Pod。
		return true
	}

	if pod.Spec.HostNetwork == true { // HostNetwork网络模式，使用节点网络，不支持直连
		return true
	}

	if pod.Status.Reason == "NodeLost" { // 典型场景，EKS正在原地更换CXM子机
		return true
	}

	// PodFailed：Pod已经失效，IP被销毁。比如被驱逐节点，不能够作为绑定目标
	// PodSucceeded：Pod已经结束任务，IP被销毁。比如Job Pod Complete。
	if pod.Status.Phase == v1.PodFailed || pod.Status.Phase == v1.PodSucceeded {
		return true
	}

	return false
}

func PodContainerReady(pod *v1.Pod) bool {
	for _, condition := range pod.Status.Conditions {
		if condition.Type != v1.ContainersReady {
			continue
		}
		return condition.Status == v1.ConditionTrue
	}
	return false
}

func GetPodIPs(pod *v1.Pod) []string {
	result := make([]string, 0)
	if pod.Status.PodIPs != nil && len(pod.Status.PodIPs) != 0 {
		for _, podIP := range pod.Status.PodIPs {
			result = append(result, podIP.IP)
		}
		return result
	}
	if pod.Status.PodIP != "" {
		result = append(result, pod.Status.PodIP)
	}
	return result
}

func GetPodBackend(pod *v1.Pod, backendType string) (string, bool) {
	if backendType == "ipv6" || backendType == "ipv4" {
		return GetPodIpv4Ipv6Backend(pod, backendType)
	} else {
		if backend, exist := GetPodIpv4Ipv6Backend(pod, "ipv6"); exist {
			return backend, exist
		}
		return GetPodIpv4Ipv6Backend(pod, "ipv4")
	}
}

func GetPodIpv4Ipv6Backend(pod *v1.Pod, backendType string) (string, bool) {
	if pod.Status.PodIPs != nil && len(pod.Status.PodIPs) != 0 {
		for _, podIP := range pod.Status.PodIPs {
			if CheckBackendType(podIP.IP, backendType) {
				return podIP.IP, true
			}
		}
	} else {
		if CheckBackendType(pod.Status.PodIP, backendType) {
			return pod.Status.PodIP, true
		}
	}

	return "", false
}

func GetServiceIPStack(service *v1.Service) string {
	ipv4 := false
	ipv6 := false
	if service.Spec.IPFamilies != nil && len(service.Spec.IPFamilies) != 0 {
		for _, ipFamilies := range service.Spec.IPFamilies {
			if ipFamilies == v1.IPv4Protocol {
				ipv4 = true
			} else if ipFamilies == v1.IPv6Protocol {
				ipv6 = true
			}
		}
	}
	if service.Spec.ClusterIPs != nil && len(service.Spec.ClusterIPs) != 0 {
		for _, ipFamilies := range service.Spec.ClusterIPs {
			if strings.Contains(ipFamilies, ".") {
				ipv4 = true
			} else if strings.Contains(ipFamilies, ":") {
				ipv6 = true
			}
		}
	}
	if service.Spec.ClusterIP != "" && service.Spec.ClusterIP != "None" {
		if strings.Contains(service.Spec.ClusterIP, ".") {
			ipv4 = true
		} else if strings.Contains(service.Spec.ClusterIP, ":") {
			ipv6 = true
		}
	}
	if ipv4 && ipv6 {
		return "mixed"
	} else if ipv6 {
		return "ipv6"
	}
	return "ipv4"
}

func StringSortJoin(elems []*string, sep string) string {
	elemsWrraper := make([]string, len(elems))
	for index, _ := range elems {
		elemsWrraper[index] = *elems[index]
	}
	sort.Strings(elemsWrraper)
	return strings.Join(elemsWrraper, ",")
}

func KubernetesVersionCheck(ctx context.Context, clientSet *kubernetes.Clientset, version string, nodeCheck bool) (bool, error) {
	span, ctx := jaeger.StartSpanFromContext(ctx)
	defer span.Finish()

	semverBase, _ := go_version.NewSemver(version)
	if nodeCheck {
		// 检查所有节点的Kubelet版本
		nodeList, err := clientSet.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
		if err != nil {
			klog.Errorf("ClusterInfoService Kubernetes ClientSet NodesList Error. %s", err.Error())
			return false, err
		}
		for _, node := range nodeList.Items {
			semver, err := go_version.NewSemver(node.Status.NodeInfo.KubeletVersion)
			if err != nil {
				klog.Errorf("ClusterInfoService Unexpected Node KubeletVersion Error. %s", err.Error())
				return false, err
			}
			if semver.Compare(semverBase) < 0 { // 当前服务版本小于指定版本
				return false, nil
			}
		}
	}

	serverVersion, err := clientSet.ServerVersion()
	for err != nil {
		klog.Errorf("Unexpected Kubernetes Version Error. %s", err.Error())
		return false, err
	}
	serverSemver, err := go_version.NewSemver(serverVersion.GitVersion)
	if err != nil {
		klog.Errorf("Unexpected Kubernetes Version Error. %s", err.Error())
		return false, err
	}
	if serverSemver.Compare(semverBase) < 0 { // 当前服务版本小于指定版本
		return false, nil
	}
	return true, nil
}

func GetListenerKey(port int32, protocol string) string {
	return fmt.Sprintf("%d_%s", port, strings.ToUpper(protocol))
}

func ParseListenerKey(key string) (int64, string) {
	indexA := strings.Index(key, "_")
	port, _ := strconv.ParseInt(key[0:indexA], 10, 64)
	protocal := key[indexA+1:]
	return port, protocal
}

func IsL4Protocol(protocol string) bool {
	return protocol == PROTOCOL_TCP || protocol == PROTOCOL_UDP || protocol == PROTOCOL_TCP_SSL || protocol == PROTOCOL_QUIC
}

func IsL7Protocol(protocol string) bool {
	return protocol == PROTOCOL_HTTP || protocol == PROTOCOL_HTTPS
}

func IsUDPFamilyProtocol(protocol string) bool {
	return protocol == PROTOCOL_UDP || protocol == PROTOCOL_QUIC
}

func IsTCPFamilyProtocol(protocol string) bool {
	return protocol == PROTOCOL_HTTP || protocol == PROTOCOL_HTTPS || protocol == PROTOCOL_TCP || protocol == PROTOCOL_TCP_SSL
}

func Errors(errs []error) string {
	result := make([]string, len(errs))
	for index, err := range errs {
		result[index] = err.Error()
	}
	return strings.Join(result, "; ")
}

func AnalyzeErrors(errs string) map[string]bool {
	result := make(map[string]bool)
	errList := strings.Split(errs, "; ")
	for _, err := range errList {
		if err == "" {
			result["S2000"] = true
			continue
		}
		split := strings.Split(err, "Code: ")
		if len(split) < 2 {
			result[""] = true
			continue
		}
		errorCode := strings.Split(split[1], " ")
		if _, exist := ingress.IngressErrorCodeMap[errorCode[0]]; exist {
			result[errorCode[0]] = true
		}
	}
	return result
}

func StringWrapper(str *string) string {
	if str == nil {
		return "nil"
	}
	return *str
}
