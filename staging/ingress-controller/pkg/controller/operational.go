package controller

import (
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/types"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/plugins/tencent"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/cluster/nodestatus"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

const (
	SYNC_OPERATIONAL_METRTC_FREQUENCY = 10 * time.Minute

	// 统计指标
	// 内外网数量
	INTERNAL_LOADBALANCER = "internal_loadbalancer" // 内网负载均衡数量
	INTERNET_LOADBALANCER = "internet_loadbalancer" // 外网负载均衡数量
	INGRESS_COUNT         = "ingress_count"         // Ingress资源数量
	QCLOUD_INGRESS_COUNT  = "qcloud_ingress_count"  // 腾讯云类型的Ingress资源数量

	// 产品使用指标
	PROTOCOL_HTTP       = "protocol_http"       // 使用Http协议暴露服务
	PROTOCOL_HTTPS      = "protocol_https"      // 使用Https协议暴露服务
	PROTOCOL_HTTP_HTTPS = "protocol_http_https" // 同时使用Https协议暴露服务
	SNI                 = "sni"                 // 使用多域名暴露服务
	LISTENER_TOTAL      = "listener_total"      // 监听器总数
	RULE_TOTAL          = "rule_total"          // 转发规则总数
	REWRITE_RULE_TOTAL  = "rewrite_rule_total"  // 重定向规则总数
	BACKEND_TOTAL       = "backend_total"       // 后端绑定总数
	RULE_1_3            = "rule_1_3"            // 转发规则数量范围 [1-3]
	RULE_4_10           = "rule_4_10"           // 转发规则数量范围 [4-10]
	RULE_11_30          = "rule_11_30"          // 转发规则数量范围 [11-30]
	RULE_30             = "rule_30"             // 转发规则数量范围 [30+]

	// 产品功能指标
	RULE_MIX                = "rule_mix" // 混合协议 kubernetes.io/ingress.rule-mix
	FLAG_MCI                = "flag_mci"
	FLAG_WORKERS            = "flag_workers_count"
	EXIST_LB                = "exist_lb"                // 使用已有负载均衡 kubernetes.io/ingress.existLbId
	DIRECT_ACCESS           = "direct_access"           // 直连数量 ingress.cloud.tencent.com/direct-access
	GRACE_SHUTDOWN          = "grace_shutdown"          // 优雅停机 ingress.cloud.tencent.com/enable-grace-shutdown
	GRACE_SHUTDOWN_TKEX     = "grace_shutdown_tkex"     // 优雅踢除 ingress.cloud.tencent.com/enable-grace-shutdown-tkex
	TKE_SERVICE_CONFIG      = "tke_service_config"      // 外挂配置 ingress.cloud.tencent.com/tke-service-config
	TKE_SERVICE_CONFIG_AUTO = "tke_service_config_auto" // 自动外挂配置 ingress.cloud.tencent.com/tke-service-config-auto
	LOADBALANCE_NAT_IPV6    = "loadbalance_nat_ipv6"    // NAT-IPv6 ingress.cloud.tencent.com/loadbalance-nat-ipv6
	LOADBALANCE_IPV6        = "loadbalance_ipv6"        // IPv6 ingress.cloud.tencent.com/loadbalance-ipv6
	LOADBALANCE_IPV4        = "loadbalance_ipv4"        // IPv6 ingress.cloud.tencent.com/loadbalance-ipv4
	REWRITE_SUPPORT         = "rewrite_support"         // 手动重定向 ingress.cloud.tencent.com/rewrite-support
	AUTO_REWRITE            = "auto_rewrite"            // 自动重定向 ingress.cloud.tencent.com/auto-rewrite
	LB_RS_WEIGHT            = "lb_rs_weight"            // 自定义后端权重 ingress.cloud.tencent.com/lb-rs-weight
	CROSS_VPC_ID            = "cross_vpc_id"            // 跨VPC绑定 ingress.cloud.tencent.com/cross-vpc-id
	CROSS_TYPE_CCN          = "cross_type_ccn"          // 跨地域绑定方案 ingress.cloud.tencent.com/cross-type
	CROSS_TYPE_CROSSTARGET  = "cross_type_crosstarget"  // 跨地域绑定方案 ingress.cloud.tencent.com/cross-type
	CROSS_TYPE_MANAGERONLY  = "cross_type_manageronly"  // 跨地域绑定方案 ingress.cloud.tencent.com/cross-type
	CROSS_TYPE_PVGW         = "cross_type_pvgw"         // 跨地域绑定方案 ingress.cloud.tencent.com/cross-type
	CROSS_TYPE_PVGW_PRO     = "cross_type_pvgw_pro"     // 跨地域绑定方案 ingress.cloud.tencent.com/cross-type
	HYBRID_TYPE_PVGW        = "hybrid_type_pvgw"        // 混合云绑定方案 ingress.cloud.tencent.com/hybrid-type
	HYBRID_TYPE_CCN         = "hybrid_type_ccn"         // 混合云绑定方案 ingress.cloud.tencent.com/hybrid-type
	MODIFICATION_PROTECTION = "modification_protection" // 配置保护 ingress.cloud.tencent.com/modification-protection
	SECURITY_GROUPS         = "security_groups"         // 安全组配置 service.cloud.tencent.com/security-groups
	PASS_TO_TARGET          = "pass_to_target"          // 默认放通配置 service.cloud.tencent.com/pass-to-target
)

func UploadOperationalMetrtc() {
	ticker := time.NewTicker(SYNC_OPERATIONAL_METRTC_FREQUENCY)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			// 维护运营数据
			uploadOperationalMetrtc()
		}
	}
}

func uploadOperationalMetrtc() {
	var ingresses []types.Ingress
	var err error
	var serviceMap map[string]*v1.Service
	var directBackendCountMap, nodePortBackendCountMap map[string]int

	if config.Global.EnableMultiClusterIngress {
		ingresses, err = tencent.IngressLister(types.MultiClusterIngress, v1.NamespaceAll, labels.Everything())
		if err != nil {
			klog.Errorf("get multiclusteringress list failed: %s", err.Error())
			return
		}
	} else {
		serviceMap, directBackendCountMap, nodePortBackendCountMap = getServiceBackendCount()
		ingresses, err = tencent.IngressLister(types.CoreIngress, v1.NamespaceAll, labels.Everything())
		if err != nil {
			klog.Errorf("get ingress list failed: %s", err.Error())
			return
		}
	}

	metrtcMap := make(map[string]int)
	metrtcMap[INTERNAL_LOADBALANCER] = 0
	metrtcMap[INTERNET_LOADBALANCER] = 0
	metrtcMap[INGRESS_COUNT] = 0
	metrtcMap[QCLOUD_INGRESS_COUNT] = 0

	metrtcMap[PROTOCOL_HTTP] = 0
	metrtcMap[PROTOCOL_HTTPS] = 0
	metrtcMap[PROTOCOL_HTTP_HTTPS] = 0
	metrtcMap[SNI] = 0
	metrtcMap[LISTENER_TOTAL] = 0
	metrtcMap[RULE_TOTAL] = 0
	metrtcMap[REWRITE_RULE_TOTAL] = 0
	metrtcMap[BACKEND_TOTAL] = 0
	metrtcMap[RULE_1_3] = 0
	metrtcMap[RULE_4_10] = 0
	metrtcMap[RULE_11_30] = 0
	metrtcMap[RULE_30] = 0

	if config.Global.EnableMultiClusterIngress {
		metrtcMap[FLAG_MCI] = 1
	} else {
		metrtcMap[FLAG_MCI] = 0
	}

	metrtcMap[FLAG_WORKERS] = config.Global.Workers

	metrtcMap[EXIST_LB] = 0
	metrtcMap[DIRECT_ACCESS] = 0
	metrtcMap[GRACE_SHUTDOWN] = 0
	metrtcMap[GRACE_SHUTDOWN_TKEX] = 0
	metrtcMap[TKE_SERVICE_CONFIG] = 0
	metrtcMap[TKE_SERVICE_CONFIG_AUTO] = 0
	metrtcMap[LOADBALANCE_NAT_IPV6] = 0
	metrtcMap[LOADBALANCE_IPV6] = 0
	metrtcMap[LOADBALANCE_IPV4] = 0
	metrtcMap[REWRITE_SUPPORT] = 0
	metrtcMap[AUTO_REWRITE] = 0
	metrtcMap[LB_RS_WEIGHT] = 0
	metrtcMap[RULE_MIX] = 0
	metrtcMap[CROSS_TYPE_CCN] = 0
	metrtcMap[CROSS_TYPE_CROSSTARGET] = 0
	metrtcMap[CROSS_TYPE_MANAGERONLY] = 0
	metrtcMap[CROSS_TYPE_PVGW] = 0
	metrtcMap[CROSS_TYPE_PVGW_PRO] = 0
	metrtcMap[CROSS_VPC_ID] = 0
	metrtcMap[HYBRID_TYPE_PVGW] = 0
	metrtcMap[MODIFICATION_PROTECTION] = 0
	metrtcMap[SECURITY_GROUPS] = 0
	metrtcMap[PASS_TO_TARGET] = 0

	IngressMap := make(map[string]types.Ingress)
	for index, ingress := range ingresses {
		IngressMap[utils.IngressName(ingress)] = ingresses[index]
		metrtcMap[INGRESS_COUNT] = metrtcMap[INGRESS_COUNT] + 1
		if utils.IsQCLOUDIngress(ingress) {
			metrtcMap[QCLOUD_INGRESS_COUNT] = metrtcMap[QCLOUD_INGRESS_COUNT] + 1
		} else {
			continue
		}

		loadBalancerStatus := ingress.StatusLoadBalancer()
		if loadBalancerStatus.Ingress != nil && len(loadBalancerStatus.Ingress) != 0 {
			if isInnerIP(loadBalancerStatus.Ingress[0].IP) {
				metrtcMap[INTERNAL_LOADBALANCER] = metrtcMap[INTERNAL_LOADBALANCER] + 1
			} else {
				metrtcMap[INTERNET_LOADBALANCER] = metrtcMap[INTERNET_LOADBALANCER] + 1
			}
		}
		if _, exist := utils.GetExistLbId(ingress); exist {
			metrtcMap[EXIST_LB] = metrtcMap[EXIST_LB] + 1
		}
		if isDirectAccess, _ := utils.IsDirectAccessIngress(ingress); isDirectAccess {
			metrtcMap[DIRECT_ACCESS] = metrtcMap[DIRECT_ACCESS] + 1
		}
		if _, exist := ingress.Annotations()[utils.TkeServiceConfigAnnontation]; exist {
			metrtcMap[TKE_SERVICE_CONFIG] = metrtcMap[TKE_SERVICE_CONFIG] + 1
		}
		if exist, _ := utils.IsTkeServiceConfigAuto(ingress); exist {
			metrtcMap[TKE_SERVICE_CONFIG_AUTO] = metrtcMap[TKE_SERVICE_CONFIG_AUTO] + 1
		}
		ipv4 := false
		ipv6 := false
		for _, ingress := range loadBalancerStatus.Ingress {
			if strings.Contains(ingress.IP, ".") {
				ipv4 = true
			} else if strings.Contains(ingress.IP, ":") {
				ipv6 = true
			}
		}
		if ipv4 && ipv6 {
			metrtcMap[LOADBALANCE_NAT_IPV6] = metrtcMap[LOADBALANCE_NAT_IPV6] + 1
		} else if ipv4 {
			metrtcMap[LOADBALANCE_IPV4] = metrtcMap[LOADBALANCE_IPV4] + 1
		} else if ipv6 {
			metrtcMap[LOADBALANCE_IPV6] = metrtcMap[LOADBALANCE_IPV6] + 1
		}
		if isRewriteSupport, _ := utils.IsRewriteSupport(ingress); isRewriteSupport {
			metrtcMap[REWRITE_SUPPORT] = metrtcMap[REWRITE_SUPPORT] + 1
		}
		if isAutoRewrite, _ := utils.IsAutoRewrite(ingress); isAutoRewrite {
			metrtcMap[AUTO_REWRITE] = metrtcMap[AUTO_REWRITE] + 1
		}
		if customizedWeight, _ := utils.IsEnableCustomizedWeight(ingress); customizedWeight != nil {
			metrtcMap[LB_RS_WEIGHT] = metrtcMap[LB_RS_WEIGHT] + 1
		}
		if crossRegionID, exist := utils.GetCrossRegionId(ingress); exist && crossRegionID != config.Global.Region {
			if crossType := utils.GetCrossType(ingress); crossType != "err" {
				switch crossType {
				case utils.CrossType2_0:
					metrtcMap[CROSS_TYPE_CCN] = metrtcMap[CROSS_TYPE_CCN] + 1
				case utils.CrossType1_0:
					metrtcMap[CROSS_TYPE_CROSSTARGET] = metrtcMap[CROSS_TYPE_CROSSTARGET] + 1
				case utils.CrossType1_1:
					metrtcMap[CROSS_TYPE_PVGW] = metrtcMap[CROSS_TYPE_PVGW] + 1
				case utils.CrossType1_2:
					metrtcMap[CROSS_TYPE_PVGW_PRO] = metrtcMap[CROSS_TYPE_PVGW_PRO] + 1
				case utils.CrossType0_0:
					metrtcMap[CROSS_TYPE_MANAGERONLY] = metrtcMap[CROSS_TYPE_MANAGERONLY] + 1
				}
			}
		}
		if hybridType := utils.GetHybridType(ingress); hybridType != "err" {
			switch hybridType {
			case utils.HybridType_PVGW:
				metrtcMap[HYBRID_TYPE_PVGW] = metrtcMap[HYBRID_TYPE_PVGW] + 1
			case utils.HybridType_CCN:
				metrtcMap[HYBRID_TYPE_CCN] = metrtcMap[HYBRID_TYPE_CCN] + 1
			}
		}
		if isModificationProtection, _ := utils.IsModificationProtection(ingress); isModificationProtection {
			metrtcMap[MODIFICATION_PROTECTION] = metrtcMap[MODIFICATION_PROTECTION] + 1
		}
		if _, exist := utils.HasServiceSecurityGroups(ingress); exist {
			metrtcMap[SECURITY_GROUPS] = metrtcMap[SECURITY_GROUPS] + 1
		}
		if passToTarget, _ := utils.IsServicePassToTarget(ingress); passToTarget {
			metrtcMap[PASS_TO_TARGET] = metrtcMap[PASS_TO_TARGET] + 1
		}

		syncContext := tencent.NewSyncContext(ingress)
		syncContext.LoadBalancerContext = &tencent.LoadBalancerContext{
			DefaultDomain: common.StringPtr("default.com"),
		}
		syncContext.IngressContext = &tencent.IngressContext{
			IngressListeners: make(map[string]*tencent.IngressListener),
		}

		rulesCount := 0
		rewriteRulesCount := 0
		if isRuleMixed, _ := utils.IsRuleMixed(ingress); isRuleMixed {
			metrtcMap[RULE_MIX] = metrtcMap[RULE_MIX] + 1
			syncContext.IngressContext.IsRewriteSupport = false
			if exist := utils.HasAutoRewrite(ingress); exist {
				syncContext.IngressContext.IsRewriteSupport = true
			}
			if rewriteSupport, _ := utils.IsRewriteSupport(ingress); rewriteSupport {
				syncContext.IngressContext.IsRewriteSupport = true
			}
			if err := buildIngressListeners(syncContext, ingress); err == nil {
				listeners := syncContext.IngressContext.IngressListeners
				for _, port := range []string{"80_HTTP", "443_HTTPS"} {
					if listener, exist := listeners[port]; exist {
						rulesCount = rulesCount + len(listener.IngressRules)
						rewriteRulesCount = rewriteRulesCount + len(listener.IngressRewriteRules)
					}
				}
				if listeners["80_HTTP"] != nil && len(listeners["80_HTTP"].IngressRules) != 0 && listeners["443_HTTPS"] != nil && len(listeners["443_HTTPS"].IngressRules) != 0 {
					metrtcMap[PROTOCOL_HTTP_HTTPS] = metrtcMap[PROTOCOL_HTTP_HTTPS] + 1
				}
				if listeners["80_HTTP"] != nil && len(listeners["80_HTTP"].IngressRules) != 0 {
					metrtcMap[PROTOCOL_HTTP] = metrtcMap[PROTOCOL_HTTP] + 1
					metrtcMap[LISTENER_TOTAL] = metrtcMap[LISTENER_TOTAL] + 1
				} else if listeners["443_HTTPS"] != nil && len(listeners["443_HTTPS"].IngressRules) != 0 {
					metrtcMap[PROTOCOL_HTTPS] = metrtcMap[PROTOCOL_HTTPS] + 1
					metrtcMap[LISTENER_TOTAL] = metrtcMap[LISTENER_TOTAL] + 1
				}

				metrtcMap[RULE_TOTAL] = metrtcMap[RULE_TOTAL] + rulesCount
				metrtcMap[REWRITE_RULE_TOTAL] = metrtcMap[REWRITE_RULE_TOTAL] + rewriteRulesCount
				if !config.Global.EnableMultiClusterIngress {
					for _, listener := range listeners {
						for _, ingressRule := range listener.IngressRules {
							if ingressRule == nil || ingressRule.Backend == nil {
								continue
							}
							serviceName := ingress.Namespace() + "/" + ingressRule.Backend.Name
							if service, exist := serviceMap[serviceName]; exist {
								directAccess := utils.IsDirectAccessIngressCascade(ingress, service)
								if directAccess {
									if count, exist := directBackendCountMap[serviceName]; exist {
										metrtcMap[BACKEND_TOTAL] = metrtcMap[BACKEND_TOTAL] + count
									}
								} else {
									if count, exist := nodePortBackendCountMap[serviceName]; exist {
										metrtcMap[BACKEND_TOTAL] = metrtcMap[BACKEND_TOTAL] + count
									}
								}
							}
						}
					}
				}
			}
		} else {
			metrtcMap[LISTENER_TOTAL] = metrtcMap[LISTENER_TOTAL] + 1
			listeners := syncContext.IngressContext.IngressListeners
			for _, port := range []string{"80_HTTP", "443_HTTPS"} {
				if listener, exist := listeners[port]; exist {
					rulesCount = rulesCount + len(listener.IngressRules)
					rewriteRulesCount = rewriteRulesCount + len(listener.IngressRewriteRules)
				}
			}
			metrtcMap[RULE_TOTAL] = metrtcMap[RULE_TOTAL] + rulesCount
			metrtcMap[REWRITE_RULE_TOTAL] = metrtcMap[REWRITE_RULE_TOTAL] + rewriteRulesCount
			if len(ingress.TLS()) != 0 {
				metrtcMap[PROTOCOL_HTTPS] = metrtcMap[PROTOCOL_HTTPS] + 1
				if err := buildIngressListeners(syncContext, ingress); err == nil {
					listeners := syncContext.IngressContext.IngressListeners
					if !config.Global.EnableMultiClusterIngress {
						for _, listener := range listeners {
							for _, ingressRule := range listener.IngressRules {
								if ingressRule == nil || ingressRule.Backend == nil {
									continue
								}
								serviceName := ingress.Namespace() + "/" + ingressRule.Backend.Name
								if service, exist := serviceMap[serviceName]; exist {
									directAccess := utils.IsDirectAccessIngressCascade(ingress, service)
									if directAccess {
										if count, exist := directBackendCountMap[serviceName]; exist {
											metrtcMap[BACKEND_TOTAL] = metrtcMap[BACKEND_TOTAL] + count
										}
									} else {
										if count, exist := nodePortBackendCountMap[serviceName]; exist {
											metrtcMap[BACKEND_TOTAL] = metrtcMap[BACKEND_TOTAL] + count
										}
									}
								}
							}
						}
					}
				}
			} else {
				metrtcMap[PROTOCOL_HTTP] = metrtcMap[PROTOCOL_HTTP] + 1
				if err := buildIngressListeners(syncContext, ingress); err == nil {
					listeners := syncContext.IngressContext.IngressListeners
					if !config.Global.EnableMultiClusterIngress {
						for _, listener := range listeners {
							for _, ingressRule := range listener.IngressRules {
								if ingressRule == nil || ingressRule.Backend == nil {
									continue
								}
								serviceName := ingress.Namespace() + "/" + ingressRule.Backend.Name
								if service, exist := serviceMap[serviceName]; exist {
									directAccess := utils.IsDirectAccessIngressCascade(ingress, service)
									if directAccess {
										if count, exist := directBackendCountMap[serviceName]; exist {
											metrtcMap[BACKEND_TOTAL] = metrtcMap[BACKEND_TOTAL] + count
										}
									} else {
										if count, exist := nodePortBackendCountMap[serviceName]; exist {
											metrtcMap[BACKEND_TOTAL] = metrtcMap[BACKEND_TOTAL] + count
										}
									}
								}
							}
						}
					}
				}
			}
		}

		for _, tls := range ingress.TLS() {
			if len(tls.Hosts) != 0 {
				metrtcMap[SNI] = metrtcMap[SNI] + 1
				break
			}
		}

		if rulesCount > 30 {
			metrtcMap[RULE_30] = metrtcMap[RULE_30] + 1
		} else if rulesCount >= 11 {
			metrtcMap[RULE_11_30] = metrtcMap[RULE_11_30] + 1
		} else if rulesCount >= 4 {
			metrtcMap[RULE_4_10] = metrtcMap[RULE_4_10] + 1
		} else {
			metrtcMap[RULE_1_3] = metrtcMap[RULE_1_3] + 1
		}
	}

	if config.Global.EnableMultiClusterIngress {
		metrics.Instance.SetOperationalCount(string(types.MultiClusterIngress), metrtcMap)
	} else {
		metrics.Instance.SetOperationalCount(string(types.CoreIngress), metrtcMap)
	}
}

func isInnerIP(ip string) bool {
	if isBelong(ip, "*******/8") {
		return true
	}
	if isBelong(ip, "********/8") {
		return true
	}
	if isBelong(ip, "10.0.0.0/8") {
		return true
	}
	if isBelong(ip, "**********/12") {
		return true
	}
	if isBelong(ip, "***********/16") {
		return true
	}
	return false
}

func isBelong(ip, cidr string) bool {
	cidrArr := strings.Split(cidr, `/`)
	if len(cidrArr) < 2 {
		return false
	}
	ipInt := ip2Int(ip)
	cidrInt := ip2Int(cidrArr[0])

	mask := string2Int(cidrArr[1])
	if ipInt >= cidrInt && ipInt <= (cidrInt+(int64)(math.Pow(2, float64(32-mask)))-1) {
		return true
	}
	return false
}

func ip2Int(ip string) int64 {
	if len(ip) == 0 {
		return 0
	}
	bits := strings.Split(ip, ".")
	if len(bits) < 4 {
		return 0
	}
	b0 := string2Int(bits[0])
	b1 := string2Int(bits[1])
	b2 := string2Int(bits[2])
	b3 := string2Int(bits[3])

	var sum int64
	sum += int64(b0) << 24
	sum += int64(b1) << 16
	sum += int64(b2) << 8
	sum += int64(b3)

	return sum
}

func string2Int(in string) (out int) {
	out, _ = strconv.Atoi(in)
	return
}

func getServiceBackendCount() (map[string]*v1.Service, map[string]int, map[string]int) {
	defaultBackendCount := 0 // 默认情况下NodePort后端的数量，注意后端上线限制。
	nodes, err := getNode()
	if err != nil {
		defaultBackendCount = len(nodes)
		if defaultBackendCount > config.Global.BackendQuotaInUse.UserQuota {
			defaultBackendCount = config.Global.BackendQuotaInUse.UserQuota
		}
	}

	serviceMap := make(map[string]*v1.Service)
	directBackendCountMap := make(map[string]int)
	nodePortBackendCountMap := make(map[string]int)
	services, err := cluster_service.Instance.ServiceLister().List(labels.Everything())
	if err != nil {
		return serviceMap, directBackendCountMap, nodePortBackendCountMap
	}
	for index, service := range services {
		serviceMap[utils.ServiceName(service)] = services[index]
		// 直连
		if service.Spec.Selector != nil && len(service.Spec.Selector) != 0 {
			pods, err := cluster_service.Instance.PodLister().Pods(service.Namespace).List(labels.SelectorFromSet(service.Spec.Selector))
			if err == nil {
				directBackendCountMap[utils.ServiceName(service)] = len(pods)
			}
		}

		// NodePort
		if service.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal { // Local模式下只绑定工作负载所在节点
			if service.Spec.Selector != nil && len(service.Spec.Selector) != 0 {
				pods, err := cluster_service.Instance.PodLister().Pods(service.Namespace).List(labels.SelectorFromSet(service.Spec.Selector))
				if err == nil {
					nodes := make(map[string]bool)
					for _, pod := range pods {
						if pod.Status.HostIP != "" {
							nodes[pod.Status.HostIP] = true
						}
					}
					nodePortBackendCountMap[utils.ServiceName(service)] = len(nodes)
				}
			}
		} else { // 正常情况下，可能会有后端节点选择的场景
			backendCount := defaultBackendCount
			if availList, exist := utils.GetBackendsListLabel(service); exist {
				if labelList, err := labels.Parse(availList); err == nil {
					backendCount = 0
					for _, node := range nodes {
						labelSet := labels.Set(node.ObjectMeta.Labels)
						if labelList.Matches(labelSet) {
							backendCount = backendCount + 1
						}
					}
				}
				lbId := service.GetObjectMeta().GetAnnotations()["service.kubernetes.io/loadbalance-id"]
				quotaInUse := config.Global.BackendQuotaInUse.GetQuotaInUse(lbId)
				if backendCount > quotaInUse {
					backendCount = quotaInUse
				}
			}
			nodePortBackendCountMap[utils.ServiceName(service)] = len(nodes)
		}
	}
	return serviceMap, directBackendCountMap, nodePortBackendCountMap
}

func getNode() ([]*v1.Node, error) {
	predicate := func(node *v1.Node) bool {
		// 容忍节点抖动
		if _, exist := nodestatus.NodeStatusMapInstance.Get(node.Name); exist {
			return true
		}

		if node.Labels != nil {
			if value, exist := node.Labels["node.kubernetes.io/exclude-from-external-load-balancers"]; exist && value == "true" {
				return false
			}
		}

		// If we have no info, don't accept
		if len(node.Status.Conditions) == 0 {
			return false
		}

		// If we have node, but no InternalIP
		flag := false
		for _, address := range node.Status.Addresses {
			if address.Type == "InternalIP" && address.Address != "" {
				flag = true
				break
			}
		}
		if flag == false {
			return false
		}

		if utils.IsIDCNode(node) {
			return false
		}

		if types.NewNode(node).HasWaitFinalizer() || node.DeletionTimestamp != nil {
			return false
		}
		for _, cond := range node.Status.Conditions {
			// We consider the node for load balancing only when its NodeReady condition status
			// is ConditionTrue
			if cond.Type == v1.NodeReady && cond.Status != v1.ConditionTrue {
				klog.V(4).Infof("Ignoring node %v with %v condition status %v", node.Name, cond.Type, cond.Status)
				return false
			}
		}
		return true
	}

	nodes, err := cluster_service.Instance.NodeLister().List(labels.Everything())
	if err != nil {
		return nil, err
	}

	var filtered []*v1.Node
	for i := range nodes {
		if predicate(nodes[i]) {
			filtered = append(filtered, nodes[i])
		}
	}

	return filtered, nil
}
