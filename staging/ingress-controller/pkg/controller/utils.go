/*
Copyright 2015 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	v1 "k8s.io/api/core/v1"
	discovery "k8s.io/api/discovery/v1"
	"k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/ingress/ingress_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/types"
	tkeserviceapi "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/plugins/tencent"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

const (
	// allowHTTPKey tells the Ingress controller to allow/block HTTP access.
	// If either unset or set to true, the controller will create a
	// forwarding-rule for port 80, and any additional rules based on the TLS
	// section of the Ingress. If set to false, the controller will only create
	// rules for port 443 based on the TLS section.
	allowHTTPKey = "kubernetes.io/ingress.allow-http"

	// ingressClassKey picks a specific "class" for the Ingress. The controller
	// only processes Ingresses with this annotation either unset, or set
	// to either gceIngessClass or the empty string.
	IngressClassKey    = "kubernetes.io/ingress.class"
	QcloudIngressClass = "qcloud"
	IngressSubnetId    = "kubernetes.io/ingress.subnetId"

	InternetChargeType      = "kubernetes.io/ingress.internetChargeType"
	InternetMaxBandwidthOut = "kubernetes.io/ingress.internetMaxBandwidthOut"
	AnnoExtensiveParameters = "kubernetes.io/ingress.extensiveParameters"

	invalidPort = 0
	defaultPath = "/"

	SvcOnlyBindNodeWithPodAnnontation = "service.kubernetes.io/svc-only-bind-node-with-pod"
	SvcOnlyBindNodeWithPodTrue        = "true"
)

var (
	keyFunc = cache.DeletionHandlingMetaNamespaceKeyFunc
)

// ingAnnotations represents Ingress annotations.
type ingAnnotations map[string]string

func (ing ingAnnotations) certId() string {
	if certId, ok := ing[services.QcloudCertID]; ok {
		return certId
	}
	return ""
}

type RuleMap map[string]*utils.Rule

func httpRules(ingress types.Ingress, defaultHost *string) (RuleMap, RuleMap, error) {
	rules, err := utils.GetHttpRulesList(ingress)
	if err != nil {
		return nil, nil, err
	}

	return toRuleMap(ingress, rules, defaultHost)
}

func httpsRules(ingress types.Ingress, defaultHost *string) (RuleMap, RuleMap, error) {
	rules, err := utils.GetHttpsRulesList(ingress)
	if err != nil {
		return nil, nil, err
	}

	return toRuleMap(ingress, rules, defaultHost)
}

func toRuleList(rule string) ([]*utils.Rule, error) {
	rules := make([]*utils.Rule, 0)
	if err := json.Unmarshal([]byte(rule), &rules); err != nil {
		return nil, err
	}
	return rules, nil
}

func toRuleMap(ingress types.Ingress, rules []*utils.Rule, defaultDomain *string) (RuleMap, RuleMap, error) {
	ruleMap := make(RuleMap)
	ruleReriteMap := make(RuleMap)
	for _, rule := range rules {
		if rule.Host == "" {
			if defaultDomain == nil { // 无IPv4 IP作为默认域名，用户又未设置域名的情况
				continue
			}
			rule.Host = *defaultDomain
		}
		if rule.Path == "" {
			rule.Path = defaultPath
		}
		if (rule.Backend != nil && rule.Rewrite != nil) || (rule.Backend == nil && rule.Rewrite == nil) {
			return nil, nil, types.NewError(errcode.RuleInvalidError, "", utils.IngressName(ingress))
		}
		if rule.Backend != nil {
			if _, exist := ruleMap[ruleToString(rule)]; exist {
				return nil, nil, types.NewError(errcode.ConflictRule, "", utils.IngressName(ingress))
			}
			ruleMap[ruleToString(rule)] = rule
		} else if rule.Rewrite != nil {
			if _, exist := ruleReriteMap[ruleTo(rule.Host, rule.Path)]; exist {
				return nil, nil, types.NewError(errcode.ConflictRule, "", utils.IngressName(ingress))
			}
			ruleReriteMap[ruleTo(rule.Host, rule.Path)] = rule
		}
	}
	return ruleMap, ruleReriteMap, nil
}

func ToL7ListenerConfigMap(tkeServiceConfig *tkeserviceapi.TkeServiceConfig) map[string]*tkeserviceapi.L7ListenerConfig {
	configsKeyMap := make(map[string]*tkeserviceapi.L7ListenerConfig)
	for index, l7Listener := range tkeServiceConfig.Spec.LoadBalancer.L7Listeners {
		key := ruleToListener(strings.ToUpper(l7Listener.Protocol), int64(l7Listener.Port))
		configsKeyMap[key] = tkeServiceConfig.Spec.LoadBalancer.L7Listeners[index]
	}
	return configsKeyMap
}

func ToL7RuleConfigMap(tkeServiceConfig *tkeserviceapi.TkeServiceConfig, defaultDomain *string) map[string]*tkeserviceapi.L7RuleConfig {
	configsKeyMap := make(map[string]*tkeserviceapi.L7RuleConfig)
	for _, l7Listener := range tkeServiceConfig.Spec.LoadBalancer.L7Listeners {
		for _, domain := range l7Listener.Domains {
			for index, rule := range domain.Rules {
				ruleDomain := ""
				if defaultDomain != nil {
					ruleDomain = *defaultDomain
				}
				if domain.Domain != "" {
					ruleDomain = domain.Domain
				}
				key := ruleToPath(strings.ToUpper(l7Listener.Protocol), int64(l7Listener.Port), ruleDomain, rule.Url)
				configsKeyMap[key] = domain.Rules[index]
			}
		}
	}
	return configsKeyMap
}

func ToRuleOutputMap(listeners map[string]*clb.Listener) map[string]*clb.RuleOutput {
	ruleOutputMap := make(map[string]*clb.RuleOutput)
	for _, listener := range listeners {
		for index, rule := range listener.Rules {
			ruleOutputMap[ruleToPath(*listener.Protocol, *listener.Port, *rule.Domain, *rule.Url)] = listener.Rules[index]
		}
	}
	return ruleOutputMap
}

func ruleToListener(protocol string, port int64) string {
	return fmt.Sprintf("%s_%d", protocol, port)
}

func ruleToPath(protocol string, port int64, domain string, path string) string {
	return fmt.Sprintf("%s_%d_%s_%s", protocol, port, domain, path)
}

func ruleToPath2(port int64, domain string, path string) string {
	return fmt.Sprintf("%d_%s_%s", port, domain, path)
}

func location(listener string, location string) string {
	return fmt.Sprintf("%s_%s", listener, location)
}

func ruleTo(host string, path string) string {
	return fmt.Sprintf("%s_%s", host, path)
}

func ruleToString(r *utils.Rule) string { // kateway rule 的唯一标识
	servicePort := r.Backend.ServicePort()
	return fmt.Sprintf("%s_%s_%s_%s", r.Host, r.Path, r.Backend.ServiceName(), servicePort.String())
}

func rewritePath(listenerId string, locationId string, targetListenerId string, targetLocationId string) string {
	return fmt.Sprintf("%s_%s_%s_%s", listenerId, locationId, targetListenerId, targetLocationId)
}

func ruleBackendToString(locationID string, backend *utils.Backend) string {
	servicePort := &backend.Port
	return fmt.Sprintf("%s_%s_%s", locationID, backend.ServiceName(), servicePort.String())
}

func backendToString(backend *utils.Backend) string {
	servicePort := backend.Port
	return fmt.Sprintf("%s_%s", backend.Name, servicePort.String())
}

// errorNodePortNotFound is an implementation of error.
type errorNodePortNotFound struct {
	backend v1beta1.IngressBackend
	origErr error
}

func (e errorNodePortNotFound) Error() string {
	return fmt.Sprintf("Could not find nodeport for backend %+v: %v",
		e.backend, e.origErr)
}

func IgnoreRule(rule *utils.Rule) bool {
	return rule.Host == "localhost" && rule.RealPath == "/" && rule.Backend.ServiceName() == IgnoredServiceName && rule.Backend.ServicePort().IntVal == 65535
}

func GetEndpointSlices(ctx context.Context, service *v1.Service) []*discovery.EndpointSlice {
	var endpointSlices []*discovery.EndpointSlice = nil
	kubernetesVersionCheck, err := utils.KubernetesVersionCheck(ctx, cluster_service.Instance.KubeClient(), "v1.22", false)
	if err != nil {
		klog.Errorf(err.Error())
	}
	if kubernetesVersionCheck {
		selector, _ := labels.Parse(fmt.Sprintf("kubernetes.io/service-name=%s", service.Name))
		endpointSlices, err = cluster_service.Instance.EndpointSliceLister().EndpointSlices(service.Namespace).List(selector)
		if err != nil {
			klog.Errorf(err.Error())
		}
	}
	return endpointSlices
}

func UpdateStatus(ctx context.Context, ingress types.Ingress, errors []error) (types.Ingress, error) {
	if config.Global.DryRun {
		return ingress, nil
	}

	toMessage := ""
	toReason := errcode.Success.ReasonDetail
	if len(errors) != 0 {
		toMessage = utils.Errors(errors)
		for _, err := range errors {
			if codeError, ok := lo.ErrorsAs[*types.Error](err); ok {
				toReason = codeError.ErrorCode.ReasonDetail
				break
			}
		}
	}

	isChanged := false
	originReason, originMessage := GetIngressConditionReason(ctx, ingress)
	if originReason == errcode.SuccessReason && toReason == errcode.SuccessReason {
		if originMessage != "" {
			klog.Infof("clean diry success reason %s/%s. \n", ingress.Namespace(), ingress.Name())
			isChanged = true
			toMessage = ""
		} else {
			return ingress, nil
		}
	}

	originAnalyzeErrors := utils.AnalyzeErrors(originMessage)
	toAnalyzeErrors := utils.AnalyzeErrors(toMessage)
	if originReason == "" { // 没有Condition字段, 必须更新
		isChanged = true
	} else {
		if len(originAnalyzeErrors) != len(toAnalyzeErrors) {
			isChanged = true
		} else {
			for code, _ := range originAnalyzeErrors {
				if _, exist := toAnalyzeErrors[code]; !exist {
					isChanged = true
					break
				}
			}
		}
	}
	if !isChanged {
		return ingress, nil
	}

	status := metav1.ConditionTrue
	if toReason != errcode.SuccessReason {
		status = metav1.ConditionFalse
	}

	var err error
	var updatedIngress types.Ingress
	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		status := []metav1.Condition{
			{
				Type:               utils.IngressConditionType,
				Reason:             toReason,
				Status:             status,
				LastTransitionTime: metav1.NewTime(time.Now()),
				Message:            toMessage,
			},
		}
		if ingress.Type() == types.CoreIngress {
			update := map[string]string{
				utils.IngressCondition: jsonWrapper(status),
			}
			updatedIngress, err = ingress.UpdateAnnotation(ctx, update, nil)
			if err != nil {
				klog.Errorf("UpdateAnnotation Error %v", err)
				continue
			}
			break
		} else {
			if err := ingress_wrapper.SetLoadBalancerConditions(ctx, ingress.Namespace(), ingress.Name(), status); err != nil {
				klog.Errorf("SetLoadBalancerConditions Error %v", err)
				continue
			}
		}

	}
	klog.Infof("Update ingress status to end %v: %s", utils.IngressName(ingress), toReason)
	return updatedIngress, err
}

func GetIngressConditionReason(ctx context.Context, ingress types.Ingress) (string, string) {
	annotations := ingress.Annotations()
	if ingress.Type() == types.CoreIngress {
		if annotation, exist := annotations[utils.IngressCondition]; exist {
			var conditions []metav1.Condition
			if err := json.Unmarshal([]byte(annotation), &conditions); err != nil {
				klog.Errorf("Annotation format error %v", err) // 理论上不可能出现
				return errcode.UnexpectedErrorReason, err.Error()
			}
			for _, condition := range conditions {
				if condition.Type == utils.IngressConditionType {
					return condition.Reason, condition.Message
				}
			}
		}
	} else {
		conditions, err := ingress_wrapper.GetLoadBalancerConditions(ctx, ingress.Namespace(), ingress.Name())
		if err != nil {
			klog.Errorf("GetLoadBalancerConditions error %v", err) // 理论上不可能出现
			return errcode.UnexpectedErrorReason, err.Error()
		}
		for _, condition := range conditions {
			if condition.Type == utils.IngressConditionType {
				return condition.Reason, condition.Message
			}
		}
	}

	return "", ""
}

func jsonWrapper(obj interface{}) string {
	if jsonStr, jsonErr := json.Marshal(obj); jsonErr == nil {
		return string(jsonStr)
	}
	return "json_format_error"
}

func wildcardDomain(domain string) bool {
	return strings.Contains(domain, "*")
}

func convertPathType(pathTypes []types.CLBPathType) []utils.TkePathType {
	if pathTypes == nil {
		return nil
	}
	return lo.Map(pathTypes, func(p types.CLBPathType, _ int) utils.TkePathType {
		return utils.TkePathType(p)
	})
}

func convertErr(err error) *types.Error {
	table := []struct {
		target error
		code   types.ErrorCode
	}{
		{
			services.ErrHostTLSConflict,
			errcode.SecretSpecifyConflictError,
		},
		{
			services.ErrDefaultHostTLSConflict,
			errcode.SecretConflictError,
		},
		{
			services.ErrEmptyTLSSecretName,
			errcode.SecretEmptyError,
		},
		{
			services.ErrTLSSecretNotFound,
			errcode.SecretNotFoundError,
		},
		{
			services.ErrInvalidTLSSecretContent,
			errcode.SecretContentError,
		},
		{
			services.ErrInvalidPortValue,
			errcode.InvalidListenPortError,
		},
		{
			services.ErrEmptyRuleHost,
			errcode.RuleHostEmptyError,
		},
		{
			services.ErrIllegalRuleHost,
			errcode.RuleHostFormatError,
		},
		{
			services.ErrPathLenLimitExceeded,
			errcode.PathLengthIllegal,
		},
		{
			services.ErrIllegalRegexPath,
			errcode.RegexPathIllegal,
		},
		{
			services.ErrIllegalPathFormat,
			errcode.PathIllegal,
		},
		{
			services.ErrInvalidAnnotationRules,
			errcode.RuleHttpAnnotationError,
		},
		{
			services.ErrAnnotationRuleConflict,
			errcode.ConflictRule,
		},
		{
			services.ErrRewriteRuleConflict,
			errcode.RewriteRuleConflictError,
		},
		{
			services.ErrListenPortConflict,
			errcode.ConflictListenPortError,
		},
		{
			services.ErrInvalidRewritePathType,
			errcode.RewriteInvalidPathType,
		},
		{
			services.ErrBackendServicePortNotFound,
			errcode.ServicePortNotFoundError,
		},
		{
			services.ErrBackendServiceNotFound,
			errcode.ServiceNotFoundError,
		},
		{
			services.ErrInvalidBackendServiceType,
			errcode.ServiceTypeInvalidError,
		},
		{
			services.ErrCLBPathTypeConflict,
			errcode.ConflictPathType,
		},
		{
			services.ErrCLBPathTypeUnsupported,
			errcode.UnsupportedPathType,
		},
		{
			services.ErrInvalidAutoTKEServiceConfigAnnotation,
			errcode.TkeServiceConfigAutoAnnontationError,
		},
		{
			services.ErrTKEServiceConfigNameConflict,
			errcode.TkeServiceConfigConflictError,
		},
		{
			services.ErrHostNoMatchingTLSSecret,
			errcode.SecretNotMatchError,
		},
		{
			services.ErrTKEServiceConfigNotFound,
			errcode.TkeServiceConfigNotFoundError,
		},
		{
			services.ErrAutoRewritePortRequired,
			errcode.AutoRewritePortRequiredError,
		},
		{
			services.ErrListenProtocolUnsupported,
			errcode.ListenProtocolUnsupportedError,
		},
	}
	for _, item := range table {
		if errors.Is(err, item.target) {
			return types.NewError(types.ErrorCode{
				Code:         item.code.Code,
				ReasonDetail: item.code.ReasonDetail,
				Message:      "%s",
				Retry:        item.code.Retry,
			}, "", err.Error())
		}
	}
	return types.NewError(errcode.UnexpectedError, err.Error())
}

func convertListener(lis types.Listener) tencent.IngressListener {
	l := tencent.IngressListener{
		Port:             int32(lis.Port),
		Protocol:         lis.Protocol,
		L7ListenerConfig: lis.ListenerConfig,
	}
	rules := []*utils.Rule{}
	rewrites := []*utils.Rule{}
	for _, r := range lis.FlattenRules() {
		rule := &utils.Rule{
			Domain:       r.Domain,
			Path:         r.Path,
			PathType:     convertPathType(r.CLBPathTypes),
			RealPath:     r.CLBPath,
			L7RuleConfig: r.RuleConfig,
		}
		if f := r.Action.Forward; f != nil {
			rule.TargetPort = f.TargetPort
			rule.Backend = &utils.Backend{
				Name: f.ServiceName,
				Port: f.ServicePort,
			}
		}
		rules = append(rules, rule)
		if rewrite := r.Action.Rewrite; rewrite != nil {
			rewrites = append(rewrites, &utils.Rule{
				Domain:   types.Domain{Host: r.Host},
				Path:     r.Path,
				RealPath: r.CLBPath,
				PathType: convertPathType(r.CLBPathTypes),
				Rewrite: &utils.Rewrite{
					Port:       int32(rewrite.Port),
					Host:       rewrite.Host,
					Path:       rewrite.Path,
					RealPath:   rewrite.CLBPath,
					AutoCreate: rewrite.Auto,
				},
			})
		}
	}
	sort.Stable(SortableListenerRule(rules))
	l.IngressRules = rules
	l.IngressRewriteRules = rewrites
	return l
}
