package controller

import (
	"context"

	"git.woa.com/kateway/pkg/domain/controllercm"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
)

const (
	NamespaceKubeSystem = "kube-system"
)

func ResourceInitFlag(kubeClient *kubernetes.Clientset) error {
	configMap, err := kubeClient.CoreV1().ConfigMaps(NamespaceKubeSystem).Get(context.Background(), controllercm.ConfigMapIngress, metav1.GetOptions{})
	if err != nil {
		if !apierrors.IsNotFound(err) {
			klog.Errorf("ResourceInitReuseFlag. Unexpected kubernetes client configMap get error. %s", err.Error())
			return err
		}

		if _, createErr := kubeClient.CoreV1().ConfigMaps(NamespaceKubeSystem).Create(context.Background(), &v1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Namespace: NamespaceKubeSystem,
				Name:      controllercm.ConfigMapIngress,
			},
		}, metav1.CreateOptions{}); createErr != nil {
			if !apierrors.IsAlreadyExists(createErr) {
				klog.Errorf("ResourceInitReuseFlag. Unexpected kubernetes client configMap create error. %s", err.Error())
				return err
			}
		}

		newConfigMap, getErr := kubeClient.CoreV1().ConfigMaps(NamespaceKubeSystem).Get(context.Background(), controllercm.ConfigMapIngress, metav1.GetOptions{})
		if getErr != nil {
			klog.Errorf("ResourceInitReuseFlag. Unexpected kubernetes client configMap try get error. %s", err.Error())
		}
		configMap = newConfigMap
	}

	// 三次重试修改Pod状态，失败重新
	for i := 0; i < 3; i++ {
		if err := updateResourceInitReuseFlag(configMap, kubeClient); err != nil {
			if configMap, err = kubeClient.CoreV1().ConfigMaps(NamespaceKubeSystem).Get(context.Background(), controllercm.ConfigMapIngress, metav1.GetOptions{}); err != nil {
				return err
			}
		} else { // Pod的状态修改成功处理.
			return nil
		}
	}

	klog.Errorf("ResourceInitReuseFlag. kubernetes client configMap try update three times error.")
	return nil
}

func updateResourceInitReuseFlag(configMap *v1.ConfigMap, kubeClient *kubernetes.Clientset) error {
	if configMap.Data == nil {
		configMap.Data = make(map[string]string)
	}

	isModify := false
	if value, exist := configMap.Data[controllercm.KeyLoadbalancerCRDEnabled]; !exist || value != "true" {
		isModify = true
		configMap.Data[controllercm.KeyLoadbalancerCRDEnabled] = "true"
	}
	if value, exist := configMap.Data[controllercm.KeyVersion]; !exist || value != config.GitVersion {
		isModify = true
		configMap.Data[controllercm.KeyVersion] = config.GitVersion
	}

	if isModify {
		if _, err := kubeClient.CoreV1().ConfigMaps(NamespaceKubeSystem).Update(context.Background(), configMap, metav1.UpdateOptions{}); err != nil {
			klog.Errorf("ResourceInitReuseFlag. kubernetes client configMap try update error. %s", err)
			return err
		}
	}
	return nil
}
