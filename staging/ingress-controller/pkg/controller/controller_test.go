package controller

import (
	"context"
	"errors"
	"fmt"
	"os"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes/fake"

	"git.woa.com/kateway/pkg/domain/errdef"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/types"
	fakeing "git.woa.com/kateway/pkg/domain/types/fake/ingress"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
	tscclientsetfake "git.woa.com/kateway/tke-service-config/pkg/client/clientset/versioned/fake"
	extversions "git.woa.com/kateway/tke-service-config/pkg/client/informers/externalversions"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/plugins/tencent"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
)

func cmpErrors(t *testing.T, actual error, expectErrors []error) bool {
	expectIngErrors := []*types.Error{}
	for _, e := range expectErrors {
		ingErr, ok := lo.ErrorsAs[*types.Error](e)
		if ok {
			expectIngErrors = append(expectIngErrors, ingErr)
		}
	}
	expectCodes := lo.Map(expectIngErrors, func(e *types.Error, _ int) string { return e.ErrorCode.Code })
	_, expectRetry := lo.Find(expectIngErrors, func(e *types.Error) bool { return e.ErrorCode.Retry })
	actualIngErrors := lo.Map(errdef.Unwrap(actual), func(e error, _ int) *types.Error {
		return convertErr(e)
	})
	_, actualRetry := lo.Find(actualIngErrors, func(e *types.Error) bool { return e.ErrorCode.Retry })
	actualCodes := lo.Map(actualIngErrors, func(e *types.Error, _ int) string { return e.ErrorCode.Code })
	expectNotExist, _ := lo.Difference(expectCodes, actualCodes)
	if len(expectNotExist) > 0 {
		t.Errorf("expect error codes %v not exists, expect errors: \n%v\nactual errors: \n%v", expectNotExist, expectIngErrors, actual)
	}
	return assert.Equal(t, expectRetry, actualRetry, "expect retry: %v, actual retry: %v", expectRetry, actualRetry)
}

func cmpListeners(t *testing.T, a []types.Listener, b []*tencent.IngressListener) (ok bool) {
	ok = assert.Equal(t, len(a), len(b))
	ports := lo.Map(b, func(lis *tencent.IngressListener, _ int) int {
		return int(lis.Port)
	})
	for _, expect := range b {
		actual, exists := lo.Find(a, func(item types.Listener) bool {
			return item.Protocol == expect.Protocol && item.Port == int(expect.Port)
		})
		if !exists {
			ok = false
			t.Errorf("expect listener %s:%v not found", expect.Protocol, expect.Port)
			continue
		}
		if equal := cmpListener(t, actual, *expect, ports); !equal {
			ok = false
			t.Errorf("mismatch listener of %s:%v", actual.Protocol, actual.Port)
		}
	}
	return
}

func cmpListener(t *testing.T, a types.Listener, expect tencent.IngressListener, expectPorts []int) (ok bool) {
	type uniqueKey struct {
		host    string
		path    string
		clbPath string
	}
	type rewriteKey struct {
		uniqueKey
		targetPort    int
		targetHost    string
		targetPath    string
		targetCLBPath string
	}
	process := func(rules []*utils.Rule, forward bool) []*utils.Rule {
		for i := range rules {
			rules[i].Secret = nil
			if rules[i].Rewrite != nil {
				rules[i].Rewrite.PathType = nil
			}
		}
		if forward {
			nonPlaceholderRules := lo.Filter(rules, func(r *utils.Rule, _ int) bool {
				return r.Backend != nil || r.Rewrite != nil
			})
			placeholderRules := lo.Filter(rules, func(r *utils.Rule, _ int) bool { return r.Backend == nil && r.Rewrite == nil })
			placeholderRules = lo.UniqBy(placeholderRules, func(r *utils.Rule) uniqueKey {
				return uniqueKey{host: r.Host, path: r.Path, clbPath: r.RealPath}
			})
			return append(nonPlaceholderRules, placeholderRules...)
		}
		return lo.UniqBy(rules, func(r *utils.Rule) rewriteKey {
			return rewriteKey{
				uniqueKey: uniqueKey{
					host:    r.Host,
					path:    r.Path,
					clbPath: r.RealPath,
				},
				targetPort:    int(r.Rewrite.Port),
				targetHost:    r.Rewrite.Host,
				targetPath:    r.Rewrite.Path,
				targetCLBPath: r.Rewrite.RealPath,
			}
		})
	}
	expectRules := process(expect.IngressRules, true)
	expectRewrites := process(expect.IngressRewriteRules, false)
	invalidRewrites := []*utils.Rule{}
	expectRewrites = lo.Filter(expectRewrites, func(r *utils.Rule, _ int) bool {
		if lo.Contains(expectPorts, int(r.Rewrite.Port)) {
			return true
		}
		invalidRewrites = append(invalidRewrites, r)
		return false
	})
	for _, rewrite := range invalidRewrites {
		_, index, exists := lo.FindIndexOf(expectRules, func(r *utils.Rule) bool {
			return r.Host == rewrite.Host && r.Path == rewrite.Path && r.RealPath == rewrite.RealPath &&
				r.Backend == nil && r.Rewrite == nil
		})
		if exists {
			expectRules = append(expectRules[:index], expectRules[index+1:]...)
		}
	}
	for _, rewrite := range expectRewrites {
		indexes := lo.FilterMap(expectRules, func(r *utils.Rule, index int) (int, bool) {
			return index, r.Host == rewrite.Host && r.Path == rewrite.Path && r.RealPath == rewrite.RealPath && r.Backend != nil
		})
		expectRules = lo.DropByIndex(expectRules, indexes...)
	}
	actual := convertListener(a)
	ok = assert.Equal(t, expect.Port, int32(actual.Port))
	ok = assert.Equal(t, expect.Protocol, actual.Protocol) && ok
	ok = assert.Equal(t, expect.L7ListenerConfig, actual.L7ListenerConfig) && ok
	ok = assert.ElementsMatch(t, expectRules, actual.IngressRules, "forward rule mismatch") && ok
	ok = assert.ElementsMatch(t, expectRewrites, actual.IngressRewriteRules, "rewrite rules mismatch") && ok
	return
}

func cmpResult(t *testing.T, env *fakeing.Environment) (ok bool) {
	coreCli := fake.NewSimpleClientset()
	tscCli := tscclientsetfake.NewSimpleClientset()
	coreFactory := informers.NewSharedInformerFactory(coreCli, 0)
	tscFactory := extversions.NewSharedInformerFactory(tscCli, 0)
	svcInformer := coreFactory.Core().V1().Services()
	for _, svc := range env.Services {
		svcInformer.Informer().GetStore().Add(lo.ToPtr(svc))
	}
	secretInformer := coreFactory.Core().V1().Secrets()
	for _, secret := range env.Secrets {
		secretInformer.Informer().GetStore().Add(lo.ToPtr(secret))
	}
	tscInformer := tscFactory.Cloud().V1alpha1().TkeServiceConfigs()
	for _, tsc := range env.TKEServiceConfigs {
		tscInformer.Informer().GetStore().Add(lo.ToPtr(tsc))
	}
	defaultDomain := lo.ToPtr("8.8.8.8")
	sc := tencent.NewSyncContext(nil)
	sc.ContextMeta = &tencent.ContextMeta{
		Ingress: env.GetWrappedIngress(),
		IngressContext: &tencent.IngressContext{
			IngressListeners: map[string]*tencent.IngressListener{},
		},
		LoadBalancerContext: &tencent.LoadBalancerContext{
			DefaultDomain: defaultDomain,
		},
	}
	expectErr := (error)(nil)
	listeners, err := services.NewListenerService(services.NewTKEServiceConfigService(tscCli, tscInformer.Lister()),
		svcInformer.Lister(), secretInformer.Lister(), defaultDomain, 0).BuildIngressListeners(context.Background(), env.GetWrappedIngress())
	if expectErr != nil {
		ok = assert.True(t, errdef.IsFatal(err), "expect err: %s", expectErr)
	} else {
		ok = assert.False(t, errdef.IsFatal(err), "fatal err returned: %s", err)
		ok = cmpErrors(t, err, sc.Errors) && ok
	}
	expectListeners := lo.Values(sc.IngressContext.IngressListeners)
	ok = cmpListeners(t, listeners, expectListeners) && ok
	return
}

func TestBuildListenersFromFile(t *testing.T) {
	t.Parallel()
	jaeger.Init(jaeger.Config{})
	config.Global = &config.Config{}
	env, err := fakeing.LoadEnvironment(".test/index-9111")
	if err != nil {
		t.Fatal(err)
	}
	cmpResult(t, env)
}

func TestBuildListeners(t *testing.T) {
	t.Parallel()
	jaeger.Init(jaeger.Config{})
	config.Global = &config.Config{}
	if _, err := os.Stat(".test"); err != nil {
		if errors.Is(err, os.ErrNotExist) {
			os.Mkdir(".test", os.ModePerm)
		} else {
			t.Fatal(err)
		}
	}
	for i := range 10000 {
		index := i
		t.Run(fmt.Sprintf("index-%d", index), func(t *testing.T) {
			env := fakeing.New()
			if !cmpResult(t, env) {
				assert.Nil(t, env.Dump(fmt.Sprintf(".test/index-%d", index)))
			}
		})
	}
}
