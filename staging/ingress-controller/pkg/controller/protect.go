package controller

import (
	"context"
	goerrors "errors"
	"fmt"
	"strings"
	"sync"

	"git.woa.com/kateway/pkg/domain/types"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/sets"
	glog "k8s.io/klog/v2"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/plugins/tencent"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

// ensureProtectPods
// 方案：https://iwiki.woa.com/p/4010284815
// 当前 pods 和当前 targets 的交集，是期望开启删除保护的集合
// 当前 pods 去掉当前 targets， 是期望去掉删除保护的集合
func (lbc *LoadBalancerController) ensureProtectPods(syncContext *tencent.SyncContext) (err error) {
	if !config.Global.MergeMode {
		return nil
	}
	ctx := context.TODO()
	ing := syncContext.Ingress
	ns := ing.Namespace()
	ingName := ing.Name()
	if ing.Type() != types.CoreIngress {
		return nil
	}

	if !types.IsIngressEnableGraceDeletion(ing) {
		return lbc.cleanProtectPods(ctx, ing)
	}

	cacheKey := IngressCacheKey(types.CoreIngress, utils.IngressName(ing))
	cs, ok := lbc.ingCache.get(cacheKey)
	if !ok { // 不应该出现
		return fmt.Errorf("cache of ingress %s/%s is missing", ns, ingName)
	}

	loadBalancerContext := syncContext.LoadBalancerContext
	loadBalancerResource := loadBalancerContext.LoadBalancerResource
	loadBalancer, err := loadBalancerContext.GetLoadBalancer(ctx)
	if err != nil {
		return
	}

	backendType := utils.GetBackendType(loadBalancer)
	listenerIDs := make(sets.String)                                                         // 该 ingress 当前的所有监听器ID
	currentTargets := make(sets.String)                                                      // 属于当前 ingress 的所有 targets
	listeners := tencent.GetLoadBalancerResourceListenerByIngress(ing, loadBalancerResource) // 该 ingress 当前的监听器

	// 1. 先获取属于当前 ingress 的所有监听器
	for _, listener := range listeners {
		listenerIDs.Insert(utils.GetListenerKey(listener.Port, listener.Protocol))
	}
	// 2. 获取当前 LB 的所有 targets
	listenerBackends, err := tencent.GetTargetInfo(ctx, ing, loadBalancerContext.LoadBalancerId, loadBalancerContext.Region)
	if err != nil {
		return
	}

	// 3. {currentTargets}: 通过上述两个数据集， 过滤出属于当前 ingress 的 targets
	for _, listenerBackend := range listenerBackends {
		listenerKey := utils.GetListenerKey(int32(*listenerBackend.Port), *listenerBackend.Protocol) // 一个监听器的key：80_TCP
		if _, exist := listenerIDs[listenerKey]; !exist {
			continue
		}

		var targets []*clb.Backend
		if utils.IsL4Protocol(*listenerBackend.Protocol) {
			targets = listenerBackend.Targets
		} else if utils.IsL7Protocol(*listenerBackend.Protocol) {
			for _, rule := range listenerBackend.Rules {
				targets = append(targets, rule.Targets...)
			}
		}
		for _, target := range targets {
			if target.Type == nil {
				continue
			}
			if target.Weight != nil && utils.IsHealthyWeight(*target.Weight) && utils.IsENILikeType(*target.Type) {
				for _, privateIp := range target.PrivateIpAddresses {
					currentTargets.Insert(*privateIp)
				}
			}
		}
	}

	// 4. {pods}: 获取 ingress 所有的 pods
	serviceNames := make(sets.String)
	for _, rule := range ing.Rules() {
		for _, path := range rule.HTTPPaths {
			serviceNames.Insert(path.Backend.ServiceName)
		}
	}
	var pods []*v1.Pod
	for _, svcName := range serviceNames.List() {
		svc, e := cluster_service.Instance.ServiceLister().Services(ns).Get(svcName)
		if e != nil {
			if errors.IsNotFound(e) {
				continue
			}
			return e
		}

		if utils.IsDirectAccessIngressCascade(ing, svc) {
			svcPods, e := cluster_service.Instance.PodLister().Pods(ing.Namespace()).List(labels.SelectorFromSet(svc.Spec.Selector))
			if e != nil {
				return e
			}

			pods = append(pods, svcPods...)
		}
	}

	// 5. 计算删除保护
	deProtectPods, toProtectPods := calculateProtectPods(backendType, pods, currentTargets, &cs.ProtectPods)

	// 6. 去掉删除保护
	for podName, pod := range deProtectPods { // todo 考虑并发提速
		// pod 为 nil 表示这是在 cached protectPods 中，但不在当前 pods 中的数据。
		// 如果 pod 不存在了， 则直接删除 cache
		// 如果 pod 存在，则走删除保护逻辑
		if pod == nil {
			p, e := cluster_service.Instance.KubeClient().CoreV1().Pods(ns).Get(ctx, podName, metav1.GetOptions{})
			if e != nil {
				if errors.IsNotFound(e) {
					cs.ProtectPods.Delete(podName)
				} else {
					err = goerrors.Join(e)
				}
				continue
			}

			pod = p // 这种情况可能是用户调整了 service 和 pod 的选择关系， 导致之前开启了删除保护的pod，这次通过service selector没有查出来, 这种pod也应该去掉删除保护
		}

		e := removePodProtectIngresses(ctx, pod, ingName)
		if e == nil {
			cs.ProtectPods.Delete(podName)
		} else {
			err = goerrors.Join(e)
		}
	}

	// 6. 添加删除保护
	for _, pod := range toProtectPods { // todo 考虑并发提速
		e := addPodProtectedIngress(ctx, pod, ingName)
		if e == nil {
			cs.ProtectPods.Store(pod.Name, pod.Status.PodIP) // value 暂时没使用
		} else {
			err = goerrors.Join(e)
		}
	}

	return
}

func (lbc *LoadBalancerController) cleanProtectPods(ctx context.Context, ing types.Ingress) (err error) {
	if ing.Type() != types.CoreIngress {
		return
	}
	ns := ing.Namespace()
	ingName := ing.Name()
	cacheKey := IngressCacheKey(types.CoreIngress, fmt.Sprintf("%s/%s", ns, ingName))

	cs, ok := lbc.ingCache.get(cacheKey)
	if !ok {
		return
	}

	cs.ProtectPods.Range(func(key, _ any) bool {
		podName := key.(string)
		pod, e := cluster_service.Instance.PodLister().Pods(ns).Get(podName)
		if e != nil {
			if errors.IsNotFound(e) {
				cs.ProtectPods.Delete(podName)
				return true
			}

			err = goerrors.Join(err, e)
			return true
		}

		if types.IsPodEnableDeleteProtectionOfIngress(pod) {
			e := removePodProtectIngresses(ctx, pod, ingName)
			if e != nil {
				err = goerrors.Join(err, e)
			} else {
				cs.ProtectPods.Delete(podName)
			}
		} else {
			cs.ProtectPods.Delete(podName)
		}
		return true
	})

	return
}

func (lbc *LoadBalancerController) HandleIngressProtectLeak(pod *v1.Pod) error {
	if !types.IsPodEnableDeleteProtectionOfIngress(pod) {
		return nil
	}

	var ingressesNeedRemove []string
	ingNames := strings.Split(pod.Annotations[types.PodProtectedByIngresses], ",")
	for _, ingName := range ingNames {
		cs, ok := lbc.ingCache.get(IngressCacheKey(types.CoreIngress, fmt.Sprintf("%s/%s", pod.Namespace, ingName)))
		if ok {
			_, ok := cs.ProtectPods.Load(pod.Name)
			if ok {
				continue // 保留, 让 ingress 去调谐
			}
		}
		ingressesNeedRemove = append(ingressesNeedRemove, ingName)
	}

	if len(ingressesNeedRemove) > 0 {
		glog.Infof("remove delete-protection ingresses %s from pod %s/%s, previous ingresses: %s", ingressesNeedRemove, pod.Namespace, pod.Name, ingNames)
		return removePodProtectIngresses(context.TODO(), pod, ingressesNeedRemove...)
	}

	return nil
}

func RemoveAllPodProtectIngresses(ctx context.Context, pod *v1.Pod) error {
	glog.Infof("remove all delete-protection ingresses from pod %s/%s", pod.Namespace, pod.Name)
	delete(pod.Annotations, types.PodEnableDeleteProtectionByIngress)
	delete(pod.Annotations, types.PodProtectedByIngresses)
	_, err := cluster_service.Instance.KubeClient().CoreV1().Pods(pod.Namespace).Update(ctx, pod, metav1.UpdateOptions{})
	return err
}

// calculateProtectPods
// 期望加上删除保护的pod toProtectPods = {pods} ∩ {targets}
// 期望去掉删除保护的pod deProtectPods = ({pods} - {targets}) + ({protectPods} - {pods})
// https://iwiki.woa.com/p/4010284815#%E9%80%9A%E8%BF%87-Service-%E8%B0%83%E8%B0%90%E8%BF%87%E7%A8%8B%EF%BC%9A
func calculateProtectPods(backendType string, pods []*v1.Pod, targets sets.String, cachedProtectPods *sync.Map) (map[string]*v1.Pod, map[string]*v1.Pod) {
	deProtectPods := make(map[string]*v1.Pod)
	toProtectPods := make(map[string]*v1.Pod)
	podSet := make(sets.String)

	// toProtectPods = {pods} ∩ {targets}
	// deProtectPods = {pods} - {targets}
	for _, pod := range pods {
		if podSet.Has(pod.Name) {
			continue
		}
		podSet.Insert(pod.Name)
		podIP, exist := utils.GetPodBackend(pod, backendType)
		if exist {
			if targets.Has(podIP) {
				toProtectPods[pod.Name] = pod
			} else {
				deProtectPods[pod.Name] = pod
			}
		} else {
			deProtectPods[pod.Name] = pod
		}
	}

	// {protectPods} - {pods}: protectPods 中已经不是属于该service 的pods，需要被去掉删除保护
	// 因此 deProtectPods = deProtectPods + ({protectPods} - {pods}) - {pods not found}
	// 另外不存在的 pods 也需要去掉删除保护
	cachedProtectPods.Range(func(key, _ any) bool {
		podName := key.(string)
		if !podSet.Has(podName) {
			deProtectPods[podName] = nil
		}
		return true
	})

	return deProtectPods, toProtectPods
}

// addPodProtectedIngress 将 ingName 添加到 pod 的 annotation ingress.cloud.tencent.com/protect-by-ingresses 中
func addPodProtectedIngress(ctx context.Context, pod *v1.Pod, ingName string) error {
	currentNames := strings.Split(pod.Annotations[types.PodProtectedByIngresses], ",")
	ingMap := make(sets.String)
	var existed bool
	for _, name := range currentNames {
		name := strings.TrimSpace(name)
		if name != "" {
			if name == ingName {
				existed = true
			}
			ingMap.Insert(name)
		}
	}

	if types.IsPodEnableDeleteProtectionOfIngress(pod) && existed {
		return nil
	}

	if pod.Annotations == nil {
		pod.Annotations = make(map[string]string)
	}

	glog.Infof("add delete-protection ingresses %s to pod %s/%s", ingName, pod.Namespace, pod.Name)
	ingMap.Insert(ingName)
	pod.Annotations[types.PodEnableDeleteProtectionByIngress] = "true"
	pod.Annotations[types.PodProtectedByIngresses] = strings.Join(ingMap.List(), ",")

	_, err := cluster_service.Instance.KubeClient().CoreV1().Pods(pod.Namespace).Update(ctx, pod, metav1.UpdateOptions{})
	return err
}

// RemovePodProtectIngresses 将 ingNames 从 pod 的 annotation ingress.cloud.tencent.com/protected-by-ingresses 中移除
// 然后会重新计算 pod 是否还需要开启删除保护
func removePodProtectIngresses(ctx context.Context, pod *v1.Pod, ingNames ...string) error {
	ingMap := make(sets.String)
	currentNames := strings.Split(pod.Annotations[types.PodProtectedByIngresses], ",")
	for _, name := range currentNames {
		name := strings.TrimSpace(name)
		if name != "" {
			ingMap.Insert(name)
		}
	}

	var existedIngresses []string
	for _, ingName := range ingNames {
		if ingMap.Has(ingName) {
			existedIngresses = append(existedIngresses, ingName)
			ingMap.Delete(ingName)
		}
	}

	if len(existedIngresses) == 0 {
		return nil
	}

	glog.Infof("remove delete-protection ingresses %s from pod %s/%s", existedIngresses, pod.Namespace, pod.Name)
	if len(ingMap) == 0 {
		delete(pod.Annotations, types.PodEnableDeleteProtectionByIngress)
		delete(pod.Annotations, types.PodProtectedByIngresses)
	} else {
		pod.Annotations[types.PodProtectedByIngresses] = strings.Join(ingMap.List(), ",")
	}

	_, err := cluster_service.Instance.KubeClient().CoreV1().Pods(pod.Namespace).Update(ctx, pod, metav1.UpdateOptions{})
	return err
}
