package controller

import (
	"fmt"

	"github.com/fvbommel/sortorder"

	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
)

type SortableListenerRule []*utils.Rule

func (s SortableListenerRule) Len() int {
	return len(s)
}

func (s SortableListenerRule) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

func (s SortableListenerRule) Less(i, j int) bool {
	return sortorder.NaturalLess(fmt.Sprintf("%s_%s", s[i].Host, s[i].RealPath), fmt.Sprintf("%s_%s", s[j].Host, s[j].RealPath))
}
