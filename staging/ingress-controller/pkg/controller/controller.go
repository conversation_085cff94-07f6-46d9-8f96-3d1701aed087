/*
Copyright 2015 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"container/list"
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	errors2 "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	v1 "k8s.io/api/core/v1"
	discovery "k8s.io/api/discovery/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/apimachinery/pkg/util/wait"
	listerscorev1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"

	lbrv1alpha1 "git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	crd2 "git.woa.com/kateway/loadbalancer-resource-api/pkg/crd"
	"git.woa.com/kateway/pkg/domain/errdef"
	"git.woa.com/kateway/pkg/domain/event"
	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/ingress/ingress_wrapper"
	ingresslabels "git.woa.com/kateway/pkg/domain/ingress/labels"
	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/net"
	"git.woa.com/kateway/pkg/runtime/conv"
	sets2 "git.woa.com/kateway/pkg/sets"
	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	tkeserviceapi "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	"git.woa.com/kateway/tke-service-config/pkg/crd"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/pkg/plugins/tencent"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/cluster"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/cluster/nodestatus"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/crossregion"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

const (
	ProtocolHttps = "HTTPS"
	ProtocolHttp  = "HTTP"

	// QcloudCertId = "qcloud_cert_id"

	QcloudLoadBalancerId = "kubernetes.io/ingress.qcloud-loadbalance-id"

	IgnoredServiceName       = "non-service"
	TKEDedicatedListenerName = "TKE-DEDICATED-LISTENER"

	// LabelNodeRoleMaster specifies that a node is a master
	// It's copied over to kubeadm until it's merged in core: https://github.com/kubernetes/kubernetes/pull/39112
	LabelNodeRoleMaster     = "node-role.kubernetes.io/master"
	NodePortNotFound    int = -1

	SYNC_CHECK_CRD_FREQUENCY  = 5 * time.Minute
	SYNC_QUOTA_FREQUENCY      = 30 * time.Second
	SYNC_PROJECT_ID_FREQUENCY = 2 * time.Hour
)

var (
	storeSyncPollPeriod = 5 * time.Second
	ingressSyncPeriod   = 30 * time.Second
)

type LoadBalancerController struct {
	ingCache *ingressCache

	// hasSynced returns true if all associated sub-controllers have synced.
	// Abstracted into a func for testing.
	// hasSynced func() bool

	// clusterInstanceId string

	workers int
	// lbBackendQuota      int
	// lbRuleQuota         int
	// lbBackendQuotaInUse int
	// lbBackendQuotaInClb int
}

func NewLoadBalancerController(workers int) *LoadBalancerController {
	return &LoadBalancerController{
		ingCache: &ingressCache{ingressMap: make(map[string]*cachedIngress)},
		workers:  workers,
	}
}

// Run starts the loadbalancer controller.
func (lbc *LoadBalancerController) Run(stopCh <-chan struct{}) error {

	lbc.syncClusterProjectId()
	lbc.syncLoadBalancerTargetQuota()
	go CheckCRDAlready(wait.NeverStop)
	go lbc.syncProjectId(wait.NeverStop)
	go lbc.syncLoadBalancerQuota(wait.NeverStop)
	go UploadOperationalMetrtc()

	ingressList, err := tencent.IngressLister(types.CoreIngress, v1.NamespaceAll, labels.Everything())
	if err != nil {
		return err
	}
	for index := range ingressList {
		ingress := ingressList[index]
		lbc.ingCache.set(&cachedIngress{Ingress: ingress})
	}

	if config.Global.EnableMultiClusterIngress {
		mciList, err := tencent.IngressLister(types.MultiClusterIngress, v1.NamespaceAll, labels.Everything())
		if err != nil {
			return err
		}
		for index := range mciList {
			ingress := mciList[index]
			lbc.ingCache.set(&cachedIngress{Ingress: ingress})
		}
	}

	cluster.InitQueueServiceInstance(lbc.syncIngress, lbc.syncMultiClusterIngress, lbc.workers) // kateway: 所有异步队列的并发度都是一样的（可优化）
	cluster_service.Instance.Run(stopCh)

	return nil
}

func (lbc *LoadBalancerController) syncIngress(key string) []error {
	errs := make([]error, 0)
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		errs = append(errs, err)
		return errs
	}

	ingress, err := tencent.IngressGetter(types.CoreIngress, namespace, name)
	if err != nil {
		if !k8serrors.IsNotFound(err) {
			klog.Infof("Unable to retrieve ingress %v from store: %v", key, err)
			errs = append(errs, err)
			return errs
		}
	}

	return lbc.syncLifeCycle(key, types.CoreIngress, ingress)
}

func (lbc *LoadBalancerController) syncMultiClusterIngress(key string) []error {
	errs := make([]error, 0)
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		errs = append(errs, err)
		return errs
	}

	ingress, err := tencent.IngressGetter(types.MultiClusterIngress, namespace, name)
	if err != nil {
		if !k8serrors.IsNotFound(err) {
			klog.Infof("Unable to retrieve multiClusterIngress %v from store: %v", key, err)
			errs = append(errs, err)
			return errs
		}
	}

	return lbc.syncLifeCycle(key, types.MultiClusterIngress, ingress)
}

func (lbc *LoadBalancerController) syncLoadBalancerQuota(stopCh <-chan struct{}) {
	ticker := time.NewTicker(SYNC_QUOTA_FREQUENCY)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			lbc.syncLoadBalancerTargetQuota()
		case <-stopCh:
			return
		}
	}
}

func (lbc *LoadBalancerController) syncLoadBalancerTargetQuota() {
	backendQuotaOfClb, err := tencent.DescribeQuota()
	if err != nil {
		klog.Errorf("DescribeQuota Error, %v", err)
		return
	}

	config.Global.BackendQuotaInClb = backendQuotaOfClb
	// 账号级别限额考虑option的约束
	if config.Global.BackendQuota != 0 && config.Global.BackendQuota < backendQuotaOfClb.UserQuota { // kateway: options 的设置和 clb平台的设置，二者取最小值
		backendQuotaOfClb.UserQuota = config.Global.BackendQuota
	}

	changeFlag := false
	// 比较两个维度的限额是否有变化
	if backendQuotaOfClb.UserQuota != config.Global.BackendQuotaInUse.UserQuota {
		changeFlag = true
		klog.Infof("backendQuotaInUse changed: User CLB Quota:%d, Limit:%d, LbBackendQuotaInUse:%d.",
			backendQuotaOfClb.UserQuota, config.Global.BackendQuota, config.Global.BackendQuotaInUse.UserQuota)
	}

	if changeFlag {
		lbc.ingCache.forEach(func(_ string, ci *cachedIngress) {
			if ci.rsLimitExceeded {
				cluster_service.QueueServiceInstance.IngressQueue().Enqueue(taskqueue.Item{
					Data:   utils.IngressName(ci),
					Weight: 1,
				})
			}
		})
	} else {
		for lbId, instanceQuota := range backendQuotaOfClb.InstanceQuota {
			if quota, exist := config.Global.BackendQuotaInUse.InstanceQuota[lbId]; (!exist) || instanceQuota != quota { // kateway: clb 平台设置发生变化, 更新使用值
				lbc.ingCache.forEach(func(_ string, ci *cachedIngress) {
					if ci.rsLimitExceeded {
						annotations := ci.Annotations()
						if annotations != nil {
							curServiceIbId := annotations[QcloudLoadBalancerId]
							if lbId == curServiceIbId {
								cluster_service.QueueServiceInstance.IngressQueue().Enqueue(taskqueue.Item{
									Data:   utils.IngressName(ci),
									Weight: 1,
								})
							}
						}
					}
				})
				klog.Infof("backendQuotaInUse changed:Instance CLB Quota:%d, Limit:%d, LbBackendQuotaInUse:%d, clbId:%s.",
					instanceQuota, config.Global.BackendQuota, quota, lbId)
			}
		}
	}
	config.Global.BackendQuotaInUse = backendQuotaOfClb
}

func (lbc *LoadBalancerController) syncProjectId(stopCh <-chan struct{}) {
	ticker := time.NewTicker(SYNC_PROJECT_ID_FREQUENCY)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			lbc.syncClusterProjectId()
		case <-stopCh:
			return
		}
	}
}

func (lbc *LoadBalancerController) syncClusterProjectId() {
	if config.Global.ProjectID == -1 { // 独立部署时，指定为-1，可解除对tke接口的依赖
		klog.V(4).Infof("skip sync project id")
		return
	}

	if !utils.IsInEKSCluster() {
		request := tke.NewDescribeClustersRequest()
		request.ClusterIds = []*string{&config.Global.ClusterName}
		response, err := tencentapi.Instance.DescribeClusters(cloudctx.From(context.Background(), nil, config.Global.Region), request)
		if err != nil {
			klog.Errorf("DescribeClusters Error, %v", err)
			return
		}
		if len(response.Response.Clusters) == 0 {
			klog.Errorf("DescribeClusters Not Found, %v", err)
			return
		}
		projectId := common.Int64Ptr(int64(*response.Response.Clusters[0].ProjectId))
		if config.Global.ProjectID != *projectId {
			klog.Infof("projectId update to: %d", *projectId)
			config.Global.ProjectID = *projectId
		}
	}
}

func CheckCRDAlready(stopChan <-chan struct{}) {
	cron := time.NewTicker(SYNC_CHECK_CRD_FREQUENCY)
	defer cron.Stop()
	for {
		select {
		case <-stopChan:
			return
		case <-cron.C:
			checkCRDAlready()
		}
	}
}

func checkCRDAlready() {
	apiextensionsKubeClient := cluster_service.Instance.ApiextensionsKubeclient()
	_ = crd.TkeServiceConfigInitTransaction(apiextensionsKubeClient)
	_ = crd2.LoadBalancerResourceInitTransaction(apiextensionsKubeClient)
}

func (lbc *LoadBalancerController) ensureLoadBalancerDetail(sc *tencent.SyncContext) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	ing := sc.Ingress
	region := sc.LoadBalancerContext.Region
	loadBalancer, err := sc.LoadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		return err
	}

	if err := tencent.SyncLoadbalancerNetworks(sc); err != nil {
		return err
	}

	// 同步负载均衡默认放通，错误不阻塞主流程
	syncLbPassToTarget(sc, ing, loadBalancer, region)

	// 同步负载均衡配置保护，错误不阻塞主流程
	syncLbProtected(sc, ing, loadBalancer, region)

	// 同步安全组配置，错误不阻塞主流程
	syncSecurityGroups(sc, ing, loadBalancer, region)

	// 同步负载均衡删除保护
	if err := tencent.EnsureDeletionProtection(sc, sc.LoadBalancerContext); err != nil {
		return err
	}

	return nil
}

func diffListeners(expect map[string]*tencent.IngressListener, actual map[string]*clb.Listener) (listenersToAdd, listenersToDel []types.ListenerMeta) {
	expectListenerMetas := lo.Map(lo.Values(expect), func(lis *tencent.IngressListener, _ int) types.ListenerMeta {
		return types.ListenerMeta{Protocol: lis.Protocol, Port: int(lis.Port)}
	})
	actualListenerMetas := lo.Map(lo.Values(actual), func(lis *clb.Listener, _ int) types.ListenerMeta {
		return types.ListenerMeta{Protocol: strings.ToUpper(*lis.Protocol), Port: int(*lis.Port)}
	})
	listenersToAdd, listenersToDel = lo.Difference(expectListenerMetas, actualListenerMetas)
	return
}

// 获取当前证书 ID 列表
func getCLBListenerCertIds(listener *clb.Listener) []string {
	curSlices := []string{*listener.Certificate.CertId}
	if listener.Certificate.ExtCertIds != nil {
		for _, id := range listener.Certificate.ExtCertIds {
			if id != nil {
				curSlices = append(curSlices, *id)
			}
		}
	}
	return curSlices
}

func shouldUpdateMultiCertInfo(listener *clb.Listener, setsExp, setsCur *sets2.Set[string], caCertId *string) bool {
	return (*listener.Certificate.SSLMode == "UNIDIRECTIONAL" && (!setsExp.Equal(*setsCur) || caCertId != nil)) ||
		(*listener.Certificate.SSLMode == "MUTUAL" && (!setsExp.Equal(*setsCur) || caCertId == nil || *listener.Certificate.CertCaId != *caCertId))
}

// 更新多证书
func updateMultiCertInfo(request *clbinternal.ModifyListenerRequest, listener *clb.Listener, slices []string, caCertId *string) bool {
	// 多证书
	curSlices := getCLBListenerCertIds(listener)
	// 对比
	setsExp := sets2.New(slices...)
	setsCur := sets2.New(curSlices...)
	if shouldUpdateMultiCertInfo(listener, &setsExp, &setsCur, caCertId) {
		request.MultiCertInfo = createMultiCertInfo(setsExp.UnsortedList(), caCertId)
		return true
	}
	return false
}

// 创建多证书结构体
func createMultiCertInfo(slices []string, caCertId *string) *clb.MultiCertInfo {
	crtList := make([]*clb.CertInfo, len(slices))
	for i, slice := range slices {
		crtList[i] = &clb.CertInfo{CertId: common.StringPtr(slice)}
	}

	sslMode := "UNIDIRECTIONAL"
	if caCertId != nil {
		crtList = append(crtList, &clb.CertInfo{CertId: caCertId})
		sslMode = "MUTUAL"
	}

	return &clb.MultiCertInfo{CertList: crtList, SSLMode: common.StringPtr(sslMode)}
}

func (lbc *LoadBalancerController) ensureIngressLBListener(sc *tencent.SyncContext, ingress types.Ingress) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	isNeedSNI := utils.IsNeedSNI(ingress)

	// 查询当前LB状态
	loadbalancer := *sc.LoadBalancerContext.LoadBalancer
	region := sc.LoadBalancerContext.Region
	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return err
	}
	listenersToAdd, _ := diffListeners(sc.IngressContext.IngressListeners, listeners)
	httpListenersToAdd := lo.Filter(listenersToAdd, func(meta types.ListenerMeta, _ int) bool { return meta.Protocol == net.ProtocolHTTP })
	httpsListenersToAdd := lo.Filter(listenersToAdd, func(meta types.ListenerMeta, _ int) bool { return meta.Protocol == net.ProtocolHTTPS })

	for _, meta := range httpListenersToAdd {
		request := clbinternal.NewCreateListenerRequest()
		request.LoadBalancerId = loadbalancer.LoadBalancerId
		request.Ports = []*int64{lo.ToPtr(int64(meta.Port))}
		request.Protocol = common.StringPtr(net.ProtocolHTTP)
		request.ListenerNames = common.StringPtrs([]string{TKEDedicatedListenerName})

		listener := sc.IngressContext.IngressListeners[utils.GetListenerKey(int32(meta.Port), net.ProtocolHTTP)]
		if listener.L7ListenerConfig != nil {
			if listener.L7ListenerConfig.KeepaliveEnable != nil {
				request.KeepaliveEnable = common.Int64Ptr(int64(*listener.L7ListenerConfig.KeepaliveEnable))
			}
		}

		if _, err := tencentapi.Instance.CreateListener(cloudctx.From(sc, ingress, region), request); err != nil {
			if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
				if sdkError.Code == "LimitExceeded" {
					return types.NewError(errcode.ListenerLimitExceeded, sdkError.Error())
				}
				// "CreateListener", "InvalidParameterValue", "E4012", sdkError // 理论上不可能发生
			}
			klog.Errorf("CreateL7LbLister(%s) failed! err:%v", *loadbalancer.LoadBalancerId, err)
			return err
		}
	}

	for _, meta := range httpsListenersToAdd {
		request := clbinternal.NewCreateListenerRequest()
		request.LoadBalancerId = loadbalancer.LoadBalancerId
		request.Ports = []*int64{lo.ToPtr(int64(meta.Port))}
		request.Protocol = common.StringPtr(net.ProtocolHTTPS)
		request.ListenerNames = common.StringPtrs([]string{TKEDedicatedListenerName})
		if isNeedSNI {
			request.SniSwitch = common.Int64Ptr(1)
		} else {
			certId, caCertId, err := lbc.getDefaultCertId(ingress)
			if err != nil {
				return err
			}
			slices, err := services.ProcessCertIDs(certId)
			if err == nil && len(slices) > 1 {
				request.MultiCertInfo = createMultiCertInfo(slices, caCertId)
			} else {
				if caCertId == nil {
					request.Certificate = &clb.CertificateInput{CertId: common.StringPtr(certId), SSLMode: common.StringPtr("UNIDIRECTIONAL")}
				} else {
					request.Certificate = &clb.CertificateInput{CertId: common.StringPtr(certId), CertCaId: caCertId, SSLMode: common.StringPtr("MUTUAL")}
				}
			}

		}
		listener := sc.IngressContext.IngressListeners[utils.GetListenerKey(int32(meta.Port), ProtocolHttps)]
		if listener.L7ListenerConfig != nil {
			if listener.L7ListenerConfig.KeepaliveEnable != nil {
				request.KeepaliveEnable = common.Int64Ptr(int64(*listener.L7ListenerConfig.KeepaliveEnable))
			}
		}

		if _, err := tencentapi.Instance.CreateListener(cloudctx.From(sc, ingress, region), request); err != nil {
			if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
				if sdkError.Code == "LimitExceeded" {
					return types.NewError(errcode.ListenerLimitExceeded, sdkError.Error())
				} else if sdkError.Code == "InvalidParameter" {
					if strings.Contains(sdkError.Message, "status error") {
						return types.NewError(errcode.CertificateStatusError, sdkError.Error(), utils.IngressName(ingress))
					} else if strings.Contains(sdkError.Message, "is not SVR type") {
						return types.NewError(errcode.CertificateTypeError, sdkError.Error(), utils.IngressName(ingress))
					} else if strings.Contains(sdkError.Message, "is out of date") {
						return types.NewError(errcode.CertificateOutOfDateError, sdkError.Error(), utils.IngressName(ingress))
					}
				} else if sdkError.Code == "InvalidParameterValue" { // Code = InvalidParameterValue
					if strings.Contains(sdkError.Message, "CertList algorithm types cannot be repeated") {
						// 双证书算法相同错误捕获
						return types.NewError(errcode.CertificateAlgorithmTypeError, sdkError.Error(), utils.IngressName(ingress))
					}
				} else if sdkError.Code == "InvalidParameterValue.Length" && strings.Contains(sdkError.Message, "The length of CertificateId") {
					return types.NewError(errcode.CertificateFormatError, sdkError.Error(), utils.IngressName(ingress))
				} else if sdkError.Code == "FailedOperation.CertificateNotFound" || (sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "Query certificate")) {
					return types.NewError(errcode.CertificateNotFoundError, sdkError.Error(), utils.IngressName(ingress))
				}
			}
			klog.Errorf("CreateL7LbLister(%s) failed! err:%v", *loadbalancer.LoadBalancerId, err)
			return err
		}
	}

	if len(httpListenersToAdd)+len(httpsListenersToAdd) > 0 {
		if err := sc.LoadBalancerContext.UpdateListeners(sc); err != nil {
			return err
		}
	}

	if listeners, err = sc.LoadBalancerContext.GetListeners(sc); err != nil {
		return err
	}

	listenersChanged := false
	for _, listener := range listeners {
		// syncContext.LoadBalancerContext.Listeners[int32(*listener.Port)] = listeners[index]

		listenerChanged := false
		request := clbinternal.NewModifyListenerRequest()
		request.LoadBalancerId = loadbalancer.LoadBalancerId
		request.ListenerId = listener.ListenerId

		listenerContext := sc.IngressContext.IngressListeners[utils.GetListenerKey(int32(*listener.Port), *listener.Protocol)]
		if listenerContext == nil || *listener.ListenerName != TKEDedicatedListenerName {
			// 不是当前ingress 期望的监听器，忽略相关更新
			continue
		}

		if listener.KeepaliveEnable == nil || (listenerContext.L7ListenerConfig != nil && listenerContext.L7ListenerConfig.KeepaliveEnable != nil && *listenerContext.L7ListenerConfig.KeepaliveEnable != int32(*listener.KeepaliveEnable)) {
			request.KeepaliveEnable = common.Int64Ptr(int64(*listenerContext.L7ListenerConfig.KeepaliveEnable))
			listenerChanged = true
		}

		if strings.ToUpper(*listener.Protocol) == net.ProtocolHTTPS {
			if len(ingress.TLS()) == 0 { // 监听器随后会被删除
				// syncContext.LoadBalancerContext.Listeners[IngressDefaultHttpsPort] = nil
				continue
			}

			if *listener.SniSwitch == 0 { // 监听器开了SNI，就不用关心证书了。
				if isNeedSNI { // 有必要开启SNI的场景
					request.SniSwitch = common.Int64Ptr(1)
					listenerChanged = true
				} else { // 没有开启SNI的场景，需要维护监听器上的证书
					if listener.Certificate == nil || listener.Certificate.CertId == nil {
						klog.Errorf("CLB error info, listener https do not have cert.")
						continue
					}
					certId, caCertId, err := lbc.getDefaultCertId(ingress)
					if err != nil {
						sc.Errors = append(sc.Errors, err)
						continue
					}
					slices, err := services.ProcessCertIDs(certId)
					if err == nil && len(slices) > 1 {
						if updateMultiCertInfo(request, listener, slices, caCertId) {
							listenerChanged = true
						}
					} else {
						// 单证书
						if (*listener.Certificate.SSLMode == "UNIDIRECTIONAL" && (*listener.Certificate.CertId != certId || caCertId != nil)) ||
							(*listener.Certificate.SSLMode == "MUTUAL" && (*listener.Certificate.CertId != certId || caCertId == nil || *listener.Certificate.CertCaId != *caCertId)) { // CLB 证书更新
							if caCertId == nil {
								request.Certificate = &clb.CertificateInput{CertId: common.StringPtr(certId), SSLMode: common.StringPtr("UNIDIRECTIONAL")}
							} else {
								request.Certificate = &clb.CertificateInput{CertId: common.StringPtr(certId), CertCaId: caCertId, SSLMode: common.StringPtr("MUTUAL")}
							}
							listenerChanged = true
						}
					}
				}
			}
		}

		if listenerChanged {
			if _, err := tencentapi.Instance.ModifyListener(cloudctx.From(sc, ingress, region), request); err != nil {
				if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
					if sdkError.Code == "InvalidParameter" && strings.Contains(sdkError.Message, "status error") {
						sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateStatusError, sdkError.Error(), utils.IngressName(ingress)))
						continue
					} else if sdkError.Code == "InvalidParameter" && strings.Contains(sdkError.Message, "is not SVR type") {
						sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateTypeError, sdkError.Error(), utils.IngressName(ingress)))
						continue
					} else if sdkError.Code == "InvalidParameter" && strings.Contains(sdkError.Message, "is out of date") {
						sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateOutOfDateError, sdkError.Error(), utils.IngressName(ingress)))
						continue
					} else if sdkError.Code == "InvalidParameterValue.Length" && strings.Contains(sdkError.Message, "The length of CertificateId") {
						sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateFormatError, sdkError.Error(), utils.IngressName(ingress)))
						continue
					} else if sdkError.Code == "FailedOperation.CertificateNotFound" || (sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "Query certificate")) {
						sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateNotFoundError, sdkError.Error(), utils.IngressName(ingress)))
						continue
					} else if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "CertList algorithm types cannot be repeated") { // 多证书错误捕获
						sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateAlgorithmTypeError, sdkError.Error(), utils.IngressName(ingress)))
						continue
					}
				}
				sc.Errors = append(sc.Errors, err)
				continue
			}
			listenersChanged = true
		}
	}

	if listenersChanged {
		if err := sc.LoadBalancerContext.UpdateListeners(sc); err != nil {
			return err
		}
	}
	return nil
}

func diffSnatIps(snatProInfo *utils.SnatProInfo, snatIps []*clb.SnatIp) ([]*clb.SnatIp, []*clb.SnatIp, []*string, []*string) {
	subnetMap := make(map[string]bool)
	ipMap := make(map[string]map[string]bool)
	countMap := make(map[string]int)
	for _, snatIp := range snatProInfo.SnatIPs {
		subnetMap[snatIp.SubnetId] = true
		if snatIp.IP == nil {
			if _, exist := countMap[snatIp.SubnetId]; !exist {
				countMap[snatIp.SubnetId] = 0
			}
			countMap[snatIp.SubnetId] = countMap[snatIp.SubnetId] + 1
		} else {
			if _, exist := ipMap[snatIp.SubnetId]; !exist {
				ipMap[snatIp.SubnetId] = make(map[string]bool)
			}
			ipMap[snatIp.SubnetId][*snatIp.IP] = true
		}
	}

	addSnatIp := make([]*clb.SnatIp, 0)
	deleteSnatIp := make([]*string, 0)
	if snatIps != nil {
		for _, snatIp := range snatIps {
			if snatIp.SubnetId != nil && snatIp.Ip != nil {
				if _, exist := subnetMap[*snatIp.SubnetId]; !exist {
					deleteSnatIp = append(deleteSnatIp, common.StringPtr(*snatIp.Ip))
				}
			}
		}
	}

	for subnet, _ := range subnetMap {
		if snatIps != nil {
			for _, snatIp := range snatIps {
				if snatIp.SubnetId != nil && *snatIp.SubnetId == subnet && snatIp.Ip != nil {
					if snatIp.Ip != nil {
						if _, exist := ipMap[subnet]; exist {
							if _, exist := ipMap[subnet][*snatIp.Ip]; exist {
								delete(ipMap[subnet], *snatIp.Ip)
								continue
							}
						}
					}
					if count, exist := countMap[subnet]; exist {
						if count > 0 {
							countMap[subnet] = countMap[subnet] - 1
							continue
						}
					}
					deleteSnatIp = append(deleteSnatIp, common.StringPtr(*snatIp.Ip))
				}
			}
		}

		if _, exist := ipMap[subnet]; exist {
			for ip, _ := range ipMap[subnet] {
				addSnatIp = append(addSnatIp, &clb.SnatIp{SubnetId: common.StringPtr(subnet), Ip: common.StringPtr(ip)})
			}
		}

		if _, exist := countMap[subnet]; exist {
			for i := 0; i < countMap[subnet]; i++ {
				addSnatIp = append(addSnatIp, &clb.SnatIp{SubnetId: common.StringPtr(subnet)})
			}
		}
	}

	// 删除和添加的限制
	// 1. 删除SnatIP的时候，不能将全部SnatIP都删光。
	// 2. 添加SnatIP的时候，不能超过10个的数量限制。
	deleteFirstSnatIp := deleteSnatIp
	deleteSecondSnatIp := make([]*string, 0)
	if len(deleteSnatIp) != 0 && len(snatIps) == len(deleteSnatIp) {
		if len(deleteSnatIp) == 1 {
			deleteFirstSnatIp = make([]*string, 0)
		} else {
			deleteFirstSnatIp = deleteSnatIp[1:]
		}
		deleteSecondSnatIp = deleteSnatIp[0:1]
	}

	addFirstSnatIp := addSnatIp
	addSecondSnatIp := make([]*clb.SnatIp, 0)
	if len(addSnatIp) == 10 {
		addFirstSnatIp = addSnatIp[1:]
		addSecondSnatIp = addSnatIp[0:1]
	}

	return addFirstSnatIp, addSecondSnatIp, deleteFirstSnatIp, deleteSecondSnatIp
}

func (lbc *LoadBalancerController) ensureIngressLBListenerDelete(sc *tencent.SyncContext, ingress types.Ingress) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	// 查询当前LB状态
	loadbalancer := sc.LoadBalancerContext.LoadBalancer
	region := sc.LoadBalancerContext.Region
	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return err
	}

	listenersOwnedByOthers := tencent.GetLoadBalancerResourceListenerWithoutIngress(ingress, sc.LoadBalancerContext.LoadBalancerResource)
	metas := lo.SliceToMap(listenersOwnedByOthers, func(lis *lbrv1alpha1.LoadBalancerResourceListener) (types.ListenerMeta, struct{}) {
		return types.ListenerMeta{
			Protocol: strings.ToUpper(lis.Protocol),
			Port:     int(lis.Port),
		}, struct{}{}
	})
	_, listenersToDel := diffListeners(sc.IngressContext.IngressListeners, listeners)
	shouldDelete := func(lis *clb.Listener) bool {
		meta := types.ListenerMeta{
			Protocol: strings.ToUpper(*lis.Protocol),
			Port:     int(*lis.Port),
		}
		_, ownedByOthers := metas[meta]
		return *lis.ListenerName == TKEDedicatedListenerName &&
			lo.Contains(listenersToDel, meta) &&
			!ownedByOthers
	}

	for _, listener := range listeners {
		if shouldDelete(listener) {
			listenerChanged := false
			rewrites, err := sc.LoadBalancerContext.GetRewrites(sc)
			if err != nil {
				return err
			}
			needDeleteRewrite := make([]*clb.RuleOutput, 0)
			for index, rewrite := range rewrites {
				if *rewrite.ListenerId == *listener.ListenerId || *rewrite.RewriteTarget.TargetListenerId == *listener.ListenerId {
					needDeleteRewrite = append(needDeleteRewrite, rewrites[index])
				}
			}
			if sc.IngressContext.IsRewriteSupport {
				if len(needDeleteRewrite) > 0 {
					if err := tencent.DeleteRewrites(sc, ingress, loadbalancer, region, tencent.ConvertToRewriteRules(needDeleteRewrite)); err != nil {
						return err
					}
					if err := sc.LoadBalancerContext.UpdateRewrites(sc); err != nil {
						return err
					}
				}
				if err := tencent.DeleteLoadBalancerListener(sc, ingress, *loadbalancer.LoadBalancerId, region, *listener.ListenerId); err != nil {
					return err
				}
				listenerChanged = true
			} else {
				if len(needDeleteRewrite) == 0 {
					if err := tencent.DeleteLoadBalancerListener(sc, ingress, *loadbalancer.LoadBalancerId, region, *listener.ListenerId); err != nil {
						return err
					}
					listenerChanged = true
				}
			}

			if listenerChanged {
				if err := sc.LoadBalancerContext.UpdateListeners(sc); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// 1. 删除不需要的重定向规则
// 2. 同步用户配置的转发规则
// 3. 添加手动或自动的重定向规则
func (lbc *LoadBalancerController) ensureIngressLBListenerRules(sc *tencent.SyncContext, ing types.Ingress) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	for _, ingressListener := range sc.IngressContext.IngressListeners {
		if err := lbc.ensureIngressLBListenerRule(sc, ing, ingressListener, true); err != nil {
			return err
		}
	}

	if sc.IngressContext.IsRewriteSupport {
		if err := lbc.ensureIngressListenerRewriteDelete(sc, ing); err != nil {
			return err
		}
	}

	for _, ingressListener := range sc.IngressContext.IngressListeners {
		if err := lbc.ensureIngressLBListenerRuleDelete(sc, ing, ingressListener); err != nil {
			return err
		}
	}

	if sc.IngressContext.IsRewriteSupport {
		if err := lbc.ensureIngressListenerRewriteCreate(sc, ing); err != nil {
			return err
		}
	}
	for _, ingressListener := range sc.IngressContext.IngressListeners {
		if err := lbc.ensureRewriteRuleTargets(sc, ing, ingressListener); err != nil {
			return err
		}
	}
	return nil
}

func (lbc *LoadBalancerController) ensureIngressLBListenerOnlyRules(sc *tencent.SyncContext, ing types.Ingress) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	for _, ingressListener := range sc.IngressContext.IngressListeners {
		if err := lbc.ensureIngressLBListenerRule(sc, ing, ingressListener, false); err != nil {
			return err
		}
	}

	if sc.IngressContext.IsRewriteSupport {
		if err := lbc.ensureIngressListenerRewriteDelete(sc, ing); err != nil {
			return err
		}
	}

	for _, ingressListener := range sc.IngressContext.IngressListeners {
		if err := lbc.ensureIngressLBListenerRuleDelete(sc, ing, ingressListener); err != nil {
			return err
		}
	}

	if sc.IngressContext.IsRewriteSupport {
		if err := lbc.ensureIngressListenerRewriteCreate(sc, ing); err != nil {
			return err
		}
	}
	return nil
}

func (lbc *LoadBalancerController) ensureIngressLBListenerRule(sc *tencent.SyncContext, ingress types.Ingress, ingressListener *tencent.IngressListener, ensureTarget bool) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	region := sc.LoadBalancerContext.Region
	loadBalancer, err := sc.LoadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		return err
	}
	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return err
	}
	listenerKey := utils.GetListenerKey(ingressListener.Port, ingressListener.Protocol)
	listener := listeners[listenerKey]
	rules := ingressListener.IngressRules

	// 通过对比期望和现状，来得到要改变的对象
	toAdd, _, toModifyDomain, toModifyRule := diffRules(rules, listener, loadBalancer, sc.LoadBalancerContext.DefaultDomain)
	span.LogAny("rulesToAdd", spew.Sdump(toAdd))
	span.LogAny("rulesToModifyDomain", spew.Sdump(toModifyDomain))
	span.LogAny("rulesToModify", spew.Sdump(toModifyRule))

	listenerChange := false

	if len(toAdd) > 0 {
		klog.Infof("toAdd rule %s", jsonWrapper(toAdd))
		listenerChange = true
		// 如果创建rule报错则直接返回，否则如果继续同步到删除rule的逻辑会将已有的rule删除，导致增量没有成功但是存量也被删除的情况
		if err = func() error {
			errList := []error{}
			if err1 := tencent.CreateRules(sc, ingress, *loadBalancer.LoadBalancerId, sc.LoadBalancerContext.Region, *listener.ListenerId, toAdd); err1 != nil { // O(N) + 轮询
				// 如果一次性create报错，我们希望尝试分批create
				if ingressErr, ok := lo.ErrorsAs[*types.Error](err1); ok {
					// 如果是超限错误，不要直接返回，因为我们不想阻塞后续流程(后续的 delete rule 等操作)。
					if ingressErr.ErrorCode.Code == errcode.RuleLimitExceeded.Code {
						sc.Errors = append(sc.Errors, err1)
					} else {
						errList = append(errList, err1)
					}
				}

				if len(toAdd) > 1 {
					// 如果有声明多个转发规则，尝试独立创建每一个转发规则. 不管成功还是失败。
					for index := range toAdd {
						if err2 := tencent.CreateRules(sc, ingress, *loadBalancer.LoadBalancerId, sc.LoadBalancerContext.Region, *listener.ListenerId, toAdd[index:index+1]); err2 != nil {
							if ingressErr, ok := lo.ErrorsAs[*types.Error](err2); ok {
								// 如果超过数量限制，不要直接返回，不要阻塞后续流程，但放入 syncContext.Errors 这样整体流程会进行重试。
								if ingressErr.ErrorCode.Code == errcode.RuleLimitExceeded.Code {
									sc.Errors = append(sc.Errors, err2)
									break
								}
							}
							errList = append(errList, err2) // 其他错误都视为致命性错误，中断外部流程。但还是希望能把所有rule遍历add一遍
						}
					}
				}
			}
			if total := len(errList); total > 0 {
				return errList[total-1]
			}
			return nil
		}(); err != nil {
			return err
		}
	}

	if len(toModifyDomain) > 0 {
		listenerChange = true
		for _, modify := range toModifyDomain {
			if _, err := tencentapi.Instance.ModifyDomainAttributes(cloudctx.From(sc, ingress, region), modify); err != nil {
				if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
					if sdkError.Code == "InvalidParameter" {
						if strings.Contains(sdkError.Message, "status error") {
							sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateStatusError, sdkError.Error(), utils.IngressName(ingress)))
							continue
						} else if strings.Contains(sdkError.Message, "is not SVR type") {
							sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateTypeError, sdkError.Error(), utils.IngressName(ingress)))
							continue
						} else if strings.Contains(sdkError.Message, "is out of date") {
							sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateOutOfDateError, sdkError.Error(), utils.IngressName(ingress)))
							continue
						}
					} else if sdkError.Code == "InvalidParameterValue" {
						if strings.Contains(sdkError.Message, "CertList algorithm types cannot be repeated") { // 双证书，加密算法重复时报错捕获
							sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateAlgorithmTypeError, sdkError.Error(), utils.IngressName(ingress)))
							continue
						}
					} else if sdkError.Code == "InvalidParameterValue.Length" {
						if strings.Contains(sdkError.Message, "The length of CertificateId") {
							sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateFormatError, sdkError.Error(), utils.IngressName(ingress)))
							continue
						}
					} else if sdkError.Code == "FailedOperation.CertificateNotFound" || (sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "Query certificate")) {
						sc.Errors = append(sc.Errors, types.NewError(errcode.CertificateNotFoundError, sdkError.Error(), utils.IngressName(ingress)))
						continue
					}
				}
				sc.Errors = append(sc.Errors, err)
			}
		}
	}
	if len(toModifyRule) > 0 {
		listenerChange = true
		for _, modify := range toModifyRule {
			if _, err := tencentapi.Instance.ModifyRule(cloudctx.From(sc, ingress, region), modify); err != nil {
				if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
					// rule维度没有证书参数，无需捕获证书异常
					if sdkError.Code == "InvalidParameterValue" {
						if strings.Contains(sdkError.Message, "HealthCheck.HttpCheckDomain is illegal") {
							sc.Errors = append(sc.Errors, types.NewError(errcode.InvalidHealthCheckDomainError, sdkError.Error(), ingress.String()))
						} else {
							sc.Errors = append(sc.Errors, types.NewError(errcode.TkeServiceConfigInvalidError, sdkError.Error(), utils.IngressName(ingress)))
						}
						continue
					}
				}
				sc.Errors = append(sc.Errors, err)
			}
		}
	}

	// update ruleHosts and combineRules after Add L7LbRules
	if listenerChange {
		if err := sc.LoadBalancerContext.UpdateListeners(sc); err != nil {
			return err
		}
		listener = sc.LoadBalancerContext.Listeners[listenerKey]
	}

	if ensureTarget {
		if err = lbc.ensureRuleTargets(sc, ingress, ingressListener); err != nil {
			return err
		}
	}
	return nil
}

func (lbc *LoadBalancerController) ensureIngressLBListenerRuleDelete(sc *tencent.SyncContext, ingress types.Ingress, ingressListener *tencent.IngressListener) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	loadBalancer, err := sc.LoadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		return err
	}
	listenerKey := utils.GetListenerKey(ingressListener.Port, ingressListener.Protocol)
	listener := sc.LoadBalancerContext.Listeners[listenerKey]
	rules := ingressListener.IngressRules

	// 通过对比期望和现状，来得到要改变的对象
	_, toDelete, _, _ := diffRules(rules, listener, loadBalancer, sc.LoadBalancerContext.DefaultDomain)
	listenerChange := false

	if sc.IngressContext.IsRewriteSupport {
		rewrites, err := sc.LoadBalancerContext.GetRewrites(sc)
		if err != nil {
			return err
		}
		if err = lbc.deleteRewriteLocation(sc, ingress, loadBalancer, sc.LoadBalancerContext.Region, listener, rewrites, toDelete); err != nil {
			return err
		}
	} else {
		if len(toDelete) > 0 {
			rewrites, err := sc.LoadBalancerContext.GetRewrites(sc)
			if err != nil {
				return err
			}
			if toDelete, err = lbc.filterRewriteLocation(ingress, listener, rewrites, toDelete); err != nil {
				klog.Errorf("filterRewriteLocation error, %v", err)
				return err
			}
		}
	}

	if len(toDelete) > 0 {
		klog.Infof("toDelete rule %v", toDelete)
		listenerChange = true
		if err := tencent.DeleteRules(sc, ingress, *loadBalancer.LoadBalancerId, sc.LoadBalancerContext.Region, *listener.ListenerId, toDelete); err != nil {
			return err
		}
	}

	// update ruleHosts and combineRules after Add L7LbRules
	if listenerChange {
		if err := sc.LoadBalancerContext.UpdateListeners(sc); err != nil {
			return err
		}
	}
	return nil
}

// kateway: 清除转发源规则下面注册的后端RS，例如"www.test.com/a" 下面注册了一些RS，现在用户配置"www.test.com/a" 转发到/b，那注册的RS需要解绑
func (lbc *LoadBalancerController) ensureRewriteRuleTargets(sc *tencent.SyncContext, ingress types.Ingress, listener *tencent.IngressListener) error {
	if listener.IngressRewriteRules == nil || len(listener.IngressRewriteRules) == 0 {
		return nil
	}

	ruleHosts, err := sc.LoadBalancerContext.GetListenersBackend(sc)
	if err != nil {
		return err
	}

	allDeleteList := make([]*clb.BatchTarget, 0)
	if listenerBackend, exist := ruleHosts[utils.GetListenerKey(listener.Port, listener.Protocol)]; exist {
		targets := make(map[string]*clb.RuleTargets)
		for index, rule := range listenerBackend.Rules {
			targets[ruleTo(*rule.Domain, *rule.Url)] = listenerBackend.Rules[index]
		}

		for _, ingressRewriteRules := range listener.IngressRewriteRules {
			if ruleTargets, exist := targets[ruleTo(ingressRewriteRules.Host, ingressRewriteRules.RealPath)]; exist {
				if len(ruleTargets.Targets) != 0 {
					toDeleteList := make([]*clb.BatchTarget, 0)
					for _, target := range ruleTargets.Targets {
						if utils.GetBackendManagementMode(ingress) == utils.BackendManagementModeTag && !types.ManagedTarget(target.Tag, config.Global.ClusterName) {
							continue
						}
						if *target.Type == string(utils.CVM) {
							toDeleteList = append(toDeleteList, NewCVMBatchTarget(*listenerBackend.ListenerId, *ruleTargets.LocationId, *target.InstanceId, *target.Port))
						} else {
							toDeleteList = append(toDeleteList, NewENIBatchTarget(*listenerBackend.ListenerId, *ruleTargets.LocationId, *target.PrivateIpAddresses[0], *target.Port))
						}
					}
					allDeleteList = append(allDeleteList, toDeleteList...)
				}
			}
		}
	}

	if err := tencent.BatchDeregisterTarget(sc, ingress, sc.LoadBalancerContext.LoadBalancerId, sc.LoadBalancerContext.Region, allDeleteList); err != nil {
		// if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
		//	if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "is targetgroup mode") {
		//		return types.NewError("E4037", "Listener is target group mode.", sdkError.Error())
		//	}
		// }
		return err
	} else if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) && len(allDeleteList) > 0 {
		// 所有解绑或绑定成功的targets，可能需要修改相应节点的保护finalizer
		targetsToEnqueue := make([]*clb.BatchTarget, 0)
		var nodesByID map[string]*v1.Node
		if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			nodesByID, err = services.GetNodesByID(cluster_service.Instance.NodeLister())
			if err != nil {
				return err
			}
		}
		targetsToEnqueue = append(targetsToEnqueue, allDeleteList...)
		cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueTargets(targetsToEnqueue, nodesByID)
	}

	return nil
}

// 如果一个 rule存在rewrite，则过滤掉
func (lbc *LoadBalancerController) filterRewriteLocation(ing types.Ingress, loadBalancer *clb.Listener, rewriteSets []*clb.RuleOutput, locationIds []string) ([]string, error) {
	if len(locationIds) == 0 {
		return []string{}, nil
	}

	var filtered []string
	rewriteSetMap := make(map[string]*clb.RuleOutput, 0)
	for _, r := range rewriteSets {
		rewriteSetMap[*r.LocationId] = r
		if r.RewriteTarget != nil && r.RewriteTarget.TargetLocationId != nil {
			rewriteSetMap[*r.RewriteTarget.TargetLocationId] = r
		}
	}
	for _, id := range locationIds {
		if _, ok := rewriteSetMap[id]; !ok {
			filtered = append(filtered, id)
		}
	}

	return filtered, nil
}

func (lbc *LoadBalancerController) deleteRewriteLocation(ctx context.Context, ingress types.Ingress,
	loadBalancer *clb.LoadBalancer, region string, listener *clb.Listener, rewriteSets []*clb.RuleOutput, toDelete []string) error {
	toDeleteMap := make(map[string]bool)
	for _, item := range toDelete {
		toDeleteMap[item] = true
	}

	deleteRewrite := make([]*clb.RuleOutput, 0)
	// 1. 定向到其他转发规则的重定向规则
	// 2. 被其他转发规则指向的重定向规则
	for index, rewrite := range rewriteSets {
		if _, exist := toDeleteMap[*rewrite.LocationId]; exist && *rewrite.ListenerId == *listener.ListenerId {
			deleteRewrite = append(deleteRewrite, rewriteSets[index])
		}
		if _, exist := toDeleteMap[*rewrite.RewriteTarget.TargetLocationId]; exist && *rewrite.RewriteTarget.TargetListenerId == *listener.ListenerId {
			deleteRewrite = append(deleteRewrite, rewriteSets[index])
		}
	}
	if len(deleteRewrite) != 0 {
		if err := tencent.DeleteRewrites(ctx, ingress, loadBalancer, region, tencent.ConvertToRewriteRules(deleteRewrite)); err != nil {
			return err
		}
	}
	return nil
}

func (lbc *LoadBalancerController) ensureIngressListenerRewriteDelete(sc *tencent.SyncContext, ingress types.Ingress) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	_, toDelRewriteKeys, err := diffRewrite(sc)
	if err != nil {
		return err
	}

	changed := false
	if toDelRewriteKeys.Len() > 0 {
		if err := tencent.DeleteRewrites(sc, ingress, sc.LoadBalancerContext.LoadBalancer, sc.LoadBalancerContext.Region,
			tencent.ChangeToRewriteRules(toDelRewriteKeys)); err != nil {
			sc.Errors = append(sc.Errors, types.NewError(errcode.UnexpectedError, err.Error()))
			return nil
		}
		changed = true
	}

	if changed {
		if err := sc.LoadBalancerContext.UpdateRewrites(sc); err != nil {
			return err
		}
	}
	return nil
}

func (lbc *LoadBalancerController) ensureIngressListenerRewriteCreate(sc *tencent.SyncContext, ingress types.Ingress) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	loadBalancer := sc.LoadBalancerContext.LoadBalancer
	region := sc.LoadBalancerContext.Region
	changed := false
	if isAutoRewrite, _ := utils.IsAutoRewrite(ingress); isAutoRewrite {
		listeners, err := sc.LoadBalancerContext.GetListeners(sc)
		if err != nil {
			return err
		}

		if listener, exist := listeners[utils.GetListenerKey(443, net.ProtocolHTTPS)]; !exist { // 用户开启了自动重定向却没有声明HTTPS协议的监听器？
			// TODO misakazhou
		} else {
			needAutoRewrite, err := needAutoRewrite(sc)
			if err != nil {
				return err
			}
			if needAutoRewrite {
				request := clb.NewAutoRewriteRequest()
				request.LoadBalancerId = loadBalancer.LoadBalancerId
				request.ListenerId = listener.ListenerId
				if _, err := tencentapi.Instance.AutoRewrite(cloudctx.From(sc, ingress, sc.LoadBalancerContext.Region), request); err != nil {
					return err
				}
				changed = true
			}
		}
	} else {
		toAddRewriteKeys, _, err := diffRewrite(sc)
		if err != nil {
			return err
		}

		if toAddRewriteKeys.Len() > 0 {
			if err := tencent.CreateRewrites(sc, ingress, loadBalancer, region, tencent.ChangeToRewriteRules(toAddRewriteKeys)); err != nil {
				return err
			}
			changed = true
		}
	}

	if changed {
		if err := sc.LoadBalancerContext.UpdateRewrites(sc); err != nil {
			return err
		}
	}
	return nil
}

func needAutoRewrite(sc *tencent.SyncContext) (bool, error) {
	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return false, err
	}
	sss := make(map[string]string)
	for _, listener := range listeners {
		for _, rule := range listener.Rules {
			sss[ruleToPath2(*listener.Port, *rule.Domain, *rule.Url)] = location(*listener.ListenerId, *rule.LocationId)
		}
	}
	rewrites, err := sc.LoadBalancerContext.GetRewrites(sc)
	if err != nil {
		return false, err
	}
	currentRewriteSet := make(sets.String)
	for _, rewrite := range rewrites {
		currentRewriteSet.Insert(rewritePath(*rewrite.ListenerId, *rewrite.LocationId, *rewrite.RewriteTarget.TargetListenerId, *rewrite.RewriteTarget.TargetLocationId))
	}
	for _, ingressListener := range sc.IngressContext.IngressListeners {
		for _, rewriteRule := range ingressListener.IngressRewriteRules {
			if rewriteRule.Rewrite.AutoCreate {
				from, exist := sss[ruleToPath2(int64(ingressListener.Port), rewriteRule.Host, rewriteRule.RealPath)]
				if !exist {
					return true, nil
				}
				to, exist := sss[ruleToPath2(int64(rewriteRule.Rewrite.Port), rewriteRule.Rewrite.Host, rewriteRule.Rewrite.RealPath)]
				if !exist {
					continue
				}
				if !currentRewriteSet.Has(fmt.Sprintf("%s_%s", from, to)) {
					return true, nil
				}
			}
		}
	}
	return false, nil
}

func diffRewrite(sc *tencent.SyncContext) (sets.String, sets.String, error) {
	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return nil, nil, err
	}
	sss := make(map[string]string)
	for _, listener := range listeners {
		for _, rule := range listener.Rules {
			sss[ruleToPath2(*listener.Port, *rule.Domain, *rule.Url)] = location(*listener.ListenerId, *rule.LocationId)
		}
	}
	rewrites, err := sc.LoadBalancerContext.GetRewrites(sc)
	if err != nil {
		return nil, nil, err
	}
	currentRewriteSet := make(sets.String)
	for _, rewrite := range rewrites {
		currentRewriteSet.Insert(rewritePath(*rewrite.ListenerId, *rewrite.LocationId, *rewrite.RewriteTarget.TargetListenerId, *rewrite.RewriteTarget.TargetLocationId))
	}
	desiredRewriteSet := make(sets.String)
	for _, ingressListener := range sc.IngressContext.IngressListeners {
		for _, rewriteRule := range ingressListener.IngressRewriteRules {
			from, exist := sss[ruleToPath2(int64(ingressListener.Port), rewriteRule.Host, rewriteRule.RealPath)]
			if !exist {
				continue
			}
			to, exist := sss[ruleToPath2(int64(rewriteRule.Rewrite.Port), rewriteRule.Rewrite.Host, rewriteRule.Rewrite.RealPath)]
			if !exist {
				continue
			}
			desiredRewriteSet.Insert(fmt.Sprintf("%s_%s", from, to))
		}
	}
	toAddRewriteKeys := desiredRewriteSet.Difference(currentRewriteSet)
	toDelRewriteKeys := currentRewriteSet.Difference(desiredRewriteSet)
	return toAddRewriteKeys, toDelRewriteKeys, nil
}

type CombineRule struct {
	IngRule *utils.Rule      `json:"IngRule,omitempty"`
	LbRule  *clb.RuleTargets `json:"LbRule,omitempty"`
}

// 处理ingRules绑定后端的逻辑
// ingRules 		目标 Rules
// lbRules      	根据 Backend Target 查询获得的 Rules
// lbRuleNoHost 	根据 LB Listeners 查询获得的 Rules
func CombineRules(ingRules []*utils.Rule, lbRules []*clb.RuleTargets, lbRuleNoHost []*clb.RuleOutput) ([]CombineRule, error) {
	ingRuleMap := make(map[string]*utils.Rule, 0)
	lbRuleMap := make(map[string]*clb.RuleTargets, 0)
	lbRuleNoHostMap := make(map[string]*clb.RuleOutput, 0)

	for index, r := range ingRules {
		if r.Backend != nil && !IgnoreRule(r) {
			ingRuleMap[getKeyFromRule(r.Host, r.RealPath)] = ingRules[index]
		}
	}
	for index, r := range lbRules { // CLB DescribeTargets
		lbRuleMap[getKeyFromRule(*r.Domain, *r.Url)] = lbRules[index]
	}
	for index, r := range lbRuleNoHost { // CLB DescribeListeners
		lbRuleNoHostMap[getKeyFromRule(*r.Domain, *r.Url)] = lbRuleNoHost[index]
	}

	combineRules := make([]CombineRule, 0)
	for k, ingRule := range ingRuleMap {
		if lbRuleNoHost, lbRuleNoHostOk := lbRuleNoHostMap[k]; lbRuleNoHostOk {
			if lbRule, lbRuleOk := lbRuleMap[k]; lbRuleOk { // 如果存在直接传入combineRules
				combineRules = append(combineRules, CombineRule{
					IngRule: ingRule,
					LbRule:  lbRule,
				})
			} else { // 如果有这个rule但是查询rs的时候却不存在,证明没有绑定rs
				combineRules = append(combineRules, CombineRule{
					IngRule: ingRule,
					LbRule: &clb.RuleTargets{
						Targets:    []*clb.Backend{},
						Domain:     lbRuleNoHost.Domain,
						LocationId: lbRuleNoHost.LocationId,
						Url:        lbRuleNoHost.Url,
					},
				})
			}
		} else { // 如果这条rule在LB中根本不存在则报错
			return nil, types.NewError(errcode.RuleNotExistedInLBWhenEnsuringTargets, fmt.Sprintf("key: %s not in LB rules", k))
		}
	}
	return combineRules, nil
}

type Target struct {
	Node        *v1.Node
	Target      string
	Weight      *int64
	Pod         *v1.Pod
	Port        int
	BackendType utils.BackendType
}

func (t *Target) Isolating() bool {
	if t.Pod != nil {
		for _, condition := range t.Pod.Status.Conditions {
			if condition.Type == "platform.tkex/debug-pod" && condition.Status == "False" {
				return true
			}
		}
	}
	return false
}

func (t *Target) LocalUpgrading() bool {
	if t.Pod != nil {
		for _, condition := range t.Pod.Status.Conditions {
			if condition.Type == "platform.tkex/InPlace-Update-Ready" && condition.Status == "False" {
				return true
			}
		}
	}
	return false
}

type Targets []*Target

func (s Targets) Len() int { return len(s) }

func (s Targets) Less(i, j int) bool { return s[i].Target < s[j].Target }

func (s Targets) Swap(i, j int) { s[i], s[j] = s[j], s[i] }

type RuleTarget struct {
	Skipped         bool
	Service         *v1.Service
	Targets         []*Target
	ExcludedTargets map[string]bool

	AllDown bool
}

func (r *RuleTarget) FakeAllDown() bool {
	if r.AllDown {
		for _, target := range r.Targets {
			if target.Isolating() || target.LocalUpgrading() {
				return true
			}
		}
	}
	return false
}

func SkipRuleResult() *RuleTarget {
	return &RuleTarget{
		Skipped: true,
	}
}

func processTargets(sc *tencent.SyncContext, ing types.Ingress, listener *clb.Listener, combinedRules []CombineRule,
	expectTargetByBackendKey map[string]*RuleTarget) types.ProcessedTargetsBundle {

	lbId := *sc.LoadBalancerContext.LoadBalancer.LoadBalancerId
	bundle := types.ProcessedTargetsBundle{}
	targetListToDelete := list.New()
	targetListToAdd := list.New()
	targetListToUpdate := list.New()
	for _, rule := range combinedRules {
		ruleTarget, ok := expectTargetByBackendKey[ruleBackendToString(*rule.LbRule.LocationId, rule.IngRule.Backend)]
		if !ok || ruleTarget.Skipped {
			continue
		}

		toDelete, toAdd, toUpdate := diffTarget(utils.GetBackendManagementMode(ing), listener, ruleTarget, rule.LbRule)
		targetListToDelete.PushBackList(toDelete)
		targetListToAdd.PushBackList(toAdd)
		targetListToUpdate.PushBackList(toUpdate)
	}
	bundle.TargetsToUpdate = convertRsWeightRule(targetListToUpdate)

	// 记录当前每条规则下绑定了多少个target
	ruleByLocationID := lo.SliceToMap(combinedRules, func(r CombineRule) (string, CombineRule) {
		return *r.LbRule.LocationId, r
	})
	targetsToAdd := convert(targetListToAdd)
	targetsToAddByLocationID := lo.GroupBy(targetsToAdd, func(t *clb.BatchTarget) string { return *t.LocationId })
	bundle.TargetsToDelete = convert(targetListToDelete)
	targetsToDeleteByLocationID := lo.GroupBy(bundle.TargetsToDelete, func(t *clb.BatchTarget) string { return *t.LocationId })

	quotaInUse := config.Global.BackendQuotaInUse.GetQuotaInUse(lbId)
	processor := types.NewListenerTargetsProcessor(quotaInUse)
	for locationID, targets := range targetsToAddByLocationID {
		r := ruleByLocationID[locationID]
		// 当前期望的总量，即当前已绑定的数量-即将删除的数量+期望增加的数量
		expectTotal := len(r.LbRule.Targets) - len(targetsToDeleteByLocationID[locationID]) + len(targets)
		processed := processor.ProcessAddition(sc, len(r.LbRule.Targets), len(targetsToDeleteByLocationID[locationID]), targets)
		bundle.TargetsToAdd.BeforeDeletion = append(bundle.TargetsToAdd.BeforeDeletion, processed.BeforeDeletion...)
		bundle.TargetsToAdd.AfterDeletion = append(bundle.TargetsToAdd.AfterDeletion, processed.AfterDeletion...)
		if processed.Len() < len(targets) {
			if types.CVMTargetsOnly(targets) {
				sc.Errors = append(sc.Errors, types.NewError(errcode.NodeRSLimitExceeded, "",
					r.IngRule.GetHostPath(), expectTotal, quotaInUse))
			} else {
				sc.Errors = append(sc.Errors, types.NewError(errcode.RSLimitExceeded, "",
					r.IngRule.GetHostPath(), expectTotal, quotaInUse))
			}
		}
	}
	return bundle
}

func (lbc *LoadBalancerController) ensureRuleTargets(sc *tencent.SyncContext, ing types.Ingress, ingressListener *tencent.IngressListener) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	loadBalancer, err := sc.LoadBalancerContext.GetLoadBalancer(sc)

	if err != nil {
		return err
	}
	region := sc.LoadBalancerContext.Region

	listenerKey := utils.GetListenerKey(ingressListener.Port, ingressListener.Protocol)

	// 获取当前LB下的后端
	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return err
	}
	ruleHosts, err := sc.LoadBalancerContext.GetListenersBackend(sc)
	if err != nil {
		return err
	}
	combineRules, err := CombineRules(ingressListener.IngressRules, ruleHosts[listenerKey].Rules, listeners[listenerKey].Rules) // 实际情况
	if err != nil {
		return err
	}

	directService := make([]CombineRule, 0)
	noDirectService := make([]CombineRule, 0)

	// 遍历combineRules里面的ingressRule,每个规则对应一个backend是一个service，检查这个规则是不是需要直通
	for index, rules := range combineRules {
		backend := rules.IngRule.Backend
		svc, err := cluster_service.Instance.ServiceLister().Services(ing.Namespace()).Get(backend.ServiceName())
		if err != nil {
			if k8serrors.IsNotFound(err) {
				if !utils.BackendOnlyIngress(ing) {
					sc.Errors = append(sc.Errors, types.NewError(errcode.ServiceNotFoundError, "", utils.IngressName(ing), fmt.Sprintf("%s/%s", ing.Namespace(), backend.ServiceName())))
				}
				continue
			}
			klog.Errorf("ServiceLister get error for service %s/%s. %v", ing.Namespace(), backend.ServiceName(), err)
			return err
		}
		// 根据service或者ingress的注解，判断是否属于CLB直通
		if utils.IsDirectAccessIngressCascade(ing, svc) {
			directService = append(directService, combineRules[index])
		} else {
			noDirectService = append(noDirectService, combineRules[index])
		}
	}

	backendInstanceMap := make(map[string]*RuleTarget)
	// backendInstanceMapENI: map[string]RuleTarget
	backendInstanceMapENI, err := lbc.getBackendInstanceMapENI(sc, ing, loadBalancer, directService)
	if err != nil {
		return err
	}
	if err := makeWeightForENI(sc, combineRules, backendInstanceMapENI, ing, listeners[listenerKey]); err != nil {
		return err
	}
	for key, value := range backendInstanceMapENI {
		backendInstanceMap[key] = value
	}

	backendInstanceMapNodePort, err := lbc.getBackendInstanceMapNodePort(sc, ing, loadBalancer, region, noDirectService)
	if err != nil {
		return err
	}

	backendInstanceMapEksENI, err := lbc.getBackendInstanceMapENI(sc, ing, loadBalancer, noDirectService)
	if err != nil {
		return err
	}
	if err := makeWeightForENI(sc, combineRules, backendInstanceMapEksENI, ing, listeners[listenerKey]); err != nil {
		return err
	}
	// 筛选纯EKS Pod
	for _, backendInstance := range backendInstanceMapEksENI {
		targets := make([]*Target, 0)
		for index, target := range backendInstance.Targets {
			node, err := cluster_service.Instance.NodeLister().Get(target.Pod.Spec.NodeName)
			if err != nil {
				klog.Errorf("%s/%s failed to get node of pod(%s), for %s", ing.Namespace(), ing.Name(), target.Target, err)
				continue
			}
			if utils.IsEKSNode(node) {
				targets = append(targets, backendInstance.Targets[index])
			}
		}
		backendInstance.Targets = targets
	}
	for key, value := range backendInstanceMapNodePort {
		backendInstanceMap[key] = value
	}
	// For noDirectService, choose PODs as CLB RS when there is no Nodes.
	for key, value := range backendInstanceMapEksENI {
		if value == nil || value.Targets == nil || len(value.Targets) == 0 { // 降级也没有合适后端就不要降级了。
			continue
		}
		nodeports, exist := backendInstanceMapNodePort[key]
		if !exist || len(nodeports.Targets) == 0 {
			// 兜底逻辑，NodePort全部丢失，退化成EKS模式接入
			backendInstanceMap[key] = value
		} else {
			if nodeports.Service != nil && nodeports.Service.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal {
				// TKE Local场景，添加EKS Pod作为后端。
				backendInstanceMap[key].Targets = append(backendInstanceMap[key].Targets, value.Targets...)
				for k, v := range value.ExcludedTargets {
					backendInstanceMap[key].ExcludedTargets[k] = v
				}
			}
		}
	}

	// 检查是否存在 node port 单点风险
	var singleNodeRules []string
	for key, backendInstance := range backendInstanceMap {
		if len(backendInstance.Targets) == 1 &&
			(backendInstance.Targets[0].BackendType == utils.CVM || backendInstance.Targets[0].BackendType == utils.EVM) { // 普通节点或者原生节点, 排除掉 eks pod 单点
			singleNodeRules = append(singleNodeRules, key)
		}
	}
	if len(singleNodeRules) > 0 {
		event.Instance.EventError(ing, types.NewError(errcode.SingleNodeRisk, fmt.Sprintf("risky rules: %s", singleNodeRules)))
	}

	if err := checkAllDown(sc, combineRules, backendInstanceMap, ing, listeners[listenerKey]); err != nil {
		return err
	}

	// 按可用区调整权重
	customizedWeights, customizedAnnoErr := utils.IsEnableCustomizedWeight(ing)
	if customizedAnnoErr != nil {
		sc.Errors = append(sc.Errors, types.NewError(errcode.CustomizedWeightAnnotationError, "", utils.IngressName(ing)))
	} else {
		if customizedWeights != nil {
			for _, backendInstance := range backendInstanceMap {
				for _, target := range backendInstance.Targets {
					services.ModifyWeightByZones(target.Pod, target.Weight, customizedWeights.Zones)
				}
			}
		}
	}

	processed := processTargets(sc, ing, listeners[listenerKey], combineRules, backendInstanceMap)

	if len(processed.TargetsToUpdate) > 0 {
		if err := batchModifyTargetWeight(sc, ing, *loadBalancer.LoadBalancerId, sc.LoadBalancerContext.Region, processed.TargetsToUpdate); err != nil {
			isSuccess := false
			if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
				if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "not support") { // [TencentCloudSDKError] Code=FailedOperation, Message=not support evm / eks
					if err := modifyTargetWeight(sc, ing, *loadBalancer.LoadBalancerId, sc.LoadBalancerContext.Region, processed.TargetsToUpdate); err != nil {
						sc.Errors = append(sc.Errors, err)
					} else {
						isSuccess = true
					}
				}
			}
			if !isSuccess {
				sc.Errors = append(sc.Errors, err)
			}
		}
	}

	// 所有解绑或绑定成功的targets，可能需要修改相应节点的保护finalizer
	targetsToEnqueue := make([]*clb.BatchTarget, 0)
	var nodesByID map[string]*v1.Node
	if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) && (len(processed.TargetsToAdd.BeforeDeletion) > 0 || len(processed.TargetsToAdd.AfterDeletion) > 0 || len(processed.TargetsToDelete) > 0) {
		nodesByID, err = services.GetNodesByID(cluster_service.Instance.NodeLister())
		if err != nil {
			return err
		}
		for _, ruleTarget := range backendInstanceMap {
			for _, target := range ruleTarget.Targets {
				if target.Node != nil {
					nodesByID[target.Target] = target.Node
				}
			}
		}
	}

	defer func() {
		cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueTargets(targetsToEnqueue, nodesByID)
	}()
	// TODO misakazhou 批量接口失败的情况下，无法知道具体错误后端，需要有重试方案。
	if err := tencent.BatchRegisterTarget(sc, ing, *loadBalancer.LoadBalancerId, region, processed.TargetsToAdd.BeforeDeletion); err != nil { // Code=InvalidParameterValue, Message=listenerId lbl-c2entvws is targetgroup mode, RequestId=b3f2640f-c381-4f10-86be-64ff5eb560cf.
		// if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
		//	if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "is targetgroup mode") {
		//		return types.NewError("E4037", "Listener is target group mode.", sdkError.Error())
		//	}
		// }
		return err
	} else if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
		targetsToEnqueue = append(targetsToEnqueue, processed.TargetsToAdd.BeforeDeletion...)
	}

	if err := tencent.BatchDeregisterTarget(sc, ing, *loadBalancer.LoadBalancerId, region, processed.TargetsToDelete); err != nil {
		// if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
		//	if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "is targetgroup mode") {
		//		return types.NewError("E4037", "Listener is target group mode.", sdkError.Error())
		//	}
		// }
		return err
	} else if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
		targetsToEnqueue = append(targetsToEnqueue, processed.TargetsToDelete...)
	}

	if err := tencent.BatchRegisterTarget(sc, ing, *loadBalancer.LoadBalancerId, region, processed.TargetsToAdd.AfterDeletion); err != nil { // Code=InvalidParameterValue, Message=listenerId lbl-c2entvws is targetgroup mode, RequestId=b3f2640f-c381-4f10-86be-64ff5eb560cf.
		// if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
		//	if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "is targetgroup mode") {
		//		return types.NewError("E4037", "Listener is target group mode.", sdkError.Error())
		//	}
		// }
		return err
	} else if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
		targetsToEnqueue = append(targetsToEnqueue, processed.TargetsToAdd.AfterDeletion...)
		cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueTargets(targetsToEnqueue, nodesByID)
	}

	if processed.TargetsToAdd.Len()+len(processed.TargetsToUpdate)+len(processed.TargetsToDelete) > 0 {
		if err := sc.LoadBalancerContext.UpdateListenersBackend(sc); err != nil {
			return err
		}
	}

	// 如果开启了标签管理，需要更新后端 RS 标签
	if utils.NeedPatchRSTags(ing) {
		klog.Infof("ingress: %s/%s lb: %s need to patch rs tags", ing.Namespace(), ing.Name(), *loadBalancer.LoadBalancerId)
		return BatchModifyTargetTags(ing, region, *loadBalancer.LoadBalancerId, ingressListener)
	}

	return nil
}

func BatchModifyTargetTags(ingress types.Ingress, region string, lbID string, ingressListener *tencent.IngressListener) error {
	listeners, err := tencent.GetTargetInfo(context.Background(), ingress, lbID, region)
	if err != nil {
		return err
	}

	rsRules := []*clb.RsTagRule{}
	for _, listener := range listeners {
		if listener.Port != nil && listener.Protocol != nil && *listener.Port == int64(ingressListener.Port) && *listener.Protocol == ingressListener.Protocol {
			if net.IsL4Protocol(*listener.Protocol) {
				targetList := []*clb.Target{}
				for _, target := range listener.Targets {
					if needToPatch, newTag := ParseTag(target.Tag, config.Global.ClusterName); needToPatch {
						if target.Type == nil || *target.Type == "" {
							return fmt.Errorf("unexpected backend, backend type not exist")
						}
						if utils.IsENILikeType(*target.Type) {
							for _, privateIP := range target.PrivateIpAddresses {
								targetList = append(targetList, &clb.Target{
									EniIp:  privateIP,
									Port:   target.Port,
									Weight: target.Weight,
									Tag:    &newTag,
								})
							}
						} else if utils.IsCVMLikeType(*target.Type) {
							targetList = append(targetList, &clb.Target{
								InstanceId: target.InstanceId,
								Port:       target.Port,
								Weight:     target.Weight,
								Tag:        &newTag,
							})
						}
					}
				}
				if len(targetList) != 0 {
					rsRules = append(rsRules, &clb.RsTagRule{
						ListenerId: listener.ListenerId,
						Targets:    targetList,
					})
				}
			} else if net.IsL7Protocol(*listener.Protocol) {
				for _, rule := range listener.Rules {
					targetList := []*clb.Target{}
					for _, target := range rule.Targets {
						if needToPatch, newTag := ParseTag(target.Tag, config.Global.ClusterName); needToPatch {
							if target.Type == nil || *target.Type == "" {
								return fmt.Errorf("unexpected backend, backend type not exist")
							}
							if utils.IsENILikeType(*target.Type) {
								for _, privateIP := range target.PrivateIpAddresses {
									targetList = append(targetList, &clb.Target{
										EniIp:  privateIP,
										Port:   target.Port,
										Weight: target.Weight,
										Tag:    &newTag,
									})
								}
							} else if utils.IsCVMLikeType(*target.Type) {
								targetList = append(targetList, &clb.Target{
									InstanceId: target.InstanceId,
									Port:       target.Port,
									Weight:     target.Weight,
									Tag:        &newTag,
								})
							}
						}
					}
					if len(targetList) != 0 {
						rsRules = append(rsRules, &clb.RsTagRule{
							LocationId: rule.LocationId,
							ListenerId: listener.ListenerId,
							Targets:    targetList,
						})
					}
				}
			}
		}
	}

	if len(rsRules) != 0 {
		klog.Infof("ingress: %s/%s lb: %s try to patch rs tags with groups rsRuleTags: %d", ingress.Namespace(), ingress.Name(), lbID, len(rsRules))
		rsRuleGroups := SplitRSTagRulesGreedy(rsRules, tencent.TEMPMAXBATCHMODIFYREQUEST, tencent.MAXBATCHTARGETPERREQUEST)
		for _, rsGroup := range rsRuleGroups {
			modifyTargetTagRequest := clb.NewBatchModifyTargetTagRequest()
			modifyTargetTagRequest.LoadBalancerId = &lbID
			modifyTargetTagRequest.ModifyList = rsGroup

			_, err := tencentapi.Instance.BatchModifyTargetTag(cloudctx.New(ingress, region), modifyTargetTagRequest)
			if err != nil {
				klog.Errorf("ingress: %s/%s lb: %s patch rs tags failed with groups rsRuleTags: %d for %s", ingress.Namespace(), ingress.Name(), lbID, len(rsRules), err.Error())
				return err
			}
		}
		klog.Infof("ingress: %s/%s lb: %s patch rs tags successfully with groups rsRuleTags: %d", ingress.Namespace(), ingress.Name(), lbID, len(rsRules))
	}

	return nil
}

// SplitRSTagRulesGreedy 分割 RsTagRule 为多个组，每组最多 maxListenersPerGroup 个 Listener，每个 Listener 最多 maxBackendsPerListener 个 Targets
func SplitRSTagRulesGreedy(listeners []*clb.RsTagRule, maxListenersPerGroup, maxBackendsPerListener int) [][]*clb.RsTagRule {
	var groups [][]*clb.RsTagRule

	for _, listener := range listeners {
		// 如果 Listener 的 Backends 数量超过限制，分割 Backends
		if len(listener.Targets) > maxBackendsPerListener {
			for i := 0; i < len(listener.Targets); i += maxBackendsPerListener {
				end := i + maxBackendsPerListener
				if end > len(listener.Targets) {
					end = len(listener.Targets)
				}
				// 创建新的 Listener，只包含部分 Backends
				splitListener := &clb.RsTagRule{
					ListenerId: listener.ListenerId,
					Targets:    listener.Targets[i:end],
					Tag:        listener.Tag,
					LocationId: listener.LocationId,
				}
				groups = addToGroup(groups, splitListener, maxListenersPerGroup)
			}
		} else {
			groups = addToGroup(groups, listener, maxListenersPerGroup)
		}
	}

	return groups
}

func ParseTag(input *string, clusterID string) (bool, string) {
	clustertag := fmt.Sprintf("clusterId:%s", clusterID)

	if input == nil || *input == "" {
		return true, clustertag
	}

	pairs := strings.Split(*input, ";")
	clusterIdNotExists := true
	resultPairs := []string{}

	for _, pair := range pairs {
		if pair != "" {
			kv := strings.Split(pair, ":")
			if len(kv) == 2 {
				if kv[0] == "clusterId" {
					clusterIdNotExists = false
				}
				resultPairs = append(resultPairs, pair)
			}
		}
	}

	if clusterIdNotExists {
		resultPairs = append(resultPairs, clustertag)
	}

	return clusterIdNotExists, strings.Join(resultPairs, ";")
}

func addToGroup(groups [][]*clb.RsTagRule, listener *clb.RsTagRule, maxListenersPerGroup int) [][]*clb.RsTagRule {
	added := false
	for i := range groups {
		if len(groups[i]) < maxListenersPerGroup && !listenerIDExistsInGroup(groups[i], *listener.ListenerId) {
			groups[i] = append(groups[i], listener)
			added = true
			break
		}
	}
	if !added {
		groups = append(groups, []*clb.RsTagRule{listener})
	}
	return groups
}

func listenerIDExistsInGroup(group []*clb.RsTagRule, listenerId string) bool {
	for _, listener := range group {
		if listener.ListenerId == &listenerId {
			return true
		}
	}
	return false
}

func keySet(o map[string]*RuleTarget) string {
	d := make([]string, 0)
	for key, _ := range o {
		d = append(d, key)
	}
	return strings.Join(d, ",")
}

// batchRegisterTargets batch register targets
func batchModifyTargetWeight(ctx context.Context, ingress types.Ingress, lbID string, region string, targets []*clb.RsWeightRule) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	TEMPMAXBATCHMODIFYREQUEST := 100
	request := clb.NewBatchModifyTargetWeightRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	// 先根据ListenerId、LocationId做聚合
	rules := make(map[string]map[string]*clb.RsWeightRule) // Map ListenerId To (Map LocationId To RsWeightRule)
	for _, target := range targets {
		if _, exist := rules[*target.ListenerId]; !exist {
			rules[*target.ListenerId] = make(map[string]*clb.RsWeightRule)
		}
		locationId := ""
		if target.LocationId != nil {
			locationId = *target.LocationId
		}
		if _, exist := rules[*target.ListenerId][locationId]; !exist {
			rules[*target.ListenerId][locationId] = &clb.RsWeightRule{
				ListenerId: common.StringPtr(*target.ListenerId),
				Targets:    make([]*clb.Target, 0),
			}
			if locationId != "" {
				rules[*target.ListenerId][locationId].LocationId = common.StringPtr(locationId)
			}
		}
		rules[*target.ListenerId][locationId].Targets = append(rules[*target.ListenerId][locationId].Targets, target.Targets...)
	}

	// 拆分单个大块
	spilitRules := make([]*clb.RsWeightRule, 0)
	for _, rule := range rules {
		for _, singleRule := range rule {
			if len(singleRule.Targets) <= tencent.NEW_BATCH_TARGET_LIMIT {
				spilitRules = append(spilitRules, singleRule)
			} else {
				for i := 0; i < len(singleRule.Targets); i += tencent.NEW_BATCH_TARGET_LIMIT {
					targetGroup := singleRule.Targets[i:utils.Min(i+tencent.NEW_BATCH_TARGET_LIMIT, len(singleRule.Targets))]
					spilitRules = append(spilitRules, &clb.RsWeightRule{
						ListenerId: singleRule.ListenerId,
						LocationId: singleRule.LocationId,
						Targets:    targetGroup,
					})
				}
			}
		}
	}

	// 两个维度开始分组
	listenerCount := 0
	targetCount := 0
	resultRules := make([][]*clb.RsWeightRule, 1)
	resultRulesIndex := 0
	resultRules[resultRulesIndex] = make([]*clb.RsWeightRule, 0)
	for index, rule := range spilitRules {
		if listenerCount+1 <= TEMPMAXBATCHMODIFYREQUEST && targetCount+len(rule.Targets) <= tencent.NEW_BATCH_TARGET_LIMIT {
			listenerCount = listenerCount + 1
			targetCount = targetCount + len(rule.Targets)
			resultRules[resultRulesIndex] = append(resultRules[resultRulesIndex], spilitRules[index])
		} else { // 需要开启下一个分批
			resultRules = append(resultRules, make([]*clb.RsWeightRule, 0))
			resultRulesIndex = resultRulesIndex + 1
			listenerCount = 1
			targetCount = len(rule.Targets)
			resultRules[resultRulesIndex] = append(resultRules[resultRulesIndex], spilitRules[index])
		}
	}

	for _, resultRule := range resultRules {
		request.ModifyList = resultRule
		if _, err := tencentapi.Instance.BatchModifyTargetWeight(cloudctx.From(ctx, ingress, region), request); err != nil {
			if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
				// [TencentCloudSDKError] Code=FailedOperation, Message=eks-oljjepj0(cls-4g9b5b18) is "deleting", only support "normal" status, RequestId=2a5de623-a64a-46e4-ae91-e3e1f1ff0f4f
				// [TencentCloudSDKError] Code=FailedOperation, Message=eks-6ibc5eb6(cls-5vzjheeo) is "creating", only support "normal" status, RequestId=2879a6f1-6ac9-45ac-99a3-eaebd6a3fe34
				if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "eks") && strings.Contains(sdkError.Message, "only support \"normal\" status") {
					return types.NewError(errcode.EVMUpdatingError, sdkError.Error())
				}
			}
			return err
		}
	}
	return nil
}

func modifyTargetWeight(ctx context.Context, ingress types.Ingress, lbID string, region string, targets []*clb.RsWeightRule) error {
	// region, err := crossregion.CrossRegionServiceInstance.GetIngressRegion(ingress)
	// if err != nil {
	//	return err
	// }

	request := clb.NewModifyTargetWeightRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	for _, target := range targets {
		request.Weight = target.Weight
		request.ListenerId = target.ListenerId
		request.LocationId = target.LocationId

		MAXTARGETPERREQUEST := 20
		for i := 0; i < len(target.Targets); i += MAXTARGETPERREQUEST {
			targetGroup := target.Targets[i:utils.Min(i+MAXTARGETPERREQUEST, len(target.Targets))]
			request.Targets = targetGroup
			_, err := tencentapi.Instance.ModifyTargetWeight(cloudctx.From(ctx, ingress, region), request)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (lbc *LoadBalancerController) getBackendInstanceMapENI(sc *tencent.SyncContext, ing types.Ingress, loadBalancer *clb.LoadBalancer, combineRules []CombineRule) (map[string]*RuleTarget, error) {
	backendType := utils.GetBackendType(loadBalancer)
	backendInstanceMap := make(map[string]*RuleTarget) // Service to InstanceId
	for _, rule := range combineRules {
		backend := rule.IngRule.Backend
		backendKey := ruleBackendToString(*rule.LbRule.LocationId, backend)

		// combineRules中有重复的，跳过
		//
		// 提问：gokutang 为什么这里只要是backend存在了就要pass掉
		// 因为：misakazhou 因为每组ServiceName和ServicePort，其对应的后端是一样的。
		// 		Map中存在说明已经查询过，查询过一次之后可以避免重复查询。
		if _, exist := backendInstanceMap[backendKey]; exist {
			continue
		}

		if rule.IngRule.TargetPort == nil { // 目标端口找不到说明这个后端配置有问题，跳过该后端的同步
			backendInstanceMap[backendKey] = SkipRuleResult()
			continue
		}

		svc, err := cluster_service.Instance.ServiceLister().Services(ing.Namespace()).Get(backend.ServiceName())
		if err != nil {
			if k8serrors.IsNotFound(err) {
				if !utils.BackendOnlyIngress(ing) {
					sc.Errors = append(sc.Errors, types.NewError(errcode.ServiceNotFoundError, "", utils.IngressName(ing), fmt.Sprintf("%s/%s", ing.Namespace(), backend.ServiceName())))
				}
				backendInstanceMap[backendKey] = SkipRuleResult()
				continue
			}
			klog.Errorf("ServiceLister get error for service %s/%s. %v", ing.Namespace(), backend.ServiceName(), err)
			return nil, err
		}
		var notReadyEndpoint = make(map[string]bool)
		var normalEndpoint = make(map[string]bool)

		endpoints, err := cluster_service.Instance.EndpointsLister().Endpoints(svc.Namespace).Get(svc.Name)
		if err != nil {
			if k8serrors.IsNotFound(err) {
				if !utils.BackendOnlyIngress(ing) {
					sc.Errors = append(sc.Errors, types.NewError(errcode.ServiceEndpointNotFoundError, "",
						utils.IngressName(ing), fmt.Sprintf("%s/%s", ing.Namespace(), backend.ServiceName())))
				}
				backendInstanceMap[backendKey] = SkipRuleResult()
				continue
			}
			klog.Errorf("EndpointLister get error for service %s. %v\n", utils.ServiceName(svc), err)
			return nil, err
		}
		if endpoints != nil {
			for _, subnet := range endpoints.Subsets {
				for _, notReadyAddr := range subnet.NotReadyAddresses {
					notReadyEndpoint[notReadyAddr.IP] = true
				}
				for _, normalAddr := range subnet.Addresses {
					normalEndpoint[normalAddr.IP] = true
				}
			}
		}
		endpointSlices := GetEndpointSlices(sc, svc)
		if endpointSlices != nil && len(endpointSlices) != 0 {
			for _, endpointSlice := range endpointSlices {
				for _, endpoint := range endpointSlice.Endpoints {
					if !((endpoint.Conditions.Serving != nil && *endpoint.Conditions.Serving == true) || (endpoint.Conditions.Ready != nil && *endpoint.Conditions.Ready == true)) {
						for _, address := range endpoint.Addresses {
							notReadyEndpoint[address] = true
						}
					} else {
						for _, address := range endpoint.Addresses {
							normalEndpoint[address] = true
						}
					}
				}
			}
		}

		pods := make([]*v1.Pod, 0)
		if pods, err = cluster_service.Instance.PodLister().Pods(ing.Namespace()).List(labels.SelectorFromSet(svc.Spec.Selector)); err != nil {
			klog.Errorf("PodLister get error for service %s. %v\n", utils.ServiceName(svc), err)
			return nil, err
		}
		if utils.IsDirectAccessIngressCascade(ing, svc) {
			if svc.Spec.Selector == nil || len(svc.Spec.Selector) == 0 {
				sc.Errors = append(sc.Errors, types.NewError(errcode.NoSelectorService, "", utils.IngressName(ing), utils.ServiceName(svc)))
				pods = make([]*v1.Pod, 0) // 不绑定任何节点
			}
		}

		newTarget := make([]*Target, 0)
		endpointNotAdd := make(map[string]bool)
		for _, pod := range pods {
			if utils.IsPodUnableBind(pod) { // ENI IP 还未分配的话，跳过该Pod。
				continue
			}

			backendIp, exist := utils.GetPodBackend(pod, backendType)
			if !exist { // mixed but use l4 listener. In ingress there is impossible
				continue
			}

			if pod.DeletionTimestamp != nil {
				endpointNotAdd[backendIp] = true
			} else if needDelayRegister(svc, pod) { // 对于满足优雅注册（等容器探针通过后再绑定为rs）的pod，加入endpointNotAdd, 这部分 pod 在对账时不会新增到 clb， 但也不会主动从 clb 剔除。
				endpointNotAdd[backendIp] = true
			}

			// 目前只有GlobalRoute和ENI两种
			// 1. ENI网络类型的工作负载都支持直连
			// 2. GlobalRoute网络类型的工作负载，目前由用户自行控制白名单
			if !service.IsENINetwork(sc, pod) {
				if !service.ClusterInfoServiceInstance.IsGlobalRoute(sc, false) {
					continue
				}
			}
			if service.IsPodClbReachable(sc, pod) {
				port, exists := determineTargetPort(rule.IngRule.TargetPort, pod)
				if exists {
					newTarget = append(newTarget, &Target{
						Target:      backendIp,
						BackendType: utils.ENI,
						Port:        port,
						Pod:         pod,
					})
				}
			}
		}
		backendInstanceMap[backendKey] = &RuleTarget{
			Skipped:         false,
			Targets:         newTarget,
			ExcludedTargets: endpointNotAdd,
		}
	}
	return backendInstanceMap, nil
}

// needDelayRegister pod 是否需要延迟注册 rs
// 优雅注册方案 https://iwiki.woa.com/p/4013004019
func needDelayRegister(svc *v1.Service, pod *v1.Pod) bool {
	p := types.NewPod(pod)

	return config.Global.MergeMode && !types.NewService(svc).SkipReadinessGate() && !p.IsContainersReadyConditionTrue()
	// !p.IsDirectAccessReadyConditionTrue() // 如果 gate 已经通过，说明 rs 已经挂载到 clb了，不需要考虑延迟绑定
}

func determineTargetPort(targetPort *intstr.IntOrString, pod *v1.Pod) (int, bool) {
	if targetPort.Type == intstr.Int {
		return int(targetPort.IntVal), true
	}
	for _, c := range pod.Spec.Containers {
		port, exists := lo.Find(c.Ports, func(p v1.ContainerPort) bool {
			return p.Name == targetPort.StrVal
		})
		if exists {
			return int(port.ContainerPort), true
		}
	}
	return 0, false
}

func (lbc *LoadBalancerController) getBackendInstanceMapNodePort(sc *tencent.SyncContext, ingress types.Ingress, loadbalancer *clb.LoadBalancer, region string, combineRules []CombineRule) (map[string]*RuleTarget, error) {
	availableNode, err := getAvailableNode(ingress)
	if err != nil {
		return nil, err
	}

	// If there are no available nodes for LoadBalancer service, make a EventTypeWarning event for it.
	if len(availableNode) == 0 && !utils.IsInEKSCluster() && len(combineRules) > 0 {
		sc.Errors = append(sc.Errors, types.NewError(errcode.NoAvalibaleNodeForService, "", utils.IngressName(ingress)))
	}

	backendMap := make(map[string]*RuleTarget) // Service to Backend
	for _, rule := range combineRules {
		backendKey := ruleBackendToString(*rule.LbRule.LocationId, rule.IngRule.Backend)
		if _, exist := backendMap[backendKey]; !exist {
			services, err := cluster_service.Instance.ServiceLister().Services(ingress.Namespace()).Get(rule.IngRule.Backend.ServiceName())
			if err != nil {
				if k8serrors.IsNotFound(err) {
					if !utils.BackendOnlyIngress(ingress) {
						sc.Errors = append(sc.Errors, types.NewError(errcode.ServiceNotFoundError, "", utils.IngressName(ingress), fmt.Sprintf("%s/%s", ingress.Namespace(), rule.IngRule.Backend.ServiceName())))
					}
					backendMap[backendKey] = SkipRuleResult()
					continue
				}
				klog.Errorf("ServiceLister get error for service %s/%s. %v", ingress.Namespace(), rule.IngRule.Backend.ServiceName(), err)
				return nil, err
			}

			backendType := utils.GetBackendType(loadbalancer)
			serviceIPStack := utils.GetServiceIPStack(services)
			if (backendType == "ipv4" && serviceIPStack == "ipv6") || (backendType == "ipv6" && serviceIPStack == "ipv4") {
				sc.Errors = append(sc.Errors, types.NewError(errcode.AddressIPVersionError, "", utils.IngressName(ingress), utils.ServiceName(services), backendType, serviceIPStack))
			}
			nodePort, targets, targetNotAdd, err := filterNodes(sc, rule.IngRule.Backend, services, availableNode, backendType)
			if err != nil {
				return nil, err
			}
			if nodePort == NodePortNotFound {
				if services.Spec.AllocateLoadBalancerNodePorts != nil && *services.Spec.AllocateLoadBalancerNodePorts == false {
					if len(availableNode) != 0 && !utils.IsInEKSCluster() {
						// 零节点可能退化为纯EKS绑定
						// 零节点又不是EKS的场景，优先报错 NoAvalibaleNodeForService
						sc.Errors = append(sc.Errors, types.NewError(errcode.ServiceCloseNodePortsError, "", utils.IngressName(ingress), fmt.Sprintf("%s %s", rule.IngRule.Host, rule.IngRule.Path), fmt.Sprintf("%s/%s", ingress.Namespace(), rule.IngRule.Backend.ServiceName())))
					}
				}
				backendMap[backendKey] = SkipRuleResult()
				continue
			}

			backendMap[backendKey] = &RuleTarget{
				Skipped:         false,
				Service:         services,
				Targets:         targets,
				ExcludedTargets: targetNotAdd,
			}
		}
	}

	// Change PrivateIP To InstanceId
	ipList := make([]string, 0)
	for _, ruleTarget := range backendMap {
		for _, target := range ruleTarget.Targets {
			if target.Target != "" { // 不需要转换，已经通过节点信息获得InstanceId
				continue
			}

			for _, address := range target.Node.Status.Addresses {
				if address.Type == "InternalIP" && address.Address != "" {
					ipList = append(ipList, address.Address)
				}
			}
		}
	}
	nodesMap, err := tencent.GetBackendIDs(sc, ingress, region, ipList)
	if err != nil {
		return nil, err
	}
	for _, ruleTarget := range backendMap {
		for _, target := range ruleTarget.Targets {
			if target.Target != "" { // 不需要转换，已经通过节点信息获得InstanceId
				continue
			}

			for _, address := range target.Node.Status.Addresses {
				if address.Type == "InternalIP" && address.Address != "" {
					target.Target = nodesMap[address.Address]
				}
			}
		}
	}

	// 1. 如果是跨VPC绑定的场景，NodePort需要通过云联网的能力，通过CVM的IP进行绑定
	// 2. 为了保证在不同地域下，过滤规则不变。需要通过filteredTargets进行转换
	if loadbalancer.VpcId != nil && *loadbalancer.VpcId != config.Global.VPCID {
		if utils.GetCrossType(ingress) == utils.CrossType2_0 || utils.GetCrossType(ingress) == utils.CrossType1_1 || utils.GetCrossType(ingress) == utils.CrossType1_2 {
			// 跨域绑定 2.0 的后端类型特殊
			for _, target := range backendMap {
				for index, ruleTarget := range target.Targets {
					if ruleTarget.BackendType != utils.CVM {
						continue
					}
					for _, address := range ruleTarget.Node.Status.Addresses {
						if address.Type == "InternalIP" && address.Address != "" {
							if utils.GetCrossType(ingress) == utils.CrossType2_0 {
								target.Targets[index].BackendType = utils.CCN
							} else if utils.GetCrossType(ingress) == utils.CrossType1_1 {
								target.Targets[index].BackendType = utils.NAT
							} else if utils.GetCrossType(ingress) == utils.CrossType1_2 {
								target.Targets[index].BackendType = utils.PVGW
							}
							if target.ExcludedTargets[target.Targets[index].Target] {
								target.ExcludedTargets[address.Address] = true
							}
							target.Targets[index].Target = address.Address
							break
						}
					}
				}
			}
		}
	}
	return backendMap, nil
}

// getInstanceIDByIP get lb instance by private ip
// func (lbc *LoadBalancerController) getInstanceIDByIP(targets []*Target) ([]*Target, error) {
//	ipList := make([]string, 0)
//	for _, target := range targets {
//		for _, address := range target.Node.Status.Addresses {
//			if address.Type == "InternalIP" && address.Address != "" {
//				if target.Target == "" { // 不需要转换，已经通过节点信息获得InstanceId
//					ipList = append(ipList, address.Address)
//				}
//			}
//		}
//	}
//	privateIpToInstanceId, err := tencent.GetBackendIDs(ingress, ipList)
//	if err != nil {
//		return nil, err
//	}
//
//	result := make([]*Target, 0)
//	for index, target := range targets {
//		if target.Target != "" {
//			result = append(result, targets[index])
//		} else {
//			for _, address := range target.Node.Status.Addresses {
//				if address.Type == "InternalIP" && address.Address != "" {
//					if instanceId, exist := privateIpToInstanceId[address.Address]; exist {
//						targets[index].Target = instanceId
//						result = append(result, targets[index])
//					}
//				}
//			}
//		}
//	}
//	return result, nil
// }

func getAvailableNode(ingress types.Ingress) ([]*v1.Node, error) {
	predicate := func(node *v1.Node) bool {
		// We add the master to the node list, but its unschedulable.  So we use this to filter
		// the master.
		// if node.Spec.Unschedulable {
		//	return false
		// }
		if node.Labels != nil {
			if value, exist := node.Labels["node.kubernetes.io/exclude-from-external-load-balancers"]; exist && value == "true" {
				return false
			}
		}

		// If we have no info, don't accept
		if len(node.Status.Conditions) == 0 {
			return false
		}

		// If we have node, but no InternalIP
		flag := false
		for _, address := range node.Status.Addresses {
			if address.Type == "InternalIP" && address.Address != "" {
				flag = true
				break
			}
		}
		if flag == false {
			return false
		}

		if utils.IsEKSNode(node) {
			return false
		}

		if hybridType := utils.GetHybridType(ingress); hybridType == utils.HybridType_NONE { // 非混合云场景，屏蔽混合云节点。
			if utils.IsIDCNode(node) {
				return false
			}
		}

		if types.NewNode(node).HasWaitFinalizer() || node.DeletionTimestamp != nil {
			return false
		}
		for _, cond := range node.Status.Conditions {
			// We consider the node for load balancing only when its NodeReady condition status
			// is ConditionTrue
			if cond.Type == v1.NodeReady && cond.Status != v1.ConditionTrue {
				// 容忍节点抖动
				if _, exist := nodestatus.NodeStatusMapInstance.Get(node.Name); !exist {
					klog.V(4).Infof("Ignoring node %v with %v condition status %v", node.Name, cond.Type, cond.Status)
					return false
				}
			}
		}
		return true
	}

	nodes, err := cluster_service.Instance.NodeLister().List(labels.Everything())
	if err != nil {
		klog.Errorf("NodeLister list error. %v", err)
		return nil, err
	}

	var filtered []*v1.Node
	for i := range nodes {
		if predicate(nodes[i]) {
			filtered = append(filtered, nodes[i])
		}
	}

	return filtered, nil
}

// filterNodes filter nodes for quota and when with AnnoBackendsListLabelAnnotation annontation
func filterNodes(syncContext *tencent.SyncContext, backend *utils.Backend, service *v1.Service, nodes []*v1.Node, backendType string) (int, Targets, map[string]bool, error) {
	var labelList labels.Selector
	var err error

	var nodePort = NodePortNotFound
	p, exists := lo.Find(service.Spec.Ports, func(p v1.ServicePort) bool {
		if p.NodePort == 0 {
			return false
		}
		if backend.Port.Type == intstr.Int {
			return p.Port == backend.Port.IntVal
		}
		return p.Name == backend.Port.StrVal
	})
	if exists {
		nodePort = int(p.NodePort)
	}

	if nodePort == NodePortNotFound {
		return NodePortNotFound, nil, nil, nil
	}

	targetNotAdd := make(map[string]bool)
	targetMap := make(map[string]*Target)
	for index, node := range nodes { // 构建默认的NodePort后端，所有正常节点都会作为NodePort后端
		if target := BuildTargetInstanceId(nodes[index], backendType, nodePort, utils.WeightDefault); target != nil {
			targetMap[node.Name] = target
		}
	}

	// Selector Local
	// Service是Local转发，后端需要重新选择
	if service.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal {
		targetMap = make(map[string]*Target) // 重置默认后端
		nodeMap := make(map[string]*v1.Node)
		for index, node := range nodes {
			nodeMap[node.Name] = nodes[index]
		}

		pods := make([]*v1.Pod, 0)
		if pods, err = cluster_service.Instance.PodLister().Pods(service.Namespace).List(labels.SelectorFromSet(service.Spec.Selector)); err != nil {
			klog.Errorf("PodLister get error for service %s. %v\n", utils.ServiceName(service), err)
			return NodePortNotFound, nil, nil, err
		}
		// 保证优雅停机状态
		for _, pod := range pods {
			if node, exist := nodeMap[pod.Spec.NodeName]; exist { // nodeMap中都是合法节点，不会有EKS节点
				if _, exist := targetMap[pod.Spec.NodeName]; !exist {
					targetMap[pod.Spec.NodeName] = BuildTargetInstanceId(node, backendType, nodePort, 0)
				}
			}
		}

		// Service是否开启加权平衡
		endpoints, err := cluster_service.Instance.EndpointsLister().Endpoints(service.Namespace).Get(service.Name)
		if err != nil {
			klog.Errorf("EndpointLister get error for service %s. %v\n", utils.ServiceName(service), err)
			return NodePortNotFound, nil, nil, err
		}
		klog.V(5).Infof("[%s/%s]endpoint has [%d] subsets. [%v]", service.Namespace, service.Name, len(endpoints.Subsets), endpoints.Subsets)

		for _, sub := range endpoints.Subsets {
			for _, add := range sub.Addresses {
				if node, exist := nodeMap[*add.NodeName]; exist { // 不存在时：大概率当前Pod处于的节点，状态不正确，不在节点列表中
					if _, exist := targetMap[*add.NodeName]; !exist {
						if target := BuildTargetInstanceId(node, backendType, nodePort, utils.WeightGracefulShutdown); target != nil {
							targetMap[*add.NodeName] = target
						}
					}
					if _, exist := targetMap[*add.NodeName]; exist {
						targetMap[*add.NodeName].Weight = common.Int64Ptr(*targetMap[*add.NodeName].Weight + 1)
					}
				}
			}
			for _, notReady := range sub.NotReadyAddresses {
				if node, exist := nodeMap[*notReady.NodeName]; exist { // 不存在时：大概率当前Pod处于的节点，状态不正确，不在节点列表中
					if _, exist := targetMap[*notReady.NodeName]; !exist {
						if target := BuildTargetInstanceId(node, backendType, nodePort, utils.WeightGracefulShutdown); target != nil {
							targetMap[*notReady.NodeName] = target
						}
					}
				}
			}
		}

		// 保证优雅停机状态
		for _, pod := range pods {
			if node, exist := nodeMap[pod.Spec.NodeName]; exist {
				if _, exist := targetMap[pod.Spec.NodeName]; !exist {
					if target := BuildTargetInstanceId(node, backendType, nodePort, utils.WeightGracefulShutdown); target != nil {
						targetMap[pod.Spec.NodeName] = target
						if pod.DeletionTimestamp != nil {
							targetNotAdd[target.Target] = true
						}
					}
				}
			}
		}
	}

	// 增加节点下线时的截流能力: tke.cloud.tencent.com/intercept-from-external-load-balancers
	// 优先级高于其他Local场景的权重控制，在节点存在该Label时，节点上线权重调整为0
	for _, target := range targetMap {
		if target.Node.Labels != nil {
			if value, exist := target.Node.Labels["tke.cloud.tencent.com/graceful-shutdown-from-load-balancers"]; exist && value == "true" {
				target.Weight = common.Int64Ptr(0)
			}
		}
	}

	// Selector BackendsList
	newTargetMap := make(map[string]*Target)
	availList, labelExists := utils.GetBackendsListLabel(service)
	if labelExists {
		if labelList, err = labels.Parse(availList); err != nil {
			// TODO misakazhou 怎么办
			// 解析错误的情况下，当做用户该注解不存在
			syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.BackendsLabelAnnotationError, err.Error(), utils.ServiceName(service)))
			labelExists = false
		}
	}

	for key, target := range targetMap {
		value, hasMasterRoleLabel := target.Node.Labels[LabelNodeRoleMaster]
		if !labelExists { // 没有`BackendsLabel`注解
			// 1. 默认选择非Master节点作为后端
			// 2. Local绑定情况下，不考虑Master节点的限制
			if master, err := utils.ParseBool(value); !hasMasterRoleLabel || (master != true && err != nil) {
				newTargetMap[key] = target
			} else if service.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal {
				newTargetMap[key] = target
			}
		} else { // node match label or master node when ignore
			labelSet := labels.Set(target.Node.ObjectMeta.Labels)
			if labelList.Matches(labelSet) {
				newTargetMap[key] = target
			}
		}
	}
	targetMap = newTargetMap

	// TODO misakazhou 参数中的Node就已经过滤掉了EKSNode，所以下面的逻辑不会生效。
	// 处理TKE扩展EKS集群的场景
	// 1. 当TKE节点存在时，流量全部绑定到TKE的节点上。以此保证流量均衡
	// 2. 当用户开启Local绑定时，不能执行上述操作。
	// existTKENode := false
	// existEKSNode := false
	// for _, target := range targetMap {
	//	if utils.IsEKSNode(target.Node) {
	//		existEKSNode = true
	//	} else if utils.IsTKENode(target.Node) {
	//		existTKENode = true
	//	}
	// }
	// if service.Spec.ExternalTrafficPolicy != v1.ServiceExternalTrafficPolicyTypeLocal && (existTKENode && existEKSNode) {
	//	newTargetMap = make(map[string]*Target)
	//	for key, target := range targetMap {
	//		if utils.IsTKENode(target.Node) {
	//			newTargetMap[key] = target
	//		}
	//	}
	// }
	// targetMap = newTargetMap

	// Map To Arrary
	var availTargets = Targets{}
	for _, target := range targetMap {
		availTargets = append(availTargets, target)
	}
	return nodePort, availTargets, targetNotAdd, nil
}

// 不保证能够从Node中获取到InstanceId
func BuildTargetInstanceId(node *v1.Node, backendType string, nodePort int, weight int64) *Target {
	target := &Target{
		Node:   node,
		Target: "",
		Port:   nodePort,
		Weight: common.Int64Ptr(weight),
	}

	if utils.IsIDCNode(node) {
		if backend, exist := utils.GetNodeBackend(node, backendType); exist {
			target.BackendType = utils.NAT
			target.Target = backend
			return target
		}
		klog.Errorf("Node %s, InternalIP not exist. Can not bind to the Loadbalancer.", node.Name)
		return nil
	}

	if utils.IsCXMNode(node) { // 特殊CXM节点
		if backend, exist := utils.GetNodeBackend(node, backendType); exist {
			target.BackendType = utils.EVM
			target.Target = backend
			return target
		}
		klog.Errorf("Node %s, InternalIP not exist. Can not bind to the Loadbalancer.", node.Name)
		return nil
	}

	if backendType == "ipv6" || backendType == "mixed" { // 混合协议优先IPv6
		if backend, exist := utils.GetNodeBackend(node, backendType); exist {
			target.BackendType = utils.ENI
			target.Target = backend
			return target
		}
	}

	if utils.IsTencentCloudCVMNode(node) {
		target.BackendType = utils.CVM
		// ProviderID的内容格式：tencentcloud://ins-mmnyndtg
		providerId := node.Spec.ProviderID
		if strings.HasPrefix(providerId, "tencentcloud:") {
			instanceId := providerId[strings.LastIndex(providerId, "/")+1:]
			if strings.HasPrefix(instanceId, "ins-") {
				target.Target = instanceId
				return target
			}
		}
	}

	target.BackendType = utils.CVM
	// ProviderID的内容格式：qcloud:///190001/ins-mmnyndtg
	providerId := node.Spec.ProviderID
	if strings.HasPrefix(providerId, "qcloud:") {
		instanceId := providerId[strings.LastIndex(providerId, "/")+1:]
		if strings.HasPrefix(instanceId, "ins-") {
			target.Target = instanceId
		}
	}

	return target
}

func makeWeightForENI(sc *tencent.SyncContext, combineRules []CombineRule, backendInstanceMap map[string]*RuleTarget, ing types.Ingress, listener *clb.Listener) error {
	for _, rule := range combineRules {
		ruleTarget, ok := backendInstanceMap[ruleBackendToString(*rule.LbRule.LocationId, rule.IngRule.Backend)]
		if !ok || ruleTarget.Skipped {
			continue
		}
		backend := rule.IngRule.Backend
		svc, err := cluster_service.Instance.ServiceLister().Services(ing.Namespace()).Get(backend.ServiceName())
		if err != nil {
			if k8serrors.IsNotFound(err) {
				if !utils.BackendOnlyIngress(ing) {
					sc.Errors = append(sc.Errors, types.NewError(errcode.ServiceNotFoundError, "", utils.IngressName(ing), fmt.Sprintf("%s/%s", ing.Namespace(), backend.ServiceName())))
				}
				continue
			}
			klog.Errorf("ServiceLister get error for service %s/%s. %v", ing.Namespace(), backend.ServiceName(), err)
			return err
		}

		if ruleTarget.Targets != nil && len(ruleTarget.Targets) != 0 {
			endpoints, err := cluster_service.Instance.EndpointsLister().Endpoints(svc.Namespace).Get(svc.Name)
			if err != nil {
				klog.Errorf("EndpointLister get error for service %s. %v\n", utils.ServiceName(svc), err)
			}
			endpointSlices := GetEndpointSlices(sc, svc)
			for _, rs := range ruleTarget.Targets {
				// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
				if rs.Pod != nil && utils.IsENILikeType(string(rs.BackendType)) {
					rs.Weight = utils.Int64Ptr(determinePodWeight(sc, rs.Pod, ing, svc, listener, rule.IngRule, endpoints, endpointSlices, false))
				}
			}
		}
	}
	return nil
}

func checkAllDown(sc *tencent.SyncContext, combineRules []CombineRule, backendInstanceMap map[string]*RuleTarget, ing types.Ingress, listener *clb.Listener) error {
	for _, rule := range combineRules {
		ruleTarget, ok := backendInstanceMap[ruleBackendToString(*rule.LbRule.LocationId, rule.IngRule.Backend)]
		if !ok || ruleTarget.Skipped {
			continue
		}
		backend := rule.IngRule.Backend
		svc, err := cluster_service.Instance.ServiceLister().Services(ing.Namespace()).Get(backend.ServiceName())
		if err != nil {
			if k8serrors.IsNotFound(err) {
				if !utils.BackendOnlyIngress(ing) {
					sc.Errors = append(sc.Errors, types.NewError(errcode.ServiceNotFoundError, "", utils.IngressName(ing), fmt.Sprintf("%s/%s", ing.Namespace(), backend.ServiceName())))
				}
				continue
			}
			klog.Errorf("ServiceLister get error for service %s/%s. %v", ing.Namespace(), backend.ServiceName(), err)
			return err
		}

		if ruleTarget.Targets != nil && len(ruleTarget.Targets) != 0 {
			endpoints, err := cluster_service.Instance.EndpointsLister().Endpoints(svc.Namespace).Get(svc.Name)
			if err != nil {
				klog.Errorf("EndpointLister get error for service %s. %v\n", utils.ServiceName(svc), err)
			}
			endpointSlices := GetEndpointSlices(sc, svc)
			allDown := true
			for _, rs := range ruleTarget.Targets {
				// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
				if *rs.Weight != 0 {
					allDown = false
					break
				}
			}
			if allDown {
				ruleTarget.AllDown = true
				if utils.IsDirectAccessIngressCascade(ing, svc) {
					sc.Errors = append(sc.Errors, types.NewError(errcode.WeightZeroError, "", utils.IngressName(ing), rule.IngRule.Host, rule.IngRule.RealPath))
				}
				for _, rs := range ruleTarget.Targets {
					// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
					if rs.Pod != nil && utils.IsENILikeType(string(rs.BackendType)) {
						rs.Weight = utils.Int64Ptr(determinePodWeight(sc, rs.Pod, ing, svc, listener, rule.IngRule, endpoints, endpointSlices, true))
					} else {
						rs.Weight = utils.Int64Ptr(determineNodeWeight(ing, allDown))
					}
				}
			}
		}
	}
	return nil
}

// func diffTarget(syncContext *tencent.SyncContext, ing types.Ingress, svc *v1.Service, listener *clbInner.Listener, toTargets *RuleTarget, fromTargets *clb.RuleTargets, ingRule *utils.Rule) (toDeregister *list.List, toRegister *list.List, toUpdate *list.List) {
//	return diffENITarget(syncContext, ing, svc, listener, toTargets, fromTargets, ingRule)
// }

func diffTarget(backendManagementMode utils.BackendManagementModeType, listener *clb.Listener, expectedTargets *RuleTarget, actualTargets *clb.RuleTargets) (*list.List, *list.List, *list.List) {
	LocationID := *actualTargets.LocationId
	listenerId := *listener.ListenerId

	toDeregisterList := list.New()
	toRegisterList := list.New()
	toUpdateList := list.New()

	fakeAllDown := expectedTargets.FakeAllDown()

	currentTarget := make(map[string]int) // map[podIP]Pod端口  // map[instanceId]Pod端口
	for _, toTarget := range expectedTargets.Targets {
		if utils.IsENILikeType(string(toTarget.BackendType)) {
			currentTarget[toTarget.Target] = toTarget.Port
		} else {
			currentTarget[toTarget.Target] = toTarget.Port
		}
	}

	// 以下找到的这些target是要解绑的了。原因有：1.原来的后端类型是CVM，而现在需要的是直接绑pod。2.后端绑定的Pod的端口变了。
	for _, backend := range actualTargets.Targets {
		if backendManagementMode == utils.BackendManagementModeTag && !types.ManagedTarget(backend.Tag, config.Global.ClusterName) {
			continue // 开启标签管理，标签不符合预期，不在管理范围
		}
		if utils.IsENILikeType(*backend.Type) {
			if toPort, ok := currentTarget[*backend.PrivateIpAddresses[0]]; !ok || int64(toPort) != *backend.Port {
				toDeregisterList.PushBack(NewENIBatchTarget(listenerId, LocationID, *backend.PrivateIpAddresses[0], *backend.Port))
			}
		} else {
			if toPort, ok := currentTarget[*backend.InstanceId]; !ok || int64(toPort) != *backend.Port {
				toDeregisterList.PushBack(NewCVMBatchTarget(listenerId, LocationID, *backend.InstanceId, *backend.Port))
			}
		}
	}

	rulesTarget := make(map[string]*clb.Backend) // instanceId to Port
	for _, fromTarget := range actualTargets.Targets {
		if utils.IsENILikeType(*fromTarget.Type) {
			rulesTarget[*fromTarget.PrivateIpAddresses[0]] = fromTarget
		} else {
			rulesTarget[*fromTarget.InstanceId] = fromTarget
		}
	}

	if len(expectedTargets.Targets) != 0 {
		// endpoints, err := cluster_service.ClusterServiceInstance.EndpointLister().Endpoints(svc.Namespace).Get(svc.Name)
		// if err != nil {
		//	klog.Errorf("EndpointLister get error for service %s. %v\n", utils.ServiceName(svc), err)
		// }
		// endpointSlices := GetEndpointSlices(svc)
		// for _, rs := range toTargets.Target {
		//	// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
		//	if rs.Pod != nil && utils.IsENILikeType(string(rs.BackendType)) {
		//		rs.Weight = utils.Int64Ptr(determinePodWeight(syncContext, rs.Pod, ing, svc, listener, rule, endpoints, endpointSlices, false))
		//	}
		// }
		// allDown := true
		// for _, rs := range toTargets.Target {
		//	// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
		//	if rs.Pod != nil && utils.IsENILikeType(string(rs.BackendType)) {
		//		if *rs.Weight != 0 {
		//			allDown = false
		//			break
		//		}
		//	}
		// }
		// if allDown {
		//	if utils.IsDirectAccessIngressCascade(ing, svc) {
		//		klog.Errorf("Ingress:%s. Host:%s. Path:%s. All of the Endpoint weight will be down to zero. stop the graceful shutdown.", utils.IngressName(ing), rule.Host, rule.RealPath)
		//		syncContext.Errors = append(syncContext.Errors, types.NewError(ingressError.WeightZeroError, "", utils.IngressName(ing), rule.Host, rule.RealPath))
		//	}
		//	for _, rs := range toTargets.Target {
		//		// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
		//		if rs.Pod != nil && utils.IsENILikeType(string(rs.BackendType)) {
		//			rs.Weight = utils.Int64Ptr(determinePodWeight(syncContext, rs.Pod, ing, svc, listener, rule, endpoints, endpointSlices, true))
		//		}
		//	}
		// }

		for _, toTarget := range expectedTargets.Targets {
			// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
			if utils.IsENILikeType(string(toTarget.BackendType)) {
				if fromTarget, ok := rulesTarget[toTarget.Target]; ok && *fromTarget.Port == int64(toTarget.Port) { // 可能需要更新的情况
					if backendManagementMode == utils.BackendManagementModeTag && !types.ManagedTarget(fromTarget.Tag, config.Global.ClusterName) {
						continue // 开启标签管理，标签不符合预期，不在管理范围
					}
					// rs 开启 skip 能力，不调整权重
					if types.NeedSKipTarget(fromTarget.Tag) {
						continue
					}
					if fakeAllDown {
						klog.Errorf("fake all down for target %s in locationID %s", toTarget.Target, LocationID)
						if *fromTarget.Weight != 0 {
							toUpdateList.PushBack(NewRsWeightRule(utils.ENI, listenerId, LocationID, toTarget.Target, int64(toTarget.Port), 0))
						}
					} else if *fromTarget.Weight != *toTarget.Weight {
						toUpdateList.PushBack(NewRsWeightRule(utils.ENI, listenerId, LocationID, toTarget.Target, int64(toTarget.Port), *toTarget.Weight))
					}
				} else {
					if _, exist := expectedTargets.ExcludedTargets[toTarget.Target]; exist { // 特例：明确不挂载的旧版本后端（存在的情况下不主动摘除）
						continue
					}
					var tag string
					if backendManagementMode == utils.BackendManagementModeTag {
						tag = types.BuildTargetTag(config.Global.ClusterName)
					}
					// 以下找到的是需要绑定的Target：CLB还未绑定toTarget，或需要绑定的端口发生了变化
					toRegisterList.PushBack(NewENICreateBatchTarget(listenerId, LocationID, toTarget.Target, int64(toTarget.Port), *toTarget.Weight, tag))
				}
			} else {
				if fromTarget, ok := rulesTarget[toTarget.Target]; ok && *fromTarget.Port == int64(toTarget.Port) { // 可能需要更新的情况
					if backendManagementMode == utils.BackendManagementModeTag && !types.ManagedTarget(fromTarget.Tag, config.Global.ClusterName) {
						continue // 开启标签管理，标签不符合预期，不在管理范围
					}
					// rs 开启 skip 能力，不调整权重
					if types.NeedSKipTarget(fromTarget.Tag) {
						continue
					}
					if fakeAllDown {
						klog.Errorf("fake all down for target %s in locationID %s", toTarget.Target, LocationID)
						if *fromTarget.Weight != 0 {
							toUpdateList.PushBack(NewRsWeightRule(utils.CVM, listenerId, LocationID, toTarget.Target, int64(toTarget.Port), 0))
						}
					} else if *fromTarget.Weight != *toTarget.Weight {
						toUpdateList.PushBack(NewRsWeightRule(utils.CVM, listenerId, LocationID, toTarget.Target, int64(toTarget.Port), *toTarget.Weight))
					}
				} else {
					if _, exist := expectedTargets.ExcludedTargets[toTarget.Target]; exist { // 特例：明确不挂载的旧版本后端（存在的情况下不主动摘除）
						continue
					}
					var tag string
					if backendManagementMode == utils.BackendManagementModeTag {
						tag = types.BuildTargetTag(config.Global.ClusterName)
					}
					toRegisterList.PushBack(NewCVMCreateBatchTarget(listenerId, LocationID, toTarget.Target, int64(toTarget.Port), *toTarget.Weight, tag))
				}
			}
		}
	}

	return toDeregisterList, toRegisterList, toUpdateList
}

// 后端的权重逻辑
func determinePodWeight(syncContext *tencent.SyncContext, pod *v1.Pod, ing types.Ingress, svc *v1.Service, listener *clb.Listener, rule *utils.Rule, endpoints *v1.Endpoints, endpointSlices []*discovery.EndpointSlice, allDown bool) int {
	if !allDown { // 权重全部为0的场景下，全死全活的逻辑将不生效，兜底为工作负载开放流量。
		var notReadyEndpoint = make(map[string]bool)
		if endpoints != nil {
			for _, subnet := range endpoints.Subsets {
				for _, notReadyAddr := range subnet.NotReadyAddresses {
					notReadyEndpoint[notReadyAddr.IP] = true
				}
			}
		}

		if endpointSlices != nil && len(endpointSlices) != 0 {
			for _, endpointSlice := range endpointSlices {
				for _, endpoint := range endpointSlice.Endpoints {
					// Docs: https://kubernetes.io/zh/docs/concepts/services-networking/endpoint-slices/#%E7%8A%B6%E5%86%B5
					if !((endpoint.Conditions.Serving != nil && *endpoint.Conditions.Serving == true) || (endpoint.Conditions.Ready != nil && *endpoint.Conditions.Ready == true)) {
						for _, address := range endpoint.Addresses {
							notReadyEndpoint[address] = true
						}
					}
				}
			}
		}

		for _, podIP := range utils.GetPodIPs(pod) {
			if _, podInNotReadyList := notReadyEndpoint[podIP]; podInNotReadyList {
				return utils.WeightGracefulShutdown
			}
		}

		if pod.DeletionTimestamp != nil {
			return utils.WeightGracefulShutdown
		}
	}

	customizedWeights, customizedAnnoErr := utils.IsEnableCustomizedWeight(ing)
	if customizedAnnoErr != nil {
		// 这里注解写了，但是写错了。 当成不生效对待
		syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.CustomizedWeightAnnotationError, "", utils.IngressName(ing)))
	}
	if customizedWeights != nil {
		if customizedWeight := utils.DeterminePodCustomizedWeight(*listener.Protocol, rule.RealPath, rule.Host, pod, customizedWeights); customizedWeight != nil {
			klog.Infof("[Customized weight]: %d for protocol %s, path %s, host %s, pod %s", *customizedWeight, *listener.Protocol, rule.RealPath, rule.Host, pod.Name)
			return *customizedWeight
		}
		if customizedWeights.DefaultWeight != nil {
			return *customizedWeights.DefaultWeight
		}
	}

	if !allDown {
		// 在Pod Ready情况下，Pod自定义权重优先级最高
		if weight := types.NewPod(pod).Attributes().IngressAttributes.CustomWeight; weight != nil {
			return *weight
		}
		if !utils.IsDirectAccessIngressCascade(ing, svc) &&
			svc.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal {
			// 未显式声明直连的兜底场景、Service开启Traffic Local模式、Local权重的能力开启的情况下。
			// 工作负载全部在EKS上时，该逻辑也依然生效。所以可以不需要参考节点的绑定情况来确定权重。
			return utils.WeightDefaultLocal
		}
		return utils.WeightDefault
	} else {
		return utils.WeightDefaultProtect
	}
}

func determineNodeWeight(ing types.Ingress, allDown bool) int {
	if !allDown { // 权重全部为0的场景下，全死全活的逻辑将不生效，兜底为工作负载开放流量。
		return utils.WeightDefault
	}

	customizedWeights, _ := utils.IsEnableCustomizedWeight(ing)
	if customizedWeights != nil && customizedWeights.DefaultWeight != nil {
		return *customizedWeights.DefaultWeight
	}

	return utils.WeightDefaultProtect
}

func NewCVMBatchTarget(listenerId string, LocationId string, instanceId string, port int64) *clb.BatchTarget {
	return &clb.BatchTarget{
		ListenerId: &listenerId,
		LocationId: &LocationId,
		InstanceId: &instanceId,
		Port:       &port,
	}
}

func NewCVMCreateBatchTarget(listenerId string, LocationId string, instanceId string, port int64, weight int64, tag string) *clb.BatchTarget {
	if tag != "" {
		return &clb.BatchTarget{
			ListenerId: &listenerId,
			LocationId: &LocationId,
			InstanceId: &instanceId,
			Weight:     &weight,
			Port:       &port,
			Tag:        &tag,
		}
	}
	return &clb.BatchTarget{
		ListenerId: &listenerId,
		LocationId: &LocationId,
		InstanceId: &instanceId,
		Weight:     &weight,
		Port:       &port,
	}
}

func convert(batchTargetList *list.List) []*clb.BatchTarget {
	index := 0
	batchTargets := make([]*clb.BatchTarget, batchTargetList.Len())
	for temp := batchTargetList.Front(); temp != nil; temp = temp.Next() {
		batchTargets[index] = temp.Value.(*clb.BatchTarget)
		index++
	}
	return batchTargets
}

func convertRsWeightRule(batchTargetList *list.List) []*clb.RsWeightRule {
	index := 0
	rsWeightRules := make([]*clb.RsWeightRule, batchTargetList.Len())
	for temp := batchTargetList.Front(); temp != nil; temp = temp.Next() {
		rsWeightRules[index] = temp.Value.(*clb.RsWeightRule)
		index++
	}
	return rsWeightRules
}

func getKeyFromRule(host string, path string) string {
	return fmt.Sprintf("%s_%s", host, path)
}

// ingRules是期望； lbListner.Rules是现状
func diffRules(ingRules []*utils.Rule, lbListner *clb.Listener, loadBalancer *clb.LoadBalancer,
	defaultDomain *string) (toAdd []*clb.RuleInput, toDelete []string, toUpdateDomain []*clb.ModifyDomainAttributesRequest, toUpdateRule []*clb.ModifyRuleRequest) {
	ingRuleMap := make(map[string]*utils.Rule, 0)
	lbRuleMap := make(map[string]*clb.RuleOutput, 0)

	for index, rule := range ingRules {
		ingRuleMap[getKeyFromRule(rule.Host, rule.RealPath)] = ingRules[index]
	}
	if lbListner.Rules != nil && len(lbListner.Rules) != 0 {
		for index, r := range lbListner.Rules {
			lbRuleMap[getKeyFromRule(*r.Domain, *r.Url)] = lbListner.Rules[index]
		}
	}

	// 期望的规则，在现状中没有，且不能忽略
	for k, v := range ingRuleMap {
		if _, ok := lbRuleMap[k]; !ok {
			if request := buildRuleInput(lbListner, v, defaultDomain, loadBalancer.IPv6Mode); request != nil {
				toAdd = append(toAdd, request)
			}
		}
	}
	visitedDomains := map[string]struct{}{}
	for k, v := range lbRuleMap {
		// lb rule not in ingress,to delete
		if rule, ok := ingRuleMap[k]; !ok {
			toDelete = append(toDelete, *v.LocationId)
		} else {
			if _, exist := visitedDomains[*v.Domain]; !exist {
				visitedDomains[*v.Domain] = struct{}{}
				if request := buildModifyDomainAttributesRequest(loadBalancer, lbListner, rule, defaultDomain); request != nil {
					toUpdateDomain = append(toUpdateDomain, request)
				}
			}
			if request := buildModifyRuleRequest(loadBalancer, v, rule, defaultDomain); request != nil {
				toUpdateRule = append(toUpdateRule, request)
			}
		}
	}
	return
}

// ingRules是期望； lbListner.Rules是现状
func diffDeletedRules(ingRules []*utils.Rule, lbListner *clb.Listener, loadBalancer *clb.LoadBalancer,
	defaultDomain *string) (toDelete []string) {
	ingRuleMap := make(map[string]*utils.Rule, 0)
	lbRuleMap := make(map[string]*clb.RuleOutput, 0)

	for index, rule := range ingRules {
		ingRuleMap[getKeyFromRule(rule.Host, rule.RealPath)] = ingRules[index]
	}
	if lbListner.Rules != nil && len(lbListner.Rules) != 0 {
		for index, r := range lbListner.Rules {
			lbRuleMap[getKeyFromRule(*r.Domain, *r.Url)] = lbListner.Rules[index]
		}
	}

	for k, v := range lbRuleMap {
		// lb rule not in ingress,to delete
		if _, ok := ingRuleMap[k]; !ok {
			toDelete = append(toDelete, *v.LocationId)
		}
	}
	return
}

func buildRuleInput(lbListner *clb.Listener, rule *utils.Rule, defaultDomain *string, ipv6Mode *string) *clb.RuleInput {
	ruleInput := &clb.RuleInput{
		Domain: &rule.Host,
		Url:    &rule.RealPath,
		HealthCheck: &clb.HealthCheck{
			HealthSwitch: lo.Ternary(wildcardDomain(rule.Host), lo.ToPtr(int64(0)), lo.ToPtr(int64(1))),
		},
		Http2: rule.HTTP2Enabled,
	}

	if ipv6Mode != nil && strings.ToLower(*ipv6Mode) == "ipv6fullchain" { // IPv6FullChain 仅支持 VIP 探测
		ruleInput.HealthCheck.SourceIpType = lo.ToPtr[int64](0)
	} else {
		ruleInput.HealthCheck.SourceIpType = lo.ToPtr[int64](1) // 默认 使用 100.64 作为探测源 IP
	}

	if *lbListner.Protocol == "HTTPS" && *lbListner.SniSwitch == 1 { // Https规则，开启SNI需要配置域名证书
		// if rule.TLSConfig == nil || rule.TLSConfig.CertificateID == "" { // Https规则没有指定证书
		if rule.TLSConfig == nil || len(rule.TLSConfig.CertificateIDs) == 0 {
			klog.Infof("Can not Create the rule. Rule: %s %s. Secret %s is Invalid.", utils.StringWrapper(ruleInput.Domain), utils.StringWrapper(ruleInput.Url), utils.StringWrapper(rule.Secret))
			return nil
		}
		if len(rule.TLSConfig.CertificateIDs) > 1 {
			ruleInput.MultiCertInfo = conv.Convert[*clb.MultiCertInfo](rule.TLSConfig)
		} else {
			ruleInput.Certificate = conv.Convert[*clb.CertificateInput](rule.TLSConfig)
		}
		// if rule.CaCertId == nil {
		// 	ruleInput.Certificate = &clb.CertificateInput{CertId: rule.CertId, SSLMode: common.StringPtr("UNIDIRECTIONAL")}
		// } else {
		// 	ruleInput.Certificate = &clb.CertificateInput{CertId: rule.CertId, CertCaId: rule.CaCertId, SSLMode: common.StringPtr("MUTUAL")}
		// }
	}

	if rule.Default {
		ruleInput.DefaultServer = lo.ToPtr(true)
	}

	if rule.L7RuleConfig != nil {
		if rule.L7RuleConfig.HealthCheck != nil {
			conv.ConvertInto(*rule.L7RuleConfig.HealthCheck, ruleInput.HealthCheck)
			if ruleInput.HealthCheck.HttpCheckDomain != nil && *ruleInput.HealthCheck.HttpCheckDomain == "" && defaultDomain != nil {
				ruleInput.HealthCheck.HttpCheckDomain = lo.ToPtr(*defaultDomain)
			}
		}
		if rule.L7RuleConfig.Session != nil {
			if rule.L7RuleConfig.Session.Enable {
				var sessionExpireTime int64 = 30
				if rule.L7RuleConfig.Session.SessionExpireTime != nil {
					sessionExpireTime = int64(*rule.L7RuleConfig.Session.SessionExpireTime)
				}
				ruleInput.SessionExpireTime = common.Int64Ptr(sessionExpireTime)
			} else {
				ruleInput.SessionExpireTime = common.Int64Ptr(0)
			}
		}
		if rule.L7RuleConfig.Scheduler != nil {
			ruleInput.Scheduler = rule.L7RuleConfig.Scheduler
		}
		if rule.L7RuleConfig.ForwardType != nil {
			ruleInput.ForwardType = rule.L7RuleConfig.ForwardType
		}
	}

	return ruleInput
}

func buildModifyDomainAttributesRequest(loadBalancer *clb.LoadBalancer, listener *clb.Listener, rule *utils.Rule, defaultDomain *string) *clb.ModifyDomainAttributesRequest {
	request := clb.NewModifyDomainAttributesRequest()
	request.LoadBalancerId = loadBalancer.LoadBalancerId
	request.ListenerId = listener.ListenerId
	request.Domain = &rule.Host

	l := types.NewCLBListener(listener)
	current := l.GetDomain(rule.Host)
	if current == nil {
		return nil
	}
	expect := rule.Domain

	if !l.SNIEnabled() {
		expect.TLSConfig = nil
	}
	if expect.Equals(*current) {
		return nil
	}

	request.Http2 = expect.HTTP2Enabled
	if expect.Default {
		request.DefaultServer = lo.ToPtr(true)
	}
	if expect.TLSConfig == nil {
		return request
	}
	if len(expect.TLSConfig.CertificateIDs) > 1 {
		request.MultiCertInfo = conv.Convert[*clb.MultiCertInfo](expect.TLSConfig)
		return request
	}
	request.Certificate = conv.Convert[*clb.CertificateInput](expect.TLSConfig)

	return request
}

func buildModifyRuleRequest(loadBalancer *clb.LoadBalancer, currentRule *clb.RuleOutput, rule *utils.Rule, defaultDomain *string) *clb.ModifyRuleRequest {
	if rule == nil || rule.L7RuleConfig == nil {
		return nil
	}
	req := clb.NewModifyRuleRequest()
	req.LoadBalancerId = loadBalancer.LoadBalancerId
	req.ListenerId = currentRule.ListenerId
	req.LocationId = currentRule.LocationId
	final, needUpdate := types.L7RuleConfig(*rule.L7RuleConfig).Apply(req, currentRule, defaultDomain)
	return lo.Ternary(needUpdate, final, nil)
}

func HealthCheckToInt64(enabled bool) *int64 {
	if enabled {
		return common.Int64Ptr(1)
	} else {
		return common.Int64Ptr(0)
	}
}

type Cert struct {
	CertId   string
	CaCertId *string
}

func buildIngressListeners(sc *tencent.SyncContext, ingress types.Ingress) error {
	ins := cluster_service.Instance
	svc := services.NewListenerService(
		services.NewTKEServiceConfigService(ins.TkeServiceConfigClient(), ins.TkeServiceConfigLister()),
		ins.ServiceLister(),
		ins.SecretLister(),
		sc.LoadBalancerContext.DefaultDomain,
		config.Global.ListenerQuota,
	)
	listeners, err := svc.BuildIngressListeners(sc, ingress)
	if err != nil {
		es := errdef.Unwrap(err)
		for _, e := range es {
			ingErr := convertErr(e)
			if errdef.IsFatal(e) {
				return ingErr
			}
			sc.Errors = append(sc.Errors, ingErr)
		}
	}
	sc.IngressContext = &tencent.IngressContext{
		IngressListeners: map[string]*tencent.IngressListener{},
	}
	if exist := utils.HasAutoRewrite(ingress); exist {
		sc.IngressContext.IsRewriteSupport = true
	}
	if rewriteSupport, _ := utils.IsRewriteSupport(ingress); rewriteSupport {
		sc.IngressContext.IsRewriteSupport = true
	}
	for _, lis := range listeners {
		sc.IngressContext.IngressListeners[utils.GetListenerKey(int32(lis.Port), lis.Protocol)] = lo.ToPtr(convertListener(lis))
	}
	return nil
}

func getIngressTLSMap(ingress types.Ingress) (map[string]string, []error) {
	errs := make([]error, 0)
	secretMap := make(map[string]string)
	for _, tls := range ingress.TLS() {
		if len(tls.Hosts) == 0 {
			if secret, exist := secretMap[""]; exist && secret != tls.SecretName {
				errs = append(errs, types.NewError(errcode.SecretConflictError, "", utils.IngressName(ingress)))
				continue
			}
			secretMap[""] = tls.SecretName
		} else {
			for _, host := range tls.Hosts {
				if secret, exist := secretMap[host]; exist && secret != tls.SecretName {
					errs = append(errs, types.NewError(errcode.SecretSpecifyConflictError, "", utils.IngressName(ingress), host))
					continue
				}
				secretMap[host] = tls.SecretName
			}
		}
	}

	return secretMap, errs
}

// ingress变化的时候会调用此接口
// 失败了 会自动enqueue重试
func (lbc *LoadBalancerController) syncLifeCycle(key string, ingressType types.IngressType, ing types.Ingress) (errs []error) {
	cacheKey := IngressCacheKey(ingressType, key)
	startTime := time.Now()

	klog.Infof("Syncing %v", key)

	shouldDeleteIng := false
	if ing.RuntimeObject() == nil {
		ci, ok := lbc.ingCache.get(cacheKey) // kateway 唯一使用的地方 todo 为什么需要
		if !ok {                             // if not in cache,lb is deleted
			klog.Infof(fmt.Sprintf("no such key %s in ingress cache", key))
			return nil
		}
		ing = ci.Ingress.DeepCopy()
		shouldDeleteIng = true
	} else {
		ing = ing.DeepCopy()
		if ing.DeletionTimestamp() != nil {
			shouldDeleteIng = true
		}
	}

	sc := tencent.NewSyncContext(ing)
	span, sc := sc.StartSpan()
	span.SetTag("name", ing.Name())
	span.SetTag("namespace", ing.Namespace())
	span.LogAny("ingress", spew.Sdump(ing))
	span.Finish()

	defer func() {
		klog.Infof("Finished syncing ingress %q (%v)", key, time.Since(startTime))

		if r := recover(); r != nil {
			errs = []error{types.NewError(errcode.UnexpectedError, "internal error")}
			defer panic(r)
		}
		if errCode := ing.GetChaosErrorcode(); errCode != "" {
			if obj, ok := errcode.Registry.Get(errCode); ok {
				errs = append(errs, types.NewError(obj, "inject chaos error", utils.IngressName(ing)))
			}
		}
		lbc.updateUpdateStats(ing, errs, startTime)
		if _, updateError := UpdateStatus(sc, ing, errs); updateError != nil {
			klog.Infof("Unable to update service status to end %v: error %v", key, updateError)
			return
		}
	}()

	if shouldDeleteIng {
		defer func() {
			if len(errs) != 0 {
				lbc.updateUpdateStats(ing, errs, startTime)
			} else {
				metrics.Instance.RemoveObjectMetrics(ing)
				lbc.ingCache.delete(cacheKey)
			}
		}()
		if err := lbc.cleanProtectPods(context.TODO(), ing); err != nil {
			errs = append(errs, err)
			return errs
		}
		if err := tencent.EnsureDeleteLoadBalancer(sc); err != nil {
			errs = append(errs, err)
			return errs
		}
		if err := ing.RemoveFinalizer(sc, utils.IngressFinalizer); err != nil {
			if !k8serrors.IsNotFound(err) {
				klog.Errorf("Ingress remove finalizer error %s. %v", utils.IngressName(ing), err)
				errs = append(errs, err)
				return errs
			}
		}
		return nil
	}

	// kateway 从 qcloud 改成了其他
	if !utils.IsQCLOUDIngress(ing) { // 同步class变化，脱离管理的Ingress
		defer func() {
			if len(errs) != 0 {
				lbc.updateUpdateStats(ing, errs, startTime)
			} else {
				metrics.Instance.RemoveObjectMetrics(ing)
				lbc.ingCache.delete(cacheKey)
			}
		}()
		klog.Errorf("User change the class for ingress: %s/%s\n", ing.Name(), ing.Namespace())
		if err := lbc.cleanProtectPods(context.TODO(), ing); err != nil {
			errs = append(errs, err)
			return errs
		}

		if err := tencent.EnsureDeleteLoadBalancer(sc); err != nil {
			errs = append(errs, err)
			return errs
		}
		if err := lbc.cleanIngressStatus(sc, ing); err != nil {
			errs = append(errs, err)
			return errs
		}
		return nil
	}

	ci := lbc.ingCache.upsert(ing)
	if err := lbc.syncMain(sc); err != nil {
		errs = append(errs, err)
	}
	errs = append(errs, sc.Errors...)

	ci.rsLimitExceeded = errcode.ContainsRSLimitExceededErrOrWarn(errs)

	return errs
}

func (lbc *LoadBalancerController) syncMain(sc *tencent.SyncContext) (err error) {
	span, sc := sc.StartSpan()
	defer func() {
		span.LogError(err)
		span.Finish()
	}()

	ing := sc.Ingress
	if err = ing.AddFinalizer(sc, utils.IngressFinalizer); err != nil {
		return err
	}

	// 给ingress创建对应的LB
	if err = tencent.EnsureCreateLoadBalancer(sc); err != nil {
		return err
	}

	// 检查刚刚创建的LB
	var loadBalancer *clb.LoadBalancer
	loadBalancer, err = sc.LoadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		return err
	}
	// fist lb status should be ok
	if loadBalancer == nil {
		return fmt.Errorf("lb is nil after ensure creating loadbalancer")
	}
	if *loadBalancer.Status != 1 {
		return fmt.Errorf("lb status is not running when get ingress rules")
	}

	uLBId := *loadBalancer.LoadBalancerId
	// 将创建出来的LB的ID写到ingress的annotation里面
	if err = lbc.updateIngressAnnLbId(sc, ing, uLBId); err != nil {
		klog.Errorf("Ingress update annotation error %s. %v\n", utils.IngressName(ing), err)
		return err
	}

	// 同步lb类型到labels中
	newLabels, ok := services.SetLabel(ing.GetObjectMeta().GetLabels(), ingresslabels.LoadBalancerType, *loadBalancer.LoadBalancerType)
	if ok {
		klog.V(4).InfoS("change to labels", ing.Kind(), ing.String(), "labels", newLabels)
		ing.GetObjectMeta().SetLabels(newLabels)
		_, err = ing.Update(sc)
		if err != nil {
			return err
		}
	} else {
		klog.V(4).InfoS("no change to labels", ing.Kind(), ing.String())
	}

	// 初始化自动创建的TKEServiceConfig
	ins := cluster_service.Instance
	tscSvc := services.NewTKEServiceConfigService(ins.TkeServiceConfigClient(), ins.TkeServiceConfigLister())
	if name, auto, _ := tscSvc.GetIngressConfigName(sc, ing); auto {
		if _, err := tscSvc.Get(sc, ing.Namespace(), name); err != nil {
			if errdef.IsFatal(err) {
				return err
			}
			if err := tscSvc.Create(sc, &tkeserviceapi.TkeServiceConfig{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: ing.Namespace(),
					Name:      name,
				},
				Spec: tkeserviceapi.TkeServiceConfigSpec{},
			}); err != nil {
				klog.Errorf("failed to create TKEServiceConfig %s/%s: %v", ing.Namespace(), name, err)
				return err
			}
		}
	}

	// 取出ingress规则，包括http和https
	if err = buildIngressListeners(sc, ing); err != nil {
		return err
	}

	// 检查ingress的annotation里面的一些关键字段有没有填错
	if err = PreCheckIngressBeforeSync(sc, ing, loadBalancer); err != nil {
		return err
	}

	if !sc.LoadBalancerContext.LoadBalancerResource.Spec.Created {
		if err = CheckPortConflict(sc); err != nil {
			return err
		}
	}

	if canProcess := tencent.LockLoadBalancerResource(sc, *loadBalancer.LoadBalancerId, ing); !canProcess {
		err = types.NewError(errcode.ReuseConcurrentOperationError, "", utils.IngressName(ing))
		return err
	}
	defer func(loadBalancerId string) {
		if lockErr := tencent.UnlockLoadBalancerResource(sc, loadBalancerId); lockErr != nil {
			klog.Errorf("LoadBalancerResource unlock failed for lbid: %s, err: %v", loadBalancerId, lockErr)
			err = lockErr
		}
	}(*loadBalancer.LoadBalancerId)

	if err = lbc.ensureLoadBalancer(sc); err != nil {
		return err
	}

	if err = lbc.ensureProtectPods(sc); err != nil {
		return err
	}

	if err = lbc.updateIngressStatus(sc, ing, loadBalancer); err != nil {
		return err
	}

	return nil
}

func CheckPortConflict(sc *tencent.SyncContext) error {
	ingress := sc.Ingress

	resourceListeners := tencent.GetLoadBalancerResourceListener(sc.LoadBalancerContext.LoadBalancerResource)
	for _, resourceListeners := range resourceListeners {
		for _, reference := range resourceListeners.References {
			if reference.Kind == "Ingress" || reference.Kind == "MultiClusterIngress" {
				if reference.Namespace == ingress.Namespace() && reference.Name == ingress.Name() {
					continue
				}
				return types.NewError(errcode.ReuseExistLoadBalancerConflictIngressError, "", utils.IngressName(ingress), sc.LoadBalancerContext.LoadBalancerId, fmt.Sprintf("%s/%s", ingress.Namespace(), ingress.Name()))
			}
		}
	}

	// get listeners not belong to ingress
	desiredKeySet := make(sets.String)
	for key, _ := range sc.IngressContext.IngressListeners {
		desiredKeySet.Insert(key)
	}
	listenerByIngress := tencent.GetLoadBalancerResourceListenerByIngress(ingress, sc.LoadBalancerContext.LoadBalancerResource)
	for _, listener := range listenerByIngress {
		desiredKeySet.Delete(utils.GetListenerKey(listener.Port, listener.Protocol))
	}
	if desiredKeySet.Len() == 0 {
		return nil
	}

	// get listeners belong to other resource
	usedKeySet := make(sets.String)
	listenerByOther := tencent.GetLoadBalancerResourceListenerWithoutIngress(ingress, sc.LoadBalancerContext.LoadBalancerResource)
	for _, listener := range listenerByOther {
		usedKeySet.Insert(utils.GetListenerKey(listener.Port, listener.Protocol))
	}

	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return err
	}
	for desiredKey, _ := range desiredKeySet {
		port, _ := utils.ParseListenerKey(desiredKey)
		for _, listener := range listeners {
			if listener.ListenerId == nil || listener.Port == nil || listener.Protocol == nil {
				continue
			}
			if *listener.Port == port {
				if utils.IsTCPFamilyProtocol(*listener.Protocol) && *listener.ListenerName != TKEDedicatedListenerName {
					return types.NewError(errcode.ConflictListenerError, "", utils.IngressName(ingress), desiredKey)
				}
				for _, protocolList := range []string{utils.PROTOCOL_HTTP, utils.PROTOCOL_HTTPS, utils.PROTOCOL_TCP, utils.PROTOCOL_TCP_SSL} {
					listenerKey := utils.GetListenerKey(int32(port), protocolList)
					if _, exist := usedKeySet[listenerKey]; exist {
						return types.NewError(errcode.ConflictListenerError, "", utils.IngressName(ingress), listenerKey)
					}
				}
			}
		}
	}

	return nil
}

func PreCheckIngressBeforeSync(sc *tencent.SyncContext, ingress types.Ingress, loadbalancer *clb.LoadBalancer) error {
	// "kubernetes.io/ingress.rule-mix" Annotation Check.
	if _, err := utils.IsRuleMixed(ingress); err != nil {
		return err
	}

	// "ingress.cloud.tencent.com/auto-rewrite" Annotation Check.
	if _, err := utils.IsAutoRewrite(ingress); err != nil {
		sc.Errors = append(sc.Errors, err)
	}

	// "ingress.cloud.tencent.com/rewrite-support" Annotation Check.
	if _, err := utils.IsRewriteSupport(ingress); err != nil {
		sc.Errors = append(sc.Errors, err)
	}

	// "kubernetes.io/ingress.http-rules" Annotation Check.
	if httpRules, err := utils.GetHttpRulesList(ingress); err != nil {
		return types.NewError(errcode.RuleHttpAnnotationError, err.Error(), utils.IngressName(ingress))
	} else {
		for _, httpRule := range httpRules {
			if (httpRule.Backend == nil && httpRule.Rewrite == nil) || (httpRule.Backend != nil && httpRule.Rewrite != nil) {
				return types.NewError(errcode.RuleHttpAnnotationError, "", utils.IngressName(ingress))
			}
		}
	}

	// "kubernetes.io/ingress.https-rules" Annotation Check.
	if httpsRules, err := utils.GetHttpsRulesList(ingress); err != nil {
		return types.NewError(errcode.RuleHttpsAnnotationError, err.Error(), utils.IngressName(ingress))
	} else {
		for _, httpsRule := range httpsRules {
			if (httpsRule.Backend == nil && httpsRule.Rewrite == nil) || (httpsRule.Backend != nil && httpsRule.Rewrite != nil) {
				return types.NewError(errcode.RuleHttpsAnnotationError, "", utils.IngressName(ingress))
			}
		}
	}

	// // Rule Host校验
	// for _, rule := range ingress.Rules() {
	//	if rule.Host() != "" {
	//		if match, _ := regexp.MatchString("(\\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)+", rule.Host()); !match {
	//			return types.NewError(ingressError.RuleHostFormatError, "", utils.IngressName(ingress), rule.Host())
	//		}
	//	}
	// }

	// ENI直绑注解校验
	if _, err := utils.IsDirectAccessIngress(ingress); err != nil {
		sc.Errors = append(sc.Errors, err)
	}

	// TkeServiceConfig 自动配置注解校验
	if _, err := utils.GetTkeServiceConfig(ingress); err != nil {
		sc.Errors = append(sc.Errors, err)
	}

	// PassToTarget 默认放通注解校验
	if _, err := utils.IsServicePassToTarget(ingress); err != nil {
		sc.Errors = append(sc.Errors, err)
	}

	if ingress.Type() == types.CoreIngress {
		if hybridType := utils.GetHybridType(ingress); hybridType == utils.HybridType_CCN {
			// 验证当前集群所在VPC已经加入云联网。并且该云联网中已经声明云联网专线资源
			if ok := crossregion.CrossRegionServiceInstance.CheckCCNInstance(config.Global.Region, config.Global.VPCID); !ok {
				return types.NewError(errcode.HybridCNNConfigErrorError, "", utils.IngressName(ingress))
			}
		}
	}

	return nil
}

func (lbc *LoadBalancerController) updateIngressAnnLbId(ctx context.Context, ing types.Ingress, lbId string) error {
	_, err := ing.UpdateAnnotation(ctx, map[string]string{
		QcloudLoadBalancerId: lbId,
	}, nil)
	return err
}

func (lbc *LoadBalancerController) cleanIngressStatus(ctx *tencent.SyncContext, ing types.Ingress) error {
	if config.Global.DryRun {
		return types.NewError(errcode.MockError, "", "cleanIngressStatus", fmt.Sprintf("%s/%s", ing.Namespace(), ing.Name()))
	}
	klog.Infof("cleanIngressStatus %s", utils.IngressName(ing))

	newLabels, ok := services.DeleteLabel(ing.GetObjectMeta().GetLabels(), ingresslabels.LoadBalancerType)
	if ok {
		klog.V(4).InfoS("change to labels", ing.Kind(), ing.String(), "labels", newLabels)
		ing.GetObjectMeta().SetLabels(newLabels)
		_, err := ing.Update(ctx)
		if err != nil {
			return err
		}
	} else {
		klog.V(4).InfoS("no change to labels", ing.Kind(), ing.String())
	}

	_, err := ing.UpdateAnnotation(ctx, nil, []string{
		QcloudLoadBalancerId,
		utils.IngressCondition,
		utils.ClientTokenAnnotation,
	})
	if err != nil {
		klog.Errorf("Ingress update annotation error %s. %v\n", utils.IngressName(ing), err)
		return err
	}

	err = ing.UpdateLoadBalancerStatus(ctx, v1.LoadBalancerStatus{
		Ingress: []v1.LoadBalancerIngress{},
	})
	if err != nil {
		klog.Errorf("Ingress update status error %s. %v\n", utils.IngressName(ing), err)
		return err
	}
	if err = ing.RemoveFinalizer(ctx, utils.IngressFinalizer); err != nil {
		if !k8serrors.IsNotFound(err) {
			return err
		}
	}
	if ing.Type() == types.MultiClusterIngress {
		// 删除多集群 Ingress 的 Conditions
		ingress_wrapper.SetLoadBalancerConditions(ctx, ing.Namespace(), ing.Name(), []metav1.Condition{})
	}
	return nil
}

func (lbc *LoadBalancerController) updateIngressStatus(ctx context.Context, ing types.Ingress, lb *clb.LoadBalancer) error {
	var currentIngress types.Ingress
	var err error

	ingress, err := tencent.IngressGetter(ing.Type(), ing.Namespace(), ing.Name())
	if err != nil {
		return err
	}
	currentIngress = ingress

	updateAnnotation := make(map[string]string)
	deleteAnnotation := make([]string, 0)
	oldipv6, ipv6exist := currentIngress.Annotations()[utils.LoadBalancerIPv6Annontation]
	oldnatipv6, natipv6exist := currentIngress.Annotations()[utils.LoadBalancerNatIPv6Annontation]
	if lb.AddressIPVersion != nil && *lb.AddressIPVersion == "ipv6" && lb.IPv6Mode != nil && lb.AddressIPv6 != nil {
		if *lb.IPv6Mode == "IPv6Nat64" {
			if oldnatipv6 != *lb.AddressIPv6 || ipv6exist {
				updateAnnotation[utils.LoadBalancerNatIPv6Annontation] = *lb.AddressIPv6
				deleteAnnotation = append(deleteAnnotation, utils.LoadBalancerIPv6Annontation)
			}
		} else {
			if oldipv6 != *lb.AddressIPv6 || natipv6exist {
				updateAnnotation[utils.LoadBalancerIPv6Annontation] = *lb.AddressIPv6
				deleteAnnotation = append(deleteAnnotation, utils.LoadBalancerNatIPv6Annontation)
			}
		}
	} else if ipv6exist || natipv6exist {
		deleteAnnotation = append(deleteAnnotation, utils.LoadBalancerIPv6Annontation)
		deleteAnnotation = append(deleteAnnotation, utils.LoadBalancerNatIPv6Annontation)
	}
	if len(updateAnnotation) != 0 || len(deleteAnnotation) != 0 {
		ing, err = ing.UpdateAnnotation(ctx, updateAnnotation, deleteAnnotation)
		if err != nil {
			klog.Errorf("Ingress update annotation error %s. %v\n", utils.IngressName(ing), err)
			return err
		}
	}

	if ing.Type() == types.CoreIngress && !utils.BackendOnlyIngress(ing) ||
		ing.Type() == types.MultiClusterIngress {
		// 使用SNI功能监听器下会有多个证书，无法在注解中提供单一的证书ID，保持原有逻辑提供列表第一个证书的ID。
		// 同步证书 Annotation
		if len(ing.TLS()) != 0 {
			ingress, err := tencent.IngressGetter(ing.Type(), ing.Namespace(), ing.Name())
			if err != nil {
				return err
			}
			currentIngress = ingress

			// 同步注解中的证书Id，以下两种情况不会配置注解中的证书
			// 1. 没有配置默认证书
			// 2. 默认证书的格式非法
			if certId, _, err := lbc.getDefaultCertId(ing); err == nil {
				currentCertId := ingAnnotations(currentIngress.Annotations()).certId()
				if currentCertId != certId {
					klog.Infof("update secret(%s),certId:%s", ing.TLS()[0].SecretName, certId)
					if ing, err = ing.UpdateAnnotation(ctx, map[string]string{services.QcloudCertID: certId}, nil); err != nil {
						klog.Errorf("Ingress update annotation error %s. %v\n", utils.IngressName(ing), err)
						return err
					}
				}
			}
		}
	}

	// 补丁逻辑，负载均衡存在申请到负载均衡却不返回负载均衡ID情况，该情况会导致Ingress Status空缺。
	// 同时为了避免覆盖其他的Ingress Controller可能使用的VIP，这个补丁逻辑的限制条件较为严格。
	vips, domain := utils.GetVipAndDomain(lb)
	// loadBalancerStatus := ing.StatusLoadBalancer()
	// if len(loadBalancerStatus.Ingress) != 0 {
	//	vipMap := make(map[string]bool)
	//	for _, vip := range vips {
	//		vipMap[*vip] = true
	//	}
	//	for _, statusIngress := range loadBalancerStatus.Ingress {
	//		if statusIngress.IP != "" {
	//			if _, exist := vipMap[statusIngress.IP]; !exist {
	//				klog.Infof("skip update ingress status(%s), vip(%s) not expect.", utils.IngressName(ing), statusIngress.IP)
	//				return nil
	//			}
	//		}
	//	}
	//
	//	if domain != nil {
	//		for _, statusIngress := range loadBalancerStatus.Ingress {
	//			if statusIngress.Hostname != "" && statusIngress.Hostname != *domain {
	//				klog.Infof("skip update ingress status(%s), hostName(%s) not expect.", utils.IngressName(ing), statusIngress.Hostname)
	//				return nil
	//			}
	//		}
	//	}
	// }
	if err := tencent.UpdateIngressEntry(ctx, ing, vips, domain); err != nil {
		return err
	}
	return nil
}

// BackendType ENI: backend is EniIp
// BackendType CVM: backend is InstanceId
func NewENIBatchTarget(listenerId string, LocationId string, eniip string, port int64) *clb.BatchTarget {
	batchTarget := &clb.BatchTarget{
		ListenerId: &listenerId,
		LocationId: &LocationId,
		EniIp:      &eniip,
		Port:       &port,
	}
	return batchTarget
}

// BackendType ENI: backend is EniIp
// BackendType CVM: backend is InstanceId
func NewENICreateBatchTarget(listenerId string, LocationId string, eniip string, port int64, weight int64, tag string) *clb.BatchTarget {
	if tag != "" {
		return &clb.BatchTarget{
			ListenerId: &listenerId,
			LocationId: &LocationId,
			EniIp:      &eniip,
			Weight:     &weight,
			Port:       &port,
			Tag:        &tag,
		}
	}
	return &clb.BatchTarget{
		ListenerId: &listenerId,
		LocationId: &LocationId,
		EniIp:      &eniip,
		Weight:     &weight,
		Port:       &port,
	}
}

// BackendType ENI: backend is EniIp
// BackendType CVM: backend is InstanceId
func NewRsWeightRule(backendType utils.BackendType, listenerId string, locationId string, backend string, port int64, weight int64) *clb.RsWeightRule {
	target := &clb.Target{
		Port:   &port,
		Weight: &weight,
	}
	if utils.IsENILikeType(string(backendType)) {
		target.EniIp = &backend
	} else if backendType == utils.CVM {
		target.InstanceId = &backend
	} else {
		// Unexpected Here
	}

	batchTarget := &clb.RsWeightRule{
		ListenerId: &listenerId,
		LocationId: &locationId,
		Targets: []*clb.Target{
			target,
		},
	}
	return batchTarget
}

func getDataCertID(secret *v1.Secret, ing types.Ingress) (string, *string, error) {
	dataCrtID, dataCrtIDExist := secret.Data[services.QcloudCertID]
	dataCaCrtID, dataCaCrtIDExist := secret.Data[services.QcloudCACertID]
	// 如果data区域有服务器证书ID则不去看annotation区域（兼容老逻辑）
	if !dataCrtIDExist {
		return "", nil, nil
	}
	if len(dataCrtID) == 0 {
		return "", nil, types.NewError(errcode.SecretContentError, "", utils.IngressName(ing), secret.Name)
	}
	if !dataCaCrtIDExist {
		return string(dataCrtID), nil, nil
	}
	if len(dataCaCrtID) == 0 {
		return string(dataCrtID), nil, types.NewError(errcode.SecretContentError, "", utils.IngressName(ing), secret.Name)
	}
	return string(dataCrtID), lo.ToPtr(string(dataCaCrtID)), nil
}

func getAnnoCertID(secret *v1.Secret, ing types.Ingress) (string, *string, error) {
	// 如果data区域不存在服务器证书ID，且secret类型不是TLS，直接返回报错
	if secret.Type != v1.SecretTypeTLS {
		return "", nil, types.NewError(errcode.SecretContentError, "", utils.IngressName(ing), secret.Name)
	}
	// 如果data区域不存在服务器证书ID，且secret类型是TLS，继续查看annotation区域
	annoCrtID, annoCrtIDExist := secret.GetAnnotations()[types.AnnotationCertID]
	annoCaCrtID, annoCaCrtIDExist := secret.GetAnnotations()[types.AnnotationCaCertID]
	if !annoCrtIDExist {
		// annotation区域服务器证书ID不存在，则返回错误
		return "", nil, nil
	}
	if annoCrtID == "" {
		return "", nil, types.NewError(errcode.SecretContentError, "", utils.IngressName(ing), secret.Name)
	}
	if !annoCaCrtIDExist {
		return annoCrtID, nil, nil
	}
	if annoCaCrtID == "" {
		return annoCrtID, nil, types.NewError(errcode.SecretContentError, "", utils.IngressName(ing), secret.Name)
	}
	return annoCrtID, lo.ToPtr(annoCaCrtID), nil
}

func getCertID(secret *v1.Secret, ing types.Ingress) (string, *string, error) {
	// 从secret的data区域获取证书ID（兼容Oquaue secret）
	svrID, caID, err := getDataCertID(secret, ing)
	if err != nil || svrID != "" || secret.Type != v1.SecretTypeTLS {
		// 满足其中一个条件：1.存在报错 2.服务器证书ID不为空 3.secret类型不是TLS，直接返回
		return svrID, caID, err
	}
	// 尝试从secret的annotation区域获取证书ID（tls secret）
	return getAnnoCertID(secret, ing)
}

func (lbc *LoadBalancerController) getDefaultCertId(ing types.Ingress) (string, *string, error) {
	tlsMap, _ := getIngressTLSMap(ing)
	secretName, exist := tlsMap[""]
	if !exist {
		return "", nil, types.NewError(errcode.SecretEmptyError, "", utils.IngressName(ing))
	}
	secret, err := cluster_service.Instance.SecretLister().Secrets(ing.Namespace()).Get(secretName)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return "", nil, types.NewError(errcode.SecretNotFoundError, "", utils.IngressName(ing), secretName)
		}
		klog.Errorf("SecretLister get error for service %s/%s. %v", ing.Namespace(), secretName, err)
		return "", nil, err
	}
	return getCertID(secret, ing)
}

func getCertIdBySecretName(ing types.Ingress, secretName string, lister listerscorev1.SecretLister) (string, *string, error) {
	if secretName == "" {
		return "", nil, types.NewError(errcode.SecretEmptyError, "", utils.IngressName(ing))
	}
	secret, err := lister.Secrets(ing.Namespace()).Get(secretName)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return "", nil, types.NewError(errcode.SecretNotFoundError, "", utils.IngressName(ing), secretName)
		}
		klog.Errorf("SecretLister get error for service %s/%s. %v", ing.Namespace(), secretName, err)
		return "", nil, err
	}
	ingressCertId, ok := secret.Data[services.QcloudCertID]
	if !ok {
		return "", nil, types.NewError(errcode.SecretContentError, "", utils.IngressName(ing), secretName)
	}
	if ingressCaCertId, ok := secret.Data[services.QcloudCACertID]; ok {
		return string(ingressCertId), common.StringPtr(string(ingressCaCertId)), nil
	}
	return string(ingressCertId), nil, nil
}

func (lbc *LoadBalancerController) updateUpdateStats(ing types.Ingress, errs []error, startTime time.Time) {
	if len(errs) == 0 {
		services.UploadMetricsAndEvent(ing, types.NewError(errcode.Success, ""))
		metrics.Instance.UpdateSyncTime(ing, errcode.Success.Code, time.Since(startTime))
		return
	}

	errorCodeDeduplication := make(map[string]bool)
	for _, err := range errs {
		ingError := types.NewError(errcode.UnexpectedError, err.Error())
		returnCode := errcode.UnexpectedError.Code
		if wrapperErr, ok := lo.ErrorsAs[*types.Error](err); ok {
			ingError = wrapperErr
			returnCode = wrapperErr.ErrorCode.Code
		}

		if _, exist := errorCodeDeduplication[returnCode]; exist { // 同一个资源对象同一个错误码的事件是会覆盖，没有必要再次上报。
			continue
		}
		errorCodeDeduplication[returnCode] = true

		services.UploadMetricsAndEvent(ing, ingError)
		metrics.Instance.UpdateSyncTime(ing, returnCode, time.Since(startTime))
	}
}

// kateway todo 会回写 TkeServiceConfig
func (lbc *LoadBalancerController) ensureTkeServiceConfig(sc *tencent.SyncContext, ingress types.Ingress) error {
	span, sc := sc.StartSpan()
	defer span.Finish()

	// Already Prechecked Error of the annotation.
	if serviceConfigAuto, _ := utils.IsTkeServiceConfigAuto(ingress); serviceConfigAuto {
		autoServiceConfigName := utils.IngressAutoServiceConfigName(ingress)

		tkeServiceConfig, _ := cluster_service.Instance.TkeServiceConfigLister().TkeServiceConfigs(ingress.Namespace()).Get(autoServiceConfigName)
		for i := 0; i < 3; i++ {
			if err := updateTkeServiceConfig(sc, tkeServiceConfig); err != nil {
				if tkeServiceConfig, err = cluster_service.Instance.TkeServiceConfigLister().TkeServiceConfigs(ingress.Namespace()).Get(autoServiceConfigName); err != nil {
					if k8serrors.IsNotFound(err) {
						klog.Infof("TkeServiceConfig Sync Error. TkeServiceConfig Not Exist. %v", err)
						return nil
					}
					return err
				}
			} else { // TkeServiceConfig 的状态修改成功处理
				return nil
			}
		}
		klog.Infof("TkeServiceConfig Sync Retry Error.")
	}
	return nil
}

func syncLbPassToTarget(sc *tencent.SyncContext, ing types.Ingress, loadBalancer *clb.LoadBalancer, region string) {
	if _, exist := utils.HasServicePassToTarget(ing); exist {
		if loadBalancer.VpcId != nil && *loadBalancer.VpcId == "0" {
			// 基础网络负载均衡不支持默认放通，忽略该注解。
			return
		}

		passToTarget, _ := utils.IsServicePassToTarget(ing)
		if loadBalancer.LoadBalancerPassToTarget != nil && (*loadBalancer.LoadBalancerPassToTarget != passToTarget) {
			request := clbinternal.NewModifyLoadBalancerAttributesRequest()
			request.LoadBalancerId = loadBalancer.LoadBalancerId
			request.LoadBalancerPassToTarget = common.BoolPtr(passToTarget)
			if _, err := tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.From(sc, ing, region), request); err != nil {
				sc.Errors = append(sc.Errors, types.NewError(errcode.PassToTargetSettingError, err.Error(), utils.IngressName(ing)))
			}
		}
	}
}

func syncLbProtected(sc *tencent.SyncContext, ing types.Ingress, loadBalancer *clb.LoadBalancer, region string) {
	modificationProtection, err := utils.IsModificationProtection(ing)
	if err != nil {
		sc.Errors = append(sc.Errors, err)
		return
	}
	currentModificationProtection := false
	if loadBalancer.AttributeFlags != nil && len(loadBalancer.AttributeFlags) != 0 {
		for _, attributeFlag := range loadBalancer.AttributeFlags {
			if attributeFlag != nil && *attributeFlag == "OperateProtect" {
				currentModificationProtection = true
			}
		}
	}
	if modificationProtection != currentModificationProtection {
		if !sc.LoadBalancerContext.LoadBalancerResource.Spec.Created { // 使用已有与该功能相冲突
			sc.Errors = append(sc.Errors, types.NewError(errcode.ReuseNotSupportModificationProtectionError, "", utils.IngressName(ing)))
			return
		}

		if (modificationProtection == true && currentModificationProtection == false) || (modificationProtection == false && currentModificationProtection == true) {
			operateProtectRequest := clbinternal.NewModifyLBOperateProtectRequest()
			operateProtectRequest.LoadBalancerId = loadBalancer.LoadBalancerId
			operateProtectRequest.OperateProtect = common.BoolPtr(modificationProtection)
			operateProtectRequest.RoleName = common.StringPtr("TKE_QCSRole")
			if modificationProtection {
				kv := make(map[string]string)
				kv["product"] = "tke"
				kv["clusterType"] = "tke"
				if utils.IsInEKSCluster() {
					kv["clusterType"] = "eks"
				}
				kv["resourceType"] = "ingress"
				kv["regionName"] = config.Global.Region
				kv["clusterId"] = config.Global.ClusterName
				kv["namespace"] = ing.Namespace()
				if ing.Namespace() == "" {
					kv["namespace"] = "default"
				}
				kv["name"] = ing.Name()

				kvJson, err := json.Marshal(kv)
				if err != nil { // 固定Key, value也是合法内容，转Json字符串理论上没有任何风险。
					klog.Errorf("Unexpected error for %s. %v", utils.IngressName(ing), err)
					return
				}
				operateProtectRequest.LinkInfo = common.StringPtr(string(kvJson))
				operateProtectRequest.AllowOperate = common.BoolPtr(true)
			}
			if _, err = tencentapi.Instance.ModifyLBOperateProtect(cloudctx.From(sc, ing, region), operateProtectRequest); err != nil {
				if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
					if strings.Contains(sdkError.Message, "don't support operate peotect") {
						sc.Errors = append(sc.Errors, types.NewError(errcode.ModificationProtectionDoNotSupportError, sdkError.Error(), utils.IngressName(ing)))
						return
					} else if strings.Contains(sdkError.Message, "cannot be operate") && strings.Contains(sdkError.Message, "operate protection") {
						sc.Errors = append(sc.Errors, types.NewError(errcode.ModificationProtectionConflictError, sdkError.Error(), utils.IngressName(ing)))
						return
					}
				}
				sc.Errors = append(sc.Errors, err)
			}
		}
	}
	return
}

func syncSecurityGroups(sc *tencent.SyncContext, ingress types.Ingress, loadBalancer *clb.LoadBalancer, region string) {
	if _, exist := utils.HasServiceSecurityGroups(ingress); exist {
		if _, exist := utils.GetExistLbId(ingress); exist {
			sc.Errors = append(sc.Errors, types.NewError(errcode.SecurityGroupsNotSupportError, "", utils.IngressName(ingress)))
			return
		}

		securityGroups := utils.GetServiceSecurityGroups(ingress)
		securityGroupsMap := make(map[string]bool)
		for _, securityGroup := range securityGroups {
			securityGroupsMap[securityGroup] = true
		}

		needToModifySecurityGroups := false
		if len(loadBalancer.SecureGroups) != len(securityGroups) {
			needToModifySecurityGroups = true
		} else {
			for _, secureGroup := range loadBalancer.SecureGroups {
				if _, exist := securityGroupsMap[*secureGroup]; !exist {
					needToModifySecurityGroups = true
					break
				}
			}
		}

		if needToModifySecurityGroups {
			securityGroupsRequest := clb.NewSetLoadBalancerSecurityGroupsRequest()
			securityGroupsRequest.LoadBalancerId = loadBalancer.LoadBalancerId
			if len(securityGroups) > 0 {
				securityGroupsRequest.SecurityGroups = common.StringPtrs(securityGroups)
			}
			if _, err := tencentapi.Instance.SetLoadBalancerSecurityGroups(cloudctx.From(sc, ingress, region), securityGroupsRequest); err != nil {
				if sdkError, ok := err.(*errors2.TencentCloudSDKError); ok {
					// [TencentCloudSDKError] Code=InvalidParameter.FormatError, Message=The format of SecurityGroupId '' is not valid.
					// [TencentCloudSDKError] Code=InvalidParameterValue.Length, Message=The length of SecurityGroupId 'sg-ocoafjc8ac' is not valid.
					// [TencentCloudSDKError] Code=InvalidParameterValue, Message=Check security groups failed, sg-ocoafjc9: USG doesn't exist
					if (sdkError.Code == "InvalidParameter.FormatError" || sdkError.Code == "InvalidParameterValue.Length") && strings.Contains(sdkError.Message, "SecurityGroupId") {
						sc.Errors = append(sc.Errors, types.NewError(errcode.SecurityGroupsFormatError, sdkError.Error(), utils.IngressName(ingress)))
						return
					} else if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "doesn't exist") {
						sc.Errors = append(sc.Errors, types.NewError(errcode.SecurityGroupsNotExistError, sdkError.Error(), utils.IngressName(ingress)))
						return
					}
				}
				sc.Errors = append(sc.Errors, err)
			}
		}
	}
}

// kateway todo 将clb端监听器信息(仅7层)同步到 tkeserviceconfig, 有无风险？
// kateway todo ingress controller 和 service controller 都会写l7监听器到该cr， 有无风险?
func updateTkeServiceConfig(sc *tencent.SyncContext, tkeServiceConfig *tkeserviceapi.TkeServiceConfig) error {
	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return err
	}

	ruleOutputMap := ToRuleOutputMap(listeners)
	l7ListenerConfigMap := ToL7ListenerConfigMap(tkeServiceConfig)
	l7RuleConfigMap := ToL7RuleConfigMap(tkeServiceConfig, sc.LoadBalancerContext.DefaultDomain)

	tkeServiceConfig.Spec.LoadBalancer.L7Listeners = make([]*tkeserviceapi.L7ListenerConfig, 0)
	for _, ingressListener := range sc.IngressContext.IngressListeners {
		listener := listeners[utils.GetListenerKey(ingressListener.Port, ingressListener.Protocol)]
		config := buildL7ListenerConfig(listener, ingressListener.IngressRules, ingressListener.Protocol, ingressListener.Port, l7ListenerConfigMap, l7RuleConfigMap, ruleOutputMap)
		tkeServiceConfig.Spec.LoadBalancer.L7Listeners = append(tkeServiceConfig.Spec.LoadBalancer.L7Listeners, config)
	}

	if _, err := cluster_service.Instance.TkeServiceConfigClient().CloudV1alpha1().TkeServiceConfigs(tkeServiceConfig.Namespace).Update(sc, tkeServiceConfig, metav1.UpdateOptions{}); err != nil {
		klog.Errorf("TkeServiceConfigLister update error for %s/%s. %v", tkeServiceConfig.Namespace, tkeServiceConfig.Name, err)
		return err
	}
	return nil
}

// 将用户定义的 Ingress Rules 配置映射到 TkeServiceConfig
//
// 1. 优先映射当前 TkeServiceConfig 中已经存在的配置内容。
// 2. 不存在时映射当前负载均衡上的实际配置。
// 3. 两者都不存在的情况理论上不存在，实际处理忽略这个配置。
func buildL7ListenerConfig(listener *clb.Listener, rules []*utils.Rule, protocol string, port int32, l7ListenerConfigMap map[string]*tkeserviceapi.L7ListenerConfig, l7RuleConfigMap map[string]*tkeserviceapi.L7RuleConfig, ruleOutputMap map[string]*clb.RuleOutput) *tkeserviceapi.L7ListenerConfig {
	httpL7ListenerConfig := &tkeserviceapi.L7ListenerConfig{
		Protocol:        protocol,
		Port:            port,
		Domains:         []*tkeserviceapi.L7DomainConfig{},
		KeepaliveEnable: utils.Int32Ptr(0),
	}
	if listener != nil {
		if listener.KeepaliveEnable != nil {
			httpL7ListenerConfig.KeepaliveEnable = utils.Int32Ptr(int32(*listener.KeepaliveEnable))
		}
	}

	// Check Exist L7ListenerConfig
	currentRule := make(map[string]map[string]bool)
	for _, httpRule := range rules {
		if _, exist := currentRule[httpRule.Host]; !exist {
			currentRule[httpRule.Host] = make(map[string]bool)
		}
		currentRule[httpRule.Host][httpRule.Path] = true
	}
	listenerKey := ruleToListener(protocol, int64(port))
	if config, exist := l7ListenerConfigMap[listenerKey]; exist {
		httpL7ListenerConfig = config
		newDomains := make([]*tkeserviceapi.L7DomainConfig, 0)
		for index, domain := range httpL7ListenerConfig.Domains {
			if _, exist := currentRule[domain.Domain]; exist {
				newDomains = append(newDomains, httpL7ListenerConfig.Domains[index])
			}
		}
		httpL7ListenerConfig.Domains = newDomains
		for _, domain := range httpL7ListenerConfig.Domains {
			newRules := make([]*tkeserviceapi.L7RuleConfig, 0)
			for index, rule := range domain.Rules {
				if _, exist := currentRule[domain.Domain][rule.Url]; exist {
					newRules = append(newRules, domain.Rules[index])
				}
			}
			domain.Rules = newRules
		}
	}

	for _, httpRule := range rules {
		key := ruleToPath(protocol, int64(port), httpRule.Host, httpRule.RealPath)
		domain := httpRule.Domain
		if listener != nil {
			d := types.NewCLBListener(listener).GetDomain(domain.Host)
			if d != nil {
				domain = *d
			}
		}
		if config, exist := l7RuleConfigMap[key]; exist {
			insertL7RuleConfig(httpL7ListenerConfig, domain, httpRule.RealPath, config)
		} else if ruleOutput, exist := ruleOutputMap[key]; exist {
			if httpL7ListenerConfig.DefaultServer == nil && *ruleOutput.DefaultServer {
				httpL7ListenerConfig.DefaultServer = common.StringPtr(httpRule.Host)
			}
			insertRuleOutput(httpL7ListenerConfig, domain, httpRule.RealPath, ruleOutput)
		}
	}

	if httpL7ListenerConfig.KeepaliveEnable == nil {
		if listener != nil && listener.KeepaliveEnable != nil {
			httpL7ListenerConfig.KeepaliveEnable = utils.Int32Ptr(int32(*listener.KeepaliveEnable))
		}
	}
	return httpL7ListenerConfig
}

func insertL7RuleConfig(l7ListenerConfigs *tkeserviceapi.L7ListenerConfig, domain types.Domain, path string, l7RuleConfig *tkeserviceapi.L7RuleConfig) {
	l7DomainConfig := getOrCreateL7DomainConfig(l7ListenerConfigs, domain)
	getOrCreateL7RuleConfig(l7DomainConfig, path, l7RuleConfig)
}

func insertRuleOutput(l7ListenerConfigs *tkeserviceapi.L7ListenerConfig, domain types.Domain, path string, ruleOutput *clb.RuleOutput) {
	l7RuleConfig := convertRuleOutputToL7RuleConfig(ruleOutput)
	insertL7RuleConfig(l7ListenerConfigs, domain, path, l7RuleConfig)
}

func convertRuleOutputToL7RuleConfig(output *clb.RuleOutput) *tkeserviceapi.L7RuleConfig {
	session := &tkeserviceapi.SessionConfig{
		Enable: false,
	}
	if output.SessionExpireTime != nil && *output.SessionExpireTime != 0 {
		session = &tkeserviceapi.SessionConfig{
			Enable:            true,
			SessionExpireTime: utils.Int32Ptr(int32(*output.SessionExpireTime)),
		}
	}

	healthCheck := &tkeserviceapi.L7HealthCheck{}
	if output.HealthCheck != nil {
		hc := output.HealthCheck
		if lo.FromPtr(hc.HealthSwitch) == 1 {
			conv.ConvertInto(*hc, healthCheck)
		}
	}

	return &tkeserviceapi.L7RuleConfig{
		Url:         *output.Url,
		Scheduler:   output.Scheduler,
		ForwardType: output.ForwardType,
		Session:     session,
		HealthCheck: healthCheck,
	}
}

func getOrCreateL7DomainConfig(l7ListenerConfigs *tkeserviceapi.L7ListenerConfig, domain types.Domain) *tkeserviceapi.L7DomainConfig {
	for index, cfg := range l7ListenerConfigs.Domains {
		if cfg.Domain == domain.Host {
			return l7ListenerConfigs.Domains[index]
		}
	}
	domainConfig := &tkeserviceapi.L7DomainConfig{
		Domain: domain.Host,
		Http2:  domain.HTTP2Enabled,
		Rules:  []*tkeserviceapi.L7RuleConfig{},
	}
	l7ListenerConfigs.Domains = append(l7ListenerConfigs.Domains, domainConfig)
	return domainConfig
}

func getOrCreateL7RuleConfig(l7DomainConfig *tkeserviceapi.L7DomainConfig, path string, config *tkeserviceapi.L7RuleConfig) {
	for index, rule := range l7DomainConfig.Rules {
		if rule.Url == path {
			l7DomainConfig.Rules[index] = config
			return
		}
	}
	l7DomainConfig.Rules = append(l7DomainConfig.Rules, config)
}
