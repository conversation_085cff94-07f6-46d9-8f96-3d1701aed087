package controller

import (
	"fmt"
	"sync"

	"git.woa.com/kateway/pkg/domain/types"

	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
)

type ingressCache struct {
	mu         sync.Mutex // protects serviceMap
	ingressMap map[string]*cachedIngress
}

type cachedIngress struct {
	types.Ingress
	rsLimitExceeded bool
	ProtectPods     sync.Map // 该 ingress 下所有因为开启优雅删除的pod
}

func IngressCacheKey(ingressType types.IngressType, ingressKey string) string {
	return fmt.Sprintf("%s/%s", ingressType, ingressKey)
}

func (c *ingressCache) get(key string) (*cachedIngress, bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	ci, ok := c.ingressMap[key]
	return ci, ok
}

//func (c *ingressCache) forEach(fn func(string, *CachedIngress)) {
//	copy := make(map[string]*CachedIngress, len(c.ingressMap))
//	c.mu.Lock()
//	for k, v := range c.ingressMap {
//		vv := &CachedIngress{Ingress: v.Ingress.DeepCopy(), rsLimitExceeded: v.rsLimitExceeded}
//		copy[k] = vv
//	}
//	c.mu.Unlock()
//	for k, v := range copy {
//		fn(k, v)
//	}
//}

func (c *ingressCache) forEach(fn func(string, *cachedIngress)) {
	c.mu.Lock()
	for k, v := range c.ingressMap {
		fn(k, v)
	}
	c.mu.Unlock()
}

// upsert 如果存在则更新，不存在则插入
func (c *ingressCache) upsert(ing types.Ingress) *cachedIngress {
	c.mu.Lock()
	defer c.mu.Unlock()
	key := IngressCacheKey(ing.Type(), utils.IngressName(ing))
	ci, ok := c.ingressMap[key]
	if ok {
		ci.Ingress = ing
	} else {
		ci = &cachedIngress{Ingress: ing}
		c.ingressMap[key] = ci
	}

	return ci
}

//func (s *ingressCache) getOrCreate(key string) *cachedIngress {
//	s.mu.Lock()
//	defer s.mu.Unlock()
//	ci, ok := s.ingressMap[key]
//	if !ok {
//		s.ingressMap[key] = &cachedIngress{}
//	}
//	return ci
//}

func (c *ingressCache) set(ing *cachedIngress) {
	//klog.Infof("cache set key %s/%s. \n", ing.Type(), ingressName)
	c.mu.Lock()
	defer c.mu.Unlock()
	key := IngressCacheKey(ing.Type(), utils.IngressName(ing))
	c.ingressMap[key] = ing
}

func (c *ingressCache) delete(key string) {
	// klog.Infof("cache delete key %s/%s. \n", ingressType, ingressName)
	c.mu.Lock()
	defer c.mu.Unlock()
	delete(c.ingressMap, key)
}
