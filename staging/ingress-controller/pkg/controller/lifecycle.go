package controller

import (
	"fmt"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	v1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/ingress/errcode"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/tke-ingress-controller/pkg/plugins/tencent"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

func (lbc *LoadBalancerController) ensureLoadBalancer(sc *tencent.SyncContext) (err error) {
	span, sc := sc.StartSpan()
	defer func() {
		if err != nil {
			span.LogError(err)
		}
		span.Finish()
	}()

	ing := sc.Ingress

	switch ing.Type() {
	case types.CoreIngress:
		if utils.BackendOnlyIngress(sc.Ingress) { // kateway 子集群 ingress
			if err := lbc.ensureBackendOnlyIngress(sc, ing); err != nil {
				klog.Errorf("Ensure load balancer type for backendonly ingress: %s/%s error: %v", ing.Namespace(), ing.Name(), err)
				return err
			}
		} else if err := lbc.ensureIngress(sc, ing); err != nil { // kateway 单集群 ingress
			klog.Errorf("Ensure load balancer type for ingress: %s/%s error: %v", ing.Namespace(), ing.Name(), err)
			return err
		}
	case types.MultiClusterIngress:
		if err := lbc.ensureMultiClusterIngress(sc, ing); err != nil { // kateway 多集群 ingress
			klog.Errorf("Ensure load balancer type for multiclusteringress: %s/%s error: %v", ing.Namespace(), ing.Name(), err)
			return err
		}
	}

	return nil
}

func (lbc *LoadBalancerController) ensureIngress(sc *tencent.SyncContext, ing types.Ingress) (err error) {
	span, sc := sc.StartSpan()
	defer func() {
		if err != nil {
			span.LogError(err)
		}
		span.Finish()
	}()

	if err = lbc.ensureLoadBalancerDetail(sc); err != nil {
		return err
	}

	// 创建listener
	if err = lbc.ensureIngressLBListener(sc, ing); err != nil {
		return err
	}

	// 创建规则，并绑定targets
	if err = lbc.ensureIngressLBListenerRules(sc, ing); err != nil {
		return err
	}

	// 回收listener
	if err = lbc.ensureIngressLBListenerDelete(sc, ing); err != nil {
		return err
	}

	if err := tencent.EnsureLoadBalancerResource(sc); err != nil {
		return err
	}

	if err = lbc.ensureTkeServiceConfig(sc, ing); err != nil {
		return err
	}
	return nil
}

func (lbc *LoadBalancerController) ensureMultiClusterIngress(sc *tencent.SyncContext, ing types.Ingress) (err error) {
	span, sc := sc.StartSpan()
	defer func() {
		if err != nil {
			span.LogError(err)
		}
		span.Finish()
	}()

	if err = lbc.ensureLoadBalancerDetail(sc); err != nil {
		return err
	}

	// 创建 listener
	if err = lbc.ensureIngressLBListener(sc, ing); err != nil {
		return err
	}

	// 创建规则，不绑定 targets
	if err = lbc.ensureIngressLBListenerOnlyRules(sc, ing); err != nil {
		return err
	}

	// 回收 listener
	if err = lbc.ensureIngressLBListenerDelete(sc, ing); err != nil {
		return err
	}

	if err := tencent.EnsureLoadBalancerResource(sc); err != nil {
		return err
	}

	if err = lbc.ensureTkeServiceConfig(sc, ing); err != nil {
		return err
	}
	return nil
}

func (lbc *LoadBalancerController) ensureBackendOnlyIngress(sc *tencent.SyncContext, ing types.Ingress) (err error) {
	span, sc := sc.StartSpan()
	defer func() {
		if err != nil {
			span.LogError(err)
		}
		span.Finish()
	}()

	for _, ingressListener := range sc.IngressContext.IngressListeners {
		if err := lbc.ensureDeletedIngressRuleTargets(sc, ing, ingressListener); err != nil {
			return err
		}
		if err := lbc.ensureRuleTargets(sc, ing, ingressListener); err != nil {
			return err
		}
		if err := lbc.ensureRewriteRuleTargets(sc, ing, ingressListener); err != nil {
			return err
		}
	}

	if err := tencent.EnsureLoadBalancerResource(sc); err != nil {
		return err
	}
	return nil
}

func (lbc *LoadBalancerController) ensureDeletedIngressRuleTargets(sc *tencent.SyncContext, ingress types.Ingress, ingressListener *tencent.IngressListener) (err error) {
	span, sc := sc.StartSpan()
	defer span.Finish()

	loadBalancer, err := sc.LoadBalancerContext.GetLoadBalancer(sc)
	if err != nil {
		return err
	}
	listenerKey := utils.GetListenerKey(ingressListener.Port, ingressListener.Protocol)
	listeners, err := sc.LoadBalancerContext.GetListeners(sc)
	if err != nil {
		return err
	}
	listener := listeners[listenerKey]
	if listener == nil {
		return types.NewError(errcode.ListenerNotExistedInLBWhenEnsuringTargets, fmt.Sprintf("listener: %s not existed", listenerKey))
	}

	rules := ingressListener.IngressRules

	listenerBackends, err := sc.LoadBalancerContext.GetListenersBackend(sc)
	if err != nil {
		return err
	}

	ruleBackends := listenerBackends[utils.GetListenerKey(ingressListener.Port, ingressListener.Protocol)]

	batchDeregiterTargets := make([]*clb.BatchTarget, 0)
	toDelete := diffDeletedRules(rules, listener, loadBalancer, sc.LoadBalancerContext.DefaultDomain)

	for _, locationID := range toDelete {
		for _, rule := range ruleBackends.Rules {
			if *rule.LocationId == locationID {
				batchDeregiterTargets = append(batchDeregiterTargets, tencent.GetCurrentTarget(ingress, *listener.ListenerId, rule.LocationId, rule.Targets)...)
			}
		}
	}

	// 所有解绑或绑定成功的targets，可能需要修改相应节点的保护finalizer
	targetsToEnqueue := make([]*clb.BatchTarget, 0)
	var nodesByID map[string]*v1.Node
	if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) && len(batchDeregiterTargets) != 0 {
		nodesByID, err = services.GetNodesByID(cluster_service.Instance.NodeLister())
		if err != nil {
			return err
		}
	}
	if len(batchDeregiterTargets) != 0 {
		if err := tencent.BatchDeregisterTarget(sc, ingress, sc.LoadBalancerContext.LoadBalancerId, sc.LoadBalancerContext.Region, batchDeregiterTargets); err != nil {
			return err
		}
		if cluster_service.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, batchDeregiterTargets...)
			cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueTargets(targetsToEnqueue, nodesByID)
		}
		if err := sc.LoadBalancerContext.UpdateListenersBackend(sc); err != nil {
			return err
		}
	}

	return nil
}
