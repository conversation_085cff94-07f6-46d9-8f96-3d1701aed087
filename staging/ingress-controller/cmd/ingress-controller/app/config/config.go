package config

import (
	"fmt"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/opentracing/opentracing-go"
	"github.com/segmentio/ksuid"
	jaegerconfig "github.com/uber/jaeger-client-go/config"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/app/version"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/norm"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/options"
)

var (
	Global     *Config
	GitVersion = version.Get().GitVersion
)

func LifecycleOwnerTagEnabled() bool {
	return version.CheckOrDie(GitVersion, ">= 2.2.8")
}

func Init(opts *options.Options) error {
	if Global != nil {
		return nil
	}

	id := ksuid.New().String()
	Global = &Config{
		Options:           opts,
		ID:                id,
		BackendQuota:      opts.BackendQuota,
		BackendQuotaInUse: NewBackendQuota(opts.BackendQuota),
		BackendQuotaInClb: NewBackendQuota(100),
		ListenerQuota:     opts.ListenerQuota,
		JaegerConfig: jaeger.Config{
			Enable: opts.EnableTracing,
			Web:    "https://zhiyan.woa.com/apm_monitor",
			ZhiYan: &jaeger.ZhiYan{
				ID:        id,
				Env:       "prod",
				ProjectID: "14315",
			},
			Configuration: jaegerconfig.Configuration{
				ServiceName: "ingress-controller",
				Reporter: &jaegerconfig.ReporterConfig{
					QueueSize:           1000,
					BufferFlushInterval: 1,
					LogSpans:            false,
					LocalAgentHostPort:  "trace.zhiyan.tencent-cloud.net:6831",
				},
				Tags: []opentracing.Tag{
					{
						Key:   "tps.tenant.id",
						Value: "4138#apm-log-bfd69ec5fdc2gab2#14315_109284___apm",
					},
				},
			},
		},
		Norm: norm.Config{
			URL: "http://************:80/norm/api",
		},
	}

	norm.Init(Global.Norm)
	if err := Global.setOwnerUin(); err != nil {
		return err
	}

	klog.V(4).Infof(spew.Sdump(Global))

	return nil
}

type BackendQuota struct {
	UserQuota     int
	InstanceQuota map[string]int
}

func NewBackendQuota(userQuota int) *BackendQuota {
	return &BackendQuota{
		UserQuota:     userQuota,
		InstanceQuota: make(map[string]int),
	}
}

func (quota *BackendQuota) GetQuotaInUse(lbId string) int {
	if v, exist := Global.BackendQuotaInUse.InstanceQuota[lbId]; exist {
		return v
	}
	return Global.BackendQuotaInUse.UserQuota
}

type Config struct {
	*options.Options
	ID string

	// // 用户指定的quota
	BackendQuota  int
	ListenerQuota int

	// 合法的Region列表
	ValidRegions map[string]bool

	// //实际上起作用的quota
	BackendQuotaInUse *BackendQuota
	BackendQuotaInClb *BackendQuota

	JaegerConfig jaeger.Config

	Norm norm.Config
}

func (c *Config) setOwnerUin() error {
	if c.OwnerUin != 0 {
		return nil
	}

	var (
		err error
		uin *int64
	)
	for i := 0; i < 3; i++ {
		uin, err = norm.GetClusterUIN()
		if err == nil {
			c.OwnerUin = *uin
			return nil
		}
		time.Sleep(time.Second * 5)
	}

	return fmt.Errorf("get cluster's uin error: %w", err)
}
