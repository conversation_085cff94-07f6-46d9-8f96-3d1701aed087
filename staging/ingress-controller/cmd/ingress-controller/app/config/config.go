package config

import (
	"fmt"
	gclone "github.com/huandu/go-clone/generic"
	"sync/atomic"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/opentracing/opentracing-go"
	"github.com/segmentio/ksuid"
	jaegerconfig "github.com/uber/jaeger-client-go/config"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/app/version"
	"git.woa.com/kateway/pkg/telemetry/jaeger"
	"git.woa.com/kateway/pkg/tencent/norm"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/options"
)

var (
	Global     *Config
	GitVersion = version.Get().GitVersion
)

const DEFAULT_LB_USER_QUOTA int = 100

func LifecycleOwnerTagEnabled() bool {
	return version.CheckOrDie(GitVersion, ">= 2.2.8")
}

func Init(opts *options.Options) error {
	if Global != nil {
		return nil
	}

	id := ksuid.New().String()
	Global = &Config{
		Options:       opts,
		ID:            id,
		BackendQuota:  opts.BackendQuota,
		ListenerQuota: opts.ListenerQuota,
		JaegerConfig: jaeger.Config{
			Enable: opts.EnableTracing,
			Web:    "https://zhiyan.woa.com/apm_monitor",
			ZhiYan: &jaeger.ZhiYan{
				ID:        id,
				Env:       "prod",
				ProjectID: "14315",
			},
			Configuration: jaegerconfig.Configuration{
				ServiceName: "ingress-controller",
				Reporter: &jaegerconfig.ReporterConfig{
					QueueSize:           1000,
					BufferFlushInterval: 1,
					LogSpans:            false,
					LocalAgentHostPort:  "trace.zhiyan.tencent-cloud.net:6831",
				},
				Tags: []opentracing.Tag{
					{
						Key:   "tps.tenant.id",
						Value: "4138#apm-log-bfd69ec5fdc2gab2#14315_109284___apm",
					},
				},
			},
		},
		Norm: norm.Config{
			URL: "http://************:80/norm/api",
		},
	}
	_ = Global.UpdateBackendQuota(NewBackendQuota(DEFAULT_LB_USER_QUOTA, opts.BackendQuota))
	norm.Init(Global.Norm)
	if err := Global.setOwnerUin(); err != nil {
		return err
	}

	klog.V(4).Infof("service-controller config: %s", spew.Sdump(Global))

	return nil
}

type BackendQuota struct {
	LBUserQuota     int
	OptionQuota     int
	LBInstanceQuota map[string]int
}

func NewBackendQuota(LBUserQuota int, OptionQuota int) *BackendQuota {
	return &BackendQuota{
		LBUserQuota:     LBUserQuota,
		OptionQuota:     OptionQuota,
		LBInstanceQuota: make(map[string]int),
	}
}

func (q *BackendQuota) GetBackendQuota(lbID string, isDirectAccess bool) int {
	if instanceQuota, exist := q.LBInstanceQuota[lbID]; exist {
		return instanceQuota
	}
	if isDirectAccess {
		return q.LBUserQuota
	}
	return min(q.OptionQuota, q.LBUserQuota)
}

type Config struct {
	*options.Options
	ID string

	// // 用户指定的quota
	BackendQuota  int
	ListenerQuota int

	// 合法的Region列表
	ValidRegions map[string]bool

	// //实际上起作用的quota
	backendQuota atomic.Value

	JaegerConfig jaeger.Config

	Norm norm.Config
}

func (c *Config) setOwnerUin() error {
	if c.OwnerUin != 0 {
		return nil
	}

	var (
		err error
		uin *int64
	)
	for i := 0; i < 3; i++ {
		uin, err = norm.GetClusterUIN()
		if err == nil {
			c.OwnerUin = *uin
			return nil
		}
		time.Sleep(time.Second * 5)
	}

	return fmt.Errorf("get cluster's uin error: %w", err)
}

// 获取全局单例Config的资源限额：
// 当service或ingress处于直连模式，忽略启动参数backend-quota的影响，优先级 1.clb实例的配额值, 2.clb默认账户配额值
// 当service或ingress处于非直连模式，考虑启动参数backend-quota的影响，优先级 1.clb实例的配额值， 2.min(clb默认账户配额值， 启动参数backend-quota值)
func (c *Config) GetBackendQuota(lbID string, isDirectAccess bool) int {
	backendQuota := c.backendQuota.Load().(*BackendQuota)
	return backendQuota.GetBackendQuota(lbID, isDirectAccess)
}

func (c *Config) GetBackendQuotaObj() *BackendQuota {
	backendQuota := c.backendQuota.Load().(*BackendQuota)
	return gclone.Clone(backendQuota)
}

func (c *Config) UpdateBackendQuota(backendQuota *BackendQuota) error {
	if backendQuota == nil {
		return fmt.Errorf("BackendQuota is nil")
	}
	quota := gclone.Clone(backendQuota)
	// 当启动参数 Option 中 backend-quota 值为0时，忽略启动参数
	if quota.OptionQuota == 0 {
		quota.OptionQuota = quota.LBUserQuota
	}
	c.backendQuota.Store(quota)
	return nil
}
