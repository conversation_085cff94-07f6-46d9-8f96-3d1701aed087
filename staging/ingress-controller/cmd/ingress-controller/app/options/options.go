package options

import (
	"errors"
	"os"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/options"
	"github.com/spf13/pflag"
)

// Options contains everything necessary to create and run controller-manager.
type Options struct {
	*options.Options

	DryRun    bool // 模拟运行，不执行任何写操作
	MergeMode bool // 融合模式运行
}

// AddFlags adds flags to fs and binds them to options.
func (o *Options) AddFlags(fs *pflag.FlagSet) {
	o.Options.AddFlags(fs)

	fs.BoolVar(&o.DryRun, "mock-run", false, "To mock run service controller,check if any write action")
}

func New() *Options {
	return &Options{
		Options: options.New("ingress-controller", 10254),
	}
}

func (o *Options) Complete() error {
	err := o.Options.Complete()
	if err != nil {
		return err
	}

	// 兼容存量逻辑，从环境变量获取
	var exists bool
	if o.Region == "" {
		o.Region, exists = os.LookupEnv("TKE_REGION")
		if !exists {
			return errors.New("env key `TKE_REGION` not found")
		}
	}

	if o.VPCID == "" {
		o.VPCID, exists = os.LookupEnv("TKE_VPC_ID")
		if !exists {
			return errors.New("env key `TKE_VPC_ID` not found")
		}
	}

	// 将当前通过独立命令行开启的特性整合到特性门控中管理
	features := map[featuregates.Feature]bool{}
	for _, f := range featuregates.KnownFeatures {
		features[f] = o.FeatureGates.Enabled(f)
	}
	features[featuregates.DryRunIngress] = o.DryRun
	features[featuregates.MultiClusterIngress] = o.EnableMultiClusterIngress
	o.FeatureGates = featuregates.NewFeatureGates(features)

	return nil
}

func (o *Options) Validate() (errs []error) {
	errs = o.Options.Validate()

	return
}
