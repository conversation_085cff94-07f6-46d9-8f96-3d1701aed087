package app

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/pflag"
	corev1 "k8s.io/api/core/v1"
	extv1beta1 "k8s.io/api/extensions/v1beta1"
	netv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
	"k8s.io/component-base/logs"
	"k8s.io/klog/v2"

	mciv1alpha1 "git.woa.com/kateway/multi-cluster-ingress-api/apis/multiclusteringress/v1alpha1"
	"git.woa.com/kateway/pkg/app"
	"git.woa.com/kateway/pkg/domain/controllercm"
	"git.woa.com/kateway/pkg/domain/event"
	"git.woa.com/kateway/pkg/domain/ingress/ingress_wrapper"
	"git.woa.com/kateway/pkg/domain/server"
	"git.woa.com/kateway/pkg/domain/services"
	clustersvc "git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/k8s/leaderelection"
	"git.woa.com/kateway/pkg/telemetry/jaeger"

	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/config"
	"git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app/options"
	"git.woa.com/kateway/tke-ingress-controller/pkg/controller"
	service2 "git.woa.com/kateway/tke-ingress-controller/pkg/service"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/cluster"
	"git.woa.com/kateway/tke-ingress-controller/pkg/service/crossregion"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils"
	"git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
)

func run(opts *options.Options) app.RunFunc {
	return func(ctx context.Context) error {
		logs.InitLogs()
		defer logs.FlushLogs()

		if err := config.Init(opts); err != nil {
			return err
		}

		jaeger.Init(config.Global.JaegerConfig)

		jaeger.LogSystem(ctx, config.Global.ID)

		cli, err := kubernetes.NewForConfig(opts.RESTConfig)
		if err != nil {
			return err
		}
		cm, err := cli.CoreV1().ConfigMaps(metav1.NamespaceSystem).Get(ctx, controllercm.ConfigMapService, metav1.GetOptions{})
		if err != nil {
			return err
		}
		if cm.Data[controllercm.KeyEnableIngressController] == "true" && !opts.DryRun {
			return errors.New("ingress controller can not be launched in standalone mode, because there is already an instance of merged version running")
		}

		server.Init(&server.Config{
			Metrics: server.Metrics{
				IngressClass:                opts.IngressClass,
				StandaloneIngressController: true,
				MetricsPort:                 opts.MetricsPort,
			},
			Port: 80,
		})

		event.Init(opts.RESTConfig, "default", opts.Name)

		err = tencentapi.Init(&tencentapi.Config{
			Admit:    true,
			ReadOnly: opts.DryRun,
			Region:   config.Global.Region,
			Config:   opts.AuthConfig(),
		})
		if err != nil {
			return fmt.Errorf("tencentapi.Init error: %w", err)
		}

		basicSvc, err := clustersvc.NewBasic(&clustersvc.Config{
			Ctx:            ctx,
			Name:           opts.ClusterName,
			Region:         config.Global.Region,
			ControllerName: opts.Name,
			KubeConfig:     opts.RESTConfig,
			FeatureGates:   config.Global.FeatureGates,
		})
		if err != nil {
			return err
		}
		ctrl := NewIngressController(basicSvc, *opts) // ingress 独立启动
		if err := ctrl.Run(ctx); err != nil {
			return err
		}
		if opts.DryRun {
			return nil
		}
		ctrl.Enable()
		<-ctx.Done()
		return nil
	}
}

type IngressController struct {
	Controller *controller.LoadBalancerController

	opts           options.Options
	clusterService clustersvc.Interface

	shouldEnable bool

	electionStarted bool
	running         bool

	electionCancel context.CancelFunc
	mutex          sync.Mutex
}

var _ingressController *IngressController // 单例模式

func NewIngressController(clusterService clustersvc.Interface, opts options.Options) *IngressController {
	if _ingressController != nil {
		panic("Ingress controller already created")
	}

	_ingressController = &IngressController{
		clusterService: clusterService,
		opts:           opts,
		Controller:     controller.NewLoadBalancerController(opts.Workers),
	}
	return _ingressController
}

func GetIngressController() *IngressController {
	return _ingressController
}

func (c *IngressController) IsRunning() bool {
	if c == nil {
		return false
	}
	return c.running
}

func (c *IngressController) Run(ctx context.Context) error {
	if err := c.run(ctx); err != nil {
		return fmt.Errorf("failed to start the ingress controller: %w", err)
	}
	go c.sync(ctx, c.disable)
	go c.sync(ctx, c.enable)
	return nil
}

func (*IngressController) sync(ctx context.Context, fn func(context.Context)) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			fn(ctx)
			time.Sleep(1 * time.Second)
		}
	}
}

func (c *IngressController) disable(_ context.Context) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.shouldEnable {
		return
	}

	c.shutdown()
}

func (c *IngressController) shutdown() {
	if c.running {
		wg := sync.WaitGroup{}
		wg.Add(3)
		go func() {
			defer wg.Done()

			cluster_service.QueueServiceInstance.IngressQueue().FastShutdown()
		}()
		go func() {
			defer wg.Done()

			cluster_service.QueueServiceInstance.MultiClusterIngressQueue().FastShutdown()
		}()
		go func() {
			defer wg.Done()

			cluster_service.QueueServiceInstance.SecretQueue().FastShutdown()
		}()
		wg.Wait()
		c.running = false
		klog.Info("Ingress controller shutdown successfully")
	}

	if c.electionCancel != nil {
		c.electionCancel()
	}
}

func (c *IngressController) enable(electionCtx context.Context) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.shouldEnable {
		return
	}

	if c.electionStarted {
		return
	}

	klog.Info("Launching the leader election of ingress controller")

	electionCtx, electionCancel := context.WithCancel(electionCtx)
	c.electionCancel = electionCancel

	go func() {
		defer func() {
			c.mutex.Lock()
			defer c.mutex.Unlock()

			// 考虑异常情况：续期出错导致选主流程退出，此时工作队列可能已经启动，需要停止。下一次选出的leader可能不在本进程
			c.shutdown()
			c.electionStarted = false
			klog.Info("Leader election of ingress controller exited")
		}()

		_ = leaderelection.Run(electionCtx, c.opts.RESTConfig, &c.opts.LeaderElection, func(ctx context.Context) {
			// 选主成功后需要定期检查当前开关是否开启，如果开启则启动工作队列。
			// 如果是一次性开启在极端情况下会有问题：考虑在选主成功，执行开启流程的瞬间开关被关闭，此时开启流程不会执行，之后开关被开启，导致工作队列一直处于关闭状态
			// resume方法也不能修改成默认开启工作队列，因为选主成功后可能开关已经关闭了，需要进入退出流程
			c.sync(ctx, c.resume)
		})
	}()

	c.electionStarted = true
}

func (c *IngressController) Enable() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.shouldEnable = true
}

func (c *IngressController) Disable() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.shouldEnable = false
}

func (c *IngressController) run(ctx context.Context) error {
	if err := config.Init(&c.opts); err != nil {
		return err
	}

	serviceType := cluster.TaskQueue
	if c.opts.DryRun {
		serviceType = cluster.TaskTraversal
	}

	if err := cluster.Init(c.clusterService, serviceType); err != nil {
		return fmt.Errorf("failed to initialize the cluster service: %w", err)
	}

	// 依赖 tencentapi.Init
	crossregion.InitCrossRegionService()

	service2.InitDefaultClusterInfoService()

	return c.Controller.Run(ctx.Done())
}

func (c *IngressController) resume(ctx context.Context) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.shouldEnable {
		return
	}

	if c.running {
		return
	}

	cluster_service.QueueServiceInstance.IngressQueue().Run()
	cluster_service.QueueServiceInstance.MultiClusterIngressQueue().Run()
	cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().Run(ctx)
	cluster_service.QueueServiceInstance.SecretQueue().Run()

	var (
		normalIngresses       []types.Ingress
		multiClusterIngresses []types.Ingress
	)
	if lo.Must(c.clusterService.IsExtensionsAPIGroupSupported()) {
		inges, err := c.clusterService.ExtensionIngressLister().List(labels.Everything())
		if err != nil {
			panic(err)
		}
		normalIngresses = lo.Map(inges, func(ing *extv1beta1.Ingress, _ int) types.Ingress {
			return ingress_wrapper.NewIngressWrapperExtensions(ing)
		})
	} else {
		inges, err := c.clusterService.NetworkingIngressLister().List(labels.Everything())
		if err != nil {
			panic(err)
		}
		normalIngresses = lo.Map(inges, func(ing *netv1.Ingress, _ int) types.Ingress {
			return ingress_wrapper.NewIngressWrapperNetworking(ing)
		})
	}
	if c.opts.EnableMultiClusterIngress {
		inges, err := c.clusterService.MultiClusterIngressLister().List(labels.Everything())
		if err != nil {
			panic(err)
		}
		multiClusterIngresses = lo.Map(inges, func(ing *mciv1alpha1.MultiClusterIngress, _ int) types.Ingress {
			return ingress_wrapper.NewIngressWrapperMultiCluster(ing)
		})
	}
	for _, ing := range normalIngresses {
		if !utils.IsQCLOUDIngress(ing) {
			continue
		}
		cluster_service.QueueServiceInstance.IngressQueue().Enqueue(taskqueue.Item{
			Data:   utils.IngressName(ing),
			Weight: 1,
		})
	}

	for _, ing := range multiClusterIngresses {
		cluster_service.QueueServiceInstance.MultiClusterIngressQueue().Enqueue(taskqueue.Item{
			Data:   utils.IngressName(ing),
			Weight: 1,
		})
	}

	secrets, err := c.clusterService.SecretLister().List(labels.Everything())
	if err != nil {
		panic(err)
	}
	for _, s := range secrets {
		if s.Type == corev1.SecretTypeTLS && services.IsTLSSecretAnnotationEnabled(s) {
			cluster_service.QueueServiceInstance.SecretQueue().Enqueue(taskqueue.Item{
				Data:   types.JoinKeyStrings("/", s.Namespace, s.Name),
				Weight: 1,
			})
		}
	}

	if err := controller.ResourceInitFlag(cluster_service.Instance.KubeClient()); err != nil {
		panic(err)
	}

	c.running = true
	klog.Info("Ingress controller started successfully")
}

// PrintFlags logs the flags in the flagset
func PrintFlags(flags *pflag.FlagSet) {
	flags.VisitAll(func(flag *pflag.Flag) {
		klog.Infof("FLAG: --%s=%q", flag.Name, flag.Value)
	})
}
