
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: (devel)
  creationTimestamp: null
  name: backendconfigs.cloud.tencent.com
spec:
  group: cloud.tencent.com
  names:
    kind: BackendConfig
    listKind: BackendConfigList
    plural: backendconfigs
    singular: backendconfig
  scope: Namespaced
  validation:
    openAPIV3Schema:
      description: BackendConfig
      properties:
        apiVersion:
          description: 'APIVersion defines the versioned schema of this representation
            of an object. Servers should convert recognized schemas to the latest
            internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
          type: string
        kind:
          description: 'Kind is a string value representing the REST resource this
            object represents. Servers may infer this from the endpoint the client
            submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
          type: string
        metadata:
          description: 'Standard object''s metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata'
          type: object
        spec:
          properties:
            listener:
              properties:
                health:
                  properties:
                    enable:
                      type: boolean
                    healthNum:
                      format: int32
                      maximum: 10
                      minimum: 2
                      type: integer
                    httpCheckDomain:
                      maxLength: 80
                      minLength: 1
                      type: string
                    httpCheckMethod:
                      enum:
                      - HEAD
                      - GET
                      type: string
                    httpCheckPath:
                      maxLength: 200
                      minLength: 1
                      type: string
                    httpCode:
                      format: int32
                      maximum: 31
                      minimum: 1
                      type: integer
                    intervalTime:
                      format: int32
                      maximum: 300
                      minimum: 5
                      type: integer
                    timeout:
                      format: int32
                      maximum: 60
                      minimum: 2
                      type: integer
                    unHealthNum:
                      format: int32
                      maximum: 10
                      minimum: 2
                      type: integer
                  type: object
                listenerName:
                  maxLength: 50
                  minLength: 1
                  pattern: '[0-9a-zA-Z_-]+'
                  type: string
                scheduler:
                  enum:
                  - WRR
                  - LEAST_CONN
                  - IP_HASH
                  type: string
                sessionExpire:
                  properties:
                    enable:
                      type: boolean
                    sessionExpireTime:
                      format: int32
                      maximum: 3600
                      minimum: 30
                      type: integer
                  type: object
              type: object
          type: object
      type: object
  version: v1alpha1
  versions:
  - name: v1alpha1
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
