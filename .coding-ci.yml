version: '2.0'
worker:
  language: go-14
  label: PUBLIC
  tools: []
env:
  gotest_timeout:
    value: 10h
    type: string
  gotest_parallel:
    value: '2'
    type: string
  CLE_API_CODING_TOKEN:
    type: string
    secret: >-
      SJ45jvdpMJIm+ZtCD1/cLbUfzelB8MBH2xHtbimpoSp0hYuRZR3K2Akh4r1y31IHuNSMVnnMgXkF7+2GWh7YRdNwR53fvAEEB6OmvhELlgHoHbGsgFBnmQ1ky5HH5vbwZUFiF3G9w1gWgcbjccbeFmEwtJTSbeGp3sTLkp+74Dk=
  HTTP_PROXY:
    value: 'http://devproxy.oa.com:8080'
    type: string
  QTAP_TOKEN:
    value: >-
      eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiIsImlzcyI6ImhMV2VmU0lmOGlidExqOHRnc0lVTnQwTjA1bDNXSllFIn0.eyJpc3MiOiJoTFdlZlNJZjhpYnRMajh0Z3NJVU50ME4wNWwzV0pZRSJ9.DN17kNi4AhD_UGqofHrMj-SOWsFFEbO6_ZJUt-POOBg
    type: string
  CCR_TOKEN:
    type: string
    secret: >-
      EtOjG0YbqKb6AMmBUN4Agq8OmcR4VzqDarIw6Zh5Uya4kMFplVawjx2LunBQoNxrixY6Ufq8WY1Ng62CLRwFFNMc9JtSx9JRgW6LjydCQZTuvsNeQFm5igL6hLE2/yRpJp9kJOr5dBZnu1NjQMPptSTAzn5jOdA4FUnoqucTqms=
  DEPLOYMENT_TIMEOUT: 10m
stages:
  - stage: 编译及静态代码扫描
    tasks:
      - task: docker build
        cmds:
          - plugin: git_credentials
            params:
              operation: '1'
          - plugin: cmds
            params:
              cmds:
                - make build
                - make docker
                - echo "IAMGE_TAG=$(git describe --tags --always --dirty)" >> $QCI_ENV_FILE
      - task: 静态代码分析
        cmds:
          - plugin: coding_codedog
            params:
              language:
                - Go
              scan-plan: service-controller分析方案
  - stage: 环境准备
    if: $QCI_TRIGGER_TYPE = TRIGGER_MR
    tasks:
      - task: docker镜像push到仓库
        cmds:
          - plugin: cmds
            params:
              cmds:
                - >-
                  docker login ccr.ccs.tencentyun.com -u admin -p
                  {ApplicationToken:$CCR_TOKEN}
                - make push
            label: docker push
      - task: 环境申请
        output_timeout: 2h
        cmds:
          - plugin: cmds
            params:
              cmds:
                - 'curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py'
                - python get-pip.py
                - yum install python-devel -y
                - python -m pip install --upgrade setuptools
                - >-
                  python -m pip install -i http://pypi.dq.oa.com/simple/
                  --trusted-host pypi.dq.oa.com yunqi-cli qtap-cli
                - >-
                  curl -LO
                  https://storage.googleapis.com/kubernetes-release/release/$(curl
                  -s
                  https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl
                - chmod a+x kubectl
                - ''
            label: 依赖安装
          - plugin: cmds
            params:
              cmds:
                - yunqi env acquire --project tke --env-def 3141 --group independent_master_tke
            label: CLE申请环境
  - stage: 自动化回归测试
    if: $QCI_TRIGGER_TYPE = TRIGGER_MR
    tasks:
      - task: QTA自动化回归测试
        cmds:
          - plugin: cmds
            params:
              cmds:
                - python pipeline.py
                - export KUBECONFIG=$(pwd)/kube_config
                - >-
                  ./kubectl set image deployment/service-controller
                  service-controller=ccr.ccs.tencentyun.com/paas/service-controller:$IAMGE_TAG
                  -n kube-system
                - ./kubectl rollout status deployment/service-controller -n kube-system --timeout $DEPLOYMENT_TIMEOUT
            label: 更新测试环境镜像
          - plugin: git_credentials
            params:
              operation: '1'
          - plugin: qtap-testtool
            params:
              project: tke
              testrepo: 'http://git.code.oa.com/k8s/service-controller.git'
              format: junit
              name: 【GoTest】tke-service-controller
              testtool: go-test
              testcase: test
        output_timeout: 2h
  - stage: 推送制品
    if: $QCI_TRIGGER_TYPE = TRIGGER_TAG
    tasks:
      - task: 推送docker镜像到各地域仓库
        cmds:
          - plugin: cmds
            params:
              cmds:
                - echo "push docker image"
finally:
  all:
    cmds:
      - plugin: cmds
        params:
          cmds:
            - >-
              (command -v yunqi && test -f environment.yaml && yunqi env release
              --filepath environment.yaml) || echo 'skip'
        label: 环境退还