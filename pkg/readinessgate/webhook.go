package readinessgate

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	admissionv1 "k8s.io/api/admission/v1"
	admissionv1beta1 "k8s.io/api/admission/v1beta1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	cluster2 "git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/service/cluster"
	"cloud.tencent.com/lb-controller/pkg/utils"
)

const (
	PatchOPType                     = "replace"
	UnderlayIPreadinessGateJsonPath = "/spec/readinessGates"
)

var scheme1 = runtime.NewScheme()
var codecs = serializer.NewCodecFactory(scheme1)

func init() {
	addToScheme(scheme1)
}

func addToScheme(scheme *runtime.Scheme) {
	utilruntime.Must(corev1.AddToScheme(scheme))
	utilruntime.Must(admissionv1beta1.AddToScheme(scheme))
	utilruntime.Must(admissionv1.AddToScheme(scheme))
}

func NewReadinessGateWebhookServer() *ReadinessGateWebhookServer {
	return &ReadinessGateWebhookServer{}
}

type ReadinessGateWebhookServer struct {
}

type ThingSpec struct {
	Op    string          `json:"op"`
	Path  string          `json:"path"`
	Value json.RawMessage `json:"value"`
}

func (webhookServer *ReadinessGateWebhookServer) StartWebhookServer() {
	mux := http.NewServeMux()
	mux.HandleFunc("/service-controller-readinessgate-webhook", webhookServer.serveMutatePods)
	server := &http.Server{
		Addr:    ":17445",
		Handler: mux,
	}
	klog.Infof("Start The ReadinessGate Server.")
	klog.Errorf("ReadinessGate Server Closed %v", server.ListenAndServe())
}

func (webhookServer *ReadinessGateWebhookServer) serveMutatePods(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()
	returnCode := http.StatusOK
	defer func() {
		updateWebhookMetrics(returnCode, startTime)
	}()

	// verify the content type is accurate
	contentType := r.Header.Get("Content-Type")
	if contentType != "application/json" {
		msg := fmt.Sprintf("contentType=%s, expect application/json", contentType)
		returnCode = http.StatusBadRequest
		klog.Error(msg)
		http.Error(w, msg, returnCode)
		return
	}

	var body []byte
	if r.Body != nil {
		if data, err := ioutil.ReadAll(r.Body); err == nil {
			body = data
		}
	}

	klog.V(4).Info(fmt.Sprintf("handling request: %s", string(body)))
	deserializer := codecs.UniversalDeserializer()
	obj, gvk, err := deserializer.Decode(body, nil, nil)
	if err != nil {
		msg := fmt.Sprintf("Request could not be decoded: %v", err)
		returnCode = http.StatusBadRequest
		klog.Error(msg)
		http.Error(w, msg, returnCode)
		return
	}

	var responseObj runtime.Object
	switch *gvk {
	case admissionv1beta1.SchemeGroupVersion.WithKind("AdmissionReview"):
		requestedAdmissionReview, ok := obj.(*admissionv1beta1.AdmissionReview)
		if !ok {
			msg := fmt.Sprintf("Expected v1beta1.AdmissionReview but got: %T", obj)
			returnCode = http.StatusBadRequest
			klog.Errorf(msg)
			http.Error(w, msg, returnCode)
			return
		}
		responseAdmissionReview := &admissionv1beta1.AdmissionReview{}
		responseAdmissionReview.SetGroupVersionKind(*gvk)
		responseAdmissionReview.Response = webhookServer.mutatePodsV1Bata1(*requestedAdmissionReview) // TODO
		responseAdmissionReview.Response.UID = requestedAdmissionReview.Request.UID
		responseObj = responseAdmissionReview
	case admissionv1.SchemeGroupVersion.WithKind("AdmissionReview"):
		requestedAdmissionReview, ok := obj.(*admissionv1.AdmissionReview)
		if !ok {
			msg := fmt.Sprintf("Expected v1.AdmissionReview but got: %T", obj)
			returnCode = http.StatusBadRequest
			klog.Errorf(msg)
			http.Error(w, msg, returnCode)
			return
		}
		responseAdmissionReview := &admissionv1.AdmissionReview{}
		responseAdmissionReview.SetGroupVersionKind(*gvk)
		responseAdmissionReview.Response = webhookServer.mutatePodsV1(*requestedAdmissionReview) // TODO
		responseAdmissionReview.Response.UID = requestedAdmissionReview.Request.UID
		responseObj = responseAdmissionReview
	default:
		msg := fmt.Sprintf("Unsupported group version kind: %v", gvk)
		returnCode = http.StatusBadRequest
		klog.Errorf(msg)
		http.Error(w, msg, returnCode)
		return
	}

	respBytes, err := json.Marshal(responseObj)
	if err != nil {
		returnCode = http.StatusInternalServerError
		klog.Error(err)
		http.Error(w, err.Error(), returnCode)
		return
	}
	klog.V(4).Info(fmt.Sprintf("sending response: %s", string(respBytes)))

	if _, err := w.Write(respBytes); err != nil {
		returnCode = http.StatusInternalServerError
		klog.Error(err)
	}
}

// mutate pods using tke-route-eni.
func (webhookServer *ReadinessGateWebhookServer) mutatePodsV1Bata1(ar admissionv1beta1.AdmissionReview) *admissionv1beta1.AdmissionResponse {
	klog.V(2).Info("mutating pods")
	podResource := metav1.GroupVersionResource{Group: "", Version: "v1", Resource: "pods"}
	if ar.Request.Resource != podResource {
		klog.Errorf("expect resource to be %s", podResource)
		return nil
	}

	raw := ar.Request.Object.Raw
	pod := corev1.Pod{}
	deserializer := codecs.UniversalDeserializer()
	if _, _, err := deserializer.Decode(raw, nil, &pod); err != nil {
		klog.Error(err)
		return toAdmissionResponseV1Beta1(err)
	}
	reviewResponse := admissionv1beta1.AdmissionResponse{}
	reviewResponse.Allowed = true

	if !needPatch(&pod) {
		return &reviewResponse
	}

	pd, err := getPatchData(pod)
	if err != nil {
		klog.Error(err)
		return toAdmissionResponseV1Beta1(err)
	}
	pt := admissionv1beta1.PatchTypeJSONPatch
	reviewResponse.Patch = pd
	reviewResponse.PatchType = &pt
	return &reviewResponse
}

// mutate pods using tke-route-eni.
func (webhookServer *ReadinessGateWebhookServer) mutatePodsV1(ar admissionv1.AdmissionReview) *admissionv1.AdmissionResponse {
	klog.V(2).Info("mutating pods")
	podResource := metav1.GroupVersionResource{Group: "", Version: "v1", Resource: "pods"}
	if ar.Request.Resource != podResource {
		klog.Errorf("expect resource to be %s", podResource)
		return nil
	}

	raw := ar.Request.Object.Raw
	pod := corev1.Pod{}
	deserializer := codecs.UniversalDeserializer()
	if _, _, err := deserializer.Decode(raw, nil, &pod); err != nil {
		klog.Error(err)
		return toAdmissionResponseV1(err)
	}
	reviewResponse := admissionv1.AdmissionResponse{}
	reviewResponse.Allowed = true

	if !needPatch(&pod) {
		return &reviewResponse
	}

	pd, err := getPatchData(pod)
	if err != nil {
		klog.Error(err)
		return toAdmissionResponseV1(err)
	}
	pt := admissionv1.PatchTypeJSONPatch
	reviewResponse.Patch = pd
	reviewResponse.PatchType = &pt
	return &reviewResponse
}

func potentialPodName(metadata metav1.ObjectMeta) string {
	if metadata.Name != "" {
		return metadata.Name
	}
	if metadata.GenerateName != "" {
		return metadata.GenerateName + "***** (actual name not yet known)"
	}
	return ""
}

// kateway: 判断 pod 是否需要加上 readnessgate
func needPatch(pod *corev1.Pod) bool {
	if config.Global.EnableReadinessGate == false { // 集群组件已关闭ReadinessGate检查
		return false
	}

	// 幂等: 防止用户犯错
	for _, readinessGates := range pod.Spec.ReadinessGates {
		if readinessGates.ConditionType == types.DirectAccessConditionType {
			return false
		}
	}

	services, err := cluster2.Instance.ServiceLister().Services(pod.Namespace).List(labels.Everything())
	if err != nil {
		klog.Errorf("failed to list Service: %v", err)
		return false
	}
	for _, service := range services {
		serviceWrapper := service_wrapper.NewService(service)
		if !utils.IsNoSelectorService(serviceWrapper) && labels.SelectorFromSet(service.Spec.Selector).Matches(labels.Set(pod.Labels)) {
			// dry-run service 不能添加，否则影响pod滚动更新
			if serviceWrapper.IsDryRun() {
				continue
			}

			if service.Spec.Type == corev1.ServiceTypeLoadBalancer && utils.IsServiceDirectAccess(serviceWrapper) {
				return true
			}
			ingresses, err := cluster.ListQCloudIngress(service.Namespace, labels.Everything())
			if err != nil {
				klog.Errorf("failed to list Ingress: %v", err)
				return false
			}
			for _, ingress := range ingresses {
				if !utils.IsIngressSelectByService(ingress, serviceWrapper.GetObjectMeta().GetName()) { // 该Ingress没有被Service选中，下一个
					continue
				}
				if utils.IsIngressDirectAccess(ingress) {
					return true
				}
			}
		}
	}
	klog.Infof("pod %s/%s don't need readinessGate, just return", pod.Namespace, potentialPodName(pod.ObjectMeta))
	return false
}

func getPatchData(pod corev1.Pod) ([]byte, error) {
	readinessGate := pod.Spec.ReadinessGates
	if readinessGate == nil {
		readinessGate = make([]corev1.PodReadinessGate, 0)
	}
	readinessGate = append(readinessGate, corev1.PodReadinessGate{
		ConditionType: types.DirectAccessConditionType,
	})
	readinessGateBytes, err := json.Marshal(readinessGate)
	if err != nil {
		return nil, err
	}

	things := []ThingSpec{
		{
			Op:    PatchOPType,
			Path:  UnderlayIPreadinessGateJsonPath,
			Value: readinessGateBytes,
		},
	}

	patchBytes, err := json.Marshal(things)
	if err != nil {
		return nil, err
	}
	return patchBytes, nil
}

func toAdmissionResponseV1Beta1(err error) *admissionv1beta1.AdmissionResponse {
	return &admissionv1beta1.AdmissionResponse{
		Result: &metav1.Status{
			Message: err.Error(),
		},
	}
}

func toAdmissionResponseV1(err error) *admissionv1.AdmissionResponse {
	return &admissionv1.AdmissionResponse{
		Result: &metav1.Status{
			Message: err.Error(),
		},
	}
}

type NetConf struct {
	DefaultDelegates string `json:"defaultDelegates"`
}

func updateWebhookMetrics(returnCode int, startTime time.Time) {
	if metrics.Instance != nil {
		labels := prometheus.Labels{"returnCode": strconv.Itoa(returnCode)}
		metrics.Instance.UpdateWebhookDelayTime(labels, time.Since(startTime))
		metrics.Instance.IncWebhookRequestCount(labels)
	}
}
