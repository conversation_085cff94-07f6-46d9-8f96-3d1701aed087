package utils

import (
	"encoding/json"
	"strings"

	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/types"
	v1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
)

type CustomizedWeight struct {
	DefaultWeight *int               `json:"defaultWeight"`
	Groups        []WeightConf       `json:"groups"`
	Zones         []types.ZoneWeight `json:"zones"`
}

type WeightConf struct {
	Key         Key           `json:"key"`
	StatefulSet []StatefulSet `json:"statefulSets"`
}

type Key struct {
	Proto string `json:"proto"`
	Port  int    `json:"port"`
}

type StatefulSet struct {
	Name       string   `json:"name"`
	PodWeights []Detail `json:"weights"`
}

type Detail struct {
	Weight   int   `json:"weight"`
	PodIndex []int `json:"podIndexes"`
}

type SnatProInfo struct {
	SnatIPs []SnatIP `json:"snatIPs"`
}

type SnatIP struct {
	SubnetId string  `json:"subnetId"`
	IP       *string `json:"ip"`
}

// // Pod 注解存在时，优先以Pod注解为准。
// // Pod 注解不存在时，以集群默认配置为准。
// func IsTKERouteENI(pod *v1.Pod) bool {
//	// qyzhaoxun/multus-cni 参考文档：https://github.com/qyzhaoxun/multus-cni/blob/master/doc/default-delegates.md
//	if network, exist := pod.Annotations[PodNetworkAnnotation]; exist {
//		return strings.Contains(network, service.DelegatesTKERouteENI)
//	}
//	return service.ClusterInfoServiceInstance.IsDefaultNeedReadinessGate(false)
// }
//
// func IsTKEDirectENI(pod *v1.Pod) bool {
//	// qyzhaoxun/multus-cni 参考文档：https://github.com/qyzhaoxun/multus-cni/blob/master/doc/default-delegates.md
//	if network, exist := pod.Annotations[PodNetworkAnnotation]; exist {
//		return strings.Contains(network, service.DelegatesTKEDirectENI)
//	}
//	return service.ClusterInfoServiceInstance.IsDefaultNeedReadinessGate(false)
// }

func isTkePODInEKS(pod *v1.Pod) bool {
	value, has := pod.Annotations["tke.cloud.tencent.com/pod-type"]
	return has && strings.ToLower(value) == "eklet"
}

// func IsPodDirectBackend(pod *v1.Pod) bool {
//	return IsTKEDirectENI(pod) || IsTKERouteENI(pod) || isTkePODInEKS(pod)
// }

func GetLoadbalancerType(service service_wrapper.ServiceWrapper) int64 {
	// For EKS, only support FORWARDTYPE
	// For MultiClusterService, only support FORWARDTYPE
	// CLASSICTYPE Only Support Service In TKE Cluster
	if service.ServiceType() == service_wrapper.CoreService && !env.IsInEKSCluster() {
		if lbType, typeExist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationLoadBalancerType]; typeExist {
			if lbType == types.ClassicLoadbalanceType || lbType == types.ClassicLoadbalanceType2 {
				return types.CLASSICTYPE
			} else if lbType == types.ForwardLoadbalanceType {
				return types.FORWARDTYPE
			} else {
				klog.Warningf("Assign unknow lb type: %s, switch to default type: %s", lbType, types.ForwardLoadbalanceType)
			}
		}
	}
	return types.FORWARDTYPE // default
}

func GetServiceSecurityGroups(service service_wrapper.ServiceWrapper) ([]string, error) {
	if value, exist := service.GetObjectMeta().GetAnnotations()[types.SecurityGroupsAnnotation]; exist { // ENI直绑
		if value == "" {
			return []string{}, nil
		}
		securityGroups := strings.Split(value, ",")
		if len(securityGroups) > 50 {
			return nil, types.NewError(errcode.SecurityGroupsAnnotationError, "", ServiceName(service))
		}
		for _, securityGroup := range securityGroups {
			if securityGroup == "" {
				return nil, types.NewError(errcode.SecurityGroupsAnnotationError, "", ServiceName(service))
			}
		}
		return securityGroups, nil
	}
	return nil, nil
}

func HasServiceSecurityGroups(service service_wrapper.ServiceWrapper) (string, bool) {
	if value, exist := service.GetObjectMeta().GetAnnotations()[types.SecurityGroupsAnnotation]; exist { // ENI直绑
		return value, true
	}
	return "", false
}

// IsServicePassToTarget
// kateway: 是否放通 CVM 上对CLB 流量的安全组校验
func IsServicePassToTarget(service service_wrapper.ServiceWrapper) (bool, error) {
	if passToTargetAnnotation, exist := service.GetObjectMeta().GetAnnotations()[types.PassToTargetAnnotation]; exist { // ENI直绑 // kateway 这注释是错的吧?
		passToTarget, err := ParseBool(passToTargetAnnotation)
		if err != nil {
			return false, types.NewError(errcode.PassToTargetAnnotationError, "", ServiceName(service))
		}
		return passToTarget, nil
	}
	return false, nil
}

func HasServicePassToTarget(service service_wrapper.ServiceWrapper) (string, bool) {
	if value, exist := service.GetObjectMeta().GetAnnotations()[types.PassToTargetAnnotation]; exist { // ENI直绑
		return value, true
	}
	return "", false
}

// kateway: tke svc 打上直绑 annotation + eks
func IsServiceDirectAccess(service service_wrapper.ServiceWrapper) bool {
	if directAccess, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceDirectAccessAnnotation]; exist { // ENI直绑
		return strings.ToLower(directAccess) == "true"
	}
	if env.IsInEKSCluster() {
		return true // For EKS, all Services are direct access
	}
	return false
}

func IsServiceEnableGraceDeletion(service service_wrapper.ServiceWrapper) bool {
	if graceDeletion, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceEnableGraceDeletion]; exist {
		return strings.ToLower(graceDeletion) == "true"
	}
	return false
}

func HasIngressDirectAccess(ingress types.Ingress) bool {
	if env.IsInEKSCluster() {
		return true // For EKS, all Ingresses are direct access
	}
	if _, exist := ingress.Annotations()[types.IngressAnnotationDirectAccess]; exist { // ENI直绑
		return true
	}
	return false
}

func IsIngressDirectAccess(ingress types.Ingress) bool {
	if env.IsInEKSCluster() {
		return true // For EKS, all Ingresses are direct access
	}
	if directAccess, exist := ingress.Annotations()[types.IngressAnnotationDirectAccess]; exist { // ENI直绑
		return strings.ToLower(directAccess) == "true"
	}
	return false
}

func GetTkeServiceConfig(service service_wrapper.ServiceWrapper) (string, error) {
	tkeServiceConfigAuto, err := IsTkeServiceConfigAuto(service)
	if err != nil {
		return "", err
	}
	// 自动化的TkeServiceConfig
	if tkeServiceConfigAuto {
		return ServiceAutoServiceConfigName(service), nil
	}
	// 自定义的TkeServiceConfig
	if config, exist := service.GetObjectMeta().GetAnnotations()[types.TkeServiceConfigAnnotation]; exist {
		if strings.HasSuffix(config, "-auto-ingress-config") || strings.HasSuffix(config, "-auto-service-config") || strings.HasSuffix(config, "-auto-multiclusterservice-config") {
			return "", types.NewError(errcode.TkeServiceConfigConflictError, "", ServiceName(service))
		}
		return config, nil
	}
	return "", nil
}

func GetExistLB(service service_wrapper.ServiceWrapper) (string, bool) {
	if existedLB, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationExistingLoadBalancerID]; exist {
		return existedLB, true
	}
	return "", false
}

func GetSpecifyProtocol(service service_wrapper.ServiceWrapper) (*SpecifyProtocolAnnotation, bool, error) {
	specifyProtocol := &SpecifyProtocolAnnotation{}
	data, exist := service.GetObjectMeta().GetAnnotations()[types.SpecifyProtocolAnnotation]
	if !exist || data == "" {
		return specifyProtocol, false, nil
	}

	if err := json.Unmarshal([]byte(data), specifyProtocol); err != nil {
		return specifyProtocol, false, types.NewError(errcode.ServiceSpecifyProtocolFormatError, "", ServiceName(service))
	}
	return specifyProtocol, exist, nil
}

// Service指定协议，允许的协议类型
func ValidProtocol(protocol string) bool {
	for _, value := range []string{"TCP", "UDP", "TCP_SSL", "QUIC", "HTTP", "HTTPS"} {
		if protocol == value {
			return true
		}
	}
	return false
}

func GetListenerParameters(service service_wrapper.ServiceWrapper) (string, bool) {
	listenerParameters, exist := service.GetObjectMeta().GetAnnotations()[types.AnnoListenerParameters]
	return listenerParameters, exist
}

func GetExtensiveParameters(service service_wrapper.ServiceWrapper) (string, bool) {
	extensiveParameters, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationExtensiveParams]
	return extensiveParameters, exist
}

func GetBackendsListLabel(service service_wrapper.ServiceWrapper) (string, bool) {
	backendsListLabel, exist := service.GetObjectMeta().GetAnnotations()[types.AnnoBackendsListLabel]
	return backendsListLabel, exist
}

// func GetLocalSvcOnlyBindNodeWithPod(service service_wrapper.ServiceWrapper) bool {
//	if localSvcOnlyBindAnnotation, exist := service.GetObjectMeta().GetAnnotations()[LocalSvcOnlyBindNodeWithPodAnnontation]; exist {
//		if localSvcOnlyBind, err := ParseBool(localSvcOnlyBindAnnotation); err == nil { // 填错的场景，忽略注解的作用
//			return localSvcOnlyBind
//		}
//	}
//	return false
// }
//
// func GetLocalSvcWeightedBalancer(service service_wrapper.ServiceWrapper) bool {
//	if localSvcWeightedAnnotation, exist := service.GetObjectMeta().GetAnnotations()[LocalSvcWeightedBalancerAnnontation]; exist {
//		if localSvcWeighted, err := ParseBool(localSvcWeightedAnnotation); err == nil { // 填错的场景，忽略注解的作用
//			return localSvcWeighted
//		}
//	}
//	return false
// }

func GetSubnetId(service service_wrapper.ServiceWrapper) (string, bool) {
	if subnetId, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationInternalSubnetID]; exist {
		return subnetId, exist
	}

	if IsClusterIPAndNeedsCLB(service) {
		// 特殊情况，CLB模拟ClusterIP模式下，所有ClusterIP类型的负载均衡由内网负载均衡模拟。
		// 可以被认为等同于有内网注解的情况，并且目前策略会主动将子网写到资源注解内。
		return config.Global.ClusterIPSubnet, true
	}

	return "", false
}

func GetCrossRegionId(service service_wrapper.ServiceWrapper) (string, bool) {
	crossRegionId, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationCrossRegionID]
	return crossRegionId, exist
}

func GetCrossVPCId(service service_wrapper.ServiceWrapper) (string, bool) {
	crossVPCId, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationCrossVPCID]
	return crossVPCId, exist
}

func GetTargetCrossRegionID(service service_wrapper.ServiceWrapper) (string, bool) {
	crossRegionId, exist := service.GetObjectMeta().GetAnnotations()[types.TargetCrossRegionIDAnnotation]
	return crossRegionId, exist
}

func GetTargetCrossVPCId(service service_wrapper.ServiceWrapper) (string, bool) {
	crossVPCId, exist := service.GetObjectMeta().GetAnnotations()[types.TargetCrossVPCIdAnnotation]
	return crossVPCId, exist
}

func GetCrossType(service service_wrapper.ServiceWrapper) (string, bool) {
	crossType, exist := service.GetObjectMeta().GetAnnotations()[types.CrossTypeAnnotation]
	if !exist {
		return types.CrossType2_0, false
	}
	if crossType != types.CrossType2_0 && crossType != types.CrossType1_0 && crossType != types.CrossType1_1 && crossType != types.CrossType0_0 && crossType != types.CrossType1_2 { // TODO
		return "err", true
	}
	return crossType, true
}

func GetHybridType(service service_wrapper.ServiceWrapper) string { // 混合云类型
	crossType, exist := service.GetObjectMeta().GetAnnotations()[types.HybridTypeAnnotation]
	if !exist {
		return types.HybridTypeNone
	}
	if crossType != types.HybridTypeNone && crossType != types.HybridTypePvgw && crossType != types.HybridTypeCcn { // TODO
		return types.HybridTypeNone
	}
	return crossType
}

func GetSnatProInfo(service service_wrapper.ServiceWrapper) *SnatProInfo {
	SNATProInfoString, existAnnotation := service.GetObjectMeta().GetAnnotations()[types.SnatProInfoAnnotation]
	if !existAnnotation {
		return nil
	}

	SnatProInfo := &SnatProInfo{}
	if err := json.Unmarshal([]byte(SNATProInfoString), &SnatProInfo); err != nil { // TODO misakazhou
		return nil
	}
	return SnatProInfo
}

func GetTkeManagement(service service_wrapper.ServiceWrapper) bool {
	if tkeManagement, exist := service.GetObjectMeta().GetAnnotations()[types.TkeManagementAnnotation]; exist {
		if management, err := ParseBool(tkeManagement); err == nil { // 填错的场景，忽略注解的作用
			return management
		}
	}
	return true
}

func GetServiceReadinessGateSkip(service service_wrapper.ServiceWrapper) bool {
	if readinessGateSkip, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceReadinessGateSkipAnnotation]; exist {
		if isSkip, err := ParseBool(readinessGateSkip); err == nil { // 填错的场景，忽略注解的作用
			return isSkip
		}
	}
	return false
}

func GetIngressCrossRegionID(ingress types.Ingress) string {
	if regionId, existAnnotation := ingress.Annotations()[types.IngressAnnotationCrossRegionID]; existAnnotation {
		return regionId
	} else {
		return config.Global.Region
	}
}

func IsModificationProtection(service service_wrapper.ServiceWrapper) (bool, error) {
	modificationProtectionAnnotation, exist := service.GetObjectMeta().GetAnnotations()[types.ModificationProtectionAnnotation]
	if !exist {
		return false, nil
	}

	modificationProtection, err := ParseBool(modificationProtectionAnnotation)
	if err != nil {
		return false, types.NewError(errcode.ModificationProtectionAnnontationError, err.Error(), ServiceName(service))
	}
	return modificationProtection, nil
}

func IsPreventLoopback(service service_wrapper.ServiceWrapper) (bool, bool, error) {
	preventLoopbackAnnontation, exist := service.GetObjectMeta().GetAnnotations()[types.PreventLoopbackAnnotation]
	if !exist {
		return false, false, nil
	}

	preventLoopback, err := ParseBool(preventLoopbackAnnontation)
	if err != nil {
		return false, true, types.NewError(errcode.PreventLoopbackAnnontationError, err.Error(), ServiceName(service))
	}
	return preventLoopback, true, nil
}

func IsExternalTrafficPolicyLocal(service service_wrapper.ServiceWrapper) bool {
	if service.ServiceType() == service_wrapper.CoreService {
		originService := service.RawService()
		return originService.Spec.ExternalTrafficPolicy == v1.ServiceExternalTrafficPolicyTypeLocal
	}
	return false
}

func GetClientToken(service service_wrapper.ServiceWrapper) string {
	if service.GetObjectMeta().GetAnnotations() == nil {
		return ""
	}
	return service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationClientToken]
}

func GetBackendManagementMode(service service_wrapper.ServiceWrapper) types.BackendManagementModeType {
	if mode, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationBackendManagementMode]; exist {
		if mode == string(types.BackendManagementModeTag) {
			return types.BackendManagementModeTag
		} else if mode == string(types.BackendManagementModeTargetGroup) {
			return types.BackendManagementModeTargetGroup
		}
	}
	return types.BackendManagementModeAll
}

func GetBackendManageOnly(service service_wrapper.ServiceWrapper) (bool, bool, error) {
	backendManageOnlyAnnotation, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationBackendManageOnly]
	if !exist {
		return false, false, nil
	}

	backendManageOnly, err := ParseBool(backendManageOnlyAnnotation)
	if err != nil {
		return false, true, types.NewError(errcode.BackendManageOnlyAnnontationError, err.Error(), ServiceName(service))
	}
	return backendManageOnly, true, nil
}

func GetFromOtherCluster(service service_wrapper.ServiceWrapper) (string, bool) {
	fromOtherCluster, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationFromOtherCluster]
	if !exist {
		return "", false
	}
	return fromOtherCluster, true
}

func IsSkip(service service_wrapper.ServiceWrapper) bool {
	if service.GetObjectMeta().GetAnnotations() == nil {
		return false
	}

	if data, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceAnnotationMode]; exist {
		return data == types.ModeSkip
	}

	return false
}

func NeedPatchRSTags(service service_wrapper.ServiceWrapper) bool {
	patchRSTagAnnotation, exist := service.GetObjectMeta().GetAnnotations()[types.AnnotationPatchRSTags]
	if !exist {
		return false
	}

	needPatchTag, err := ParseBool(patchRSTagAnnotation)
	if err != nil {
		return false
	}
	return needPatchTag
}
