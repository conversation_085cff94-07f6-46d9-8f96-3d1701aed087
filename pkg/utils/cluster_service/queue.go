package cluster_service

import (
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/taskqueue"

	"sync"
)

var (
	QueueServiceInstanceLock              = new(sync.Mutex)
	QueueServiceInstance     QueueService = nil
)

type QueueService interface {
	ServiceQueue() *taskqueue.TaskQueue
	MultiClusterServiceQueue() *taskqueue.TaskQueue
	HealthQueue() *taskqueue.TaskQueue
	StatusQueue() *taskqueue.TaskQueue
	ProtectQueue() *taskqueue.TaskQueue
	NodeGracefulDeletionManager() *services.NodeGracefulDeletionManager
	EndpointsQueue() *taskqueue.TaskQueue
	PodQueue() *taskqueue.GenericTaskQueue[string]
	PodDeleteQueue() *taskqueue.GenericTaskQueue[taskqueue.Element]
}
