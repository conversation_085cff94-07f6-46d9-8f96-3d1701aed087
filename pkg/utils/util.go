package utils

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sort"
	"strings"

	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/types"
	go_version "github.com/hashicorp/go-version"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
)

/*
 * https://cloud.tencent.com/document/api/214/30694#Backend 后端服务的类型
 */
type BackendType string
type BackendManagementModeType string

var (
	ENI         BackendType = "ENI"
	CVM         BackendType = "CVM"
	NAT         BackendType = "NAT"         // SNAT Pro，PVGW网络跨地域后端、PVGW网络混合云后端
	EVM         BackendType = "EVM"         // 弹性实例后端
	CCN         BackendType = "CCN"         // 云联网跨地域绑定类型后端
	GLOBALROUTE BackendType = "GLOBALROUTE" // 容器网络直连类型后端
	PVGW        BackendType = "PVGW"
	IPDC        BackendType = "IPDC"
)

const (
	DirectAccessConditionType     = "cloud.tencent.com/load-balancer-backendgroup-ready"
	DirectAccessConditionReady    = "LoadBalancerNetworkGroupReady"
	DirectAccessConditionNotReady = "LoadBalancerNetworkGroupNotReady"
)

const (
	PodNetworkAnnotation = "tke.cloud.tencent.com/networks"

	ServiceDirectAccessAnnotation    = "service.cloud.tencent.com/direct-access"
	IngressDirectAccessAnnotation    = "ingress.cloud.tencent.com/direct-access"
	ServiceEnableTKExGraceShutdown   = "service.cloud.tencent.com/enable-grace-shutdown-tkex" // kateway 优雅剔除，已经全量强制
	ServiceEnablePublicGraceShutdown = "service.cloud.tencent.com/enable-grace-shutdown"      // kateway 优雅停机，已经全量强制
	ServiceEnableCustomizedWeight    = "service.cloud.tencent.com/lb-rs-weight"
	PassToTargetAnnontation          = "service.cloud.tencent.com/pass-to-target"
	SecurityGroupsAnnontation        = "service.cloud.tencent.com/security-groups"

	TkeServiceConfigAnnontation         = "service.cloud.tencent.com/tke-service-config"
	TkeServiceConfigAutoAnnontation     = "service.cloud.tencent.com/tke-service-config-auto"
	SpecifyProtocolAnnontation          = "service.cloud.tencent.com/specify-protocol"
	LoadBalancerTypeAnnontation         = "service.kubernetes.io/loadbalance-type"
	ExistedLBAnnotation                 = "service.kubernetes.io/tke-existed-lbid"
	AnnoServiceLBInternalUniqSubnetID   = "service.kubernetes.io/qcloud-loadbalancer-internal-subnetid"
	AnnoBackendsListLabel               = "service.kubernetes.io/qcloud-loadbalancer-backends-label"
	AnnoExtensiveParameters             = "service.kubernetes.io/service.extensiveParameters"
	AnnoListenerParameters              = "service.kubernetes.io/service.listenerParameters"
	CrossRegionIdAnnontation            = "service.cloud.tencent.com/cross-region-id"        // kateway 本地 controller 管理另一地域的 clb, todo 待确认
	CrossVPCIdAnnontation               = "service.cloud.tencent.com/cross-vpc-id"           // kateway
	CrossTypeAnnontation                = "service.cloud.tencent.com/cross-type"             // kateway 跨域类型
	TargetCrossRegionIDAnnontation      = "service.cloud.tencent.com/target-cross-region-id" // kateway todo 这是给 master 用的？用于告诉 clb target 在哪个地域（和master 不在一个地域）
	TargetCrossVPCIdAnnontation         = "service.cloud.tencent.com/target-cross-vpc-id"
	HybridTypeAnnontation               = "service.cloud.tencent.com/hybrid-type"
	SnatProInfoAnnotation               = "service.cloud.tencent.com/snat-pro-info"
	ModificationProtectionAnnontation   = "service.cloud.tencent.com/modification-protection" // kateway todo 没有产品文档，不确定是不是用的不多 https://iwiki.woa.com/p/4007639346#%E9%85%8D%E7%BD%AE%E4%BF%AE%E6%94%B9%E4%BF%9D%E6%8A%A4
	PreventLoopbackAnnontation          = "service.cloud.tencent.com/prevent-loopback"
	IngressCrossRegionIdAnnontation     = "ingress.cloud.tencent.com/cross-region-id"
	TkeManagementAnnontation            = "service.cloud.tencent.com/tke-management"
	ServiceReadinessGateSkipAnnontation = "service.cloud.tencent.com/readiness-gate-skip"
	ClientTokenAnnotation               = "service.cloud.tencent.com/client-token"

	BackendManagementModeAnnotation = "service.cloud.tencent.com/backend-management-mode"
	BackendManageOnlyAnnotation     = "service.cloud.tencent.com/backend-manage-only" // kateway 开启该注解之后，负载均衡的转发规则将不会由子集群处理
	FromOtherClusterAnnotation      = "service.cloud.tencent.com/from-other-cluster"  // kateway 子集群允许通过使用已有的方式，使用TDCC集群的负载均衡

	// LocalSvcOnlyBindNodeWithPodAnnontation = "service.kubernetes.io/local-svc-only-bind-node-with-pod"
	// LocalSvcWeightedBalancerAnnontation    = "service.cloud.tencent.com/local-svc-weighted-balance"

	CLBRsProtection = "tke.cloud.tencent.com/protected-by-service-controller" // Node优雅删除：Finalizer 保护机制

	IngressAnnoExistedLBID = "kubernetes.io/ingress.existLbId"

	FORWARDTYPE             = 1
	CLASSICTYPE             = 0
	ForwardLoadbalanceType  = "yunapiv3_forward_clb"
	ClassicLoadbalanceType  = "yunapi_clb"
	ClassicLoadbalanceType2 = "classic"

	CLUSTER_TYPE_CLUSTER_MANAGED     = "MANAGED_CLUSTER"
	CLUSTER_TYPE_CLUSTER_INDEPENDENT = "INDEPENDENT_CLUSTER"

	PROTOCOL_TCP     = "TCP"
	PROTOCOL_UDP     = "UDP"
	PROTOCOL_TCP_SSL = "TCP_SSL"
	PROTOCOL_QUIC    = "QUIC"
	PROTOCOL_HTTP    = "HTTP"
	PROTOCOL_HTTPS   = "HTTPS"

	InstanceTypeLabelKey = "node.kubernetes.io/instance-type"
	InstanceTypeEKS      = "EKLET"
	InstanceTypeIDC      = "EXTERNAL"

	BackendManagementModeAll         BackendManagementModeType = "all"
	BackendManagementModeTag         BackendManagementModeType = "tag" // kateway 多集群模式下， 子集群 svc controller 将rs添加集群tag,，各自管理本集群后端
	BackendManagementModeTargetGroup BackendManagementModeType = "target-group"
)

const (
	IngressClassKey    = "kubernetes.io/ingress.class"
	QcloudIngressClass = "qcloud"
)

var (
	ServiceCondition = "service.cloud.tencent.com/status.conditions"

	ServiceConditionType = "Ready"
)

type BackendConfigAnnotation struct {
	Ports   map[string]string `json:"ports,omitempty"`
	Default string            `json:"default,omitempty"`
}

type SpecifyProtocolAnnotation map[int32]SpecifyProtocol

type SpecifyProtocol struct {
	Protocol []string                        `json:"protocol,omitempty"`
	Tls      *string                         `json:"tls,omitempty"`
	Hosts    map[string]*SpecifyProtocolHost `json:"hosts,omitempty"`
}

type SpecifyProtocolHost struct {
	Tls *string `json:"tls,omitempty"`
}

// Min return the min of two int
func Min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func JsonWrapper(obj interface{}) string {
	if jsonStr, jsonErr := json.Marshal(obj); jsonErr == nil {
		return string(jsonStr)
	}
	return "json_format_error"
}

func ConvertToClassicLbProtocol(protocol string) int {
	switch protocol {
	case "TCP", "tcp":
		return 2
	case "UDP", "udp":
		return 3
	default:
		return 2
	}
}

func IsQCLOUDIngress(ingress types.Ingress) bool {
	if config, exist := ingress.Annotations()[IngressClassKey]; exist {
		return config == "" || config == QcloudIngressClass
	}
	if ingress.IngressClassName() != nil && *ingress.IngressClassName() != QcloudIngressClass {
		return false
	}
	return true
}

func IngressName(ingress types.Ingress) string {
	return fmt.Sprintf("%s/%s", ingress.Namespace(), ingress.Name())
}

func GetDefaultDomain(loadBalancer *clb.LoadBalancer) *string {
	if *loadBalancer.Forward == FORWARDTYPE {
		if loadBalancer.Domain != nil && *loadBalancer.Domain != "" {
			return common.StringPtr(*loadBalancer.Domain)
		} else if len(loadBalancer.LoadBalancerVips) != 0 {
			return loadBalancer.LoadBalancerVips[0]
		}
	} else {
		if len(loadBalancer.LoadBalancerVips) != 0 {
			return loadBalancer.LoadBalancerVips[0]
		}
	}
	return nil
}

func NewTagInfo(tagKey string, tagValue string) *clb.TagInfo {
	return &clb.TagInfo{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}

func NewTagResource(tagKey string, tagValue string) *tag.TagResource {
	return &tag.TagResource{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}

func NewTag(tagKey string, tagValue string) *tag.Tag {
	return &tag.Tag{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}

func NewTagKeyObject(tagKey string) *tag.TagKeyObject {
	return &tag.TagKeyObject{
		TagKey: &tagKey,
	}
}

func NewTagFilter(tagKey string, tagValue string) *tag.TagFilter {
	return &tag.TagFilter{
		TagKey:   &tagKey,
		TagValue: []*string{&tagValue},
	}
}

func ConvertTagList(tagResources []*tag.TagResource) []*tag.Tag {
	tags := make([]*tag.Tag, len(tagResources))
	for i, tagResource := range tagResources {
		tags[i] = NewTag(*tagResource.TagKey, *tagResource.TagValue)
	}
	return tags
}

func ConvertTagResourceList(tagResources []*clb.TagInfo) []*tag.TagResource {
	tags := make([]*tag.TagResource, len(tagResources))
	for i, tagResource := range tagResources {
		tags[i] = NewTagResource(*tagResource.TagKey, *tagResource.TagValue)
	}
	return tags
}

func ConvertResourceTag(tagResources []*tag.TagResource) []*clb.TagInfo {
	tags := make([]*clb.TagInfo, len(tagResources))
	for i, tagResource := range tagResources {
		tags[i] = NewTagInfo(*tagResource.TagKey, *tagResource.TagValue)
	}
	return tags
}

func ConvertTagKeyObjectList(tagResources []*tag.TagResource) []*tag.TagKeyObject {
	tagKeyObjects := make([]*tag.TagKeyObject, len(tagResources))
	for i, tagResource := range tagResources {
		tagKeyObjects[i] = NewTagKeyObject(*tagResource.TagKey)
	}
	return tagKeyObjects
}

// getTkeConfig parse tkeconfig from cloudConfig

// todo 重命名
func PodName(pod *v1.Pod) string {
	return fmt.Sprintf("%s/%s", pod.Namespace, pod.Name)
}

func ServiceName(service service_wrapper.ServiceWrapper) string {
	return fmt.Sprintf("%s/%s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName())
}

func IsLoadBalancerType(service service_wrapper.ServiceWrapper) bool {
	if service.IsMCS() {
		originService := service.RawMCS()

		if GetTkeManagement(service) == false {
			return false
		}
		return originService.Spec.Type == v1.ServiceTypeLoadBalancer
	}

	if service.IsService() {
		originService := service.RawService()

		if GetTkeManagement(service) == false {
			return false
		}
		if IsClusterIPAndNeedsCLB(service) {
			return true
		}
		return originService.Spec.Type == v1.ServiceTypeLoadBalancer
	}

	return false
}

func IsIngressSelectByService(ingress types.Ingress, serviceName string) bool {
	for _, rules := range ingress.Rules() {
		for _, p := range rules.HTTPPaths {
			if p.Backend.ServiceName == serviceName {
				return true
			}
		}
	}
	return false
}

func IsTkeServiceConfigAuto(service service_wrapper.ServiceWrapper) (bool, error) {
	if tkeServiceConfigAutoAnnontation, exist := service.GetObjectMeta().GetAnnotations()[TkeServiceConfigAutoAnnontation]; exist {
		tkeServiceConfigAuto, err := ParseBool(tkeServiceConfigAutoAnnontation)
		if err != nil {
			return false, types.NewError(errcode.TkeServiceConfigAutoAnnontationError, "", ServiceName(service))
		}
		return tkeServiceConfigAuto, nil
	}
	return false, nil
}

// func IngressAutoServiceConfigName(ingress *v1beta1.Ingress) string {
//	return fmt.Sprintf("%s-auto-ingress-config", ingress.Name)
// }

func ServiceAutoServiceConfigName(service service_wrapper.ServiceWrapper) string {
	if service.ServiceType() == service_wrapper.CoreService {
		return fmt.Sprintf("%s-auto-service-config", service.GetObjectMeta().GetName())
	} else if service.ServiceType() == service_wrapper.MultiClusterService {
		return fmt.Sprintf("%s-auto-multiclusterservice-config", service.GetObjectMeta().GetName())
	}
	panic("unexpected service type.")
}

/**
 * Pod 是否被 Service Selector
 */
func IsPodSelectByService(service service_wrapper.ServiceWrapper, endpoints *v1.Endpoints, pod *v1.Pod) bool {
	if service.IsService() {
		originService := service.RawService()

		podLabels := labels.Set(pod.Labels)
		if !IsNoSelectorService(service) {
			if service.GetObjectMeta().GetNamespace() == pod.Namespace {
				serviceSelector := labels.SelectorFromSet(originService.Spec.Selector)
				if serviceSelector.Matches(podLabels) {
					return true
				}
			}
		} else { // Without Selector类型的Service. 参考 https://kubernetes.io/docs/concepts/services-networking/service/#services-without-selectors
			for _, subset := range endpoints.Subsets {
				for _, address := range append(subset.Addresses, subset.NotReadyAddresses...) {
					if address.IP == pod.Status.PodIP {
						return true
					}
				}
			}
		}
	}
	return false
}

func ParseBool(str string) (bool, error) {
	switch str {
	case "true", "TRUE", "True":
		return true, nil
	case "false", "FALSE", "False":
		return false, nil
	}
	return false, errors.New("ParseBool")
}

func IsL4Protocol(protocol string) bool {
	return protocol == PROTOCOL_TCP || protocol == PROTOCOL_UDP || protocol == PROTOCOL_TCP_SSL || protocol == PROTOCOL_QUIC
}

func IsL7Protocol(protocol string) bool {
	return protocol == PROTOCOL_HTTP || protocol == PROTOCOL_HTTPS
}

func IsUDPFamilyProtocol(protocol string) bool {
	return protocol == PROTOCOL_UDP || protocol == PROTOCOL_QUIC
}

func IsTCPFamilyProtocol(protocol string) bool {
	return protocol == PROTOCOL_HTTP || protocol == PROTOCOL_HTTPS || protocol == PROTOCOL_TCP || protocol == PROTOCOL_TCP_SSL
}

func GetListenerKey(port int64, protocal string) string {
	return fmt.Sprintf("%d_%s", port, strings.ToUpper(protocal))
}

func GetClassicalListenerKey(port int64, targetPort int64, protocal string) string {
	return fmt.Sprintf("%d_%d_%s", port, targetPort, strings.ToUpper(protocal))
}

func IsInEKSCluster() bool {
	_, has := os.LookupEnv("TKE_ENV_FOR_EKS_CLUSTER")
	return has
}

// IsClusterIPAndNeedsCLB
// EKS特殊功能，CLB模拟ClusterIP类型Service
// 前提条件：EKS集群，ClusterIP类型Service，开启ClusterIP模拟功能，ClusterIP不为"None"(显示声明不需要IP)。
// 允许创建内网型负载均衡，并反写VIP到ClusterIP中。
func IsClusterIPAndNeedsCLB(service service_wrapper.ServiceWrapper) bool {
	if service.ServiceType() == service_wrapper.CoreService {
		originService := service.RawService()
		return IsInEKSCluster() && originService.Spec.Type == v1.ServiceTypeClusterIP && originService.Spec.ClusterIP != "None" && config.Global.EnableClusterIP
	}
	return false
}

// IsLoadBalancerAndNeedsClusterIP
// EKS特殊功能，CLB模拟ClusterIP类型Service
// 前提条件：EKS集群，LoadBalancer类型Service，开启ClusterIP模拟功能。
// 需要反写VIP到ClusterIP中。
func IsLoadBalancerAndNeedsClusterIP(service service_wrapper.ServiceWrapper) bool {
	if service.ServiceType() == service_wrapper.CoreService {
		originService := service.RawService()
		return IsInEKSCluster() && originService.Spec.Type == v1.ServiceTypeLoadBalancer && config.Global.EnableClusterIP
	}
	return false
}

func IsENILikeType(backendType string) bool {
	return backendType == string(ENI) || backendType == string(EVM) || backendType == string(GLOBALROUTE) || backendType == string(CCN) || backendType == string(NAT) || backendType == string(PVGW) || backendType == string(IPDC)
}

func IsCVMLikeType(backendType string) bool {
	return backendType == string(CVM)
}

func IsEKSNode(node *v1.Node) bool {
	if value, has := node.Labels[InstanceTypeLabelKey]; has && strings.ToUpper(value) == InstanceTypeEKS {
		return true
	}
	return false
}

func IsIDCNode(node *v1.Node) bool {
	if value, has := node.Labels[InstanceTypeLabelKey]; has && strings.ToUpper(value) == InstanceTypeIDC {
		return true
	}
	return false
}

func IsCXMNode(node *v1.Node) bool {
	providerId := node.Spec.ProviderID
	if strings.HasPrefix(providerId, "tencentcloud:") {
		instanceId := providerId[strings.LastIndex(providerId, "/")+1:]
		if strings.HasPrefix(instanceId, "kn-") {
			return true
		}
	}
	return false
}

func IsTencentCloudCVMNode(node *v1.Node) bool {
	providerId := node.Spec.ProviderID
	if strings.HasPrefix(providerId, "tencentcloud:") {
		instanceId := providerId[strings.LastIndex(providerId, "/")+1:]
		if strings.HasPrefix(instanceId, "ins-") {
			return true
		}
	}
	return false
}

func IsTKENode(node *v1.Node) bool {
	if IsEKSNode(node) || IsIDCNode(node) {
		return false
	}
	return true
}

// 后端协议逻辑
// ipv4 -> ipv4
// ipv6(NAT) -> ipv4
// ipv6(FullChain) -> ipv6
//
// Special For L7 Listener : ipv6(FullChain) with MixIpTarget -> ipv4 or ipv6
func GetBackendType(loadBalancer *clb.LoadBalancer) string {
	addressIPVersion := strings.ToLower(*loadBalancer.AddressIPVersion)
	if addressIPVersion == "ipv6" { // IPv6
		if *loadBalancer.IPv6Mode == "IPv6FullChain" { // IPv6FullChain
			if *loadBalancer.MixIpTarget == true {
				return "mixed"
			}
			return "ipv6"
		} else { // IPv6Nat64
			return "ipv4"
		}
	} else { // IPv4
		return "ipv4"
	}
}

func FilterPodsByBackendType(pods []*v1.Pod, backendType string) []*v1.Pod {
	if backendType == "mixed" {
		return pods
	}

	result := make([]*v1.Pod, 0)
	for index, pod := range pods {
		if pod.Status.PodIPs != nil && len(pod.Status.PodIPs) != 0 {
			for _, podIP := range pod.Status.PodIPs {
				if CheckBackendType(podIP.IP, backendType) {
					result = append(result, pods[index])
					break
				}
			}
		} else {
			if CheckBackendType(pod.Status.PodIP, backendType) {
				result = append(result, pods[index])
			}
		}
	}
	return result
}

// kateway  检查 NodeInternalIP ipv4/ipv6
func FilterNodesByBackendType(nodes []*v1.Node, backendType string) []*v1.Node {
	if backendType == "mixed" {
		return nodes
	}

	result := make([]*v1.Node, 0)
	for index, node := range nodes {
		if node.Status.Addresses != nil && len(node.Status.Addresses) != 0 {
			for _, address := range node.Status.Addresses {
				if address.Type == v1.NodeInternalIP && CheckBackendType(address.Address, backendType) {
					result = append(result, nodes[index])
					break
				}
			}
		}
	}
	return result
}

func CheckBackendType(ip string, backendType string) bool {
	if backendType == "ipv4" {
		return strings.Contains(ip, ".")
	} else if backendType == "ipv6" {
		return strings.Contains(ip, ":")
	}
	return true // mixed ?
}

func GetNodeBackend(node *v1.Node, backendType string) (string, bool) {
	if backendType == "ipv6" || backendType == "ipv4" {
		return GetNodeIpv4Ipv6Backend(node, backendType)
	} else {
		if backend, exist := GetNodeIpv4Ipv6Backend(node, "ipv6"); exist {
			return backend, exist
		}
		return GetNodeIpv4Ipv6Backend(node, "ipv4")
	}
}

func GetNodeIpv4Ipv6Backend(node *v1.Node, backendType string) (string, bool) {
	if node.Status.Addresses != nil && len(node.Status.Addresses) != 0 {
		for _, address := range node.Status.Addresses {
			if address.Type == v1.NodeInternalIP && CheckBackendType(address.Address, backendType) {
				return address.Address, true
			}
		}
	}

	return "", false
}

func IsPodUnableBind(pod *v1.Pod) bool {
	if pod.Status.PodIP == "" { // ENI IP 还未分配的话，跳过该Pod。
		return true
	}

	if pod.Status.Reason == "NodeLost" { // 典型场景，EKS正在原地更换CXM子机
		return true
	}

	// PodFailed：Pod已经失效，IP被销毁。比如被驱逐节点，不能够作为绑定目标
	// PodSucceeded：Pod已经结束任务，IP被销毁。比如Job Pod Complete。
	if pod.Status.Phase == v1.PodFailed || pod.Status.Phase == v1.PodSucceeded {
		return true
	}

	return false
}

func PodContainerReady(pod *v1.Pod) bool {
	for _, condition := range pod.Status.Conditions {
		if condition.Type != v1.ContainersReady {
			continue
		}
		return condition.Status == v1.ConditionTrue
	}
	return false
}

func GetPodIPs(pod *v1.Pod) []string {
	result := make([]string, 0)
	if pod.Status.PodIPs != nil && len(pod.Status.PodIPs) != 0 {
		for _, podIP := range pod.Status.PodIPs {
			result = append(result, podIP.IP)
		}
		return result
	}
	if pod.Status.PodIP != "" {
		result = append(result, pod.Status.PodIP)
	}
	return result
}

func GetPodBackend(pod *v1.Pod, backendType string) (string, bool) {
	if backendType == "ipv6" || backendType == "ipv4" {
		return GetPodIpv4Ipv6Backend(pod, backendType)
	} else {
		if backend, exist := GetPodIpv4Ipv6Backend(pod, "ipv6"); exist {
			return backend, exist
		}
		return GetPodIpv4Ipv6Backend(pod, "ipv4")
	}
}

func GetPodIpv4Ipv6Backend(pod *v1.Pod, backendType string) (string, bool) {
	if pod.Status.PodIPs != nil && len(pod.Status.PodIPs) != 0 {
		for _, podIP := range pod.Status.PodIPs {
			if CheckBackendType(podIP.IP, backendType) {
				return podIP.IP, true
			}
		}
	} else {
		if CheckBackendType(pod.Status.PodIP, backendType) {
			return pod.Status.PodIP, true
		}
	}

	return "", false
}

func IsNoSelectorService(service service_wrapper.ServiceWrapper) bool {
	if service.ServiceType() == service_wrapper.CoreService {
		originService := service.RawService()
		return originService.Spec.Selector == nil || len(originService.Spec.Selector) == 0
	}
	return false
}

func StringSortJoin(elems []*string, sep string) string {
	elemsWrraper := make([]string, len(elems))
	for index, _ := range elems {
		elemsWrraper[index] = *elems[index]
	}
	sort.Strings(elemsWrraper)
	return strings.Join(elemsWrraper, ",")
}

func KubernetesVersionCheck(nodeList []*v1.Node, serverSemver *go_version.Version, version string, nodeCheck bool) (bool, error) {
	semverBase, _ := go_version.NewSemver(version)
	if nodeCheck {
		// 检查所有节点的Kubelet版本
		// nodeList, err := clientSet.CoreV1().Nodes().List(context.Background(), metav1.ListOptions{})
		// if err != nil {
		//	klog.Errorf("ClusterInfoService Kubernetes ClientSet NodesList Error. %s", err.Error())
		//	return false, err
		// }
		for _, node := range nodeList {
			semver, err := go_version.NewSemver(node.Status.NodeInfo.KubeletVersion)
			if err != nil {
				klog.Errorf("ClusterInfoService Unexpected Node KubeletVersion Error. %s", err.Error())
				return false, err
			}
			if semver.Compare(semverBase) < 0 { // 当前服务版本小于指定版本
				return false, nil
			}
		}
	}

	// serverVersion, err := clientSet.ServerVersion()
	// for err != nil {
	//	klog.Errorf("Unexpected Kubernetes Version Error. %s", err.Error())
	//	return false, err
	// }
	// serverSemver, err := go_version.NewSemver(serverVersion.GitVersion)
	// if err != nil {
	//	klog.Errorf("Unexpected Kubernetes Version Error. %s", err.Error())
	//	return false, err
	// }
	if serverSemver.Compare(semverBase) < 0 { // 当前服务版本小于指定版本
		return false, nil
	}
	return true, nil
}

func GetIngressExistLbID(ingress types.Ingress) (string, bool) {
	if existLbId, exist := ingress.Annotations()[IngressAnnoExistedLBID]; exist {
		return existLbId, true
	}
	return "", false
}

func Errors(errs []error) string { // 每个 errorcode 取第一个错误信息
	data := make(map[string]bool)

	result := make([]string, 0)
	for _, err := range errs {
		if svcError, ok := lo.ErrorsAs[*types.Error](err); ok {
			if _, exist := data[svcError.ErrorCode.Code]; exist {
				continue
			}
			data[svcError.ErrorCode.Code] = true
		}
		result = append(result, err.Error())
	}
	return strings.Join(result, "; ")
}
