package utils

import (
	"encoding/json"
	"strconv"
	"strings"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	v1 "k8s.io/api/core/v1"

	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/types"
)

const (
	WeightDefault          = 10
	WeightDefaultLocal     = 1
	WeightDefaultProtect   = 8
	WeightGracefulShutdown = 0
)

func IsServiceEnableCustomizedWeight(service service_wrapper.ServiceWrapper) (*CustomizedWeight, error) {
	if CustomizedWeightStr, exist := service.GetObjectMeta().GetAnnotations()[types.ServiceEnableCustomizedWeight]; exist {
		customizedWeight := CustomizedWeight{}
		err := json.Unmarshal([]byte(CustomizedWeightStr), &customizedWeight)
		if err != nil {
			return nil, err
		}
		return &customizedWeight, nil
	}
	return nil, nil
}

func DeterminePodCustomizedWeight(port int64, protocol string, pod *v1.Pod, cus *CustomizedWeight) *int {
	for _, owner := range pod.OwnerReferences {
		if owner.Kind == "StatefulSet" || owner.Kind == "StatefulSetPlus" || owner.Kind == "TApp" {
			split := strings.Split(pod.Name, "-")
			podIndexStr := split[len(split)-1]
			podIndex, err := strconv.Atoi(podIndexStr)
			if err != nil {
				return nil
			}
			for _, group := range cus.Groups {
				for _, sts := range group.StatefulSet {
					if sts.Name == owner.Name {
						if strings.ToUpper(group.Key.Proto) == protocol && group.Key.Port == int(port) {
							for _, weight := range sts.PodWeights {
								for _, index := range weight.PodIndex {
									if index == podIndex {
										return common.IntPtr(weight.Weight)
									}
								}
							}
							return nil
						}
					}
				}
			}
		}
	}
	return nil
}

func IsHealthyWeight(weight int64) bool {
	return weight > 0 && weight != WeightDefaultProtect
}
