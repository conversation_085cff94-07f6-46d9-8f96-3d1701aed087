package tencent

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/types"
	"k8s.io/apimachinery/pkg/util/intstr"

	"cloud.tencent.com/lb-controller/pkg/utils"
)

const (
	defaultPath = "/"

	RuleMix    = "kubernetes.io/ingress.rule-mix"
	HttpRules  = "kubernetes.io/ingress.http-rules"
	HttpsRules = "kubernetes.io/ingress.https-rules"
)

var TkePathTypePrefix = map[TkePathType]string{
	Exact:           "=",  // 开头表示精确匹配。
	Regex:           "~",  // 开头表示区分大小写的正则匹配。
	RegexIgnoreCase: "~*", // 开头表示不区分大小写的正则匹配。
}

func buldRealPath(ingress types.Ingress, rule *Rule) error {
	path, err := makeRealPath(ingress, rule.Path, rule.PathType)
	if err != nil {
		return err
	}
	rule.RealPath = path
	return nil
}

func makeRealPath(ingress types.Ingress, path string, pathTypes []TkePathType) (string, error) {
	if pathTypes == nil {
		return path, nil
	}
	isAbsolutePath := false
	for _, pathType := range pathTypes {
		if pathType == NonAbsolutePath {
			path = strings.TrimPrefix(path, "/")
			isAbsolutePath = true
		}
	}
	for _, pathType := range pathTypes {
		if pathType == Regex || pathType == RegexIgnoreCase {
			regex, _ := regexp.Compile("^[\\w-_/.?=:^*$()\\[\\]+|]*$")
			if !regex.MatchString(path) {
				return "", types.NewError(errcode.RegexPathIllegal, "", utils.IngressName(ingress), path)
			}
			if _, err := regexp.Compile(path); err != nil {
				return "", types.NewError(errcode.RegexPathIllegal, "", utils.IngressName(ingress), path)
			}
			path = fmt.Sprintf("%s%s", TkePathTypePrefix[pathType], path)
		}
		if pathType == Exact {
			if isAbsolutePath {
				return "", types.NewError(errcode.PathIllegal, "", utils.IngressName(ingress), path)
			}
			regex, _ := regexp.Compile("^[\\w-_/.?=:]*$")
			if !regex.MatchString(path) {
				return "", types.NewError(errcode.PathIllegal, "", utils.IngressName(ingress), path)
			}
			path = fmt.Sprintf("%s%s", TkePathTypePrefix[pathType], path)
		}
	}
	if len(path) > 200 {
		return "", types.NewError(errcode.PathLengthIllegal, "", utils.IngressName(ingress))
	}
	return path, nil
}

func getIngressRules(ingress types.Ingress, defaultDomain string) ([]*Rule, []*Rule, error) {
	httpRules := make([]*Rule, 0)
	httpsRules := make([]*Rule, 0)
	var err error

	if ruleMix(ingress) {
		httpRules, httpsRules, err = getMixedRules(ingress, defaultDomain)
		if err != nil {
			return nil, nil, err
		}
	} else {
		if len(ingress.TLS()) == 0 { // Http Rule
			httpRules, err = getRules(ingress, defaultDomain)
			if err != nil {
				return nil, nil, err
			}
		} else {
			httpsRules, err = getRules(ingress, defaultDomain)
			if err != nil {
				return nil, nil, err
			}
		}
	}

	return httpRules, httpsRules, nil
}

func getRules(ingress types.Ingress, defaultDomain string) ([]*Rule, error) {
	rules := make([]*Rule, 0)
	for _, rlist := range ingress.Rules() {
		host := rlist.Host
		if rlist.Host == "" {
			if defaultDomain == "" { // 无IPv4 IP作为默认域名，用户又未设置域名的情况
				return nil, types.NewError(errcode.IngressRulesHostEmptyError, "", utils.IngressName(ingress))
			}
			host = defaultDomain
		}
		rule := make([]*Rule, len(rlist.HTTPPaths))
		for index, r := range rlist.HTTPPaths {
			// r.Path为空的处理
			path := r.Path
			if r.Path == "" {
				path = defaultPath
			}
			rule[index] = NewRule(host, path, &r.PathType, r.Backend)
		}
		rules = append(rules, rule...)
	}

	realRules := make([]*Rule, 0)
	for index, rule := range rules {
		if err := buldRealPath(ingress, rule); err == nil {
			realRules = append(realRules, rules[index])
		}
	}
	return realRules, nil
}

func getMixedRules(ingress types.Ingress, defaultDomain string) ([]*Rule, []*Rule, error) {
	httpRuleMap, err := httpRules(ingress, defaultDomain)
	if err != nil {
		return nil, nil, err
	}
	httpsRuleMap, err := httpsRules(ingress, defaultDomain)
	if err != nil {
		return nil, nil, err
	}

	httpRules := make([]*Rule, 0)
	httpsRules := make([]*Rule, 0)
	for _, rlist := range ingress.Rules() {
		host := rlist.Host
		if rlist.Host == "" {
			if defaultDomain == "" { // 无IPv4 IP作为默认域名，用户又未设置域名的情况
				return nil, nil, types.NewError(errcode.IngressRulesHostEmptyError, "", utils.IngressName(ingress))
			}
			host = defaultDomain
		}
		for _, r := range rlist.HTTPPaths {
			path := r.Path
			if r.Path == "" {
				path = defaultPath
			}
			rule := NewRule(host, path, &r.PathType, r.Backend)
			// 如果在http中,就加到http中
			var inHttp, inHttps bool
			var httpRule, httpsRule *Rule
			if httpRule, inHttp = (*httpRuleMap)[ruleToString(rule)]; inHttp {
				targetRule := NewRule(host, path, &r.PathType, r.Backend)
				if httpRule.PathType != nil {
					targetRule.PathType = httpRule.PathType
				}
				httpRules = append(httpRules, targetRule)
			}
			// 如果在https中,就加到https中;如果都不在,也加到https中
			if httpsRule, inHttps = (*httpsRuleMap)[ruleToString(rule)]; inHttps || (!inHttp && !inHttps) {
				targetRule := NewRule(host, path, &r.PathType, r.Backend)
				if httpsRule != nil && httpsRule.PathType != nil {
					targetRule.PathType = httpsRule.PathType
				}
				httpsRules = append(httpsRules, targetRule)
			}
		}
	}

	realHttpRules := make([]*Rule, 0)
	for index, rule := range httpRules {
		if err := buldRealPath(ingress, rule); err == nil {
			realHttpRules = append(realHttpRules, httpRules[index])
		}
	}
	realHttpsRules := make([]*Rule, 0)
	for index, rule := range httpsRules {
		if err := buldRealPath(ingress, rule); err == nil {
			realHttpsRules = append(realHttpsRules, httpsRules[index])
		}
	}
	return realHttpRules, realHttpsRules, nil
}

func ruleMix(ingress types.Ingress) bool {
	if val, ok := ingress.Annotations()[RuleMix]; ok {
		if v, err := strconv.ParseBool(val); err == nil {
			return v
		}
	}
	return false
}

func httpRules(ingress types.Ingress, defaultHost string) (*RuleMap, error) {
	val, ok := ingress.Annotations()[HttpRules]
	if !ok {
		return &RuleMap{}, nil
	}

	rules, err := toRuleList(val)
	if err != nil {
		return nil, types.NewError(errcode.IngressAnnotationHttpRuleError, "", utils.IngressName(ingress))
	}

	return toRuleMap(ingress, rules, defaultHost)
}

func httpsRules(ingress types.Ingress, defaultHost string) (*RuleMap, error) {
	val, ok := ingress.Annotations()[HttpsRules]
	if !ok {
		return &RuleMap{}, nil
	}

	rules, err := toRuleList(val)
	if err != nil {
		return nil, types.NewError(errcode.IngressAnnotationHttpsRuleError, "", utils.IngressName(ingress))
	}

	return toRuleMap(ingress, rules, defaultHost)
}

func toRuleList(rule string) ([]*Rule, error) {
	rules := make([]*Rule, 0)
	if err := json.Unmarshal([]byte(rule), &rules); err != nil {
		return nil, err
	}
	return rules, nil
}

func toRuleMap(ingress types.Ingress, rules []*Rule, defaultDomain string) (*RuleMap, error) {
	ruleMap := make(RuleMap, 0)
	for _, rule := range rules {
		if rule.Host == "" {
			if defaultDomain == "" { // 无IPv4 IP作为默认域名，用户又未设置域名的情况
				return nil, types.NewError(errcode.IngressRulesHostEmptyError, "", utils.IngressName(ingress))
			}
			rule.Host = defaultDomain
		}
		if rule.Path == "" {
			rule.Path = defaultPath
		}
		if rule.Backend != nil {
			ruleMap[ruleToString(rule)] = rule
		}
	}
	return &ruleMap, nil
}

func ruleToString(r *Rule) string {
	servicePort := r.Backend.ServicePort()
	return fmt.Sprintf("%s_%s_%s_%s", r.Host, r.Path, r.Backend.ServiceName(), servicePort.String())
}

type TkePathType string

const ( // 参考：https://cloud.tencent.com/document/product/214/9032#.E8.BD.AC.E5.8F.91.E5.9F.9F.E5.90.8D.E9.85.8D.E7.BD.AE.E8.A7.84.E5.88.99
	// 负载均衡默认路径格式，已经是前缀匹配的含义，无需支持。

	// Prefix          TkePathType = "Prefix"          // ^~ 开头表示 URL 以某个常规字符串开头，不是正则匹配。
	Exact           TkePathType = "Exact"           // =  开头表示精确匹配。
	Regex           TkePathType = "Regex"           // ~  开头表示区分大小写的正则匹配。
	RegexIgnoreCase TkePathType = "RegexIgnoreCase" // ~* 开头表示不区分大小写的正则匹配。

	NonAbsolutePath TkePathType = "NonAbsolutePath" // 转发路径不要以绝对路径开头
)

type RuleBackend struct {
	Name string             `json:"serviceName,omitempty"`
	Port intstr.IntOrString `json:"servicePort,omitempty"`
}

func (this *RuleBackend) ServiceName() string {
	return this.Name
}

func (this *RuleBackend) ServicePort() intstr.IntOrString {
	return this.Port
}

type Rule struct {
	Host     string        `json:"host,omitempty"`
	Path     string        `json:"path,omitempty"`
	PathType []TkePathType `json:"pathType,omitempty"`
	Backend  *RuleBackend  `json:"backend"`

	RealPath string
}

type RuleMap map[string]*Rule

func NewRule(host string, path string, pathType *string, backend types.IngressBackend) *Rule {
	rule := &Rule{
		Host:     host,
		Path:     path,
		PathType: ConvertPathType(pathType),
		Backend: &RuleBackend{
			Name: backend.ServiceName,
			Port: backend.ServicePort,
		},
	}
	return rule
}

func ConvertPathType(pathType *string) []TkePathType {
	if pathType != nil {
		if *pathType == "Exact" {
			return []TkePathType{Exact}
		}
	}
	return nil
}
