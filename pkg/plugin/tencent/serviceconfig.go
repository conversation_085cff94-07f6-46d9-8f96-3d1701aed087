package tencent

import (
	"context"
	"strings"

	tkeserviceapi "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	glog "k8s.io/klog/v2"
	"k8s.io/utils/pointer"

	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/net"
	"git.woa.com/kateway/pkg/runtime/conv"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"

	"cloud.tencent.com/lb-controller/pkg/utils"
)

func CheckAutoTkeServiceConfig(syncContext *SyncContext) error {
	service := syncContext.Service
	// 在TkeServiceConfig开启自动同步的情况下，保证TkeServiceConfig资源被创建。
	// Already Prechecked Error of the annotation.
	if serviceConfigAuto, _ := utils.IsTkeServiceConfigAuto(service); serviceConfigAuto {
		autoServiceConfigName := utils.ServiceAutoServiceConfigName(service)
		// Create Auto ServiceConfig
		if _, err := cluster.Instance.TkeServiceConfigLister().TkeServiceConfigs(service.GetObjectMeta().GetNamespace()).Get(autoServiceConfigName); err != nil {
			if !errors.IsNotFound(err) {
				glog.Errorf("TkeServiceConfigs Get error for %s/%s. error %v", service.GetObjectMeta().GetNamespace(), autoServiceConfigName, err)
				return err
			}
			_, err := cluster.Instance.TkeServiceConfigClient().CloudV1alpha1().TkeServiceConfigs(service.GetObjectMeta().GetNamespace()).Create(context.Background(), &tkeserviceapi.TkeServiceConfig{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: service.GetObjectMeta().GetNamespace(),
					Name:      autoServiceConfigName,
				},
				Spec: tkeserviceapi.TkeServiceConfigSpec{},
			}, metav1.CreateOptions{})
			if err != nil {
				if !errors.IsAlreadyExists(err) {
					return err
				}
				glog.Errorf("TkeServiceConfigs Create error for %s/%s. error %v", service.GetObjectMeta().GetNamespace(), autoServiceConfigName, err)
			}

			if err := EnsureTkeServiceConfig(syncContext); err != nil {
				return err
			}
		}
	}
	return nil
}

// kateway todo 将clb端监听器信息(4/7层)同步到 tkeserviceconfig, 有无风险？
func EnsureTkeServiceConfig(syncContext *SyncContext) error {
	service := syncContext.Service
	currentListeners, err := GetCurrentTKEServiceListeners(syncContext.LoadBalancerContext)
	if err != nil {
		return err
	}

	// Already Prechecked Error of the annotation.
	if serviceConfigAuto, _ := utils.IsTkeServiceConfigAuto(service); serviceConfigAuto {
		autoServiceConfigName := utils.ServiceAutoServiceConfigName(service)

		tkeServiceConfig, err := cluster.Instance.TkeServiceConfigLister().TkeServiceConfigs(service.GetObjectMeta().GetNamespace()).Get(autoServiceConfigName)
		if err != nil {
			glog.Errorf("TkeServiceConfigs Get error for %s/%s. error %v", service.GetObjectMeta().GetNamespace(), autoServiceConfigName, err)
			return err
		}
		for i := 0; i < 3; i++ {
			defaultDomain := ""
			if syncContext.LoadBalancerContext.DefaultDomain != nil {
				defaultDomain = *syncContext.LoadBalancerContext.DefaultDomain
			}
			if err = updateTkeServiceConfig(tkeServiceConfig, currentListeners, defaultDomain); err != nil {
				if tkeServiceConfig, err = cluster.Instance.TkeServiceConfigLister().TkeServiceConfigs(service.GetObjectMeta().GetNamespace()).Get(autoServiceConfigName); err != nil {
					if errors.IsNotFound(err) {
						glog.Infof("TkeServiceConfig Sync Error. TkeServiceConfig Not Exist. %v", err)
						return nil
					}
					glog.Errorf("TkeServiceConfigs Get error for %s/%s. error %v", service.GetObjectMeta().GetNamespace(), autoServiceConfigName, err)
					return err
				}
			} else { // TkeServiceConfig 的状态修改成功处理
				return nil
			}
		}
		glog.Infof("TkeServiceConfig Sync Retry Error. %s", err)
	}
	return nil
}

func updateTkeServiceConfig(tkeServiceConfig *tkeserviceapi.TkeServiceConfig, currentListeners map[string]*MixedListener, defaultDomain string) error {
	l4RuleConfigMap := ToL4RuleConfigMap(tkeServiceConfig)
	l4ListenerConfigs := make([]*tkeserviceapi.L4ListenerConfig, 0)
	for key, serviceListener := range currentListeners {
		if net.IsL4Protocol(serviceListener.GetProtocol()) {
			l4ListenerConfigs = append(l4ListenerConfigs, buildL4ListenerConfig(l4RuleConfigMap[key], serviceListener.Listener))
		}
	}
	tkeServiceConfig.Spec.LoadBalancer.L4Listeners = l4ListenerConfigs

	l7ListenerConfigMap := ToL7ListenerConfigMap(tkeServiceConfig)

	l7RuleConfigMap := ToL7RuleConfigMap(tkeServiceConfig, defaultDomain)
	l7ListenerConfigs := make([]*tkeserviceapi.L7ListenerConfig, 0)
	for _, serviceListener := range currentListeners {
		if net.IsL7Protocol(serviceListener.GetProtocol()) {
			ruleOutputMap := ToRuleOutputMap(serviceListener.Listener)
			l7ListenerConfigs = append(l7ListenerConfigs, buildL7ListenerConfig(serviceListener, l7ListenerConfigMap, l7RuleConfigMap, ruleOutputMap))
		}
	}
	tkeServiceConfig.Spec.LoadBalancer.L7Listeners = l7ListenerConfigs

	if _, err := cluster.Instance.TkeServiceConfigClient().CloudV1alpha1().TkeServiceConfigs(tkeServiceConfig.Namespace).Update(context.Background(), tkeServiceConfig, metav1.UpdateOptions{}); err != nil {
		return err
	}
	return nil
}

func ToL7ListenerConfigMap(tkeServiceConfig *tkeserviceapi.TkeServiceConfig) map[string]*tkeserviceapi.L7ListenerConfig {
	configsKeyMap := make(map[string]*tkeserviceapi.L7ListenerConfig)
	for index, l7Listener := range tkeServiceConfig.Spec.LoadBalancer.L7Listeners {
		key := getListenerKey(int64(l7Listener.Port), strings.ToUpper(l7Listener.Protocol))
		configsKeyMap[key] = tkeServiceConfig.Spec.LoadBalancer.L7Listeners[index]
	}
	return configsKeyMap
}

func ToL7RuleConfigMap(tkeServiceConfig *tkeserviceapi.TkeServiceConfig, defaultDomain string) map[string]*tkeserviceapi.L7RuleConfig {
	configsKeyMap := make(map[string]*tkeserviceapi.L7RuleConfig)
	for _, l7Listener := range tkeServiceConfig.Spec.LoadBalancer.L7Listeners {
		for _, domain := range l7Listener.Domains {
			for index, rule := range domain.Rules {
				ruleDomain := defaultDomain
				if domain.Domain != "" {
					ruleDomain = domain.Domain
				}
				key := getL7RuleKey(int64(l7Listener.Port), strings.ToUpper(l7Listener.Protocol), ruleDomain, rule.Url)
				configsKeyMap[key] = domain.Rules[index]
			}
		}
	}
	return configsKeyMap
}

func ToL4RuleConfigMap(tkeServiceConfig *tkeserviceapi.TkeServiceConfig) map[string]*tkeserviceapi.L4ListenerConfig {
	configsKeyMap := make(map[string]*tkeserviceapi.L4ListenerConfig)
	for index, l4Listener := range tkeServiceConfig.Spec.LoadBalancer.L4Listeners {
		listenerKey := getListenerKey(int64(l4Listener.Port), l4Listener.Protocol)
		configsKeyMap[listenerKey] = tkeServiceConfig.Spec.LoadBalancer.L4Listeners[index]
	}
	return configsKeyMap
}

// 将用户定义的 Ingress Rules 配置映射到 TkeServiceConfig
//
// 1. 优先映射当前 TkeServiceConfig 中已经存在的配置内容。
// 2. 不存在时映射当前负载均衡上的实际配置。
func buildL4ListenerConfig(l4ListenerConfig *tkeserviceapi.L4ListenerConfig, listener *clb.Listener) *tkeserviceapi.L4ListenerConfig {
	var httpL4ListenerConfig *tkeserviceapi.L4ListenerConfig
	if l4ListenerConfig != nil {
		httpL4ListenerConfig = l4ListenerConfig
	} else {
		httpL4ListenerConfig = convertRuleOutputToL4ListenerConfig(listener)
	}
	return httpL4ListenerConfig
}

func convertRuleOutputToL4ListenerConfig(listener *clb.Listener) *tkeserviceapi.L4ListenerConfig {
	session := &tkeserviceapi.SessionConfig{
		Enable: false,
	}
	if listener.SessionExpireTime != nil && *listener.SessionExpireTime != 0 {
		session = &tkeserviceapi.SessionConfig{
			Enable:            true,
			SessionExpireTime: pointer.Int32Ptr(int32(*listener.SessionExpireTime)),
		}
	}

	healthCheck := &tkeserviceapi.L4HealthCheck{
		Enable: false,
	}
	if listener.HealthCheck != nil {
		if listener.HealthCheck.HealthSwitch != nil && *listener.HealthCheck.HealthSwitch == 1 {
			healthCheck.Enable = true
		}
		if healthCheck.Enable {
			if listener.HealthCheck.IntervalTime != nil {
				healthCheck.IntervalTime = pointer.Int32Ptr(int32(*listener.HealthCheck.IntervalTime))
			}
			if listener.HealthCheck.HealthNum != nil {
				healthCheck.HealthNum = pointer.Int32Ptr(int32(*listener.HealthCheck.HealthNum))
			}
			if listener.HealthCheck.UnHealthNum != nil {
				healthCheck.UnHealthNum = pointer.Int32Ptr(int32(*listener.HealthCheck.UnHealthNum))
			}
			if listener.HealthCheck.TimeOut != nil {
				healthCheck.Timeout = pointer.Int32Ptr(int32(*listener.HealthCheck.TimeOut))
			}
			if listener.HealthCheck.CheckPort != nil {
				healthCheck.CheckPort = pointer.Int32Ptr(int32(*listener.HealthCheck.CheckPort))
			}
			if listener.HealthCheck.CheckType != nil {
				healthCheck.CheckType = common.StringPtr(*listener.HealthCheck.CheckType)
			}
			if listener.HealthCheck.HttpCode != nil {
				healthCheck.HttpCode = pointer.Int32Ptr(int32(*listener.HealthCheck.HttpCode))
			}
			if listener.HealthCheck.HttpCheckMethod != nil {
				healthCheck.HttpCheckMethod = common.StringPtr(*listener.HealthCheck.HttpCheckMethod)
			}
			if listener.HealthCheck.HttpCheckDomain != nil {
				healthCheck.HttpCheckDomain = common.StringPtr(*listener.HealthCheck.HttpCheckDomain)
			}
			if listener.HealthCheck.HttpCheckPath != nil {
				healthCheck.HttpCheckPath = common.StringPtr(*listener.HealthCheck.HttpCheckPath)
			}
			if listener.HealthCheck.HttpVersion != nil {
				healthCheck.HttpVersion = common.StringPtr(*listener.HealthCheck.HttpVersion)
			}
			if listener.HealthCheck.SourceIpType != nil {
				healthCheck.SourceIpType = pointer.Int32Ptr(int32(*listener.HealthCheck.SourceIpType))
			}
		}
	}

	var deregisterTargetRst *bool = nil
	if *listener.Protocol == net.ProtocolTCP && listener.DeregisterTargetRst != nil {
		deregisterTargetRst = listener.DeregisterTargetRst
	}

	return &tkeserviceapi.L4ListenerConfig{
		Protocol:            *listener.Protocol,
		Port:                int32(*listener.Port),
		Scheduler:           listener.Scheduler,
		DeregisterTargetRst: deregisterTargetRst,
		Session:             session,
		HealthCheck:         healthCheck,
		ProxyProtocol: &tkeserviceapi.ProxyProtocolConfig{
			Enable: IsProxyProtocolEnabled(listener),
		},
	}
}

// 将用户定义的 Ingress Rules 配置映射到 TkeServiceConfig
//
// 1. 优先映射当前 TkeServiceConfig 中已经存在的配置内容。
// 2. 不存在时映射当前负载均衡上的实际配置。
// 3. 两者都不存在的情况理论上不存在，实际处理忽略这个配置。
func buildL7ListenerConfig(listener *MixedListener, l7ListenerConfigMap map[string]*tkeserviceapi.L7ListenerConfig, l7RuleConfigMap map[string]*tkeserviceapi.L7RuleConfig, ruleOutputMap map[string]*clb.RuleOutput) *tkeserviceapi.L7ListenerConfig {
	httpL7ListenerConfig := &tkeserviceapi.L7ListenerConfig{
		Protocol:        listener.GetProtocol(),
		Port:            int32(listener.GetListenerPort()),
		Domains:         []*tkeserviceapi.L7DomainConfig{},
		KeepaliveEnable: pointer.Int32Ptr(int32(listener.GetKeepaliveEnable())),
	}

	listenerKey := getListenerKey(int64(httpL7ListenerConfig.Port), httpL7ListenerConfig.Protocol)
	if config, exist := l7ListenerConfigMap[listenerKey]; exist {
		httpL7ListenerConfig = config
	}

	for key, rule := range ruleOutputMap {
		_, _, host, path := parseL7RuleKey(key)
		domain := types.NewCLBListener(listener.Listener).GetDomain(host)
		if config, exist := l7RuleConfigMap[key]; exist {
			insertL7RuleConfig(httpL7ListenerConfig, *domain, path, config)
		} else {
			if *rule.DefaultServer {
				httpL7ListenerConfig.DefaultServer = common.StringPtr(host)
			}
			insertRuleOutput(httpL7ListenerConfig, *domain, path, rule)
		}
	}
	return httpL7ListenerConfig
}

func insertL7RuleConfig(l7ListenerConfigs *tkeserviceapi.L7ListenerConfig, domain types.Domain, path string, l7RuleConfig *tkeserviceapi.L7RuleConfig) {
	l7DomainConfig := getOrCreateL7DomainConfig(l7ListenerConfigs, domain)
	getOrCreateL7RuleConfig(l7DomainConfig, path, l7RuleConfig)
}

func insertRuleOutput(l7ListenerConfigs *tkeserviceapi.L7ListenerConfig, domain types.Domain, path string, ruleOutput *clb.RuleOutput) {
	l7RuleConfig := convertRuleOutputToL7RuleConfig(ruleOutput)
	insertL7RuleConfig(l7ListenerConfigs, domain, path, l7RuleConfig)
}

func convertRuleOutputToL7RuleConfig(output *clb.RuleOutput) *tkeserviceapi.L7RuleConfig {
	session := &tkeserviceapi.SessionConfig{
		Enable: false,
	}
	if output.SessionExpireTime != nil && *output.SessionExpireTime != 0 {
		session = &tkeserviceapi.SessionConfig{
			Enable:            true,
			SessionExpireTime: pointer.Int32Ptr(int32(*output.SessionExpireTime)),
		}
	}

	healthCheck := &tkeserviceapi.L7HealthCheck{}
	if output.HealthCheck != nil {
		hc := output.HealthCheck
		if lo.FromPtr(hc.HealthSwitch) == 1 {
			conv.ConvertInto(*hc, healthCheck)
		}
	}

	return &tkeserviceapi.L7RuleConfig{
		Url:         *output.Url,
		Scheduler:   output.Scheduler,
		ForwardType: output.ForwardType,
		Session:     session,
		HealthCheck: healthCheck,
	}
}

func getOrCreateL7DomainConfig(l7ListenerConfigs *tkeserviceapi.L7ListenerConfig, domain types.Domain) *tkeserviceapi.L7DomainConfig {
	for index, cfg := range l7ListenerConfigs.Domains {
		if cfg.Domain == domain.Host {
			return l7ListenerConfigs.Domains[index]
		}
	}
	domainConfig := &tkeserviceapi.L7DomainConfig{
		Domain: domain.Host,
		Http2:  domain.HTTP2Enabled,
		Rules:  []*tkeserviceapi.L7RuleConfig{},
	}
	l7ListenerConfigs.Domains = append(l7ListenerConfigs.Domains, domainConfig)
	return domainConfig
}

func getOrCreateL7RuleConfig(l7DomainConfig *tkeserviceapi.L7DomainConfig, path string, config *tkeserviceapi.L7RuleConfig) {
	for index, rule := range l7DomainConfig.Rules {
		if rule.Url == path {
			l7DomainConfig.Rules[index] = config
			return
		}
	}
	l7DomainConfig.Rules = append(l7DomainConfig.Rules, config)
}

func ToRuleOutputMap(listener *clb.Listener) map[string]*clb.RuleOutput {
	ruleOutputMap := make(map[string]*clb.RuleOutput)
	for index, rule := range listener.Rules {
		ruleOutputMap[getL7RuleKey(*listener.Port, *listener.Protocol, *rule.Domain, *rule.Url)] = listener.Rules[index]
	}
	return ruleOutputMap
}
