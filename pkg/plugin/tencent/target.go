package tencent

import (
	"context"
	goerrors "errors"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"

	gclone "github.com/huandu/go-clone/generic"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/sets"
	glog "k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/domain/event"
	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/service/annotation"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	cluster2 "git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/net"
	"git.woa.com/kateway/pkg/tencent/cloudctx"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	service2 "cloud.tencent.com/lb-controller/pkg/service"
	"cloud.tencent.com/lb-controller/pkg/service/cluster/nodestatus"
	"cloud.tencent.com/lb-controller/pkg/utils"
	"cloud.tencent.com/lb-controller/pkg/utils/cluster_service"
)

// EnsureTargets 负责绑定或解绑service对应的后端
func EnsureTargets(syncContext *SyncContext) error {
	service := syncContext.Service
	newService := service.Service()
	region := syncContext.LoadBalancerContext.Region
	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	desiredListeners := syncContext.ServiceContext.ServiceListeners
	lb := types.NewLB(loadBalancer, syncContext.LoadBalancerContext.LoadBalancerResource.Spec.Created)
	if lb.IsClassicType() {
		lbId := *loadBalancer.LoadBalancerId
		targets, err := GetDesiredClassicTargets(service, lbId)
		if err != nil {
			return err
		}
		return EnsureClassicLoadBalancerTargets(service, region, loadBalancer, desiredListeners, targets)
	}

	currentListeners, err := GetCurrentTKEServiceListeners(syncContext.LoadBalancerContext)
	if err != nil {
		return err
	}

	// list得到selector选中的pods
	originPods, err := cluster.Instance.Pods(newService.Namespace, newService.Spec.Selector)
	if err != nil {
		return fmt.Errorf("list %q pods error: %w", newService, err)
	}

	pods := utils.FilterPodsByBackendType(originPods, lb.BackendType())

	backendInstanceMap := make(map[string]*types.RuleTarget)
	if newService.IsDirectAccess() {
		// 直绑不支持No Selector Service
		if newService.IsNoSelector() {
			backendInstanceMap, err = getEndpointsInstanceMap(syncContext, desiredListeners)
			if err != nil {
				return err
			}
		} else {
			// 原pod存在ip，但按lb后端类型过滤后，不存在可用pod时，报错提醒可能配置绑定后段类型错误
			if len(originPods) != 0 && len(pods) == 0 {
				if lo.ContainsBy(originPods, func(pod *v1.Pod) bool {
					return pod.Status.PodIP != ""
				}) {
					return types.NewError(errcode.AddressIPVersionError, "", newService)
				}
			}
			err, backendInstanceMap = getDirectTargets(service, lb, desiredListeners, pods)
			if err != nil {
				return err
			}
		}
	} else {
		err, backendInstanceMap = getIndirectTargets(service, desiredListeners, pods, lb)
		if err != nil {
			return err
		}
		// node port 模式下，检查是否存在单点风险
		for _, backendInstance := range backendInstanceMap {
			if len(backendInstance.Targets) == 1 &&
				(backendInstance.Targets[0].BackendType == types.CVM || backendInstance.Targets[0].BackendType == types.EVM) { // 普通节点或者原生节点, 排除掉 eks pod 单点
				event.Instance.EventError(service, types.NewError(errcode.SingleNodeRisk, "", utils.ServiceName(service)))
				break
			}
		}
	}
	if err := EnsureLoadBalancerTargets(syncContext, service, region, loadBalancer, currentListeners, desiredListeners, backendInstanceMap); err != nil {
		return err
	}
	if newService.IsDirectAccess() && newService.IsNoSelector() {
		// sync endpoints src.subsets -> dst.subsets
		if err := copyEndpoints(service); err != nil {
			return err
		}
	}
	return nil
}

func copyEndpoints(sw service_wrapper.ServiceWrapper) error {
	namespace := sw.Namespace()
	name := sw.Name()
	if namespace != metav1.NamespaceDefault {
		glog.V(5).Infof("service [%s/%s] copy endpoints is not default namespace, skip", namespace, name)
		return nil
	}
	if !strings.HasPrefix(name, "kubernetes-") {
		glog.V(5).Infof("service [%s/%s] copy endpoints is not kubernetes-*, skip", namespace, name)
		return nil
	}
	if !utils.IsLoadBalancerType(sw) {
		// 非LoadBalancer类型的service，不做任何处理
		glog.V(5).Infof("service [%s/%s] is not LoadBalancer type, skip", namespace, name)
		return nil
	}
	if !sw.Service().IsNoSelector() {
		// 具有selector选择器的service，不做任何处理
		glog.V(5).Infof("service [%s/%s] has selector, skip", namespace, name)
		return nil
	}
	endpointsBinding, err := sw.Service().LoadbalancerSourceEndpoints()
	if err != nil {
		if goerrors.Is(err, annotation.ErrAnnoKeyNotFound) {
			// 不存在 loadbalancer-source-endpoints 注解，则不做任何处理，直接返回
			glog.V(5).Infof("service [%s/%s] does not have loadbalancer-source-endpoints annotation, skip", namespace, name)
			return nil
		}
		return types.NewError(errcode.EndpointsAnnotationError, err.Error(), utils.ServiceName(sw))
	}
	// 处理endpointsBinding结构体
	if endpointsBinding.Name == "" {
		// 注解中没有指定endpoints名称，则不做任何处理，直接返回
		glog.V(5).Infof("service [%s/%s] does not specify endpoints name, skip", namespace, name)
		return nil
	}
	if endpointsBinding.Sync == nil || !*endpointsBinding.Sync {
		// service注解没有打开 sync 功能，则不做任何处理，直接返回
		glog.V(5).Infof("service [%s/%s] does not open sync feature, skip", namespace, name)
		return nil
	}
	if endpointsBinding.Name == name {
		// 注解指定的endpoints跟service同名，则不做任何处理，直接返回
		glog.V(5).Infof("service [%s/%s] annotation endpoints name is same as service name, skip", namespace, name)
		return nil
	}
	if !strings.HasPrefix(endpointsBinding.Name, "kubernetes") {
		return types.NewError(errcode.EndpointsAnnotationError, "The referenced endpoints name must start with 'kubernetes'", utils.ServiceName(sw))
	}
	ctx := context.Background()
	// 开始同步逻辑
	srcEndpoints, err := cluster2.Instance.EndpointsLister().Endpoints(namespace).Get(endpointsBinding.Name)
	if err != nil {
		return types.NewError(errcode.EndpointsOperationError, err.Error(), utils.ServiceName(sw))
	}
	dstEndpoints, err := cluster2.Instance.EndpointsLister().Endpoints(namespace).Get(name)
	if err != nil {
		if !k8serrors.IsNotFound(err) {
			return types.NewError(errcode.EndpointsOperationError, err.Error(), utils.ServiceName(sw))
		}
		// 如果dstEndpoints不存在，则创建一个
		// 创建一个跟srcEndpoints一样的endpoints
		newEndpoints := &v1.Endpoints{
			// 去掉UID等信息
			ObjectMeta: metav1.ObjectMeta{
				Namespace: sw.Namespace(),
				Name:      sw.Name(),
			},
			Subsets: gclone.Clone(srcEndpoints.Subsets),
		}
		_, err = cluster2.Instance.KubeClient().CoreV1().Endpoints(namespace).Create(ctx, newEndpoints, metav1.CreateOptions{})
		if err != nil {
			return types.NewError(errcode.EndpointsOperationError, err.Error(), utils.ServiceName(sw))
		}
		return nil
	}
	if reflect.DeepEqual(srcEndpoints.Subsets, dstEndpoints.Subsets) {
		// todo 复杂切片比较算法
		return nil
	}
	// 深拷贝，避免修改原始对象
	newEndpoints := dstEndpoints.DeepCopy()
	// 如果dstEndpoints存在, 且srcEps.Subsets != dstEps.Subsets, 则 srcEps.Subsets ->  dstEps.Subsets
	newEndpoints.Subsets = gclone.Clone(srcEndpoints.Subsets)
	_, err = cluster2.Instance.KubeClient().CoreV1().Endpoints(namespace).Update(ctx, newEndpoints, metav1.UpdateOptions{})
	if err != nil {
		return types.NewError(errcode.EndpointsOperationError, err.Error(), utils.ServiceName(sw))
	}
	// 结束
	return nil
}

func getEndpointsInstanceMap(syncContext *SyncContext, desiredListeners map[string]*ServiceListener) (map[string]*types.RuleTarget, error) {
	serviceWrapper := syncContext.Service
	// 获取待绑定作为后端RS的endpoints
	endpointsName, err := fetchEndpoints(serviceWrapper)
	if err != nil {
		return nil, err
	}
	endpoints, err := cluster.Instance.EndpointsLister().Endpoints(serviceWrapper.GetObjectMeta().GetNamespace()).Get(endpointsName)
	if err != nil {
		return nil, err
	}
	// 从endpoints获取待绑定的后端RS列表
	backendInstanceMap, err := convertEndpointsToTargets(serviceWrapper, endpoints, desiredListeners)
	if err != nil {
		return nil, err
	}
	for listenerKey, target := range backendInstanceMap {
		if target == nil {
			glog.Warningf("Listener[%s] conversion result is empty, please check port configuration", listenerKey)
			continue
		}
		// 全死全活
		if err := checkAllDownEndpoints(serviceWrapper, target); err != nil {
			return nil, fmt.Errorf("weight validation failed: %w", err)
		}
	}
	return backendInstanceMap, nil
}

// fetchEndpoints 统一处理Endpoints获取逻辑
func fetchEndpoints(serviceWrapper service_wrapper.ServiceWrapper) (string, error) {
	svc := serviceWrapper.Service()
	namespace := svc.GetNamespace()
	name := svc.GetName()

	// 处理注解绑定的Endpoints
	endpointsBinding, err := svc.LoadbalancerSourceEndpoints()
	if err != nil {
		// 非预期的注解解析错误直接返回
		if !goerrors.Is(err, annotation.ErrAnnoKeyNotFound) {
			return "", types.NewError(errcode.EndpointsAnnotationError, err.Error(), utils.ServiceName(serviceWrapper))
		}
		// 默认获取同名Endpoints
		return name, nil
	}

	// 校验注解指定的Endpoints合法性
	if namespace != metav1.NamespaceDefault || !strings.HasPrefix(name, "kubernetes") {
		return "", types.NewError(errcode.EndpointsAnnotationError,
			"annotation is only open to specific services", utils.ServiceName(serviceWrapper))
	}
	if endpointsBinding.Name == "" {
		return "", types.NewError(errcode.EndpointsAnnotationError,
			"annotation endpoints name is empty", utils.ServiceName(serviceWrapper))
	}
	if !strings.HasPrefix(endpointsBinding.Name, "kubernetes") {
		return "", types.NewError(errcode.EndpointsAnnotationError,
			fmt.Sprintf("endpoints '%s' does not meet requirements: name must start with 'kubernetes'",
				endpointsBinding.Name), utils.ServiceName(serviceWrapper))
	}

	// 获取注解指定的Endpoints
	return endpointsBinding.Name, nil
}

// 将 Kubernetes Endpoints 转换为负载均衡器Targets集合
func convertEndpointsToTargets(sw service_wrapper.ServiceWrapper, endpoints *v1.Endpoints, listeners map[string]*ServiceListener) (map[string]*types.RuleTarget, error) {
	targets := make(map[string]*types.RuleTarget)

	for key, listener := range listeners {
		if listener.Name == "" {
			return nil, types.NewError(errcode.NoSelectorService, "servicespec.ports.name must be set", utils.ServiceName(sw))
		}
		keyTargets := make([]*types.Target, 0)
		endpointNotAdd := make(map[string]bool)
		targets[key] = &types.RuleTarget{
			Port:            listener.Port,
			Protocol:        listener.Protocol,
			ExcludedTargets: endpointNotAdd,
		}
		// 通过endpoints绑定，service.spec.servicePort必须是数字，否则会导致端口不匹配
		if listener.TargetPort.Type != intstr.Int {
			return nil, types.NewError(errcode.NoSelectorService, "spec.ports.targetPort must be int", utils.ServiceName(sw))
		}
		// 校验目标端口有效性（1-65535）
		targetPort := listener.TargetPort.IntVal
		for _, subset := range endpoints.Subsets {
			for _, port := range subset.Ports {
				if listener.Name != port.Name || string(port.Protocol) != listener.Protocol || targetPort != port.Port {
					// 校验端口（如果协议不一致，跳过）
					continue
				}
				for _, addr := range subset.Addresses {
					target := &types.Target{
						Target:      addr.IP,
						Port:        port.Port,           // 后端端口 targetPort
						Weight:      lo.ToPtr[int64](10), // ready address配置权重 10
						BackendType: types.ENI,           // ENI
						Direct:      true,
					}
					keyTargets = append(keyTargets, target)
				}
				for _, addr := range subset.NotReadyAddresses {
					target := &types.Target{
						Target:      addr.IP,
						Port:        port.Port,
						Weight:      lo.ToPtr[int64](0), // not ready address配置权重 0
						BackendType: types.ENI,          // ENI
						Direct:      true,
					}
					keyTargets = append(keyTargets, target)
				}
			}
		}
		if len(keyTargets) > 0 {
			targets[key].Targets = keyTargets
		}
	}
	return targets, nil
}

func getIndirectTargets(service service_wrapper.ServiceWrapper, desiredListeners map[string]*ServiceListener, pods []*v1.Pod, loadBalancer *types.LB) (error, map[string]*types.RuleTarget) {
	backendInstanceMap := make(map[string]*types.RuleTarget)
	/*
		kateway: 非直连类型的service也有可能绑定Pod ENI IP
		1、 TKE弹EKS，负载pod全部都在EKS节点上
		2、 部分负载在TKE节点，部分在EKS，且service的externalTrafficPolicy是Local（当externalTrafficPolicy是Local时，CLB后端必须绑定EKS Pod的ENI IP，否则EKS Pod不会接收到任何流量）
	*/
	backendInstanceMapNodePort, err := getBackendInstanceMapNodePort(service, loadBalancer, desiredListeners, pods)
	if err != nil {
		return err, nil
	}
	for key, value := range backendInstanceMapNodePort {
		backendInstanceMap[key] = value
	}

	// NoSelector Service 不能直连，无法BackUp对接直连后端
	if !utils.IsNoSelectorService(service) { // kateway todo NoSelector 逻辑?
		// For noDirectService, choose PODs as LB RS when there is no Nodes.

		backendInstanceMapEksENI, _ := getBackendInstanceMapENI(service, loadBalancer.BackendType(), desiredListeners, pods) // kateway 直连的pod， 包括 GR 开启直连的pod
		// 判定后端正确权重
		// 兜底场景下，即使用户未声明直连也认为直连开启。
		for _, backendInstance := range backendInstanceMapEksENI {
			if err := makeWeightForENI(service, backendInstance); err != nil {
				return err, nil
			}
		}

		// 筛选纯EKS Pod
		for _, backendInstance := range backendInstanceMapEksENI { // kateway 经过处理后，backendInstanceMapEksENI 里只有 eks 的pod
			targets := make([]*types.Target, 0)
			for index, target := range backendInstance.Targets {
				node, err := cluster.Instance.NodeLister().Get(target.Pod.Spec.NodeName)
				if err != nil {
					glog.Errorf("%s failed to get node of pod(%s), for %s", utils.ServiceName(service), target.Target, err)
					continue
				}
				if types.NewNode(node).IsEKS() {
					targets = append(targets, backendInstance.Targets[index]) // kateway 筛选出在 eks node 上的pod
				}
			}
			backendInstance.Targets = targets // kateway 这里用eks 上的pods 来覆盖，这样就去掉了非 eks pods
		}

		for key, value := range backendInstanceMapEksENI {
			if value == nil || value.Targets == nil || len(value.Targets) == 0 { // 降级也没有合适后端就不要降级了。
				continue
			}

			nodeports, exist := backendInstanceMapNodePort[key]
			if !exist || len(nodeports.Targets) == 0 { // kateway 如果正常 node port 的rs没有，则用 eks pod 来兜底
				// 兜底逻辑，NodePort全部丢失，退化成EKS模式接入
				backendInstanceMap[key] = value
			} else {
				if service.Service().ExternalTrafficPolicyIsLocal() {
					// Service Local场景，TKE集群绑定添加EKS Pod作为后端。
					backendInstanceMap[key].Targets = append(backendInstanceMap[key].Targets, value.Targets...)
					for k, v := range value.ExcludedTargets {
						backendInstanceMap[key].ExcludedTargets[k] = v
					}
				}
			}
		}
	}

	for _, backendInstance := range backendInstanceMap {
		if err := checkAllDown(service, backendInstance); err != nil {
			return err, nil
		}
		// 百分比权重优先级最高
		customizedWeights, customizedAnnoErr := utils.IsServiceEnableCustomizedWeight(service)
		if customizedAnnoErr != nil {
			event.Instance.EventError(service, types.NewError(errcode.CustomizedWeightAnnotationError, "", utils.ServiceName(service)))
		} else {
			if customizedWeights != nil {
				for _, target := range backendInstance.Targets {
					services.ModifyWeightByZones(target.Pod, target.Weight, customizedWeights.Zones)
				}
			}
		}
	}

	return nil, backendInstanceMap
}

func getDirectTargets(service service_wrapper.ServiceWrapper, loadBalancer *types.LB, desiredListeners map[string]*ServiceListener, pods []*v1.Pod) (error, map[string]*types.RuleTarget) {
	backendInstanceMap := make(map[string]*types.RuleTarget)

	backendInstanceMapENI, err := getBackendInstanceMapENI(service, loadBalancer.BackendType(), desiredListeners, pods) // kateway 监听器key=》target 列表
	if err != nil {
		return err, nil
	}

	// 判定后端正确权重
	for _, backendInstance := range backendInstanceMapENI {
		if err := makeWeightForENI(service, backendInstance); err != nil {
			return err, nil
		}
		if err := checkAllDown(service, backendInstance); err != nil {
			return err, nil
		}

		// 百分比权重优先级最高
		customizedWeights, customizedAnnoErr := utils.IsServiceEnableCustomizedWeight(service)
		if customizedAnnoErr != nil {
			event.Instance.EventError(service, types.NewError(errcode.CustomizedWeightAnnotationError, "", utils.ServiceName(service)))
		} else {
			if customizedWeights != nil {
				for _, target := range backendInstance.Targets {
					services.ModifyWeightByZones(target.Pod, target.Weight, customizedWeights.Zones)
				}
			}
		}
	}
	for key, value := range backendInstanceMapENI {
		backendInstanceMap[key] = value
	}

	return nil, backendInstanceMap
}

// todo 重命名？ getDirectPodip？
// kateway: 从期望监听器+当前pods中 获取 监听器key=> pod eni ip 的map, 只会拿直连的pod（包括GR开启直连的pod） key 是监听器，v 是 rs 列表， todo 这里的ip可能不一定是eni
func getBackendInstanceMapENI(service service_wrapper.ServiceWrapper, backendType string, listeners map[string]*ServiceListener, pods []*v1.Pod) (map[string]*types.RuleTarget, error) {
	backendInstanceMap := make(map[string]*types.RuleTarget) // Service to InstanceId

	namePortMap := make(map[string]map[string]int32)
	for _, pod := range pods {
		if namePortMap[pod.Name] == nil {
			namePortMap[pod.Name] = make(map[string]int32)
		}
		for _, container := range pod.Spec.Containers {
			for _, port := range container.Ports {
				if port.Name != "" {
					namePortMap[pod.Name][port.Name] = port.ContainerPort
				}
			}
		}
	}

	nodeMap, err := cluster.Instance.NodesMap()
	if err != nil {
		return nil, err
	}

	warningNoEndpoint := false
	for key, listener := range listeners {
		listenerBackendType := backendType
		if backendType == "mixed" && net.IsL4Protocol(listener.Protocol) { // 四层监听器不支持混合协议。
			listenerBackendType = "ipv6"
		}

		newTarget := make([]*types.Target, 0) // kateway todo 重命名 newTargets
		endpointNotAdd := make(map[string]bool)
		for _, pod := range pods {
			if utils.IsPodUnableBind(pod) { // ENI IP 还未分配的话，跳过该Pod。
				continue
			}

			backendIp, exist := utils.GetPodBackend(pod, listenerBackendType)
			if !exist { // mixed but use l4 listener.
				continue
			}

			// 目前只有GlobalRoute和ENI两种
			// 1. ENI网络类型的工作负载都支持直连
			// 2. GlobalRoute网络类型的工作负载，目前由用户自行控制白名单
			if !service2.IsPodDirectBackend(pod) {
				if !cluster.Instance.Enabled(featuregates.GlobalRouteDirectAccess) {
					continue
				}
			}

			var targetPort int32 = 0
			if listener.TargetPort.Type == intstr.Int { // 端口类型：数字
				targetPort = listener.TargetPort.IntVal
			} else { // 端口类型：命名端口
				if namePort, exist := namePortMap[pod.Name]; exist {
					if port, exist := namePort[listener.TargetPort.StrVal]; exist {
						targetPort = port
					}
				}
			}
			if targetPort == 0 { // 命名端口场景没有找到对应端口
				// TODO misakazhou 应该增加一个Event告警
				continue
			}

			if pod.DeletionTimestamp != nil {
				endpointNotAdd[fmt.Sprintf("%s:%d", backendIp, targetPort)] = true
			} else if needDelayRegister(service, pod) { // 对于满足优雅注册（等容器探针通过后再绑定为rs）的pod，加入endpointNotAdd, 这部分 pod 在对账时不会新增到 clb， 但也不会主动从 clb 剔除。
				endpointNotAdd[fmt.Sprintf("%s:%d", backendIp, targetPort)] = true
			}

			target := &types.Target{
				Target:      backendIp,
				Port:        targetPort,
				Direct:      true,
				BackendType: types.ENI,
				Pod:         pod,
			}
			if service2.IsHostNetwork(pod) {
				if node, exist := nodeMap[pod.Spec.NodeName]; exist {
					if hostNetworkTarget := BuildHostNetworkTarget(node, pod, targetPort, listenerBackendType); hostNetworkTarget != nil {
						target = hostNetworkTarget
					}
				}
			}
			newTarget = append(newTarget, target)
		}
		if len(newTarget) == 0 && len(pods) != 0 && !env.IsInEKSCluster() { // 无对应ENI后端可用于直绑，跳过这个错误配置，并ErrorCode给用户
			warningNoEndpoint = true
			break
		}
		backendInstanceMap[key] = &types.RuleTarget{
			Port:            listener.Port,
			Protocol:        listener.Protocol,
			Targets:         newTarget,
			ExcludedTargets: endpointNotAdd,
		}
	}
	if warningNoEndpoint {
		return backendInstanceMap, types.NewError(errcode.DirectAccessNoEndpointError, "", utils.ServiceName(service))
	}
	return backendInstanceMap, nil
}

// needDelayRegister pod 是否需要延迟注册 rs
// 优雅注册方案 https://iwiki.woa.com/p/4013004019
// 只允许开启了 ReadinessGate 的直连pod 使用优雅注册能力，原因是只有开启了 ReadinessGate 的pod 才有重入队的兜底逻辑（pod从ContainerNotReady ->ContainerReady 需要触发对账）
func needDelayRegister(service service_wrapper.ServiceWrapper, pod *v1.Pod) bool {
	p := types.NewPod(pod)

	return !utils.GetServiceReadinessGateSkip(service) && !p.IsContainersReadyConditionTrue()
	// !p.IsDirectAccessReadyConditionTrue() // 如果 gate 已经通过，说明 rs 已经挂载到 clb了，不需要考虑延迟绑定
}

func getBackendInstanceMapNodePort(service service_wrapper.ServiceWrapper, loadbalancer *types.LB, listeners map[string]*ServiceListener, pods []*v1.Pod) (map[string]*types.RuleTarget, error) {
	backendInstanceMap := make(map[string]*types.RuleTarget) // Service to InstanceId
	for key, listener := range listeners {
		backendInstanceMap[key] = &types.RuleTarget{
			Port:            listener.Port,
			Protocol:        listener.Protocol,
			Targets:         []*types.Target{},
			ExcludedTargets: map[string]bool{},
		}
	}

	originNodes, err := getAvailableNodes(service)
	if err != nil {
		return nil, err
	}
	// If there are no available nodes for LoadBalancer service, make a EventTypeWarning event for it.
	if len(originNodes) == 0 && !env.IsInEKSCluster() {
		services.UploadMetricsAndEvent(service, types.NewError(errcode.NoAvalibaleNodeForService, "", utils.ServiceName(service)))
		return backendInstanceMap, nil
	}

	serviceIPStack := service.GetServiceIPStack()
	backendType := loadbalancer.BackendType()
	if (backendType == "ipv4" && serviceIPStack == "ipv6") || (backendType == "ipv6" && serviceIPStack == "ipv4") {
		services.UploadMetricsAndEvent(service, types.NewError(errcode.AddressIPVersionError, "", utils.ServiceName(service)))
	}

	nodeInstanceIds, targetIpNotAdd, err := filterNode(originNodes, service, backendType, pods)
	if err != nil {
		return nil, err
	}
	ipv6NodeInstanceIds := nodeInstanceIds
	if backendType == "mixed" {
		ipv6NodeInstanceIds, targetIpNotAdd, err = filterNode(originNodes, service, "ipv6", pods)
		if err != nil {
			return nil, err
		}
	}

	allocateLoadBalancerNodePorts := true
	if service.ServiceType() == service_wrapper.CoreService {
		originService := service.RawService()
		if originService.Spec.AllocateLoadBalancerNodePorts != nil && *originService.Spec.AllocateLoadBalancerNodePorts == false {
			allocateLoadBalancerNodePorts = false
		}
	}

	for key, listener := range listeners {
		instanceIds := nodeInstanceIds
		if backendType == "mixed" && net.IsL4Protocol(listener.Protocol) { // 混绑开启时，四层协议不支持混绑，退化为IPv6后端。
			instanceIds = ipv6NodeInstanceIds
		}

		// AllocateLoadBalancerNodePorts 特性简述
		// 创建时开启，不分配NodePort。但是后续关闭，不影响存量的NodePort。
		// 所以理论上能构造出部分端口有NodePort，部分端口没有NodePort的情况，这个时候需要有能力绑定部分端口。
		// https://kubernetes.io/docs/concepts/services-networking/service/#load-balancer-nodeport-allocation
		if service.ServiceType() == service_wrapper.CoreService && listener.NodePort == 0 && allocateLoadBalancerNodePorts == false {
			if len(originNodes) != 0 && !env.IsInEKSCluster() {
				// 零节点可能退化为纯EKS绑定
				// 零节点又不是EKS的场景，优先报错 NoAvalibaleNodeForService
				services.UploadMetricsAndEvent(service, types.NewError(errcode.ServiceCloseNodePortsError, "", utils.ServiceName(service)))
			}
			continue
		}

		nodeTargets := make([]*types.Target, len(instanceIds))
		for index, nodeInstanceId := range instanceIds {
			nodeTargets[index] = &types.Target{
				Node:        nodeInstanceId.Node,
				Direct:      false,
				BackendType: nodeInstanceId.BackendType,
				Target:      nodeInstanceId.Target,
				Weight:      nodeInstanceId.Weight,
				Port:        listener.NodePort,
			}
		}
		targetNotAdd := make(map[string]bool)
		for targetIp, _ := range targetIpNotAdd {
			targetNotAdd[fmt.Sprintf("%s:%d", targetIp, listener.NodePort)] = true
		}
		backendInstanceMap[key].Targets = nodeTargets
		backendInstanceMap[key].ExcludedTargets = targetNotAdd
	}

	// 1. 如果是跨VPC绑定的场景，NodePort需要通过云联网的能力，通过CVM的IP进行绑定
	// 2. 为了保证在不同地域下，过滤规则不变。需要通过filteredTargets进行转换
	// 3. 注意跨地域绑定模式，只有云联网模式下NodePort需要通过IP绑定
	if loadbalancer.VpcId != nil && *loadbalancer.VpcId != config.Global.VPCID {
		crossType, _ := utils.GetCrossType(service)
		if crossType == types.CrossType2_0 || crossType == types.CrossType1_1 || crossType == types.CrossType1_2 {
			for _, target := range backendInstanceMap {
				for index, ruleTarget := range target.Targets {
					if ruleTarget.BackendType != types.CVM {
						continue
					}
					for _, address := range ruleTarget.Node.Status.Addresses {
						if address.Type == "InternalIP" && address.Address != "" {
							if crossType == types.CrossType2_0 {
								target.Targets[index].BackendType = types.CCN
							} else if crossType == types.CrossType1_1 {
								target.Targets[index].BackendType = types.NAT
							} else if crossType == types.CrossType1_2 {
								target.Targets[index].BackendType = types.PVGW
							}
							if target.ExcludedTargets[fmt.Sprintf("%s:%d", ruleTarget.Target, ruleTarget.Port)] { // TODO: 这个打表在HostNetwork模式下有问题
								target.ExcludedTargets[fmt.Sprintf("%s:%d", address.Address, ruleTarget.Port)] = true
							}
							target.Targets[index].Target = address.Address
							break
						}
					}
				}
			}
		}
	}
	return backendInstanceMap, nil
}

func filterNode(nodes []*v1.Node, service service_wrapper.ServiceWrapper, backendType string, pods []*v1.Pod) ([]*types.Target, map[string]bool, error) {
	filteredTargets, targetNotAdd, _ := filterNodes(service, nodes, backendType, pods)
	// get instance id
	ipv6NodeInstanceIds, err := GetInstanceIDByIP(service, filteredTargets)
	if err != nil {
		return nil, nil, err
	}
	return ipv6NodeInstanceIds, targetNotAdd, nil
}

// kateway: https://iwiki.woa.com/p/4007773518
func makeWeightForENI(service service_wrapper.ServiceWrapper, ruleTarget *types.RuleTarget) error {
	if len(ruleTarget.Targets) != 0 {
		graceCtx, err := GetGraceContextFromService(service)
		if err != nil {
			return err
		}

		customizedWeights, customizedAnnoErr := utils.IsServiceEnableCustomizedWeight(service)
		if customizedAnnoErr != nil {
			event.Instance.EventError(service, types.NewError(errcode.CustomizedWeightAnnotationError, "", utils.ServiceName(service)))
		}

		for _, rs := range ruleTarget.Targets {
			// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
			rs.Weight = common.Int64Ptr(int64(determinePodWeight(service, ruleTarget.Port, ruleTarget.Protocol, rs.Pod, graceCtx, customizedWeights, false))) // 初次计算权重，并不知道是否allDown，所以先用 allDown false 来计算
		}
	}
	return nil
}

func checkAllDown(service service_wrapper.ServiceWrapper, ruleTarget *types.RuleTarget) error {
	if len(ruleTarget.Targets) != 0 {
		graceCtx, _ := GetGraceContextFromService(service)
		customizedWeights, _ := utils.IsServiceEnableCustomizedWeight(service)

		allDown := true
		for _, rs := range ruleTarget.Targets {
			// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
			if *rs.Weight != 0 {
				allDown = false
				break
			}
		}
		if allDown {
			ruleTarget.AllDown = true
			if utils.IsServiceDirectAccess(service) {
				event.Instance.EventError(service, types.NewError(errcode.WeightZeroError, "", utils.ServiceName(service)))
			}
			for _, rs := range ruleTarget.Targets {
				if rs.Direct {
					// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
					rs.Weight = common.Int64Ptr(int64(determinePodWeight(service, ruleTarget.Port, ruleTarget.Protocol, rs.Pod, graceCtx, customizedWeights, true)))
				} else {
					rs.Weight = common.Int64Ptr(int64(determineNodeWeight(customizedWeights, true)))
				}
			}
		}
	}
	return nil
}

func checkAllDownEndpoints(service service_wrapper.ServiceWrapper, ruleTarget *types.RuleTarget) error {
	allDown := true
	for _, rs := range ruleTarget.Targets {
		// 以下找到的是需要更新的Target：CLB已经绑定了该Pod且端口没变，但是权重变了
		if *rs.Weight != 0 {
			allDown = false
			break
		}
	}
	if allDown {
		ruleTarget.AllDown = true
		if utils.IsServiceDirectAccess(service) {
			event.Instance.EventError(service, types.NewError(errcode.WeightZeroError, "", utils.ServiceName(service)))
		}
		// 全死，需要将权重设置为8，放通流量
		for _, rs := range ruleTarget.Targets {
			rs.Weight = common.Int64Ptr(int64(determineEndpointWeight(nil, true)))
		}
	}
	return nil
}

func determineNodeWeight(customizedWeights *utils.CustomizedWeight, allDown bool) int {
	if !allDown { // 权重全部为0的场景下，全死全活的逻辑将不生效，兜底为工作负载开放流量。
		return utils.WeightDefault
	}

	if customizedWeights != nil && customizedWeights.DefaultWeight != nil {
		return *customizedWeights.DefaultWeight
	}

	return utils.WeightDefaultProtect
}

func determineEndpointWeight(customizedWeights *utils.CustomizedWeight, allDown bool) int {
	if !allDown {
		return utils.WeightDefault
	}
	if customizedWeights != nil && customizedWeights.DefaultWeight != nil {
		return *customizedWeights.DefaultWeight
	}
	return utils.WeightDefaultProtect
}

func determinePodWeight(service service_wrapper.ServiceWrapper, port int64, protocol string, pod *v1.Pod, graceCtx *types.GraceContext, customizedWeights *utils.CustomizedWeight, allDown bool) int {
	if !allDown { // 权重全部为0的场景下，全死全活的逻辑将不生效，兜底为工作负载开放流量。
		for _, podIP := range types.NewPod(pod).IPs() {
			if _, podInNotReadyList := graceCtx.TKExEndpoint.NotReadyEndpoint[podIP]; podInNotReadyList {
				return utils.WeightGracefulShutdown
			}
		}
		if pod.DeletionTimestamp != nil {
			return utils.WeightGracefulShutdown
		}
	}

	if customizedWeights != nil {
		if customizedWeight := utils.DeterminePodCustomizedWeight(port, protocol, pod, customizedWeights); customizedWeight != nil {
			return *customizedWeight
		}
		if customizedWeights.DefaultWeight != nil {
			return *customizedWeights.DefaultWeight
		}
	}

	if !allDown {
		// 在Pod Ready情况下，Pod自定义权重优先级最高
		if weight := types.NewPod(pod).Attributes().ServiceAttributes.CustomWeight; weight != nil {
			return *weight
		}

		if !utils.IsServiceDirectAccess(service) && utils.IsExternalTrafficPolicyLocal(service) {
			// 未显式声明直连的兜底场景、Service开启Traffic Local模式、Local权重的能力开启的情况下。
			// 工作负载全部在EKS上时，该逻辑也依然生效。所以可以不需要参考节点的绑定情况来确定权重。
			return utils.WeightDefaultLocal
		}
		return utils.WeightDefault
	} else {
		return utils.WeightDefaultProtect
	}
}

// ensureLoadBalancerTargets ensure targets bind for service and lb
// listeners: 期望的监听器
// ruleTargetMap 监听器=》期望的rs
func EnsureLoadBalancerTargets(sc *SyncContext, svc service_wrapper.ServiceWrapper, region string, lb *clb.LoadBalancer,
	currentListeners map[string]*MixedListener, listeners map[string]*ServiceListener, expectTargetByListenerKey map[string]*types.RuleTarget) error {
	lbId := *lb.LoadBalancerId

	// 当前clb的listener的id
	listenerIds := sets.NewString()
	for _, listener := range currentListeners {
		listenerIds.Insert(listener.GetListenerId())
	}

	// 当前clb的listener的map
	listenerBackends, err := sc.LoadBalancerContext.GetListenersBackend()
	if err != nil {
		return err
	}

	// 构造map，是listener的key(协议和端口)到
	currentListenerTargetsMap := make(map[string]sets.String) // 一个监听器的key(80_TCP) ---> 监听器的下的后端rs的信息
	listenerKeyMap := make(map[string]*Backend)               // 一个监听器的key(80_TCP) ---> 监听器的元信息
	for _, listenerBackend := range listenerBackends {
		if net.IsL4Protocol(*listenerBackend.Protocol) {
			key := getListenerKey(*listenerBackend.Port, *listenerBackend.Protocol) // 一个监听器的key：80_TCP
			if _, exist := listenerKeyMap[key]; !exist {
				listenerKeyMap[key] = NewBackend(listenerBackend)
				currentTargetSets := getTargetKeySets(sc.Service, listenerBackend.Targets)
				currentListenerTargetsMap[key] = currentTargetSets
			}
		} else if net.IsL7Protocol(*listenerBackend.Protocol) {
			for _, rule := range listenerBackend.Rules {
				key := getL7RuleKey(*listenerBackend.Port, *listenerBackend.Protocol, *rule.Domain, *rule.Url)
				if _, exist := listenerKeyMap[key]; !exist {
					listenerKeyMap[key] = NewL7Backend(listenerBackend, *rule.LocationId, rule.Targets)
					currentTargetSets := getTargetKeySets(sc.Service, rule.Targets)
					currentListenerTargetsMap[key] = currentTargetSets
				}
			}
		}
	}

	// kateway: 当前监听器下绑定的rs数量可能已经达到配额上限，因此可能需要先删除一部分rs，然后再进行绑定。
	targetsToAddBeforeDeletion := make([]*clb.BatchTarget, 0)
	targetsToAddBetweenDeletion := make([]*clb.BatchTarget, 0)
	targetsToAddAfterDeletion := make([]*clb.BatchTarget, 0)
	targetsToDelFirstDeletion := make([]*clb.BatchTarget, 0)
	targetsToDelSecondDeletion := make([]*clb.BatchTarget, 0)
	targetsToDelete := make([]*clb.BatchTarget, 0)
	targetsToUpdateWeight := make([]*clb.RsWeightRule, 0)
	backendManagementMode := utils.GetBackendManagementMode(svc)
	rsOnlyMode, _, _ := utils.GetBackendManageOnly(svc)

	for key, listener := range listeners {
		var (
			ruleTarget = expectTargetByListenerKey[key]
			targetByID = lo.SliceToMap(ruleTarget.Targets, func(item *types.Target) (string, *types.Target) {
				return item.Target, item
			})

			// 判断是否是因为隔离或者原地升级导致的全死
			byIsolating, byLocalUpgrading = ruleTarget.FakeAllDown()
		)
		desiredTargetKeys := make(sets.String)
		for _, backend := range ruleTarget.Targets {
			if ruleTarget.Protocol != "" && ruleTarget.Port != 0 &&
				(!strings.EqualFold(ruleTarget.Protocol, listener.Protocol) || ruleTarget.Port != listener.Port) { // kateway todo
				glog.Infof("not equal, need skip: %s, %d, %s, %d", ruleTarget.Protocol, ruleTarget.Port, listener.Protocol, listener.Port)
				continue
			}
			desiredTargetKeys.Insert(getTargetKey(backend))
		}

		keys := []string{key} // kateway： 四层是监听器key，七层还要加上rule
		if net.IsL7Protocol(listener.Protocol) {
			keys = make([]string, 0)
			for domain, rule := range listener.TlsSniMap {
				for rule, _ := range rule.RuleMap {
					keys = append(keys, getL7RuleKey(listener.Port, listener.Protocol, domain, rule))
				}
			}
		}

		for _, key := range keys {
			currentTargetKeys, exist := currentListenerTargetsMap[key]
			if !exist {
				glog.Errorf("service: %s/%s lb: %s listener: %s not exist after ensure", svc.GetObjectMeta().GetNamespace(), svc.GetObjectMeta().GetName(), lbId, key)
				if backendManageOnly, _, _ := utils.GetBackendManageOnly(svc); backendManageOnly {
					sc.Errors = append(sc.Errors, types.NewError(errcode.BackendManageOnlyListenerNotExistError, "", utils.ServiceName(svc), key))
				}
				continue
			}
			// 当前此listen的后端target数量
			listenerTargetsTotal := len(currentListenerTargetsMap[key])

			listenerBackend, keyExist := listenerKeyMap[key]
			if !keyExist {
				continue
			}

			targetKeysToAdd := desiredTargetKeys.Difference(currentTargetKeys)
			targetKeysToDel := currentTargetKeys.Difference(desiredTargetKeys)
			targetKeysToUpdate := desiredTargetKeys.Intersection(currentTargetKeys)

			// batch del targets
			for _, targetKey := range targetKeysToDel.List() {
				delBackendType, delBackend, port, err := parseTargetKey(targetKey)
				if err != nil {
					return err
				}
				if backend, exist := listenerBackend.Targets[targetKey]; exist {
					if backendManagementMode == types.BackendManagementModeTag && !types.ManagedTarget(backend.Tag, config.Global.ClusterName) {
						continue // 开启标签管理，标签不符合预期，不在管理范围
					}
					targetsToDelete = append(targetsToDelete, NewBatchTarget(delBackendType, *listenerBackend.ListenerId, port, delBackend, listenerBackend.LocationId))
				}
			}

			// batch add targets
			targetsToAdd := []*clb.BatchTarget{}
			for _, targetKey := range targetKeysToAdd.List() {
				addBackendType, addBackend, port, err := parseTargetKey(targetKey)
				if err != nil {
					return err
				}
				if _, exist := ruleTarget.ExcludedTargets[fmt.Sprintf("%s:%d", addBackend, port)]; exist { // 特例：明确不挂载的旧版本后端（存在的情况下不主动摘除）
					continue
				}

				batchTarget := NewBatchTarget(addBackendType, *listenerBackend.ListenerId, port, addBackend, listenerBackend.LocationId)
				if backend, exist := targetByID[addBackend]; exist && backend.Weight != nil {
					batchTarget.Weight = common.Int64Ptr(*backend.Weight)
				}
				if backendManagementMode == types.BackendManagementModeTag { // 开启标签管理，标签不符合预期，不在管理范围
					batchTarget.Tag = common.StringPtr(types.BuildTargetTag(config.Global.ClusterName))
				}
				targetsToAdd = append(targetsToAdd, batchTarget)
			}

			quotaInUse := config.Global.BackendQuotaInUse.GetQuotaInUse(lbId)
			processor := types.NewListenerTargetsProcessor(quotaInUse)
			processedAdd, processedDel := processor.ProcessTargets(context.TODO(), listenerTargetsTotal, targetsToDelete, targetsToAdd)
			// 三次绑定的RS切片
			targetsToAddBeforeDeletion = append(targetsToAddBeforeDeletion, processedAdd.TargetsToRegistBeforeFirstDeletion...)
			targetsToAddBetweenDeletion = append(targetsToAddBetweenDeletion, processedAdd.TargetsToRegistBetweenDeletion...)
			targetsToAddAfterDeletion = append(targetsToAddAfterDeletion, processedAdd.TargetsToRegistAfterSecondDeletion...)
			// 两次解绑的RS切片
			targetsToDelFirstDeletion = append(targetsToDelFirstDeletion, processedDel.FirstDeletion...)
			targetsToDelSecondDeletion = append(targetsToDelSecondDeletion, processedDel.SecondDeletion...)

			expectTotal := listenerTargetsTotal - len(targetsToDelete) + len(targetsToAdd)
			if processedAdd.Len() != len(targetsToAdd) {
				if types.CVMTargetsOnly(targetsToAdd) {
					// 对于rs全部为node的形式，如果出现了配额不足的问题，返回一个警告
					sc.Errors = append(sc.Errors, types.NewError(errcode.NodeRSLimitExceeded, "", key, expectTotal, quotaInUse))
				} else {
					sc.Errors = append(sc.Errors, types.NewError(errcode.RSLimitExceeded, "", key, expectTotal, quotaInUse))
				}
			}

			// batch update targets
			for _, targetKey := range targetKeysToUpdate.List() {
				updateBackendType, updateBackend, port, err := parseTargetKey(targetKey)
				if err != nil {
					return err
				}

				if backend, exist := listenerBackend.Targets[targetKey]; exist {
					if target, exist := targetByID[updateBackend]; exist {
						if backendManagementMode == types.BackendManagementModeTag && !types.ManagedTarget(backend.Tag, config.Global.ClusterName) {
							continue // 开启标签管理，标签不符合预期，不在管理范围
						}
						// rs 开启 skip 能力，不调整权重
						if types.NeedSKipTarget(backend.Tag) {
							continue
						}
						if target.Weight != nil {
							// 判断是否是因为隔离或者原地升级导致的全死，在以下情况权重置为 0
							// 1. 子集群 Service + 原地升级导致的全死
							// 2. 单集群或者子集群 Service 隔离导致的全死
							if (byLocalUpgrading && rsOnlyMode) || byIsolating {
								glog.Infof("fake all down rsOnlyMode svc: %s/%s for rule target: %s", svc.GetObjectMeta().GetNamespace(), svc.GetObjectMeta().GetName(), key)
								if *backend.Weight != 0 {
									targetsToUpdateWeight = append(targetsToUpdateWeight, NewRsWeightRule(updateBackendType, *listenerBackend.ListenerId, port, updateBackend, 0, listenerBackend.LocationId))
								}
							} else {
								if *target.Weight != *backend.Weight {
									targetsToUpdateWeight = append(targetsToUpdateWeight, NewRsWeightRule(updateBackendType, *listenerBackend.ListenerId, port, updateBackend, *target.Weight, listenerBackend.LocationId))
								}
							}
						}
					}
				}
			}
		}
	}

	// 所有解绑或绑定成功的targets，可能需要修改相应节点的保护finalizer
	targetsToEnqueue := make([]*clb.BatchTarget, 0)
	var nodesByID map[string]*v1.Node
	if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) && (len(targetsToAddBeforeDeletion) > 0 || len(targetsToAddAfterDeletion) > 0 || len(targetsToDelete) > 0) {
		nodesByID, err = services.GetNodesByID(cluster.Instance.NodeLister())
		if err != nil {
			return err
		}
		for _, expectTarget := range expectTargetByListenerKey {
			for _, target := range expectTarget.Targets {
				if target.Node != nil {
					nodesByID[target.Target] = target.Node
				}
			}
		}
	}

	defer func() {
		cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueTargets(targetsToEnqueue, nodesByID)
	}()
	// 批量绑定、解绑RS（首次绑定 -> 首次解绑 -> 中间绑定 -> 二次解绑 -> 最终绑定）
	// 首次绑定
	tryFirstDeleteBeforeAdd := false
	if len(targetsToAddBeforeDeletion) > 0 {
		/*
		 * TODO H`yzhou Code=InvalidParameter, Message=check eni ip error
		 * 以下场景可能出现以上报错问题。
		 * Controller同步时实例没有被销毁，但是在执行同步的过程中被销毁了。此时CLB会自动摘掉后端，而Controller会尝试Registry这个实例。
		 * 使得Controller调用CLB挂载不正确的ENI，引起以上报错。
		 */
		err := BatchRegisterTargets(svc, region, lbId, targetsToAddBeforeDeletion)
		if err != nil {
			if sdkError := new(errors.TencentCloudSDKError); goerrors.As(err, &sdkError) {
				if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "must be unique") { // [TencentCloudSDKError] Code=InvalidParameterValue, Message=input data error. vip、protocol、rsip、rsport must be unique.
					tryFirstDeleteBeforeAdd = true
				}
			}
			if !tryFirstDeleteBeforeAdd {
				return err
			}
		} else if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, targetsToAddBeforeDeletion...)
		}
	}

	// first batch deregister targets
	if len(targetsToDelFirstDeletion) > 0 {
		if err := BatchDeregisterTargets(svc, region, lbId, targetsToDelFirstDeletion); err != nil {
			return err
		} else if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, targetsToDelFirstDeletion...)
		}
	}

	// regirst targets between batch deregister targets
	targetsToAdd := targetsToAddBetweenDeletion
	// 如果第一次register失败了，就和最后一批合并起来，在second deregister之后进行
	if tryFirstDeleteBeforeAdd {
		targetsToAdd = append(targetsToAdd, targetsToAddBeforeDeletion...)
	}
	trySecondDeleteBeforeAdd := false
	// 两次解绑中间的绑定
	if len(targetsToAdd) > 0 {
		err := BatchRegisterTargets(svc, region, lbId, targetsToAdd)
		if err != nil {
			if sdkError := new(errors.TencentCloudSDKError); goerrors.As(err, &sdkError) {
				if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "must be unique") { // [TencentCloudSDKError] Code=InvalidParameterValue, Message=input data error. vip、protocol、rsip、rsport must be unique.
					trySecondDeleteBeforeAdd = true
				}
			}
			if !trySecondDeleteBeforeAdd {
				return err
			}
		} else if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, targetsToAdd...)
		}
	}
	// 第二次解绑（最后一次）
	if len(targetsToDelSecondDeletion) > 0 {
		if err := BatchDeregisterTargets(svc, region, lbId, targetsToDelSecondDeletion); err != nil {
			return err
		} else if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, targetsToDelSecondDeletion...)
		}
	}
	// 最后的绑定
	// 如果第二次register失败了，就和最后一批合并起来，在second deregister之后进行
	targetsToAddAfterSecondDeletion := targetsToAddAfterDeletion
	if trySecondDeleteBeforeAdd {
		targetsToAddAfterSecondDeletion = append(targetsToAddAfterSecondDeletion, targetsToAdd...)
	}
	if len(targetsToAddAfterSecondDeletion) > 0 {
		err := BatchRegisterTargets(svc, region, lbId, targetsToAddAfterSecondDeletion)
		if err != nil {
			if sdkError := new(errors.TencentCloudSDKError); goerrors.As(err, &sdkError) {
				if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "must be unique") { // [TencentCloudSDKError] Code=InvalidParameterValue, Message=input data error. vip、protocol、rsip、rsport must be unique.
					return types.NewError(errcode.BackendConflictError, sdkError.Error(), utils.ServiceName(svc))
				}
			}
			return err
		} else if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, targetsToAddAfterSecondDeletion...)
		}
	}

	// batch update targets wight
	if len(targetsToUpdateWeight) > 0 {
		if err := BatchModifyTargetWeight(svc, region, lbId, targetsToUpdateWeight); err != nil {
			if sdkError := new(errors.TencentCloudSDKError); goerrors.As(err, &sdkError) {
				if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "not support") { // [TencentCloudSDKError] Code=FailedOperation, Message=not support evm / eks
					if err := ModifyTargetWeight(svc, region, lbId, targetsToUpdateWeight); err != nil {
						return err
					}
					return nil
				}
			}
			return err
		}
	}

	// 如果开启了标签管理，需要更新后端 RS 标签
	if utils.NeedPatchRSTags(svc) {
		glog.Infof("service: %s/%s lb: %s need to patch rs tags", svc.GetObjectMeta().GetNamespace(), svc.GetObjectMeta().GetName(), lbId)
		return BatchModifyTargetTags(svc, region, lbId, listenerIds)
	}

	return nil
}

func BatchModifyTargetTags(service service_wrapper.ServiceWrapper, region string, lbID string, listenerIds sets.String) error {
	listeners, err := GetLoadBalancerTargets(service, region, lbID)
	if err != nil {
		return err
	}

	rsRules := []*clb.RsTagRule{}
	for _, listener := range listeners {
		if listener.ListenerId != nil && listenerIds.Has(*listener.ListenerId) {
			if net.IsL4Protocol(*listener.Protocol) {
				targetList := []*clb.Target{}
				for _, target := range listener.Targets {
					if needToPatch, newTag := ParseTag(target.Tag, config.Global.ClusterName); needToPatch {
						if target.Type == nil || *target.Type == "" {
							return fmt.Errorf("unexpected backend, backend type not exist")
						}
						if utils.IsENILikeType(*target.Type) {
							for _, privateIP := range target.PrivateIpAddresses {
								targetList = append(targetList, &clb.Target{
									EniIp:  privateIP,
									Port:   target.Port,
									Weight: target.Weight,
									Tag:    &newTag,
								})
							}
						} else if utils.IsCVMLikeType(*target.Type) {
							targetList = append(targetList, &clb.Target{
								InstanceId: target.InstanceId,
								Port:       target.Port,
								Weight:     target.Weight,
								Tag:        &newTag,
							})
						}
					}
				}
				if len(targetList) != 0 {
					rsRules = append(rsRules, &clb.RsTagRule{
						ListenerId: listener.ListenerId,
						Targets:    targetList,
					})
				}
			} else if net.IsL7Protocol(*listener.Protocol) {
				for _, rule := range listener.Rules {
					targetList := []*clb.Target{}
					for _, target := range rule.Targets {
						if needToPatch, newTag := ParseTag(target.Tag, config.Global.ClusterName); needToPatch {
							if target.Type == nil || *target.Type == "" {
								return fmt.Errorf("unexpected backend, backend type not exist")
							}
							if utils.IsENILikeType(*target.Type) {
								for _, privateIP := range target.PrivateIpAddresses {
									targetList = append(targetList, &clb.Target{
										EniIp:  privateIP,
										Port:   target.Port,
										Weight: target.Weight,
										Tag:    &newTag,
									})
								}
							} else if utils.IsCVMLikeType(*target.Type) {
								targetList = append(targetList, &clb.Target{
									InstanceId: target.InstanceId,
									Port:       target.Port,
									Weight:     target.Weight,
									Tag:        &newTag,
								})
							}
						}
					}
					if len(targetList) != 0 {
						rsRules = append(rsRules, &clb.RsTagRule{
							LocationId: rule.LocationId,
							ListenerId: listener.ListenerId,
							Targets:    targetList,
						})
					}
				}
			}
		}
	}

	if len(rsRules) != 0 {
		glog.Infof("service: %s/%s lb: %s try to patch rs tags with groups rsRuleTags: %d", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), lbID, len(rsRules))
		rsRuleGroups := SplitRSTagRulesGreedy(rsRules, TEMPMAXBATCHMODIFYREQUEST, MAXBATCHTARGETPERREQUEST)
		for _, rsGroup := range rsRuleGroups {
			modifyTargetTagRequest := clb.NewBatchModifyTargetTagRequest()
			modifyTargetTagRequest.LoadBalancerId = &lbID
			modifyTargetTagRequest.ModifyList = rsGroup

			_, err := tencentapi.Instance.BatchModifyTargetTag(cloudctx.New(service, region), modifyTargetTagRequest)
			if err != nil {
				glog.Errorf("service: %s/%s lb: %s patch rs tags failed with groups rsRuleTags: %d for %s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), lbID, len(rsRules), err.Error())
				return err
			}
		}
		glog.Infof("service: %s/%s lb: %s patch rs tags successfully with groups rsRuleTags: %d", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), lbID, len(rsRules))
	}

	return nil
}

// SplitRSTagRulesGreedy 分割 RsTagRule 为多个组，每组最多 maxListenersPerGroup 个 Listener，每个 Listener 最多 maxBackendsPerListener 个 Targets
func SplitRSTagRulesGreedy(listeners []*clb.RsTagRule, maxListenersPerGroup, maxBackendsPerListener int) [][]*clb.RsTagRule {
	var groups [][]*clb.RsTagRule

	for _, listener := range listeners {
		// 如果 Listener 的 Backends 数量超过限制，分割 Backends
		if len(listener.Targets) > maxBackendsPerListener {
			for i := 0; i < len(listener.Targets); i += maxBackendsPerListener {
				end := i + maxBackendsPerListener
				if end > len(listener.Targets) {
					end = len(listener.Targets)
				}
				// 创建新的 Listener，只包含部分 Backends
				splitListener := &clb.RsTagRule{
					ListenerId: listener.ListenerId,
					Targets:    listener.Targets[i:end],
					Tag:        listener.Tag,
					LocationId: listener.LocationId,
				}
				groups = addToGroup(groups, splitListener, maxListenersPerGroup)
			}
		} else {
			groups = addToGroup(groups, listener, maxListenersPerGroup)
		}
	}

	return groups
}

func ParseTag(input *string, clusterID string) (bool, string) {
	clustertag := fmt.Sprintf("clusterId:%s", clusterID)

	if input == nil || *input == "" {
		return true, clustertag
	}

	pairs := strings.Split(*input, ";")
	clusterIdNotExists := true
	resultPairs := []string{}

	for _, pair := range pairs {
		if pair != "" {
			kv := strings.Split(pair, ":")
			if len(kv) == 2 {
				if kv[0] == "clusterId" {
					clusterIdNotExists = false
				}
				resultPairs = append(resultPairs, pair)
			}
		}
	}

	if clusterIdNotExists {
		resultPairs = append(resultPairs, clustertag)
	}

	return clusterIdNotExists, strings.Join(resultPairs, ";")
}

func addToGroup(groups [][]*clb.RsTagRule, listener *clb.RsTagRule, maxListenersPerGroup int) [][]*clb.RsTagRule {
	added := false
	for i := range groups {
		if len(groups[i]) < maxListenersPerGroup && !listenerIDExistsInGroup(groups[i], *listener.ListenerId) {
			groups[i] = append(groups[i], listener)
			added = true
			break
		}
	}
	if !added {
		groups = append(groups, []*clb.RsTagRule{listener})
	}
	return groups
}

func listenerIDExistsInGroup(group []*clb.RsTagRule, listenerId string) bool {
	for _, listener := range group {
		if listener.ListenerId == &listenerId {
			return true
		}
	}
	return false
}

func enqueueTargetsSet(targetsToEnqueue sets.String, targetsByID map[string]*v1.Node) {
	for target := range targetsToEnqueue {
		if node, exist := targetsByID[target]; exist {
			cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueNode(node)
		}
	}
}

func getTargetKey(backend *types.Target) string {
	if types.BackendIsIP(string(backend.BackendType)) {
		return getTargetKeyForENI(backend.Target, backend.Port)
	} else {
		return getTargetKeyForCVM(backend.Target, backend.Port)
	}
}

func getBackendKey(backend *clb.Backend) []string {
	if types.BackendIsIP(*backend.Type) {
		var backendKeys []string
		for _, privateIp := range backend.PrivateIpAddresses {
			backendKeys = append(backendKeys, getTargetKeyForENI(*privateIp, int32(*backend.Port)))
		}
		return backendKeys
	} else {
		return []string{getTargetKeyForCVM(*backend.InstanceId, int32(*backend.Port))}
	}
}

type Backend struct {
	ListenerId *string
	Protocol   *string
	Port       *int64
	LocationId *string
	Targets    map[string]*clb.Backend
}

func NewBackend(backend *clb.ListenerBackend) *Backend {
	targetMap := make(map[string]*clb.Backend)
	for index, target := range backend.Targets {
		backendKeys := getBackendKey(target)
		for _, backendKey := range backendKeys {
			targetMap[backendKey] = backend.Targets[index]
		}
	}
	return &Backend{
		ListenerId: backend.ListenerId,
		Protocol:   backend.Protocol,
		Port:       backend.Port,
		LocationId: nil,
		Targets:    targetMap,
	}
}

func NewL7Backend(backend *clb.ListenerBackend, locationId string, targets []*clb.Backend) *Backend {
	targetMap := make(map[string]*clb.Backend)
	for index, target := range targets {
		backendKeys := getBackendKey(target)
		for _, backendKey := range backendKeys {
			targetMap[backendKey] = targets[index]
		}
	}
	return &Backend{
		ListenerId: backend.ListenerId,
		Protocol:   backend.Protocol,
		Port:       backend.Port,
		LocationId: common.StringPtr(locationId),
		Targets:    targetMap,
	}
}

// ensureClassicLoadBalancerTargets ensure classic loadbalancer targets
func EnsureClassicLoadBalancerTargets(service service_wrapper.ServiceWrapper, region string, lb *clb.LoadBalancer, listeners map[string]*ServiceListener, nodeTargets []*types.Target) error {
	lbId := *lb.LoadBalancerId
	currentTargets, err := GetClassicalLBTargets(service, region, lbId)
	if err != nil {
		return err
	}
	currentTargetIds := make(sets.String)
	for _, currentTarget := range currentTargets {
		currentTargetIds.Insert(*currentTarget.InstanceId)
	}
	desiredTargetIds := make(sets.String)
	for _, nodeTarget := range nodeTargets {
		desiredTargetIds.Insert(nodeTarget.Target)
	}

	toAddTargetIds := desiredTargetIds.Difference(currentTargetIds)
	toDelTargetIds := currentTargetIds.Difference(desiredTargetIds)
	if toAddTargetIds.Len() != 0 || toDelTargetIds.Len() != 0 {
		glog.Infof("ensureClassicLoadBalancerTargets for svc: %s/%s UUID: %s, lb: %s, toAddTargets: %s, toDelTargets: %s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), string(service.GetObjectMeta().GetUID()), lbId, toAddTargetIds.List(), toDelTargetIds.List())
	}

	toAddSecondTargetIds := make(sets.String)
	quotaInUse := config.Global.BackendQuotaInUse.GetQuotaInUse(lbId)
	if currentTargetIds.Len()+toAddTargetIds.Len() > quotaInUse {
		index := quotaInUse - currentTargetIds.Len()
		list := toAddTargetIds.List()
		toAddTargetIds = make(sets.String)
		toAddTargetIds.Insert(list[0:index]...)
		toAddSecondTargetIds.Insert(list[index:]...)
	}

	// add targets for classic lb
	if toAddTargetIds.Len() > 0 {
		targets := make([]*clb.ClassicalTargetInfo, toAddTargetIds.Len())
		for i, target := range toAddTargetIds.List() {
			instance := target
			// TODO weight
			targets[i] = &clb.ClassicalTargetInfo{
				InstanceId: &instance,
			}
		}
		if err := RegisterClassicLoadBalancerTargets(service, region, lbId, targets); err != nil {
			glog.Errorf("registerClassicLoadBalancerTargets for service: %s/%s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
			return err
		}
	}

	// del targets for classic lb
	if toDelTargetIds.Len() > 0 {
		if err := DeregisterClassicLoadBalancerTargets(service, region, lbId, common.StringPtrs(toDelTargetIds.List())); err != nil {
			glog.Errorf("deregisterClassicLoadBalancerTargets for service: %s/%s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
			return err
		}
	}

	// add targets for classic lb
	if toAddSecondTargetIds.Len() > 0 {
		targets := make([]*clb.ClassicalTargetInfo, toAddSecondTargetIds.Len())
		for i, target := range toAddSecondTargetIds.List() {
			instance := target
			targets[i] = &clb.ClassicalTargetInfo{
				InstanceId: &instance,
			}
		}
		if err := RegisterClassicLoadBalancerTargets(service, region, lbId, targets); err != nil {
			glog.Errorf("registerClassicLoadBalancerTargets for service: %s/%s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
			return err
		}
	}

	return nil
}

func GetDesiredClassicTargets(service service_wrapper.ServiceWrapper, lbId string) ([]*types.Target, error) {
	nodes, err := getAvailableNodes(service)
	if err != nil {
		return nil, err
	}
	// If there are no available nodes for LoadBalancer service, make a EventTypeWarning event for it.
	if len(nodes) == 0 {
		services.UploadMetricsAndEvent(service, types.NewError(errcode.NoAvalibaleNodeForService, "", utils.ServiceName(service)))
	}

	// 传统型负载均衡需要排除云原生节点
	filters := make([]*v1.Node, 0)
	for index, node := range nodes {
		if !types.NewNode(node).IsCXM() {
			filters = append(filters, nodes[index])
		}
	}
	nodes = filters

	filteredTargets, _, err := filterNodes(service, nodes, "ipv4", []*v1.Pod{})
	// filter first
	quotaInUse := config.Global.BackendQuotaInUse.GetQuotaInUse(lbId)
	if quotaInUse > 0 {
		if len(filteredTargets) > quotaInUse {
			sort.Sort(filteredTargets)
			filteredTargets = filteredTargets[0:quotaInUse]
		}
	}

	// get instance id
	nodeInstanceIds, err := GetInstanceIDByIP(service, filteredTargets)
	if err != nil {
		return nil, err
	}
	return nodeInstanceIds, nil
}

func getAvailableNodes(service service_wrapper.ServiceWrapper) ([]*v1.Node, error) {
	predicate := func(node *v1.Node) bool {
		// We add the master to the node list, but its unschedulable.  So we use this to filter
		// the master.
		// if node.Spec.Unschedulable {
		//	return false
		// }
		if node.Labels != nil {
			if value, exist := node.Labels["node.kubernetes.io/exclude-from-external-load-balancers"]; exist && value == "true" {
				return false
			}
		}

		// If we have no info, don't accept
		if len(node.Status.Conditions) == 0 {
			return false
		}

		// If we have node, but no InternalIP
		flag := false
		for _, address := range node.Status.Addresses {
			if address.Type == "InternalIP" && address.Address != "" {
				flag = true
				break
			}
		}
		if !flag {
			return false
		}

		if types.NewNode(node).IsEKS() {
			return false
		}

		if hybridType := utils.GetHybridType(service); hybridType == types.HybridTypeNone { // 非混合云场景，屏蔽混合云节点。
			if types.NewNode(node).IsIDC() {
				return false
			}
		}

		// 这里的目的是推动controller，控制CLB解绑后端CVM
		// 针对CVM实例被提前删除，导致CLB自行解绑CVM，从而对账的时候，controller这边不会调用BatchDeregisteTarget接口的问题
		// 在入队的地方，过滤出符合下面条件的node，入队
		if types.NewNode(node).HasWaitFinalizer() || types.NewNode(node).DeletionTimestamp != nil {
			return false
		}

		for _, cond := range node.Status.Conditions {
			// We consider the node for load balancing only when its NodeReady condition status
			// is ConditionTrue
			if cond.Type == v1.NodeReady && cond.Status != v1.ConditionTrue {
				// 容忍节点抖动
				if _, exist := nodestatus.NodeStatusMapInstance.Get(node.Name); !exist {
					glog.V(4).Infof("Ignoring node %v with %v condition status %v", node.Name, cond.Type, cond.Status)
					return false
				}
			}
		}
		return true
	}

	nodes, err := cluster.Instance.NodeLister().List(labels.Everything())
	if err != nil {
		glog.Errorf("NodeLister List error. error %v", err)
		return nil, err
	}

	var filtered []*v1.Node
	for i := range nodes {
		if predicate(nodes[i]) {
			filtered = append(filtered, nodes[i])
		} else {
			glog.Infof("node %v not available", nodes[i].Name)
		}
	}

	return filtered, nil
}

// getLoadBalancerTargets get lb targets page by pate
func GetLoadBalancerTargets(service service_wrapper.ServiceWrapper, region string, lbID string) ([]*clb.ListenerBackend, error) {
	request := clb.NewDescribeTargetsRequest()
	request.LoadBalancerId = &lbID
	response, err := tencentapi.Instance.DescribeTargets(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}
	return response.Response.Listeners, nil
}

func GetClassicalLBTargets(service service_wrapper.ServiceWrapper, region string, lbID string) ([]*clb.ClassicalTarget, error) {
	request := clb.NewDescribeClassicalLBTargetsRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	response, err := tencentapi.Instance.DescribeClassicalLBTargets(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}

	return response.Response.Targets, nil
}

// batchRegisterTargets batch register targets
func BatchRegisterTargets(service service_wrapper.ServiceWrapper, region string, lbID string, targets []*clb.BatchTarget) error {
	request := clb.NewBatchRegisterTargetsRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	for i := 0; i < len(targets); i += MAXBATCHTARGETPERREQUEST {
		targetGroup := targets[i:utils.Min(i+MAXBATCHTARGETPERREQUEST, len(targets))]
		request.Targets = targetGroup

		response, err := tencentapi.Instance.BatchRegisterTargets(cloudctx.New(service, region), request)
		if err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				if sdkError.Code == "LimitExceeded" && strings.Contains(sdkError.Message, "max instances registed") { // [TencentCloudSDKError] Code=LimitExceeded, Message=Over the limit of max instances registed on one rule
					return types.NewError(errcode.BackendOverLimitError, sdkError.Error(), utils.ServiceName(service))
				} else if sdkError.Code == "InvalidParameter" && strings.Contains(sdkError.Message, "cvm param error") {
					return types.NewError(errcode.NodeInsufficient, sdkError.Error(), utils.ServiceName(service))
				} else if sdkError.Code == "FailedOperation" {
					if strings.Contains(sdkError.Message, "do not support to bind other ip") { // [TencentCloudSDKError] Code=FailedOperation, Message=Account ********** do not support to bind other ip ['**********', '**********'], RequestId=552e0022-3c75-46d8-b26c-9c863e3264a9.
						return types.NewError(errcode.NetworkNotRegisterToCNNError, sdkError.Error(), utils.ServiceName(service))
					} else if strings.Contains(sdkError.Message, "eks") && strings.Contains(sdkError.Message, "only support \"normal\" status") {
						// [TencentCloudSDKError] Code=FailedOperation, Message=eks-oljjepj0(cls-4g9b5b18) is "deleting", only support "normal" status, RequestId=2a5de623-a64a-46e4-ae91-e3e1f1ff0f4f
						// [TencentCloudSDKError] Code=FailedOperation, Message=eks-6ibc5eb6(cls-5vzjheeo) is "creating", only support "normal" status, RequestId=2879a6f1-6ac9-45ac-99a3-eaebd6a3fe34
						return types.NewError(errcode.EVMUpdatingError, sdkError.Error(), utils.ServiceName(service))
					} else if strings.Contains(sdkError.Message, "in ENI is not normal state") {
						// [TencentCloudSDKError] Code=FailedOperation, Message=IP ['************'] in ENI is not normal state, not support to register, RequestId=f1fab6a4-6065-4a9e-8080-8321afeb7c0f
						return types.NewError(errcode.EVMUpdatingError, sdkError.Error(), utils.ServiceName(service))
					}
				}
			}
			return err
		}

		if response != nil && response.Response != nil && response.Response.FailListenerIdSet != nil && len(response.Response.FailListenerIdSet) != 0 {
			glog.Infof("BatchRegisterTargets error: %v", utils.JsonWrapper(response.Response.FailListenerIdSet))
			return types.NewError(errcode.CLBBackendError, "", *response.Response.RequestId)
		}
	}
	return nil
}

// batchRegisterTargets batch register targets
func BatchModifyTargetWeight(service service_wrapper.ServiceWrapper, region string, lbID string, targets []*clb.RsWeightRule) error {
	request := clb.NewBatchModifyTargetWeightRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	// 先根据ListenerId、LocationId做聚合
	rules := make(map[string]map[string]*clb.RsWeightRule) // Map ListenerId To (Map LocationId To RsWeightRule)
	for _, target := range targets {
		if _, exist := rules[*target.ListenerId]; !exist {
			rules[*target.ListenerId] = make(map[string]*clb.RsWeightRule)
		}
		locationId := ""
		if target.LocationId != nil {
			locationId = *target.LocationId
		}
		if _, exist := rules[*target.ListenerId][locationId]; !exist {
			rules[*target.ListenerId][locationId] = &clb.RsWeightRule{
				ListenerId: common.StringPtr(*target.ListenerId),
				Targets:    make([]*clb.Target, 0),
			}
			if locationId != "" {
				rules[*target.ListenerId][locationId].LocationId = common.StringPtr(locationId)
			}
		}
		rules[*target.ListenerId][locationId].Targets = append(rules[*target.ListenerId][locationId].Targets, target.Targets...)
	}

	// 拆分单个大块
	spilitRules := make([]*clb.RsWeightRule, 0)
	for _, rule := range rules {
		for _, singleRule := range rule {
			if len(singleRule.Targets) <= MAXBATCHTARGETPERREQUEST {
				spilitRules = append(spilitRules, singleRule)
			} else {
				for i := 0; i < len(singleRule.Targets); i += MAXBATCHTARGETPERREQUEST {
					targetGroup := singleRule.Targets[i:utils.Min(i+MAXBATCHTARGETPERREQUEST, len(singleRule.Targets))]
					spilitRules = append(spilitRules, &clb.RsWeightRule{
						ListenerId: singleRule.ListenerId,
						LocationId: singleRule.LocationId,
						Targets:    targetGroup,
					})
				}
			}
		}
	}

	// 两个维度开始分组
	listenerCount := 0
	targetCount := 0
	resultRules := make([][]*clb.RsWeightRule, 1)
	resultRulesIndex := 0
	resultRules[resultRulesIndex] = make([]*clb.RsWeightRule, 0)
	for index, rule := range spilitRules {
		if listenerCount+1 <= TEMPMAXBATCHMODIFYREQUEST && targetCount+len(rule.Targets) <= MAXBATCHTARGETPERREQUEST {
			listenerCount = listenerCount + 1
			targetCount = targetCount + len(rule.Targets)
			resultRules[resultRulesIndex] = append(resultRules[resultRulesIndex], spilitRules[index])
		} else { // 需要开启下一个分批
			resultRules = append(resultRules, make([]*clb.RsWeightRule, 0))
			resultRulesIndex = resultRulesIndex + 1
			listenerCount = 1
			targetCount = len(rule.Targets)
			resultRules[resultRulesIndex] = append(resultRules[resultRulesIndex], spilitRules[index])
		}
	}

	for _, resultRule := range resultRules {
		request.ModifyList = resultRule
		if _, err := tencentapi.Instance.BatchModifyTargetWeight(cloudctx.New(service, region), request); err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				// [TencentCloudSDKError] Code=FailedOperation, Message=eks-oljjepj0(cls-4g9b5b18) is "deleting", only support "normal" status, RequestId=2a5de623-a64a-46e4-ae91-e3e1f1ff0f4f
				// [TencentCloudSDKError] Code=FailedOperation, Message=eks-6ibc5eb6(cls-5vzjheeo) is "creating", only support "normal" status, RequestId=2879a6f1-6ac9-45ac-99a3-eaebd6a3fe34
				if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "eks") && strings.Contains(sdkError.Message, "only support \"normal\" status") {
					return types.NewError(errcode.EVMUpdatingError, sdkError.Error())
				}
			}
			return err
		}
	}
	return nil
}

// batchRegisterTargets batch register targets
func ModifyTargetWeight(service service_wrapper.ServiceWrapper, region string, lbID string, targets []*clb.RsWeightRule) error {
	request := clb.NewModifyTargetWeightRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	for _, target := range targets {
		request.Weight = target.Weight
		request.ListenerId = target.ListenerId
		request.LocationId = target.LocationId

		MAXTARGETPERREQUEST := 20
		for i := 0; i < len(target.Targets); i += MAXTARGETPERREQUEST {
			targetGroup := target.Targets[i:utils.Min(i+MAXTARGETPERREQUEST, len(target.Targets))]
			request.Targets = targetGroup
			_, err := tencentapi.Instance.ModifyTargetWeight(cloudctx.New(service, region), request)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// batchDeregisterTargets batch deregister targets
func BatchDeregisterTargets(service service_wrapper.ServiceWrapper, region string, lbID string, targets []*clb.BatchTarget) error {
	request := clb.NewBatchDeregisterTargetsRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	for i := 0; i < len(targets); i += MAXBATCHTARGETPERREQUEST {
		targetGroup := targets[i:utils.Min(i+MAXBATCHTARGETPERREQUEST, len(targets))]
		request.Targets = targetGroup

		response, err := tencentapi.Instance.BatchDeregisterTargets(cloudctx.New(service, region), request)
		if err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				// [TencentCloudSDKError] Code=FailedOperation, Message=eks-oljjepj0(cls-4g9b5b18) is "deleting", only support "normal" status, RequestId=2a5de623-a64a-46e4-ae91-e3e1f1ff0f4f
				// [TencentCloudSDKError] Code=FailedOperation, Message=eks-6ibc5eb6(cls-5vzjheeo) is "creating", only support "normal" status, RequestId=2879a6f1-6ac9-45ac-99a3-eaebd6a3fe34
				if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "eks") && strings.Contains(sdkError.Message, "only support \"normal\" status") {
					return types.NewError(errcode.EVMUpdatingError, sdkError.Error())
				}
			}
			return err
		}

		if response != nil && response.Response != nil && response.Response.FailListenerIdSet != nil && len(response.Response.FailListenerIdSet) != 0 {
			glog.Infof("BatchRegisterTargets error: %v", utils.JsonWrapper(response.Response.FailListenerIdSet))
			return types.NewError(errcode.CLBBackendError, "", *response.Response.RequestId)
		}
	}
	return nil
}

// registerClassicLoadBalancerTargets register targets with classic loadbalancer
func RegisterClassicLoadBalancerTargets(service service_wrapper.ServiceWrapper, region string, lbID string, targets []*clb.ClassicalTargetInfo) error {
	request := clb.NewRegisterTargetsWithClassicalLBRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	for i := 0; i < len(targets); i += MAXTARGETPERREQUEST {
		targetGroup := targets[i:utils.Min(i+MAXTARGETPERREQUEST, len(targets))]
		request.Targets = targetGroup

		if _, err := tencentapi.Instance.RegisterTargetsWithClassicalLB(cloudctx.New(service, region), request); err != nil {
			return err
		}
	}
	return nil
}

// deregisterClassicLoadBalancerTargets deregister targets with classic loadbalancer
func DeregisterClassicLoadBalancerTargets(service service_wrapper.ServiceWrapper, region string, lbID string, instanceIds []*string) error {
	request := clb.NewDeregisterTargetsFromClassicalLBRequest()
	request.LoadBalancerId = common.StringPtr(lbID)

	for i := 0; i < len(instanceIds); i += MAXTARGETPERREQUEST {
		instanceIdGroup := instanceIds[i:utils.Min(i+MAXTARGETPERREQUEST, len(instanceIds))]
		request.InstanceIds = instanceIdGroup

		if _, err := tencentapi.Instance.DeregisterTargetsFromClassicalLB(cloudctx.New(service, region), request); err != nil {
			return err
		}
	}
	return nil
}

// Service 的后端选择逻辑都在这里实现
// filterNodes filter nodes for quota and when with AnnoBackendsListLabel annontation
func filterNodes(service service_wrapper.ServiceWrapper, nodes []*v1.Node, backendType string, pods []*v1.Pod) (types.Targets, map[string]bool, error) {
	var labelList labels.Selector
	var err error

	nodes = utils.FilterNodesByBackendType(nodes, backendType)

	targetNotAdd := make(map[string]bool)
	targetMap := make(map[string]*types.Target)
	for index, node := range nodes { // 构建默认的NodePort后端，所有正常节点都会作为NodePort后端
		if target := BuildTargetInstanceId(nodes[index], backendType, 10); target != nil {
			targetMap[node.Name] = target
		}
	}

	// Selector Local
	// Service是Local转发，并且Service开启了`service.kubernetes.io/local-svc-only-bind-node-with-pod`注解时，后端需要重新选择
	if utils.IsExternalTrafficPolicyLocal(service) {
		targetMap = make(map[string]*types.Target) // 重置默认后端
		nodeMap := make(map[string]*v1.Node)
		for index, node := range nodes {
			nodeMap[node.Name] = nodes[index]
		}

		// Service是否开启加权平衡
		endpoints, err := cluster.Instance.EndpointsLister().Endpoints(service.GetObjectMeta().GetNamespace()).Get(service.GetObjectMeta().GetName())
		if err != nil {
			glog.Errorf("qcloud: find endpoints for service [%s/%s] with error [%s]", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err.Error())
			return nil, nil, err
		}
		glog.V(5).Infof("[%s/%s]endpoint has [%d] subsets. [%v]", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), len(endpoints.Subsets), endpoints.Subsets)

		for _, sub := range endpoints.Subsets {
			for _, add := range sub.Addresses {
				if node, exist := nodeMap[*add.NodeName]; exist { // 不存在时：大概率当前Pod处于的节点，状态不正确，不在节点列表中
					if _, exist := targetMap[*add.NodeName]; !exist {
						if target := BuildTargetInstanceId(node, backendType, 0); target != nil {
							targetMap[*add.NodeName] = target
						}
					} // kateway todo 用 else
					if _, exist := targetMap[*add.NodeName]; exist {
						targetMap[*add.NodeName].Weight = common.Int64Ptr(*targetMap[*add.NodeName].Weight + 1) // kateway 强制加权平均
					}
				}
			}
			for _, notReady := range sub.NotReadyAddresses {
				if node, exist := nodeMap[*notReady.NodeName]; exist { // 不存在时：大概率当前Pod处于的节点，状态不正确，不在节点列表中
					if _, exist := targetMap[*notReady.NodeName]; !exist {
						if target := BuildTargetInstanceId(node, backendType, 0); target != nil {
							targetMap[*notReady.NodeName] = target
						}
					}
				}
			}
		}

		// 保证优雅停机状态
		for _, pod := range pods {
			if node, exist := nodeMap[pod.Spec.NodeName]; exist {
				if _, exist := targetMap[pod.Spec.NodeName]; !exist {
					if target := BuildTargetInstanceId(node, backendType, 0); target != nil {
						targetMap[pod.Spec.NodeName] = target
						if pod.DeletionTimestamp != nil {
							targetNotAdd[target.Target] = true // TODO: 这个打表在HostNetwork模式下有问题
						}
					}
				}
			}
		}
	}

	// 增加节点下线时的截流能力: tke.cloud.tencent.com/intercept-from-external-load-balancers
	// 优先级高于其他Local场景的权重控制，在节点存在该Label时，节点上线权重调整为0
	for _, target := range targetMap {
		if target.Node.Labels != nil {
			if value, exist := target.Node.Labels["tke.cloud.tencent.com/graceful-shutdown-from-load-balancers"]; exist && value == "true" {
				target.Weight = common.Int64Ptr(0)
			}
		}
	}

	// Selector BackendsList
	newTargetMap := make(map[string]*types.Target)
	availList, labelExists := utils.GetBackendsListLabel(service)
	if labelExists {
		if labelList, err = labels.Parse(availList); err != nil {
			labelExists = false
		}
	}
	for key, target := range targetMap {
		value, hasMasterRoleLabel := target.Node.Labels[LabelNodeRoleMaster]
		if !labelExists { // 没有`BackendsLabel`注解
			// 1. 默认选择非Master节点作为后端
			// 2. Local绑定情况下，不考虑Master节点的限制
			if master, err := utils.ParseBool(value); !hasMasterRoleLabel || (master != true && err != nil) {
				newTargetMap[key] = target
			} else if utils.IsExternalTrafficPolicyLocal(service) {
				newTargetMap[key] = target
			}
		} else { // node match label or master node when ignore
			labelSet := labels.Set(target.Node.ObjectMeta.Labels)
			if labelList.Matches(labelSet) {
				newTargetMap[key] = target
			}
		}
	}
	targetMap = newTargetMap

	// TODO misakazhou 参数中的Node就已经过滤掉了EKSNode，所以下面的逻辑不会生效。
	// 处理TKE扩展EKS集群的场景
	// 1. 当TKE节点存在时，流量全部绑定到TKE的节点上。以此保证流量均衡
	// 2. 当用户开启Local绑定时，不能执行上述操作。
	// existTKENode := false
	// existEKSNode := false
	// for _, target := range targetMap {
	//	if utils.IsEKSNode(target.Node) {
	//		existEKSNode = true
	//	} else if utils.IsTKENode(target.Node) {
	//		existTKENode = true
	//	}
	// }
	// if !isLocalBindService && (existTKENode && existEKSNode) {
	//	newTargetMap = make(map[string]*types.Target)
	//	for key, target := range targetMap {
	//		if utils.IsTKENode(target.Node) {
	//			newTargetMap[key] = target
	//		}
	//	}
	// }
	// targetMap = newTargetMap

	// Map To Arrary
	var availTargets = types.Targets{}
	for _, target := range targetMap {
		availTargets = append(availTargets, target)
	}
	return availTargets, targetNotAdd, nil
}

func BuildHostNetworkTarget(node *v1.Node, pod *v1.Pod, port int32, backendType string) *types.Target {
	target := &types.Target{
		Pod:    pod,
		Direct: true,
		Target: "",
		Port:   port,
	}

	if types.NewNode(node).IsIDC() {
		if backend, exist := utils.GetNodeBackend(node, backendType); exist {
			target.BackendType = types.NAT
			target.Target = backend
			return target
		}
		glog.Errorf("Node %s, InternalIP not exist. Can not bind to the Loadbalancer.", node.Name)
		return nil
	}

	if types.NewNode(node).IsCXM() { // 特殊CXM节点
		if backend, exist := utils.GetNodeBackend(node, backendType); exist {
			target.BackendType = types.EVM
			target.Target = backend
			return target
		}
		glog.Errorf("Node %s, InternalIP not exist. Can not bind to the Loadbalancer.", node.Name)
		return nil
	}

	if backendType == "ipv6" || backendType == "mixed" { // 混合协议优先IPv6
		if backend, exist := utils.GetNodeBackend(node, "ipv6"); exist {
			target.BackendType = types.ENI
			target.Target = backend
			return target
		}
	}

	// ProviderID的内容格式：qcloud:///190001/ins-mmnyndtg
	providerId := node.Spec.ProviderID
	if strings.HasPrefix(providerId, "qcloud:") {
		instanceId := providerId[strings.LastIndex(providerId, "/")+1:]
		if strings.HasPrefix(instanceId, "ins-") {
			target.Target = instanceId
			target.BackendType = types.CVM
			return target
		}
	}

	glog.Errorf("Node %s, Unexpected Node. Can not bind to the Loadbalancer.", node.Name)
	return nil
}

// 不保证能够从Node中获取到InstanceId
func BuildTargetInstanceId(node *v1.Node, backendType string, weight int64) *types.Target {
	target := &types.Target{
		Node:   node,
		Direct: false,
		Target: "",
		Weight: common.Int64Ptr(weight),
	}

	if types.NewNode(node).IsIDC() {
		if backend, exist := utils.GetNodeBackend(node, backendType); exist {
			target.BackendType = types.NAT
			target.Target = backend
			return target
		}
		glog.Errorf("Node %s, InternalIP not exist. Can not bind to the Loadbalancer.", node.Name)
		return nil
	}

	if types.NewNode(node).IsCXM() { // 特殊CXM节点
		if backend, exist := utils.GetNodeBackend(node, backendType); exist {
			target.BackendType = types.EVM
			target.Target = backend
			return target
		}
		glog.Errorf("Node %s, InternalIP not exist. Can not bind to the Loadbalancer.", node.Name)
		return nil
	}

	if backendType == "ipv6" || backendType == "mixed" { // 混合协议优先IPv6
		if backend, exist := utils.GetNodeBackend(node, "ipv6"); exist {
			target.BackendType = types.ENI
			target.Target = backend
			return target
		}
	}

	if types.NewNode(node).IsCVM() {
		target.BackendType = types.CVM
		target.Target = types.NewNode(node).InstanceID()
		return target
	}

	return target
}

// Backend To KeySet
func getTargetKeySets(service service_wrapper.ServiceWrapper, listenerBackend []*clb.Backend) sets.String {
	currentTargetSets := make(sets.String)
	for _, target := range listenerBackend {
		if target.Type == nil {
			glog.Errorf("Unexpected Backend. Backend Type Not Exist. %s", utils.JsonWrapper(target))
			services.UploadMetricsAndEvent(service, types.NewError(errcode.BackendTypeEmpty, "", utils.ServiceName(service)))
		} else if types.BackendIsIP(*target.Type) {
			for _, privateIp := range target.PrivateIpAddresses {
				currentTargetSets.Insert(getTargetKeyForENI(*privateIp, int32(*target.Port)))
			}
		} else if types.BackendIsCVM(*target.Type) {
			currentTargetSets.Insert(getTargetKeyForCVM(*target.InstanceId, int32(*target.Port)))
		} else {
			glog.Errorf("Unexpected Backend. Backend Type Unexpected. %s", utils.JsonWrapper(target))
			services.UploadMetricsAndEvent(service, types.NewError(errcode.BackendTypeUnexpected, "", utils.ServiceName(service), target.Type))
		}
	}
	return currentTargetSets
}

// parseTargetKey get instance id and port
func parseTargetKey(targetKey string) (types.BackendType, string, int64, error) {
	target := strings.Split(targetKey, "_")
	if len(target) != 3 { // Impossible
		return "", "", 0, fmt.Errorf("can not retrieve instance id and port, key: %s", targetKey)
	}
	backendType := types.BackendType(target[0])
	instanceId := target[1]
	port, err := strconv.ParseInt(target[2], 10, 64)
	if err != nil {
		return backendType, instanceId, 0, err
	}
	return backendType, instanceId, port, nil

}

// BackendType ENI: backend is EniIp
// BackendType CVM: backend is InstanceId
// func NewTarget(backendType types.BackendType, port int64, backend string) *clb.Target {
//	batchTarget := &clb.Target{
//		Port: &port,
//	}
//	if backendType == types.ENI || backendType == types.CCN {
//		batchTarget.EniIp = &backend
//	} else if backendType == types.CVM {
//		batchTarget.InstanceId = &backend
//	} else {
//		// Unexpected Here
//	}
//	return batchTarget
// }

// BackendType ENI: backend is EniIp
// BackendType CVM: backend is InstanceId
func NewBatchTarget(backendType types.BackendType, listenerId string, port int64, backend string, locationId *string) *clb.BatchTarget {
	batchTarget := &clb.BatchTarget{ // kateway 新加的rs 权重都是0
		ListenerId: &listenerId,
		Port:       &port,
		LocationId: locationId,
	}
	if types.BackendIsIP(string(backendType)) {
		batchTarget.EniIp = &backend
	} else if backendType == types.CVM {
		batchTarget.InstanceId = &backend
	} else {
		// Unexpected Here
	}
	return batchTarget
}

// BackendType ENI: backend is EniIp
// BackendType CVM: backend is InstanceId
func NewRsWeightRule(backendType types.BackendType, listenerId string, port int64, backend string, weight int64, locationId *string) *clb.RsWeightRule {
	target := &clb.Target{
		Port:   &port,
		Weight: &weight,
	}
	if types.BackendIsIP(string(backendType)) {
		target.EniIp = &backend
	} else if backendType == types.CVM {
		target.InstanceId = &backend
	} else {
		// Unexpected Here
	}

	batchTarget := &clb.RsWeightRule{
		ListenerId: &listenerId,
		LocationId: locationId,
		Targets: []*clb.Target{
			target,
		},
	}
	return batchTarget
}

func GetGraceContextFromService(service service_wrapper.ServiceWrapper) (*types.GraceContext, error) {
	graceCtx := &types.GraceContext{}
	endpointsOfService, err := cluster.Instance.EndpointsLister().Endpoints(service.GetObjectMeta().GetNamespace()).Get(service.GetObjectMeta().GetName())
	if err != nil {
		glog.Errorf("EndpointLister List error for service %s. error %v", utils.ServiceName(service), err)
		return nil, err
	}
	if endpointsOfService != nil {
		graceCtx.TKExEndpoint = types.NewEndpointSet()
		for _, subnet := range endpointsOfService.Subsets {
			for _, normalAddr := range subnet.Addresses {
				graceCtx.TKExEndpoint.Endpoint[normalAddr.IP] = true
			}
			for _, notReadyAddr := range subnet.NotReadyAddresses {
				graceCtx.TKExEndpoint.Endpoint[notReadyAddr.IP] = true
				graceCtx.TKExEndpoint.NotReadyEndpoint[notReadyAddr.IP] = true
			}
		}
	}
	if lo.Must(cluster.Instance.CheckVersion(">= v1.22")) {
		selector, _ := labels.Parse(fmt.Sprintf("kubernetes.io/service-name=%s", service.GetObjectMeta().GetName()))
		endpointSlices, err := cluster.Instance.EndpointSliceLister().EndpointSlices(service.GetObjectMeta().GetNamespace()).List(selector)
		if err != nil {
			glog.Errorf("EndpointSliceLister List error for service %s. error %v", utils.ServiceName(service), err)
			return nil, err
		}
		for _, endpointSlice := range endpointSlices {
			for _, endpoint := range endpointSlice.Endpoints {
				// Docs: https://kubernetes.io/zh/docs/concepts/services-networking/endpoint-slices/#%E7%8A%B6%E5%86%B5
				for _, address := range endpoint.Addresses {
					graceCtx.TKExEndpoint.Endpoint[address] = true
				}
				if !((endpoint.Conditions.Serving != nil && *endpoint.Conditions.Serving == true) || (endpoint.Conditions.Ready != nil && *endpoint.Conditions.Ready == true)) {
					for _, address := range endpoint.Addresses {
						graceCtx.TKExEndpoint.NotReadyEndpoint[address] = true
					}
				}
			}
		}
	}
	return graceCtx, nil
}

func getTargetKeyForENI(privateIp string, port int32) string {
	return fmt.Sprintf("%s_%s_%d", types.ENI, privateIp, port)
}

func getTargetKeyForCVM(instanceId string, port int32) string {
	return fmt.Sprintf("%s_%s_%d", types.CVM, instanceId, port)
}
