package tencent

import (
	"encoding/json"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"

	tscapi "git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	v2Clb "github.com/howardshaw/qcloudapi-sdk-go/clb"
	gclone "github.com/huandu/go-clone/generic"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	v1 "k8s.io/api/core/v1"
	errors2 "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	glog "k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/net"
	"git.woa.com/kateway/pkg/runtime/conv"
	"git.woa.com/kateway/pkg/runtime/object"
	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloudctx"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/utils"
	"cloud.tencent.com/lb-controller/pkg/utils/cluster_service"
)

func EnsureLoadBalancerTargetsDeleted(loadBalancerContext *LoadBalancerContext) error {
	service := loadBalancerContext.Service
	region := loadBalancerContext.Region
	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	listeners, err := GetCurrentTKEServiceListeners(loadBalancerContext)
	if err != nil {
		return err
	}

	listenerBackends, err := loadBalancerContext.GetListenersBackend()
	if err != nil {
		return err
	}

	// 由于可能超限，所以分两批
	batchDeregiterTargets := make([]*clb.BatchTarget, 0)
	for _, listener := range listeners {
		backend := listenerBackends[utils.GetListenerKey(listener.GetListenerPort(), listener.GetProtocol())]

		batchDeregiterTargets = append(batchDeregiterTargets, getCurrentTarget(service, *backend.ListenerId, nil, backend.Targets)...)
		for _, rule := range backend.Rules {
			batchDeregiterTargets = append(batchDeregiterTargets, getCurrentTarget(service, *backend.ListenerId, rule.LocationId, rule.Targets)...)
		}
	}

	// 所有解绑或绑定成功的targets，可能需要修改相应节点的保护finalizer
	targetsToEnqueue := make([]*clb.BatchTarget, 0)
	var nodesByID map[string]*v1.Node
	if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) && len(batchDeregiterTargets) > 0 {
		nodesByID, err = services.GetNodesByID(cluster.Instance.NodeLister())
		if err != nil {
			return err
		}
	}
	// batch deregitser targets
	if len(batchDeregiterTargets) > 0 {
		if err := BatchDeregisterTargets(service, region, *loadBalancer.LoadBalancerId, batchDeregiterTargets); err != nil {
			return err
		}
		if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, batchDeregiterTargets...)
			cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueTargets(targetsToEnqueue, nodesByID)
		}
	}

	return nil
}

func getCurrentTarget(service service_wrapper.ServiceWrapper, listenerId string, locationId *string, targets []*clb.Backend) []*clb.BatchTarget {
	backendManagementMode := utils.GetBackendManagementMode(service)

	batchDeregiterTargets := make([]*clb.BatchTarget, 0)
	for _, target := range targets {
		if backendManagementMode == types.BackendManagementModeTag && !types.ManagedTarget(target.Tag, config.Global.ClusterName) {
			continue // 开启标签管理，标签不符合预期，不在管理范围
		}
		if target.Type != nil && *target.Type == string(types.CVM) && target.InstanceId != nil && *target.InstanceId != "" {
			batchDeregiterTargets = append(batchDeregiterTargets, &clb.BatchTarget{
				ListenerId: common.StringPtr(listenerId),
				LocationId: locationId,
				InstanceId: common.StringPtr(*target.InstanceId),
				Port:       common.Int64Ptr(*target.Port),
			})
		} else {
			for _, privateIp := range target.PrivateIpAddresses {
				batchDeregiterTargets = append(batchDeregiterTargets, &clb.BatchTarget{
					ListenerId: common.StringPtr(listenerId),
					LocationId: locationId,
					EniIp:      common.StringPtr(*privateIp),
					Port:       common.Int64Ptr(*target.Port),
				})
			}
		}
	}
	return batchDeregiterTargets
}

// ensureLoadBalancerListenerDeleted ensure lb listeners for service deleted
func EnsureLoadBalancerListenerDeleted(loadBalancerContext *LoadBalancerContext) error {
	service := loadBalancerContext.Service
	region := loadBalancerContext.Region
	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	listeners, err := GetCurrentTKEServiceListeners(loadBalancerContext)
	if err != nil {
		return err
	}
	listenerIds := make([]string, 0)
	listenerByID := make(map[string]*clb.Listener)
	for _, listener := range listeners {
		listenerID := listener.GetListenerId()
		listenerIds = append(listenerIds, listenerID)
		if listener.Forward {
			listenerByID[listenerID] = listener.Listener
		}
	}
	if len(listenerIds) == 0 {
		return nil
	}

	// delete load balance listener
	glog.Infof("ensureLoadBalancerListenerDeleted for svc: %s/%s lb: %s del listener: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), loadBalancerContext.LoadBalancerId, listenerIds)
	if *loadBalancer.Forward == CLASSICTYPE {
		if err := BatchDeleteClassicLoadBalancerListener(service, region, loadBalancerContext.LoadBalancerId, listenerIds); err != nil {
			glog.Errorf("batchDeleteClassicLoadBalancerListener for svc: %s/%s lb: %s del listener: %v err: %v",
				service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), loadBalancerContext.LoadBalancerId, listenerIds)
			return err
		}
	} else {
		// 监听器的删除也可能导致node与clb的解绑，所以优雅删除开关打开时需要入队处理node的finalizer
		var nodesByID map[string]*v1.Node
		var ListenersBackendsByKey map[string]*clb.ListenerBackend
		if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			nodesByID, err = services.GetNodesByID(cluster.Instance.NodeLister())
			if err != nil {
				return err
			}
			ListenersBackendsByKey, err = loadBalancerContext.GetListenersBackend()
			if err != nil {
				return err
			}
		}
		for _, listenerId := range listenerIds {
			if err := DeleteLoadBalancerListener(service, region, loadBalancerContext.LoadBalancerId, listenerId); err != nil {
				glog.Errorf("deleteLoadBalancerListener for svc: %s/%s lb: %s del listener: %s err: %v",
					service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), loadBalancerContext.LoadBalancerId, listenerId, err)
				return err
			} else if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
				listenerBackends, exist := ListenersBackendsByKey[utils.GetListenerKey(*(listenerByID[listenerId].Port), *(listenerByID[listenerId].Protocol))]
				if exist {
					nodes := services.GetNodesFromListenerBackends(listenerBackends, nodesByID)
					for node := range nodes {
						cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueNode(node)
					}
				}
			}
		}
	}
	return nil
}

// kateway 创建监听器
// ensureLoadBalancerListener ensure lb listener created for service
// func EnsureLoadBalancerListener(service service_wrapper.ServiceWrapper, currentListeners map[string]*ServiceListener, desiredListeners map[string]*ServiceListener, lb *clb.LoadBalancer, lbTags []*tag.TagResource) (bool, error) {
func EnsureLoadBalancerListener(syncContext *SyncContext) error {
	service := syncContext.Service
	region := syncContext.LoadBalancerContext.Region
	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}
	// ensure lb listeners
	currentListeners, err := GetCurrentTKEServiceListeners(syncContext.LoadBalancerContext)
	if err != nil {
		return err
	}

	// build current listener map and set
	currentListenerSets := make(sets.String)
	for listenerKey := range currentListeners {
		currentListenerSets.Insert(listenerKey)
	}

	// build desired listener set
	desiredListenerSets := make(sets.String)
	for listenerKey := range syncContext.ServiceContext.ServiceListeners {
		desiredListenerSets.Insert(listenerKey)
	}

	toAddListenerKeys := desiredListenerSets.Difference(currentListenerSets)
	toDelListenerKeys := currentListenerSets.Difference(desiredListenerSets)
	toUpdateListenerKeys := desiredListenerSets.Intersection(currentListenerSets)

	// 考虑当前监听器已经被创建，但是还没有录入到CRD的情况，对这种情况进行修正
	lbListeners, err := syncContext.LoadBalancerContext.GetMixedListeners()
	if err != nil {
		return err
	}
	if toAddListenerKeys.Len() != 0 {
		toAddListenerKeysFix := make(sets.String)
		for toAddListenerKey := range toAddListenerKeys {
			if value, exist := lbListeners[toAddListenerKey]; exist {
				toUpdateListenerKeys.Insert(toAddListenerKey)
				currentListeners[toAddListenerKey] = value
			} else {
				toAddListenerKeysFix.Insert(toAddListenerKey)
			}
		}
		toAddListenerKeys = toAddListenerKeysFix
	}

	glog.Infof("ensureLoadBalancerListener for svc: %s/%s lb: %s desired listener: %s, current listener: %s, toAdd: %s, toDel: %s, toUpdate: %s",
		service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId,
		utils.JsonWrapper(desiredListenerSets), utils.JsonWrapper(currentListenerSets),
		utils.JsonWrapper(toAddListenerKeys), utils.JsonWrapper(toDelListenerKeys), utils.JsonWrapper(toUpdateListenerKeys))

	// first delete
	toDelClassicListenerIds := make(sets.String)
	for _, listenerKey := range toDelListenerKeys.List() {
		if delListener, exist := currentListeners[listenerKey]; exist {
			toDelClassicListenerIds.Insert(delListener.GetListenerId())
		}
	}
	if *loadBalancer.Forward == CLASSICTYPE {
		if err := BatchDeleteClassicLoadBalancerListener(service, region, *loadBalancer.LoadBalancerId, toDelClassicListenerIds.List()); err != nil {
			glog.Errorf("batchDeleteClassicLoadBalancerListener for svc: %s/%s lb: %s listeners: %v err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, toDelClassicListenerIds.List(), err)
			return err
		}
	} else {
		for _, listenerKey := range toDelListenerKeys.List() {
			delListener := currentListeners[listenerKey]
			if delListener.GetListenerName() != TKEDedicatedListenerName { // 非TKE创建监听器不主动进行删除
				continue
			}
			// qingyangwu:未来如果存在一个listener被多个service复用的情况，需要在这里添加相应的检测逻辑
			// if service.ServiceType() == service_wrapper.CoreService {
			// 	// Service资源的兼容逻辑：不开启扩展协议，不删除七层监听器。
			// 	if _, exist, _ := utils.GetSpecifyProtocol(service); !exist && net.IsL7Protocol(delListener.GetProtocol()) {
			// 		continue
			// 	}
			// }
			if err := DeleteLoadBalancerListener(service, region, *loadBalancer.LoadBalancerId, delListener.GetListenerId()); err != nil {
				glog.Errorf("deleteLoadBalancerListener for svc: %s/%s lb: %s listenerId: %s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, delListener.GetListenerId(), err)
				syncContext.Errors = append(syncContext.Errors, err)
			}
		}
	}

	toAddListeners := make([]*ServiceListener, 0)
	for _, listenerKey := range toAddListenerKeys.List() {
		if addListener, exist := syncContext.ServiceContext.ServiceListeners[listenerKey]; exist {
			toAddListeners = append(toAddListeners, addListener)
		}
	}
	if len(toAddListeners) != 0 {
		if *loadBalancer.Forward == CLASSICTYPE {
			createListenerIds, err := BatchCreateClassicLoadBalancerListener(service, region, *loadBalancer.LoadBalancerId, toAddListeners)
			if err != nil {
				glog.Errorf("Service:%s/%s lb:%s %v created classic listeners failed,err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, utils.JsonWrapper(createListenerIds), err)
				return err
			}
		} else {
			createListenerIds, err := BatchCreateLoadBalancerListener(service, region, *loadBalancer.LoadBalancerId, loadBalancer.IPv6Mode, toAddListeners)
			if len(err) != 0 {
				syncContext.Errors = append(syncContext.Errors, err...)
			}
			glog.Infof("Service:%s/%s lb:%s %v created listeners: %s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, toAddListenerKeys.List(), utils.JsonWrapper(createListenerIds))
		}
	}

	// 将新创建的监听器信息同步到desiredListeners中，为了下一步同步监听器内的七层转发规则
	// if *loadBalancer.Forward == CLASSICTYPE {
	//	newListener := make(map[string]*clb.ClassicalListener)
	//	listeners, err := syncContext.LoadBalancerContext.GetClassicalListeners()
	//	if err != nil {
	//		return err
	//	}
	//	for index, listener := range listeners {
	//		listenerKey := getClassicListenerKey(*listener.ListenerPort, *listener.InstancePort, *listener.Protocol)
	//		newListener[listenerKey] = listeners[index]
	//	}
	// } else {
	//	newListener := make(map[string]*clb.Listener)
	//	listeners, err := syncContext.LoadBalancerContext.GetListeners()
	//	if err != nil {
	//		return err
	//	}
	//	for index, listener := range listeners {
	//		listenerKey := getListenerKey(*listener.Port, *listener.Protocol)
	//		newListener[listenerKey] = listeners[index]
	//	}
	// }

	toUpdated := false
	for _, listenerKey := range toUpdateListenerKeys.List() {
		updateListener, exist := syncContext.ServiceContext.ServiceListeners[listenerKey]
		if !exist {
			continue
		}
		if *loadBalancer.Forward == CLASSICTYPE { // classic
			continue
		}

		listener := currentListeners[listenerKey]
		if request := buildModifyListenerRequest(service, *loadBalancer.LoadBalancerId, loadBalancer.IPv6Mode, updateListener, listener.Listener); request != nil {
			toUpdated = true
			if _, err := tencentapi.Instance.ModifyListener(cloudctx.New(service, region), request); err != nil {
				e := err
				if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
					switch sdkError.Code {
					case "InvalidParameterValue":
						if strings.Contains(sdkError.Message, "IntervalTime") { // "Code": "InvalidParameterValue", "Message": "HealthCheck.IntervalTime should not be less than HealthCheck.TimeOut"
							e = types.NewError(errcode.TkeServiceConfigContentError, sdkError.Error(), utils.ServiceName(service), "IntervalTime should not be less than TimeOut")
						} else if strings.Contains(sdkError.Message, "HealthCheck.HttpVersion") { // {"Code":"InvalidParameterValue","Message":"HealthCheck.HttpVersion should be HTTP/1.0 or HTTP/1.1","RequestId":"e1a103ad-39af-4ec2-8d32-33aff86a77ed"}
							e = types.NewError(errcode.TkeServiceConfigContentError, sdkError.Error(), utils.ServiceName(service), "HealthCheck.HttpVersion is require.")
						}
					case "FailedOperation":
						if strings.Contains(sdkError.Message, "ProxyProtocol") {
							e = types.NewError(errcode.TkeServiceConfigContentError, sdkError.Error(), utils.ServiceName(service), "")
						}
					default:
					}
				}
				syncContext.Errors = append(syncContext.Errors, e)
			} else {
				glog.Infof("Service:%s/%s lb:%s updated listeners:%s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, listenerKey)
			}
		}
		// glog.Infof("Service:%s/%s lb:%s created listeners:%s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), lbId, utils.JsonWrapper(createListenerIds))
	}

	if len(toAddListenerKeys) != 0 || len(toDelListenerKeys) != 0 || toUpdated {
		if *loadBalancer.Forward == CLASSICTYPE { // classic
			if err := syncContext.LoadBalancerContext.UpdateClassicalListeners(); err != nil {
				return err
			}
		} else {
			if err := syncContext.LoadBalancerContext.UpdateListeners(); err != nil {
				return err
			}
		}
	}

	// ensure lb listener tags
	if err := EnsureLoadBalancerListenerTags(syncContext); err != nil {
		return err
	}

	// kateway 更新LBS里的监听器
	if err := EnsureLoadBalancerResource(syncContext); err != nil {
		return err
	}
	return nil
}

// ensureLoadBalancerListener ensure lb listener created for service
// func EnsureLoadBalancerListener(service service_wrapper.ServiceWrapper, currentListeners map[string]*ServiceListener, desiredListeners map[string]*ServiceListener, lb *clb.LoadBalancer, lbTags []*tag.TagResource) (bool, error) {
func EnsureLoadBalancerListenerBackendManagerOnly(syncContext *SyncContext) error {
	service := syncContext.Service
	region := syncContext.LoadBalancerContext.Region
	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}
	// ensure lb listeners
	currentListeners, err := GetCurrentTKEServiceListeners(syncContext.LoadBalancerContext)
	if err != nil {
		return err
	}
	listenerBackends, err := syncContext.LoadBalancerContext.GetListenersBackend()
	if err != nil {
		return err
	}

	// build current listener map and set
	currentListenerSets := make(sets.String)
	for listenerKey := range currentListeners {
		currentListenerSets.Insert(listenerKey)
	}

	// build desired listener set
	desiredListenerSets := make(sets.String)
	for listenerKey := range syncContext.ServiceContext.ServiceListeners {
		desiredListenerSets.Insert(listenerKey)
	}
	toDelListenerKeys := currentListenerSets.Difference(desiredListenerSets)

	batchDeregiterTargets := make([]*clb.BatchTarget, 0)
	for _, listenerKey := range toDelListenerKeys.List() {
		delListener := currentListeners[listenerKey]
		if delListener.GetListenerName() != TKEDedicatedListenerName { // 非TKE创建监听器不主动进行删除
			continue
		}
		if service.ServiceType() == service_wrapper.CoreService {
			// Service资源的兼容逻辑：不开启扩展协议，不删除七层监听器。
			if _, exist, _ := utils.GetSpecifyProtocol(service); !exist && net.IsL7Protocol(delListener.GetProtocol()) {
				continue
			}
		}
		backend := listenerBackends[listenerKey]
		// kateway: 对于四层监听器，Targets中记录所有的后端信息
		batchDeregiterTargets = append(batchDeregiterTargets, getCurrentTarget(service, *backend.ListenerId, nil, backend.Targets)...)
		// kateway: 对于七层监听器，需要遍历Rules字段
		for _, rule := range backend.Rules {
			batchDeregiterTargets = append(batchDeregiterTargets, getCurrentTarget(service, *backend.ListenerId, rule.LocationId, rule.Targets)...)
		}
	}

	// 所有解绑或绑定成功的targets，可能需要修改相应节点的保护finalizer
	targetsToEnqueue := make([]*clb.BatchTarget, 0)
	var nodesByID map[string]*v1.Node
	if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) && len(batchDeregiterTargets) > 0 {
		nodesByID, err = services.GetNodesByID(cluster.Instance.NodeLister())
		if err != nil {
			return err
		}
	}
	if len(batchDeregiterTargets) != 0 { // kateway: 向远程注册rs后，回写到内存
		if err := BatchDeregisterTargets(service, region, *loadBalancer.LoadBalancerId, batchDeregiterTargets); err != nil {
			return err
		}
		if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
			targetsToEnqueue = append(targetsToEnqueue, batchDeregiterTargets...)
			cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueTargets(targetsToEnqueue, nodesByID)
		}
		if err := syncContext.LoadBalancerContext.UpdateListenersBackend(); err != nil {
			return err
		}
	}

	if err := EnsureLoadBalancerResource(syncContext); err != nil {
		return err
	}
	return nil
}

func EnsureLoadBalancerListenerRule(syncContext *SyncContext) error {
	service := syncContext.Service
	region := syncContext.LoadBalancerContext.Region
	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	if *loadBalancer.Forward == CLASSICTYPE {
		return nil
	}

	listeners, err := syncContext.LoadBalancerContext.GetListeners()
	if err != nil {
		return err
	}

	currentL7Rules := make(map[string]*clb.RuleOutput)
	desiredL7RuleKeys := make(sets.String)
	currentL7RuleKeys := make(sets.String)

	defaultDomain := syncContext.LoadBalancerContext.DefaultDomain

	// 整理期望的七层转发规则
	for _, listener := range syncContext.ServiceContext.ServiceListeners {
		// if listener.Protocol == utils.PROTOCOL_HTTP && (listener.TlsSniMap == nil || len(listener.TlsSniMap) == 0) {
		//	if defaultDomain == nil {
		//		return types.NewError(errcode.ServiceSpecifyProtocolHostEmptyError, "", utils.ServiceName(service))
		//	}
		//	key := getL7RuleKey(listener.Port, listener.Protocol, *defaultDomain, "/")
		//	desiredL7RuleKeys.Insert(key)
		//	continue
		// }
		if net.IsL7Protocol(listener.Protocol) && (listener.TlsSniMap != nil || len(listener.TlsSniMap) != 0) {
			for domain, _ := range listener.TlsSniMap {
				// if domain == "" {
				//	if defaultDomain == nil {
				//		syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ServiceSpecifyProtocolHostEmptyError, "", utils.ServiceName(service)))
				//		continue
				//	}
				//	domain = *defaultDomain
				// }
				key := getL7RuleKey(listener.Port, listener.Protocol, domain, "/")
				desiredL7RuleKeys.Insert(key)
			}
		}
	}

	currentListeners, err := GetCurrentTKEServiceListeners(syncContext.LoadBalancerContext)
	if err != nil {
		return err
	}
	// 整理目前的七层转发规则
	for _, listener := range currentListeners {
		if net.IsL7Protocol(listener.GetProtocol()) && listener.GetListenerName() == TKEDedicatedListenerName && listener.Listener != nil && listener.Listener.Rules != nil {
			for index, rule := range listener.Listener.Rules {
				key := getL7RuleKey(listener.GetListenerPort(), listener.GetProtocol(), *rule.Domain, *rule.Url)
				currentL7RuleKeys.Insert(key)
				currentL7Rules[key] = listener.Listener.Rules[index]
			}
		}
	}

	toAddRuleKeys := desiredL7RuleKeys.Difference(currentL7RuleKeys)
	toDelRuleKeys := currentL7RuleKeys.Difference(desiredL7RuleKeys)
	toUpdateRuleKeys := desiredL7RuleKeys.Intersection(currentL7RuleKeys)

	glog.Infof("ensureLoadBalancerListenerRule for svc: %s/%s lb: %s toAdd: %s, toDel: %s, toUpdate: %s",
		service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId,
		utils.JsonWrapper(toAddRuleKeys), utils.JsonWrapper(toDelRuleKeys), utils.JsonWrapper(toUpdateRuleKeys))

	toAdd := map[string][]*clb.RuleInput{}
	for ruleKey, _ := range toAddRuleKeys {
		port, protocol, host, path := parseL7RuleKey(ruleKey)
		listener := listeners[getListenerKey(port, protocol)]
		if listener == nil { // 因监听器同步失败，导致监听器未创建的情况，跳过监听器下转发规则的创建。
			continue
		}
		serviceListener := syncContext.ServiceContext.ServiceListeners[getListenerKey(port, protocol)]
		if request := buildRuleInput(service, loadBalancer.IPv6Mode, host, path, serviceListener, defaultDomain); request != nil {
			if toAdd[*listener.ListenerId] == nil {
				toAdd[*listener.ListenerId] = []*clb.RuleInput{}
			}
			toAdd[*listener.ListenerId] = append(toAdd[*listener.ListenerId], request)
		}
	}

	toDel := make(map[string][]string, 0)
	for ruleKey, _ := range toDelRuleKeys {
		if rule, exist := currentL7Rules[ruleKey]; exist {
			if toDel[*rule.ListenerId] == nil {
				toDel[*rule.ListenerId] = make([]string, 0)
			}
			toDel[*rule.ListenerId] = append(toDel[*rule.ListenerId], *rule.LocationId)
		}
	}

	toUpdateDomain := []*clb.ModifyDomainAttributesRequest{}
	toUpdateRule := []*clb.ModifyRuleRequest{}
	for ruleKey, _ := range toUpdateRuleKeys {
		port, protocol, host, path := parseL7RuleKey(ruleKey)
		desiredListener := syncContext.ServiceContext.ServiceListeners[getListenerKey(port, protocol)]
		currentListener := currentListeners[getListenerKey(port, protocol)]
		domainConfig := desiredListener.TlsSniMap[host]
		if request := buildModifyDomainAttributesRequest(*loadBalancer.LoadBalancerId, host, currentListener.Listener, desiredListener, defaultDomain); request != nil {
			toUpdateDomain = append(toUpdateDomain, request)
		}
		if rule, exist := currentL7Rules[ruleKey]; exist {
			var ruleConfig *RuleConfig = nil
			if domainConfig != nil {
				ruleConfig = domainConfig.RuleMap[path]
			}
			if request := buildModifyRuleRequest(service, *loadBalancer.LoadBalancerId, rule, ruleConfig, syncContext.LoadBalancerContext.DefaultDomain); request != nil {
				toUpdateRule = append(toUpdateRule, request)
			}
		}
	}

	// TODO misakazhou 重定向规则的指向
	// toDelete, err = lbc.filterRewriteLocation(&ing, uLBId, toDelete)
	// if err != nil {
	//	glog.Errorf("filterRewriteLocation error,%v", err)
	//	return err
	// }
	if len(toDel) > 0 {
		glog.Infof("toDelete rule %v", toDel)
		if err := DeleteRules(service, region, *loadBalancer.LoadBalancerId, toDel); err != nil {
			return err
		}
	}

	if len(toAdd) > 0 {
		glog.Infof("toAdd rule %s", utils.JsonWrapper(toAdd))
		if err = CreateRules(service, region, *loadBalancer.LoadBalancerId, toAdd); err != nil { // O(N) + 轮询
			syncContext.Errors = append(syncContext.Errors, err)

		OUTER: // 如果有声明多个转发规则，尝试独立创建每一个转发规则. 不管成功还是失败。
			for listernerId, ruleInput := range toAdd {
				for index := range ruleInput {
					if err := CreateRulesByListerner(service, region, *loadBalancer.LoadBalancerId, listernerId, ruleInput[index:index+1]); err != nil {
						if ingressErr, ok := lo.ErrorsAs[*types.Error](err); ok {
							if ingressErr.ErrorCode.Code == errcode.RuleLimitExceeded.Code { // 如果超过数量限制，不需要再重试。
								syncContext.Errors = append(syncContext.Errors, err)
								break OUTER
							}
						}
					}
				}
			}
		}
	}

	if len(toUpdateDomain) > 0 {
		for _, modify := range toUpdateDomain {
			if _, err := tencentapi.Instance.ModifyDomainAttributes(cloudctx.New(service, region), modify); err != nil {
				if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
					if sdkError.Code == "InvalidParameter" {
						if strings.Contains(sdkError.Message, "Certificate status error") {
							syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.CreateLoadBalancerListenerCertStatusError, sdkError.Error(), utils.ServiceName(service)))
							continue
						} else if strings.Contains(sdkError.Message, "is not SVR type") {
							syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.CreateLoadBalancerListenerCertTypeError, sdkError.Error(), utils.ServiceName(service)))
							continue
						} else if strings.Contains(sdkError.Message, "is out of date") {
							syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.CreateLoadBalancerListenerCertOutOfDateError, sdkError.Error(), utils.ServiceName(service)))
							continue
						}
					}
				}
				syncContext.Errors = append(syncContext.Errors, err)
			}
		}
	}
	if len(toUpdateRule) > 0 {
		for _, modify := range toUpdateRule {
			if _, err := tencentapi.Instance.ModifyRule(cloudctx.New(service, region), modify); err != nil {
				if sdkError, ok := lo.ErrorsAs[*errors.TencentCloudSDKError](err); ok && sdkError.Code == "InvalidParameterValue" {
					if strings.Contains(sdkError.Message, "HealthCheck.HttpCheckDomain is illegal") {
						syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.InvalidHealthCheckDomainError,
							sdkError.Error(), service.String()))
						continue
					}
				}
				syncContext.Errors = append(syncContext.Errors, err)
			}
		}
	}

	if len(toAdd) != 0 || len(toDel) != 0 {
		if *loadBalancer.Forward == CLASSICTYPE { // classic
			if err := syncContext.LoadBalancerContext.UpdateClassicalListeners(); err != nil {
				return err
			}
		} else {
			if err := syncContext.LoadBalancerContext.UpdateListeners(); err != nil {
				return err
			}
		}
	}
	return nil
}

func DeleteRules(service service_wrapper.ServiceWrapper, region string, lbId string, locationIdMap map[string][]string) error {
	for listernerId, locationIds := range locationIdMap {
		if err := DeleteRulesByListerner(service, region, lbId, listernerId, locationIds); err != nil {
			return err
		}
	}
	return nil
}

func DeleteRulesByListerner(service service_wrapper.ServiceWrapper, region string, lbId string, listenerId string, locationIds []string) error {
	BATCH_RULE_LIMIT := 20

	locations := make([]*string, len(locationIds))
	for index, location := range locationIds {
		locationId := location
		locations[index] = &locationId
	}

	request := clb.NewDeleteRuleRequest()
	request.LoadBalancerId = &lbId
	request.ListenerId = &listenerId

	for i := 0; i < len(locations); i += BATCH_RULE_LIMIT {
		request.LocationIds = locations[i:min(i+BATCH_RULE_LIMIT, len(locations))]
		if _, err := tencentapi.Instance.DeleteRule(cloudctx.New(service, region), request); err != nil {
			return err
		}
	}
	return nil
}

func CreateRules(service service_wrapper.ServiceWrapper, region string, lbId string, RuleInputMap map[string][]*clb.RuleInput) error {
	for listernerId, ruleInput := range RuleInputMap {
		if err := CreateRulesByListerner(service, region, lbId, listernerId, ruleInput); err != nil {
			return err
		}
	}
	return nil
}

func CreateRulesByListerner(service service_wrapper.ServiceWrapper, region string, lbId string, listenerId string,
	rules []*clb.RuleInput) error {
	BATCH_RULE_LIMIT := 20

	request := clb.NewCreateRuleRequest()
	request.ListenerId = &listenerId
	request.LoadBalancerId = &lbId
	for i := 0; i < len(rules); i += BATCH_RULE_LIMIT {
		request.Rules = rules[i:min(i+BATCH_RULE_LIMIT, len(rules))]
		if _, err := tencentapi.Instance.CreateRule(cloudctx.New(service, region), request); err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				if sdkError.Code == "LimitExceeded" {
					return types.NewError(errcode.RuleLimitExceeded, sdkError.Error(), utils.ServiceName(service), listenerId)
				}
				if sdkError.Code == "InvalidParameterValue" {
					if strings.Contains(sdkError.Message, "Invalid url:") { // Message=Invalid url: /api/sns/v1/comment/{id:[a-z0-9]{24}}/detail
						return types.NewError(errcode.RuleFormatError, sdkError.Error(), utils.ServiceName(service))
					} else if strings.Contains(sdkError.Message, "This interface only support HTTP/HTTPS listener") { // Message=This interface only support HTTP/HTTPS listener.
						return types.NewError(errcode.RuleProtocolError, sdkError.Error(), utils.ServiceName(service))
					} else if strings.Contains(sdkError.Message, "can't be regular expression or wildcards") {
						return types.NewError(errcode.InvalidHealthCheckDomainError, sdkError.Error(), service.String())
					}
				}
			}
			return err
		}
	}
	return nil
}

func buildModifyDomainAttributesRequest(lbId, host string, listener *clb.Listener, svcListener *ServiceListener,
	defaultDomain *string) *clb.ModifyDomainAttributesRequest {

	request := clb.NewModifyDomainAttributesRequest()
	request.LoadBalancerId = &lbId
	request.ListenerId = listener.ListenerId
	request.Domain = &host

	currentDomain := types.NewCLBListener(listener).GetDomain(host)
	if currentDomain == nil {
		return nil
	}
	expectDomain := svcListener.GetDomain(host)
	if expectDomain == nil {
		return nil
	}
	if !expectDomain.Default && defaultDomain != nil && *defaultDomain == host {
		expectDomain.Default = true
	}

	if expectDomain.Equals(*currentDomain) {
		return nil
	}

	if expectDomain.Default {
		request.DefaultServer = lo.ToPtr(true)
	}
	request.Http2 = expectDomain.HTTP2Enabled
	if certs := expectDomain.TLSConfig; certs != nil {
		id := ""
		if certs.CertificateIDs.Len() > 0 {
			id = certs.CertificateIDs.UnsortedList()[0]
		}
		request.Certificate = &clb.CertificateInput{
			CertId:   &id,
			CertCaId: certs.CACertificateID,
			SSLMode:  &certs.SSLMode,
		}
	}
	return request
}

func buildModifyRuleRequest(service service_wrapper.ServiceWrapper, lbId string, currentRule *clb.RuleOutput,
	ruleConfig *RuleConfig, defaultDomain *string) *clb.ModifyRuleRequest {
	req := clb.NewModifyRuleRequest()
	req.LoadBalancerId = &lbId
	req.ListenerId = currentRule.ListenerId
	req.LocationId = currentRule.LocationId
	req.HealthCheck = &clb.HealthCheck{}

	var needUpdate bool
	if ruleConfig != nil && ruleConfig.L7RuleConfig != nil {
		_, needUpdate = types.L7RuleConfig(*ruleConfig.L7RuleConfig).Apply(req, currentRule, defaultDomain)
	}

	if preventLoopback, preventLoopbackExist, err := utils.IsPreventLoopback(service); err == nil && preventLoopbackExist {
		sourceIPType := lo.Ternary(preventLoopback, int64(1), int64(0))
		req.HealthCheck.SourceIpType = &sourceIPType
		if currentRule.HealthCheck == nil || currentRule.HealthCheck.SourceIpType == nil || *currentRule.HealthCheck.SourceIpType != sourceIPType {
			needUpdate = true
		}
	}

	return lo.Ternary(needUpdate, req, nil)
}

func needsSessionAffinityFallback(svc service_wrapper.ServiceWrapper) bool {
	// mci的spec中没有会话保持相关的字段，因此无需fallback
	if svc.ServiceType() == service_wrapper.MultiClusterService {
		return false
	}

	annotations := svc.GetObjectMeta().GetAnnotations()
	if len(annotations) > 0 {
		v, exists := annotations[types.ServiceAnnotationSessionAffinityFallback]
		if exists && v == "false" {
			return false
		}
	}
	return true
}

func buildRuleInput(service service_wrapper.ServiceWrapper, ipv6Mode *string, host string, path string, lbListener *ServiceListener,
	defaultDomain *string) *clb.RuleInput {

	domain := lbListener.GetDomain(host)
	ruleInput := &clb.RuleInput{
		Domain: &host,
		Url:    &path,
		Http2:  domain.HTTP2Enabled,
		HealthCheck: &clb.HealthCheck{
			HealthSwitch: lo.Ternary(strings.Contains(host, "*"), lo.ToPtr[int64](0), lo.ToPtr[int64](1)),
		},
	}

	// todo  提取到 pkg
	if ipv6Mode != nil && strings.ToLower(*ipv6Mode) == "ipv6fullchain" { // IPv6FullChain 仅支持 VIP 探测
		ruleInput.HealthCheck.SourceIpType = lo.ToPtr[int64](0)
	} else {
		ruleInput.HealthCheck.SourceIpType = lo.ToPtr[int64](1) // 默认 使用 100.64 作为探测源 IP
	}

	if lbListener.TlsSniMap != nil && lbListener.TlsSniMap[host] != nil {
		domainConfig := lbListener.TlsSniMap[host]
		// {"Response":{"Error":{"Code":"InvalidParameter","Message":"Lack of parameter Certificate or MultiCertInfo "},"RequestId":"57311611-6d44-438a-9b53-a61bbf9ec212"}}
		if lbListener.Protocol == "HTTPS" { // 没有合法证书，已经记录错误信息，跳过转发路径创建
			if domainConfig.TlsCert == nil {
				glog.Infof("LoadBalancerListenerRule for svc: %s/%s. host:%s path:%s. no exist cert.", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), host, path)
				return nil
			}
			ruleInput.Certificate = conv.Convert[*clb.CertificateInput](domainConfig.TlsCert)
		}
		if lbListener.L7ListenerConfig != nil {
			if lbListener.L7ListenerConfig.DefaultServer != nil {
				if (*lbListener.L7ListenerConfig.DefaultServer != "" && *lbListener.L7ListenerConfig.DefaultServer == host) || (*lbListener.L7ListenerConfig.DefaultServer == "" && defaultDomain != nil && *defaultDomain == host) {
					ruleInput.DefaultServer = common.BoolPtr(true)
				}
			}
		}
		if domainConfig.RuleMap != nil && domainConfig.RuleMap[path] != nil && domainConfig.RuleMap[path].L7RuleConfig != nil {
			l7RuleConfig := domainConfig.RuleMap[path].L7RuleConfig
			hcConfig := l7RuleConfig.HealthCheck
			if hcConfig != nil {
				conv.ConvertInto(*hcConfig, ruleInput.HealthCheck)
				if ruleInput.HealthCheck.HttpCheckDomain != nil && *ruleInput.HealthCheck.HttpCheckDomain == "" && defaultDomain != nil {
					ruleInput.HealthCheck.HttpCheckDomain = lo.ToPtr(*defaultDomain)
				}
			}
			if l7RuleConfig.Session != nil {
				if l7RuleConfig.Session.Enable {
					var sessionExpireTime int64 = 30
					if l7RuleConfig.Session.SessionExpireTime != nil {
						sessionExpireTime = int64(*l7RuleConfig.Session.SessionExpireTime)
					}
					ruleInput.SessionExpireTime = common.Int64Ptr(sessionExpireTime)
				} else {
					ruleInput.SessionExpireTime = common.Int64Ptr(0)
				}
			}
			if l7RuleConfig.Scheduler != nil {
				ruleInput.Scheduler = l7RuleConfig.Scheduler
			}
			if l7RuleConfig.ForwardType != nil {
				ruleInput.ForwardType = l7RuleConfig.ForwardType
			}
		}
	}

	if preventLoopback, preventLoopbackExist, err := utils.IsPreventLoopback(service); err == nil && preventLoopbackExist {
		if ruleInput.HealthCheck == nil {
			ruleInput.HealthCheck = &clb.HealthCheck{}
		}
		ruleInput.HealthCheck.SourceIpType = HealthCheckToInt64(preventLoopback)
	}

	return ruleInput
}

// kateway: GetServiceContext根据service的定义+tkeserviceconfig 获取预期的监听器配置列表, 存入context  todo 重命名
// getDesiredListeners parse service to get desired listener
func GetServiceContext(syncContext *SyncContext) error {
	service := syncContext.Service
	// kateway: 如果用户有配置TKEServiceConfig的注解，则根据service信息生成配置信息
	if err := CheckAutoTkeServiceConfig(syncContext); err != nil {
		return err
	}

	var serviceConfig *tscapi.TkeServiceConfig
	configName, err := utils.GetTkeServiceConfig(service)
	if err != nil {
		syncContext.Errors = append(syncContext.Errors, err)
	}
	if configName != "" {
		if serviceConfig, err = cluster.Instance.TkeServiceConfigLister().TkeServiceConfigs(service.GetObjectMeta().GetNamespace()).Get(configName); err != nil {
			if errors2.IsNotFound(err) { // 主实例处理完成
				syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.TkeServiceConfigNotFoundError, err.Error(), utils.ServiceName(service), fmt.Sprintf("%s/%s", service.GetObjectMeta().GetNamespace(), configName)))
			} else {
				glog.Errorf("TkeServiceConfigLister Get error for TkeServiceConfigs %s/%s. error %v", service.GetObjectMeta().GetNamespace(), configName, err)
				return err
			}
		}
	}

	balancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	// ProtocolDomainPath to TkeServiceConfig Map
	// kateway: 将tkeserviceconfig 中的四层监听器配置重新组织成map
	l4ConfigsKeyMap := make(map[string]*tscapi.L4ListenerConfig)
	if *balancer.Forward != CLASSICTYPE && serviceConfig != nil {
		for index, l4Listener := range serviceConfig.Spec.LoadBalancer.L4Listeners {
			key := getListenerKey(int64(l4Listener.Port), strings.ToUpper(l4Listener.Protocol))
			if l4Listener.HealthCheck != nil && l4Listener.HealthCheck.CheckType != nil && *l4Listener.HealthCheck.CheckType == net.ProtocolTCP {
				l4Listener.HealthCheck.HttpCode = nil
				l4Listener.HealthCheck.HttpVersion = nil
				l4Listener.HealthCheck.HttpCheckPath = nil
				l4Listener.HealthCheck.HttpCheckDomain = nil
				l4Listener.HealthCheck.HttpCheckMethod = nil
			}
			l4ConfigsKeyMap[key] = serviceConfig.Spec.LoadBalancer.L4Listeners[index]
		}
	}

	// kateway: 将tkeserviceconfig 中的七层监听器配置，domain配置以及转发规则配置重新组织成map
	l7ConfigsKeyMap := make(map[string]*tscapi.L7ListenerConfig)
	l7DomainConfigsKeyMap := make(map[string]*tscapi.L7DomainConfig)
	l7RuleConfigsKeyMap := make(map[string]*tscapi.L7RuleConfig)
	if *balancer.Forward != CLASSICTYPE && serviceConfig != nil {
		for index, l7Listener := range serviceConfig.Spec.LoadBalancer.L7Listeners {
			key := getListenerKey(int64(l7Listener.Port), strings.ToUpper(l7Listener.Protocol))
			l7ConfigsKeyMap[key] = serviceConfig.Spec.LoadBalancer.L7Listeners[index]
			for index, domain := range l7Listener.Domains {
				ruleDomain := ""
				if syncContext.LoadBalancerContext.DefaultDomain != nil {
					ruleDomain = *syncContext.LoadBalancerContext.DefaultDomain
				}
				if domain.Domain != "" {
					ruleDomain = domain.Domain
				}
				key := getL7DomainKey(int64(l7Listener.Port), strings.ToUpper(l7Listener.Protocol), ruleDomain)
				l7DomainConfigsKeyMap[key] = l7Listener.Domains[index]
				for index, rule := range domain.Rules {
					key := getL7RuleKey(int64(l7Listener.Port), strings.ToUpper(l7Listener.Protocol), ruleDomain, rule.Url)
					if rule.HealthCheck != nil && rule.HealthCheck.CheckType != nil && *rule.HealthCheck.CheckType == net.ProtocolTCP {
						rule.HealthCheck.HttpCode = nil
						rule.HealthCheck.HttpCheckPath = nil
						rule.HealthCheck.HttpCheckDomain = nil
						rule.HealthCheck.HttpCheckMethod = nil
					}
					l7RuleConfigsKeyMap[key] = domain.Rules[index]
				}
			}
		}
	}

	desiredListeners := make(map[string]*ServiceListener)
	desiredListenersKeys := make([]string, 0)
	specifyProtocolMap, _, _ := utils.GetSpecifyProtocol(service)

	var sessionAffinity int64
	if service.ServiceType() == service_wrapper.CoreService {
		originService := service.RawService()
		if originService.Spec.SessionAffinity == v1.ServiceAffinityClientIP &&
			originService.Spec.SessionAffinityConfig != nil &&
			originService.Spec.SessionAffinityConfig.ClientIP != nil &&
			originService.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds != nil {
			sessionAffinity = (int64)(*originService.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds)
			// clb 允许的会话保持范围是 30~3600: https://cloud.tencent.com/document/product/214/6154
			// k8s service 中如果用户开启但不设置，默认值是10800
			if sessionAffinity > 0 && sessionAffinity < 30 {
				sessionAffinity = 30
			} else if sessionAffinity > 3600 {
				sessionAffinity = 3600
			}
		}

		// kateway: 这里有两层循环，第一层循环是遍历service定义中的所有port，然后解析service 注解中的specifiy protocol配置，得到当前port自定义的所有协议
		// 第二层循环遍历当前port对应的协议列表，将port-protocol二元组组成ServiceListener 结构并放入desiredListeners 中
		for _, port := range originService.Spec.Ports {
			// https://iwiki.woa.com/pages/viewpage.action?pageId=410488406
			// 当启用指定协议时，用户端口暴露的协议将根据注解内容控制
			var tlsConfig *types.TLSConfig

			// kateway: 如果service没有定义specify protocol的注解，则当前port对应的protocol即为spec中定义的protocol
			protocolList := []string{strings.ToUpper(string(port.Protocol))}
			specifyProtocol, specifyProtocolExist := (*specifyProtocolMap)[port.Port] // kateway: 通过 spec 里的端口（只用端口）去反查 annotation里的设置
			if specifyProtocolExist {
				// 端口协议改写
				protocolList = []string{}
				protocolMap := make(map[string]bool)
				for _, protocol := range specifyProtocol.Protocol {
					if _, exist := protocolMap[protocol]; exist { // 协议重复声明
						syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.DuplicateProtocolError, "", utils.ServiceName(service), protocol))
						continue
					}
					if !utils.ValidProtocol(protocol) { // 不支持协议
						syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.InvalidProtocolError, "", utils.ServiceName(service), protocol))
						continue
					}
					if net.IsUDPFamilyProtocol(protocol) { // kateway UDP/QUIC 只能和 TCP族协议 复用
						for protocolExist, _ := range protocolMap {
							if net.IsUDPFamilyProtocol(protocolExist) { // 协议族冲突
								syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ConflictProtocolError, "", utils.ServiceName(service)))
								continue
							}
						}
					} else if net.IsTCPFamilyProtocol(protocol) { // kateway TCP族协议只能和 UDP/QUIC 复用
						for protocolExist, _ := range protocolMap {
							if net.IsTCPFamilyProtocol(protocolExist) { // 协议族冲突
								syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ConflictProtocolError, "", utils.ServiceName(service)))
								continue
							}
						}
					}

					if backendManageOnly, _, _ := utils.GetBackendManageOnly(service); !backendManageOnly { // 在仅后端管理模式下，不需要解析证书相关信息和报错。
						if protocol == "TCP_SSL" || protocol == "QUIC" {
							if specifyProtocol.Tls == nil {
								syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.TLSSecretEmptyError, "", utils.ServiceName(service)))
								continue
							}

							cfg, err := GetTLSConfig(service, *specifyProtocol.Tls)
							if err != nil {
								syncContext.Errors = append(syncContext.Errors, err)
								continue
							}
							tlsConfig = cfg
						}
					}

					protocolList = append(protocolList, protocol)
					protocolMap[protocol] = true
				}
			}

			// kateway: 第二层循环
			for _, protocol := range protocolList {
				balancerListener := &ServiceListener{
					Name:                    port.Name, // no selector svc通过name作为唯一索引键（因为endpoints中的port是targetport，可能不具备唯一性）
					Port:                    int64(port.Port),
					NodePort:                port.NodePort,
					TargetPort:              port.TargetPort,
					SessionAffinityFallback: needsSessionAffinityFallback(service),
					ListenerConfig: ListenerConfig{
						Protocol:         protocol,
						L4ListenerConfig: nil,
						L7ListenerConfig: nil,
						SessionAffinity:  sessionAffinity,
					},
				}

				listenerKey := getListenerKey(int64(port.Port), protocol)
				if *balancer.Forward == CLASSICTYPE {
					listenerKey = getClassicListenerKey(int64(port.Port), int64(port.NodePort), protocol)
				}

				// ketaway: 这里从之前构造的map种获取四层以及七层配置
				if *balancer.Forward == FORWARDTYPE {
					if config, exist := l4ConfigsKeyMap[listenerKey]; exist {
						balancerListener.L4ListenerConfig = config
					}
					if config, exist := l7ConfigsKeyMap[listenerKey]; exist {
						balancerListener.L7ListenerConfig = config
					}
				}

				if specifyProtocolExist { // 将证书配置同步到loadBalancerListener中
					if protocol == "TCP_SSL" || protocol == "QUIC" {
						balancerListener.TlsCert = tlsConfig
					}

					if protocol == "HTTPS" || protocol == "HTTP" {
						tlsSniCertIdMap := make(map[string]*types.TLSConfig) // 合法Domain的列表，并附带证书信息（HTTP为空）
						defaultDomain := syncContext.LoadBalancerContext.DefaultDomain
						// if specifyProtocol.Hosts == nil || len(specifyProtocol.Hosts) == 0 {
						//	syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.TLSSNISecretEmptyError, "", utils.ServiceName(service)))
						//	continue
						// }

						for domain, host := range specifyProtocol.Hosts {
							if _, exist := tlsSniCertIdMap[domain]; exist { // 重复Host
								syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.DuplicateHostError, "", utils.ServiceName(service), domain))
								continue
							}

							if domain != "" {
								if match, _ := regexp.MatchString("(\\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)+", domain); !match {
									syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.RuleHostFormatError, "", utils.ServiceName(service), domain))
									continue
								}
							} else {
								if defaultDomain == nil {
									syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ServiceSpecifyProtocolHostEmptyError, "", utils.ServiceName(service)))
									continue
								}
								domain = *defaultDomain
							}

							if protocol == "HTTPS" {
								if host.Tls == nil {
									syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.TLSSecretEmptyError, "", utils.ServiceName(service)))
									continue
								}

								certId, err := GetTLSConfig(service, *host.Tls)
								if err != nil {
									syncContext.Errors = append(syncContext.Errors, err)
									continue
								}
								tlsSniCertIdMap[domain] = certId
							} else {
								tlsSniCertIdMap[domain] = nil
							}
						}

						if len(tlsSniCertIdMap) != 0 {
							balancerListener.TlsSniMap = make(map[string]*DomainConfig)
							for domain, cert := range tlsSniCertIdMap {
								l7DomainConfig := l7DomainConfigsKeyMap[getL7DomainKey(int64(port.Port), protocol, domain)]
								balancerListener.TlsSniMap[domain] = &DomainConfig{
									TlsCert:        cert,
									L7DomainConfig: l7DomainConfig,
									RuleMap:        make(map[string]*RuleConfig),
								}
								l7RuleConfig := l7RuleConfigsKeyMap[getL7RuleKey(int64(port.Port), protocol, domain, "/")]
								balancerListener.TlsSniMap[domain].RuleMap["/"] = &RuleConfig{
									L7RuleConfig: l7RuleConfig,
								}
							}
						}
					}
				}

				desiredListeners[listenerKey] = balancerListener
				desiredListenersKeys = append(desiredListenersKeys, listenerKey)
			}
		}
	} else if service.ServiceType() == service_wrapper.MultiClusterService { // TODO: 这里的逻辑需要和上面聚合一下，没有时间处理了
		originService := service.RawMCS()
		for _, port := range originService.Spec.Ports {
			// https://iwiki.woa.com/pages/viewpage.action?pageId=410488406
			// 当启用指定协议时，用户端口暴露的协议将根据注解内容控制
			var tlsConfig *types.TLSConfig

			protocolList := []string{strings.ToUpper(string(port.Protocol))}
			specifyProtocol, specifyProtocolExist := (*specifyProtocolMap)[port.Port]
			if specifyProtocolExist {
				// 端口协议改写
				protocolList = []string{}
				protocolMap := make(map[string]bool)
				for _, protocol := range specifyProtocol.Protocol {
					if _, exist := protocolMap[protocol]; exist { // 协议重复声明
						syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.DuplicateProtocolError, "", utils.ServiceName(service), protocol))
						continue
					}
					if utils.ValidProtocol(protocol) == false { // 不支持协议
						syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.InvalidProtocolError, "", utils.ServiceName(service), protocol))
						continue
					}
					if net.IsUDPFamilyProtocol(protocol) {
						for protocolExist, _ := range protocolMap {
							if net.IsUDPFamilyProtocol(protocolExist) { // 协议族冲突
								syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ConflictProtocolError, "", utils.ServiceName(service)))
								continue
							}
						}
					} else if net.IsTCPFamilyProtocol(protocol) {
						for protocolExist, _ := range protocolMap {
							if net.IsTCPFamilyProtocol(protocolExist) { // 协议族冲突
								syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ConflictProtocolError, "", utils.ServiceName(service)))
								continue
							}
						}
					}

					if backendManageOnly, _, _ := utils.GetBackendManageOnly(service); !backendManageOnly { // 在仅后端管理模式下，不需要解析证书相关信息和报错。
						if protocol == "TCP_SSL" || protocol == "QUIC" {
							if specifyProtocol.Tls == nil {
								syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.TLSSecretEmptyError, "", utils.ServiceName(service)))
								continue
							}

							certId, err := GetTLSConfig(service, *specifyProtocol.Tls)
							if err != nil {
								syncContext.Errors = append(syncContext.Errors, err)
								continue
							}
							tlsConfig = certId
						}
					}

					protocolList = append(protocolList, protocol)
					protocolMap[protocol] = true
				}
			}

			for _, protocol := range protocolList {
				balancerListener := &ServiceListener{
					Name:                    port.Name,
					Port:                    int64(port.Port),
					TargetPort:              port.TargetPort,
					SessionAffinityFallback: needsSessionAffinityFallback(service),
					ListenerConfig: ListenerConfig{
						Protocol:         protocol,
						L4ListenerConfig: nil,
						L7ListenerConfig: nil,
						SessionAffinity:  sessionAffinity,
					},
				}

				listenerKey := getListenerKey(int64(port.Port), protocol)
				if *balancer.Forward == FORWARDTYPE {
					if config, exist := l4ConfigsKeyMap[listenerKey]; exist {
						balancerListener.L4ListenerConfig = config
					}
					if config, exist := l7ConfigsKeyMap[listenerKey]; exist {
						balancerListener.L7ListenerConfig = config
					}
				}

				if specifyProtocolExist { // 将证书配置同步到loadBalancerListener中
					if protocol == "TCP_SSL" || protocol == "QUIC" {
						balancerListener.TlsCert = tlsConfig
					}

					if protocol == "HTTPS" || protocol == "HTTP" {
						tlsSniCertIdMap := make(map[string]*types.TLSConfig) // 合法Domain的列表，并附带证书信息（HTTP为空）
						defaultDomain := syncContext.LoadBalancerContext.DefaultDomain
						// if specifyProtocol.Hosts == nil || len(specifyProtocol.Hosts) == 0 {
						//	syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.TLSSNISecretEmptyError, "", utils.ServiceName(service)))
						//	continue
						// }

						for domain, host := range specifyProtocol.Hosts {
							if _, exist := tlsSniCertIdMap[domain]; exist { // 重复Host
								syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.DuplicateHostError, "", utils.ServiceName(service), domain))
								continue
							}

							if domain != "" {
								if match, _ := regexp.MatchString("(\\*|[a-z0-9]([-a-z0-9]*[a-z0-9])?)(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)+", domain); !match {
									syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.RuleHostFormatError, "", utils.ServiceName(service), domain))
									continue
								}
							} else {
								if defaultDomain == nil {
									syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ServiceSpecifyProtocolHostEmptyError, "", utils.ServiceName(service)))
									continue
								}
								domain = *defaultDomain
							}

							if protocol == "HTTPS" {
								if host.Tls == nil {
									syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.TLSSecretEmptyError, "", utils.ServiceName(service)))
									continue
								}

								certId, err := GetTLSConfig(service, *host.Tls)
								if err != nil {
									syncContext.Errors = append(syncContext.Errors, err)
									continue
								}
								tlsSniCertIdMap[domain] = certId
							} else {
								tlsSniCertIdMap[domain] = nil
							}
						}

						if len(tlsSniCertIdMap) != 0 {
							balancerListener.TlsSniMap = make(map[string]*DomainConfig)
							for domain, cert := range tlsSniCertIdMap {
								l7DomainConfig := l7DomainConfigsKeyMap[getL7DomainKey(int64(port.Port), protocol, domain)]
								balancerListener.TlsSniMap[domain] = &DomainConfig{
									TlsCert:        cert,
									L7DomainConfig: l7DomainConfig,
									RuleMap:        make(map[string]*RuleConfig),
								}
								l7RuleConfig := l7RuleConfigsKeyMap[getL7RuleKey(int64(port.Port), protocol, domain, "/")]
								balancerListener.TlsSniMap[domain].RuleMap["/"] = &RuleConfig{
									L7RuleConfig: l7RuleConfig,
								}
							}
						}
					}
				}

				desiredListeners[listenerKey] = balancerListener
				desiredListenersKeys = append(desiredListenersKeys, listenerKey)
			}
		}
	}

	// kateway: 监听器配额，如果超过配额监听器会被截断，当前默认配置为0（无限制）
	if config.Global.ListenerQuota > 0 {
		if len(desiredListeners) > config.Global.ListenerQuota {
			sort.Strings(desiredListenersKeys)
			chunckedListeners := make(map[string]*ServiceListener)
			for _, listenerKey := range desiredListenersKeys[0:config.Global.ListenerQuota] {
				chunckedListeners[listenerKey] = desiredListeners[listenerKey]
			}
			desiredListeners = chunckedListeners
		}
	}

	syncContext.ServiceContext = &ServiceContext{
		ServiceListeners: desiredListeners,
	}
	return nil
}

func GetListenerKeysByIngress(ingress types.Ingress) ([]string, error) {
	portsByProtocol, err := services.ResolveListenPortsFromAnnotation(ingress.Annotations(), len(ingress.TLS()) > 0)
	if err != nil {
		return nil, err
	}
	keys := []string{}
	for protocol, ports := range portsByProtocol {
		for _, port := range ports {
			keys = append(keys, utils.GetListenerKey(int64(port), protocol))
		}
	}

	return keys, nil
}

func GetListenerKeyByService(service service_wrapper.ServiceWrapper) map[string]bool {
	result := make(map[string]bool)
	specifyProtocolMap, _, _ := utils.GetSpecifyProtocol(service)
	if service.IsService() {
		originService := service.RawService()
		for _, port := range originService.Spec.Ports {
			protocolList := []string{strings.ToUpper(string(port.Protocol))}
			specifyProtocol, specifyProtocolExist := (*specifyProtocolMap)[port.Port]
			if specifyProtocolExist {
				protocolList = specifyProtocol.Protocol
			}

			for _, protocol := range protocolList {
				result[getListenerKey(int64(port.Port), protocol)] = true
			}
		}
	} else if service.IsMCS() { // TODO: 这里的逻辑需要和上面聚合一下，没有时间处理了
		originService := service.RawMCS()
		for _, port := range originService.Spec.Ports {
			protocolList := []string{strings.ToUpper(string(port.Protocol))}
			specifyProtocol, specifyProtocolExist := (*specifyProtocolMap)[port.Port]
			if specifyProtocolExist {
				protocolList = specifyProtocol.Protocol
			}

			for _, protocol := range protocolList {
				result[getListenerKey(int64(port.Port), protocol)] = true
			}
		}
	}
	return result
}

// kateway clb 端属于这个svc 的监听器
// getCurrentTKEServiceListeners get lb tke listeners for service
func GetCurrentTKEServiceListeners(loadBalancerContext *LoadBalancerContext) (map[string]*MixedListener, error) {
	service := loadBalancerContext.Service
	loadBalancerResource := loadBalancerContext.LoadBalancerResource
	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return nil, err
	}

	// serviceUUID := string(service.GetObjectMeta().GetUID())
	// listenerIdsByTag := make(sets.String)

	createByTke := checkCreatedByTKENew(loadBalancerResource)
	// for no service uuid associated with lb, exclusiveUsed is false, this should not happen after ensureloadbalancer
	_, exclusiveUsed := getObjectsUsingTheSameLB(service, loadBalancerResource) // kateway todo 没搞懂啥意思

	// only reuse exist lb and reuse flag enabled need to parse listener tags
	// if !createByTke && ReuseFlag {
	// if !createByTke {
	//	listenerTagPrefix := getTagPrefix(serviceUUID)
	//	for _, lbTag := range loadBalancer.Tags {
	//		if lbTag.TagKey != nil && lbTag.TagValue != nil && strings.HasPrefix(*lbTag.TagKey, listenerTagPrefix) {
	//			listenerIdsByTag.Insert(*lbTag.TagValue)
	//		}
	//	}
	// }

	listenerIdsByResource := make(sets.String)
	listenerByService := GetLoadBalancerResourceListenerByService(service, loadBalancerResource) // kateway 该 svc 在 LBR 里的监听器
	for _, listener := range listenerByService {
		listenerIdsByResource.Insert(getListenerKey(int64(listener.Port), listener.Protocol))
	}

	tkeListeners := make(map[string]*MixedListener)
	// if yClb.getServiceLoadbalancerType(service) == CLASSICTYPE {
	// lbid type may not the same as svc descired
	if *loadBalancer.Forward == CLASSICTYPE { // classic
		lbClassicListeners, err := loadBalancerContext.GetClassicalListeners()
		if err != nil {
			return nil, err
		}
		for _, listener := range lbClassicListeners {
			if listener.ListenerId == nil || listener.ListenerPort == nil || listener.InstancePort == nil || listener.Protocol == nil || listener.ListenerName == nil {
				continue
			}
			protocol := strings.ToUpper(*listener.Protocol)
			if protocol != string(v1.ProtocolTCP) && protocol != string(v1.ProtocolUDP) && protocol != string(v1.ProtocolSCTP) {
				continue
			}
			// TODO need to check listener name?
			// create by tke => all listener
			// exist lb, TKEDedicatedListenerName, not enable reuse
			// exist lb, TKEDedicatedListenerName, enable reuse and has listener tags
			if createByTke ||
				(*listener.ListenerName == TKEDedicatedListenerName && exclusiveUsed) ||
				(*listener.ListenerName == TKEDedicatedListenerName && listenerIdsByResource.Has(getListenerKey(*listener.ListenerPort, *listener.Protocol))) {
				listenerKey := getClassicListenerKey(*listener.ListenerPort, *listener.InstancePort, *listener.Protocol)
				tkeListeners[listenerKey] = &MixedListener{
					Forward:           false,
					ClassicalListener: listener,
				}
			}
		}
	} else {
		lbListeners, err := loadBalancerContext.GetListeners() // kateway CLB 端的监听器
		if err != nil {
			return nil, err
		}
		for _, listener := range lbListeners {
			if listener.ListenerId == nil || listener.Port == nil || listener.Protocol == nil || listener.ListenerName == nil {
				glog.Warningf("service: %s/%s lb: %s listener: %s name, id, port or protocol empty", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, utils.JsonWrapper(listener))
				continue
			}
			protocol := strings.ToUpper(*listener.Protocol)
			if protocol != string(v1.ProtocolTCP) && protocol != string(v1.ProtocolUDP) && protocol != string(v1.ProtocolSCTP) && !utils.ValidProtocol(protocol) { // 支持指定其他协议，就可以脱离K8S对于Service支持协议的限制
				glog.Infof("service: %s/%s lb: %s listener: %s protocol type: %s not match service, skip", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, utils.JsonWrapper(listener), protocol)
				continue
			}
			// TODO need to check listenerName?
			// create by tke => all listener
			// exist lb, TKEDedicatedListenerName, not enable reuse
			// exist lb, TKEDedicatedListenerName, enable reuse and has listener tags
			if createByTke || // kateway 如果用户修改这个，可能是一个勿删的风险点
				(*listener.ListenerName == TKEDedicatedListenerName && exclusiveUsed) ||
				(*listener.ListenerName == TKEDedicatedListenerName && listenerIdsByResource.Has(getListenerKey(*listener.Port, *listener.Protocol))) { // kateway LBR 里属于该svc 的监听器，和clb端监听器对比
				listenerKey := getListenerKey(*listener.Port, *listener.Protocol)
				tkeListeners[listenerKey] = &MixedListener{
					Forward:  true,
					Listener: listener,
				}
			}
		}
	}
	return tkeListeners, nil
}

// getCurrentTKEServiceListeners get lb tke listeners for all resource
func GetCurrentTKEOtherResourceListeners(loadBalancerContext *LoadBalancerContext) (map[string]*MixedListener, error) {
	service := loadBalancerContext.Service
	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return nil, err
	}

	listenerIdsByResource := make(sets.String)
	// kateway: 变量名有歧义，这里过滤出的是不属于该service的监听器列表
	listenerByService := GetLoadBalancerResourceListenerWithoutService(service, loadBalancerContext.LoadBalancerResource)
	for _, listener := range listenerByService {
		listenerIdsByResource.Insert(getListenerKey(int64(listener.Port), listener.Protocol))
	}

	tkeListeners := make(map[string]*MixedListener)
	// if yClb.getServiceLoadbalancerType(service) == CLASSICTYPE {
	// lbid type may not the same as svc descired
	if *loadBalancer.Forward == CLASSICTYPE { // classic
		lbClassicListeners, err := loadBalancerContext.GetClassicalListeners()
		if err != nil {
			return nil, err
		}
		for _, listener := range lbClassicListeners {
			if listener.ListenerId == nil || listener.ListenerPort == nil || listener.InstancePort == nil || listener.Protocol == nil || listener.ListenerName == nil {
				continue
			}
			protocol := strings.ToUpper(*listener.Protocol)
			if protocol != string(v1.ProtocolTCP) && protocol != string(v1.ProtocolUDP) && protocol != string(v1.ProtocolSCTP) {
				continue
			}
			if *listener.ListenerName != TKEDedicatedListenerName || listenerIdsByResource.Has(getListenerKey(*listener.ListenerPort, *listener.Protocol)) {
				listenerKey := getClassicListenerKey(*listener.ListenerPort, *listener.InstancePort, *listener.Protocol)
				tkeListeners[listenerKey] = &MixedListener{
					Forward:           false,
					ClassicalListener: listener,
				}
			}
		}
	} else {
		lbListeners, err := loadBalancerContext.GetListeners()
		if err != nil {
			return nil, err
		}
		for _, listener := range lbListeners {
			if listener.ListenerId == nil || listener.Port == nil || listener.Protocol == nil || listener.ListenerName == nil {
				glog.Warningf("service: %s/%s lb: %s listener: %s name, id, port or protocol empty", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, utils.JsonWrapper(listener))
				continue
			}
			// 兼容teg-tkex lbcf创建的监听器，避免检测端口冲突报错
			if strings.HasPrefix(*listener.ListenerName, "tkex-teg") {
				continue
			}
			protocol := strings.ToUpper(*listener.Protocol)
			if protocol != string(v1.ProtocolTCP) && protocol != string(v1.ProtocolUDP) && protocol != string(v1.ProtocolSCTP) && !utils.ValidProtocol(protocol) { // 支持指定其他协议，就可以脱离K8S对于Service支持协议的限制
				glog.Infof("service: %s/%s lb: %s listener: %s protocol type: %s not match service, skip", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, utils.JsonWrapper(listener), protocol)
				continue
			}
			// 名称不符合的、监听器被使用的
			if *listener.ListenerName != TKEDedicatedListenerName || listenerIdsByResource.Has(getListenerKey(*listener.Port, *listener.Protocol)) {
				listenerKey := getListenerKey(*listener.Port, *listener.Protocol)
				tkeListeners[listenerKey] = &MixedListener{
					Forward:  true,
					Listener: listener,
				}
			}
		}
	}
	return tkeListeners, nil
}

// getLoadBalancerListener get lb listeners by lbID
func GetLoadBalancerListener(service service_wrapper.ServiceWrapper, region string, lbID string) ([]*clb.Listener, error) {
	request := clb.NewDescribeListenersRequest()
	request.LoadBalancerId = &lbID

	response, err := tencentapi.Instance.DescribeListeners(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}
	return response.Response.Listeners, nil
}

// getClassicLoadBalancerListener get classic loadbalancer listener
func GetClassicLoadBalancerListener(service service_wrapper.ServiceWrapper, region string, lbID string) ([]*clb.ClassicalListener, error) {
	request := clb.NewDescribeClassicalLBListenersRequest()
	request.LoadBalancerId = &lbID

	response, err := tencentapi.Instance.DescribeClassicalLBListeners(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}

	return response.Response.Listeners, nil
}

// deleteLoadBalancerListener delete lb listener for service
func DeleteLoadBalancerListener(service service_wrapper.ServiceWrapper, region string, lbID string, listenerID string) error {
	request := clb.NewDeleteListenerRequest()
	request.LoadBalancerId = &lbID
	request.ListenerId = &listenerID
	if _, err := tencentapi.Instance.DeleteListener(cloudctx.New(service, region), request); err != nil {
		return err
	}
	return nil
}

func BatchDeleteClassicLoadBalancerListener(service service_wrapper.ServiceWrapper, region string, lbID string, listenerIds []string) error {
	if len(listenerIds) == 0 {
		return nil
	}

	for i := 0; i < len(listenerIds); i += MAXLISTENERPERREQUEST {
		listenerIdGroup := listenerIds[i:utils.Min(i+MAXLISTENERPERREQUEST, len(listenerIds))]
		request := &v2Clb.DeleteLoadBalancerListenersArgs{
			LoadBalancerId: lbID,
			ListenerIds:    listenerIdGroup,
		}

		if _, err := tencentapi.Instance.DeleteLoadBalancerListenersV2(cloudctx.New(service, region), request); err != nil {
			return err
		}
	}
	return nil
}

// updateLoadBalancerListener create load balancer listener
// func UpdateLoadBalancerListener(service service_wrapper.ServiceWrapper, lbID string, serviceListener *ServiceListener, listener *clb.Listener) error {
//	if request := buildModifyListenerRequest(lbID, serviceListener, listener); request != nil {
//		if _, err := tencentapi.TencentAPIServiceInstance.ModifyListener(service, request); err != nil {
//			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
//				if sdkError.Code == "InvalidParameterValue" {
//					if strings.Contains(sdkError.Message, "IntervalTime") && strings.Contains(sdkError.Message, "IntervalTime") { // "Code": "InvalidParameterValue", "Message": "HealthCheck.IntervalTime should not be less than HealthCheck.TimeOut"
//						return types.NewError(errcode.TkeServiceConfigContentError, err.Error(), utils.ServiceName(service), "IntervalTime should not be less than TimeOut")
//					}
//				}
//			}
//			return err
//		}
//	}
//	return nil
// }

// 批量创建LoadBalancerListener 传统型 V3
func BatchCreateClassicLoadBalancerListener(service service_wrapper.ServiceWrapper, region string, lbID string, listeners []*ServiceListener) ([]string, error) {
	if len(listeners) == 0 {
		return nil, nil
	}

	listenerIds := make(sets.String)
	createListeners := make([]v2Clb.CreateListenerOpts, len(listeners))
	for index, listener := range listeners {
		createListeners[index] = *buildCreateListenerRequestV2(listener)
	}

	for i := 0; i < len(createListeners); i += MAXLISTENERPERREQUEST {
		createListenerGroup := createListeners[i:utils.Min(i+MAXLISTENERPERREQUEST, len(createListeners))]
		request := &v2Clb.CreateLoadBalancerListenersArgs{
			LoadBalancerId: lbID,
			Listeners:      createListenerGroup,
		}

		if err := getClassicListenerParamFromAnnotation(service, request); err != nil {
			glog.Errorf("getClassicListenerParamFromAnnotation for service: %s/%s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
			return nil, err
		}

		response, err := tencentapi.Instance.CreateLoadBalancerListenersV2(cloudctx.New(service, region), request)
		if err != nil {
			return nil, err
		}

		for _, listenerID := range response.ListenerIds {
			listenerIds.Insert(listenerID)
		}
	}

	return listenerIds.List(), nil
}

// 批量创建LoadBalancerListener 应用型 V3
func BatchCreateLoadBalancerListener(service service_wrapper.ServiceWrapper, region string, lbID string, ipv6Mode *string, listeners []*ServiceListener) ([]string, []error) {
	if len(listeners) == 0 {
		return nil, nil
	}

	errs := make([]error, 0)
	listenerIds := make([]string, 0)

	batchSize := 50                                                                         // CLB 接口限制，单次最多创建50个监听器
	groups := object.GobGroupBy(listeners, func(listener *ServiceListener) ListenerConfig { // 根据监听器配置是否相同进行分组
		// 对监听器配置进行相等性分组，需要把端口数字本身排除，否则永远不相等
		cfg := gclone.Clone(listener.ListenerConfig)
		if cfg.L4ListenerConfig != nil {
			cfg.L4ListenerConfig.Port = 0
		}
		if cfg.L7ListenerConfig != nil {
			cfg.L7ListenerConfig.Port = 0
		}
		return cfg
	})

	for _, listenerGroup := range groups {
		listenerConfig := listenerGroup[0].ListenerConfig
		groupPorts := lo.Map(listenerGroup, func(listener *ServiceListener, _ int) *int64 { return &listener.Port })
		batchPorts := lo.Chunk(groupPorts, batchSize)

		for _, ports := range batchPorts {
			result, err := BatchCreateLoadBalancerListenerWithGroup(service, region, lbID, ipv6Mode, listenerConfig, ports)
			if err != nil {
				if svcError, ok := lo.ErrorsAs[*types.Error](err); ok {
					if svcError.ErrorCode.Code == errcode.LoadBalancerListenerLimitExceededError.Code { // 端口超限不再继续
						errs = append(errs, err)
						return listenerIds, errs
					}
				}
				errs = append(errs, err)
				continue
			}
			listenerIds = append(listenerIds, result...)
		}
	}

	return listenerIds, errs
}

// 不要直接调用该方法，改为调用 `batchCreateLoadBalancerListener`
// 调用需要保证 listeners 中的所有 protocol/backendConfig 都和参数中传入的一致。
func BatchCreateLoadBalancerListenerWithGroup(service service_wrapper.ServiceWrapper, region string, lbId string, ipv6Mode *string, config ListenerConfig, ports []*int64) ([]string, error) {
	request := buildCreateListenerRequest(service, lbId, ipv6Mode, config, ports)
	if request == nil {
		return []string{}, nil
	}

	if err := getListenerParamFromAnnotation(service, request); err != nil {
		glog.Errorf("getListenerParamFromAnnotation for service: %s/%s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
		return nil, err
	}

	response, err := tencentapi.Instance.CreateListener(cloudctx.New(service, region), request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			switch sdkError.Code {
			case "LimitExceeded":
				return nil, types.NewError(errcode.LoadBalancerListenerLimitExceededError, sdkError.Error(), utils.ServiceName(service))
			case "InvalidParameterValue.Length":
				if strings.Contains(sdkError.Message, "The length of CertificateId") {
					return nil, types.NewError(errcode.CreateLoadBalancerListenerCertFormatError, sdkError.Error(), utils.ServiceName(service))
				}
			case "InvalidParameterValue":
				if strings.Contains(sdkError.Message, "Query certificate") && strings.Contains(sdkError.Message, "failed") { // Message=Query certificate 'PMwyKbXK' failed.
					return nil, types.NewError(errcode.CreateLoadBalancerListenerCertNotExistError, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "IntervalTime") { // "Code": "InvalidParameterValue", "Message": "HealthCheck.IntervalTime should not be less than HealthCheck.TimeOut"
					return nil, types.NewError(errcode.TkeServiceConfigContentError, sdkError.Error(), utils.ServiceName(service), "IntervalTime should not be less than TimeOut")
				}
			case "InvalidParameter":
				if strings.Contains(sdkError.Message, "Certificate status error") {
					return nil, types.NewError(errcode.CreateLoadBalancerListenerCertStatusError, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "is not SVR type") {
					return nil, types.NewError(errcode.CreateLoadBalancerListenerCertTypeError, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "is out of date") {
					return nil, types.NewError(errcode.CreateLoadBalancerListenerCertOutOfDateError, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "has been occupied") {
					return nil, types.NewError(errcode.ConflictListenerError, sdkError.Error(), utils.ServiceName(service), lbId, ports)
				}
			case "FailedOperation":
				if strings.Contains(sdkError.Message, "support QUIC") { // Code=FailedOperation, Message=uin don't support QUIC protocol. (d4ff8e7f)
					return nil, types.NewError(errcode.QUICNotSupportError, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "has been occupied") {
					return nil, types.NewError(errcode.QUICConflicWithHttpsQUIC, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "ProxyProtocol") {
					return nil, types.NewError(errcode.TkeServiceConfigContentError, sdkError.Error(), utils.ServiceName(service), "")
				}
			default:
			}
		}
		return nil, err
	}

	var listenerIds []string
	for _, listenerID := range response.Response.ListenerIds {
		listenerIds = append(listenerIds, *listenerID)
	}
	return listenerIds, nil
}

func buildCreateListenerRequest(service service_wrapper.ServiceWrapper, lbId string, ipv6Mode *string, listenerConfig ListenerConfig, ports []*int64) *clbinternal.CreateListenerRequest {
	request := clbinternal.NewCreateListenerRequest()
	request.LoadBalancerId = &lbId
	request.Protocol = &listenerConfig.Protocol
	request.Ports = ports
	request.ListenerNames = lo.RepeatBy(len(ports), func(_ int) *string { // 名字需要和批量端口数量一致
		return common.StringPtr(TKEDedicatedListenerName)
	})
	request.SessionExpireTime = &listenerConfig.SessionAffinity
	request.HealthCheck = &clbinternal.HealthCheck{
		HealthSwitch: lo.ToPtr[int64](1),
	}

	if listenerConfig.Protocol == utils.PROTOCOL_TCP { // 新建的TCP监听器，默认开启双向reset
		if ipv6Mode != nil && strings.ToLower(*ipv6Mode) == "ipv6nat64" { // ipv6nat64 不支持设置双向reset
			request.DeregisterTargetRst = nil
		} else {
			request.DeregisterTargetRst = lo.ToPtr(true)
		}
	}

	// todo  提取到 pkg
	if ipv6Mode != nil && strings.ToLower(*ipv6Mode) == "ipv6fullchain" { // IPv6FullChain 仅支持 VIP 探测
		request.HealthCheck.SourceIpType = lo.ToPtr[int64](0)
	} else {
		request.HealthCheck.SourceIpType = lo.ToPtr[int64](1) // 默认 使用 100.64 作为探测源 IP
	}

	if listenerConfig.Protocol == "TCP_SSL" || listenerConfig.Protocol == "QUIC" {
		// {"Response":{"Error":{"Code":"InvalidParameter","Message":"Lack of parameter Certificate or MultiCertInfo "},"RequestId":"9080caba-c2b8-4cb3-b8e2-771c0e16be7f"}}
		if listenerConfig.TlsCert == nil { // 没有合法证书，已经记录错误信息，跳过监听器创建
			return nil
		}
		request.Certificate = conv.Convert[*clb.CertificateInput](listenerConfig.TlsCert)
	} else if listenerConfig.Protocol == "HTTPS" {
		request.SniSwitch = common.Int64Ptr(1)
	}

	l4ListenerConfig := listenerConfig.L4ListenerConfig
	if net.IsL4Protocol(listenerConfig.Protocol) {
		if l4ListenerConfig != nil {
			if l4ListenerConfig.HealthCheck != nil {
				hc := conv.Convert[clb.HealthCheck](*l4ListenerConfig.HealthCheck)
				conv.ConvertInto(hc, request.HealthCheck)
			}
			if l4ListenerConfig.Session != nil {
				if l4ListenerConfig.Session.Enable {
					if l4ListenerConfig.Session.SessionExpireTime != nil {
						request.SessionExpireTime = wrapperInt32ToInt64(l4ListenerConfig.Session.SessionExpireTime)
					} else {
						request.SessionExpireTime = common.Int64Ptr(30)
					}
				}
			}
			if l4ListenerConfig.ProxyProtocol != nil && l4ListenerConfig.ProxyProtocol.Enable {
				request.ProxyProtocol = lo.ToPtr(true)
			}

			request.Scheduler = l4ListenerConfig.Scheduler
			if *request.Protocol == net.ProtocolTCP && l4ListenerConfig.DeregisterTargetRst != nil { // 默认开启的双向reset 可以被 tkeServiceConfig 强制覆盖
				request.DeregisterTargetRst = l4ListenerConfig.DeregisterTargetRst
			}
		}

		if preventLoopback, preventLoopbackExist, err := utils.IsPreventLoopback(service); err == nil && preventLoopbackExist {
			request.HealthCheck.SourceIpType = HealthCheckToInt64(preventLoopback)
		}
	}

	l7ListenerConfig := listenerConfig.L7ListenerConfig
	if net.IsL7Protocol(listenerConfig.Protocol) && l7ListenerConfig != nil {
		if l7ListenerConfig.KeepaliveEnable != nil {
			request.KeepaliveEnable = common.Int64Ptr(int64(*l7ListenerConfig.KeepaliveEnable))
		}
	}
	return request
}

func buildCreateListenerRequestV2(listener *ServiceListener) *v2Clb.CreateListenerOpts {
	createListenerOpts := &v2Clb.CreateListenerOpts{
		Protocol:         utils.ConvertToClassicLbProtocol(listener.Protocol),
		ListenerName:     common.StringPtr(TKEDedicatedListenerName),
		LoadBalancerPort: int32(listener.Port),
		InstancePort:     listener.NodePort,
		HealthSwitch:     common.IntPtr(1),
	}
	return createListenerOpts
}

func buildModifyListenerRequest(service service_wrapper.ServiceWrapper, lbID string, ipv6Mode *string, serviceListener *ServiceListener, listener *clb.Listener) *clbinternal.ModifyListenerRequest {
	request := clbinternal.NewModifyListenerRequest()
	request.LoadBalancerId = &lbID
	request.ListenerId = listener.ListenerId
	request.HealthCheck = &clbinternal.HealthCheck{}

	var hasChange = false
	if *listener.Protocol == "TCP_SSL" || *listener.Protocol == "QUIC" {
		// 1. 当前证书没有配置
		// 2. 当前证书模式有变化
		// 3. 当前证书信息发生了变化（单向证书、双向证书）
		if serviceListener.TlsCert != nil { // 监听器证书配置不合法可能
			id := ""
			if serviceListener.TlsCert.CertificateIDs.Len() > 0 {
				id = serviceListener.TlsCert.CertificateIDs.UnsortedList()[0]
			}
			if listener.Certificate == nil ||
				*listener.Certificate.SSLMode != serviceListener.TlsCert.SSLMode ||
				(listener.Certificate.CertId == nil || *listener.Certificate.CertId != id) ||
				(serviceListener.TlsCert.SSLMode == CERT_MODE_MUTUAL && (listener.Certificate.CertCaId == nil || *listener.Certificate.CertCaId != *serviceListener.TlsCert.CACertificateID)) {
				request.Certificate = conv.Convert[*clb.CertificateInput](serviceListener.TlsCert)
				hasChange = true
			}
		}
	} else if *listener.Protocol == "HTTPS" {
		// 1. 必须开启TLS SSL
		if listener.SniSwitch == nil || *listener.SniSwitch == 0 {
			request.SniSwitch = common.Int64Ptr(1)
			hasChange = true
		}
	}

	if net.IsL4Protocol(*listener.Protocol) {
		if preventLoopback, preventLoopbackExist, err := utils.IsPreventLoopback(service); err == nil && preventLoopbackExist {
			sourceIpType := HealthCheckToInt64(preventLoopback)
			if listener.HealthCheck.SourceIpType == nil || *listener.HealthCheck.SourceIpType != *sourceIpType {
				request.HealthCheck.SourceIpType = sourceIpType
				hasChange = true
			}
		} else if serviceListener.L4ListenerConfig != nil && serviceListener.L4ListenerConfig.HealthCheck != nil && serviceListener.L4ListenerConfig.HealthCheck.SourceIpType != nil {
			sourceIpType := int64(*serviceListener.L4ListenerConfig.HealthCheck.SourceIpType)
			if listener.HealthCheck.SourceIpType == nil || *listener.HealthCheck.SourceIpType != sourceIpType {
				request.HealthCheck.SourceIpType = common.Int64Ptr(sourceIpType)
				hasChange = true
			}
		}
	}

	if net.IsL7Protocol(*listener.Protocol) && serviceListener.L7ListenerConfig != nil {
		if serviceListener.L7ListenerConfig.KeepaliveEnable != nil {
			if listener.KeepaliveEnable == nil || *listener.KeepaliveEnable != int64(*serviceListener.L7ListenerConfig.KeepaliveEnable) {
				request.KeepaliveEnable = common.Int64Ptr(int64(*serviceListener.L7ListenerConfig.KeepaliveEnable))
				hasChange = true
			}
		}
	}

	var tscHasSessionExpireTime bool
	if net.IsL4Protocol(*listener.Protocol) && serviceListener.L4ListenerConfig != nil {
		var (
			cfg              = serviceListener.L4ListenerConfig
			ppEnabledCurrent = IsProxyProtocolEnabled(listener)
			ppEnabledExpect  = cfg.ProxyProtocol != nil && cfg.ProxyProtocol.Enable
		)

		if ppEnabledCurrent != ppEnabledExpect {
			request.ProxyProtocol = lo.ToPtr(ppEnabledExpect)
			hasChange = true
		}
		if serviceListener.L4ListenerConfig.HealthCheck != nil {
			if serviceListener.L4ListenerConfig.HealthCheck.Enable != (*listener.HealthCheck.HealthSwitch != 0) {
				request.HealthCheck.HealthSwitch = HealthCheckToInt64(serviceListener.L4ListenerConfig.HealthCheck.Enable)
				hasChange = true
			}
			if serviceListener.L4ListenerConfig.HealthCheck.Enable { // 不填默认是True
				if serviceListener.L4ListenerConfig.HealthCheck.IntervalTime != nil {
					if listener.HealthCheck.IntervalTime == nil || *listener.HealthCheck.IntervalTime != int64(*serviceListener.L4ListenerConfig.HealthCheck.IntervalTime) {
						request.HealthCheck.IntervalTime = common.Int64Ptr(int64(*serviceListener.L4ListenerConfig.HealthCheck.IntervalTime))
						hasChange = true
					}
				}
				if serviceListener.L4ListenerConfig.HealthCheck.HealthNum != nil {
					if listener.HealthCheck.HealthNum == nil || *listener.HealthCheck.HealthNum != int64(*serviceListener.L4ListenerConfig.HealthCheck.HealthNum) {
						request.HealthCheck.HealthNum = common.Int64Ptr(int64(*serviceListener.L4ListenerConfig.HealthCheck.HealthNum))
						hasChange = true
					}
				}
				if serviceListener.L4ListenerConfig.HealthCheck.UnHealthNum != nil {
					if listener.HealthCheck.UnHealthNum == nil || *listener.HealthCheck.UnHealthNum != int64(*serviceListener.L4ListenerConfig.HealthCheck.UnHealthNum) {
						request.HealthCheck.UnHealthNum = common.Int64Ptr(int64(*serviceListener.L4ListenerConfig.HealthCheck.UnHealthNum))
						hasChange = true
					}
				}
				if serviceListener.L4ListenerConfig.HealthCheck.Timeout != nil {
					if listener.HealthCheck.TimeOut == nil || *listener.HealthCheck.TimeOut != int64(*serviceListener.L4ListenerConfig.HealthCheck.Timeout) {
						request.HealthCheck.TimeOut = common.Int64Ptr(int64(*serviceListener.L4ListenerConfig.HealthCheck.Timeout))
						hasChange = true
					}
				}
				if serviceListener.L4ListenerConfig.HealthCheck.CheckPort != nil {
					if listener.HealthCheck.CheckPort == nil || *listener.HealthCheck.CheckPort != int64(*serviceListener.L4ListenerConfig.HealthCheck.CheckPort) {
						request.HealthCheck.CheckPort = common.Int64Ptr(int64(*serviceListener.L4ListenerConfig.HealthCheck.CheckPort))
						hasChange = true
					}
				}

				// if !preventLoopbackExist && serviceListener.L4ListenerConfig.HealthCheck.SourceIpType != nil { // preventLoopback注解优先级更高，当配置注解时，该属性的配置忽略
				//	sourceIpType := common.Int64Ptr(int64(*serviceListener.L4ListenerConfig.HealthCheck.SourceIpType))
				//	if listener.HealthCheck.SourceIpType == nil || *listener.HealthCheck.SourceIpType != *sourceIpType {
				//		request.HealthCheck.SourceIpType = sourceIpType
				//		hasChange = true
				//	}
				// }

				if serviceListener.L4ListenerConfig.HealthCheck.CheckType != nil {
					if listener.HealthCheck.CheckType == nil || *listener.HealthCheck.CheckType != *serviceListener.L4ListenerConfig.HealthCheck.CheckType {
						request.HealthCheck.CheckType = common.StringPtr(*serviceListener.L4ListenerConfig.HealthCheck.CheckType)
						hasChange = true
					}
				}

				// 七层健康检查配置
				if serviceListener.L4ListenerConfig.HealthCheck.CheckType != nil && *serviceListener.L4ListenerConfig.HealthCheck.CheckType == "HTTP" {
					if serviceListener.L4ListenerConfig.HealthCheck.HttpCode != nil {
						if listener.HealthCheck.HttpCode == nil || *listener.HealthCheck.HttpCode != int64(*serviceListener.L4ListenerConfig.HealthCheck.HttpCode) {
							request.HealthCheck.HttpCode = common.Int64Ptr(int64(*serviceListener.L4ListenerConfig.HealthCheck.HttpCode))
							hasChange = true
						}
					}
					if serviceListener.L4ListenerConfig.HealthCheck.HttpCheckMethod != nil {
						if listener.HealthCheck.HttpCheckMethod == nil || strings.ToUpper(*listener.HealthCheck.HttpCheckMethod) != *serviceListener.L4ListenerConfig.HealthCheck.HttpCheckMethod {
							request.HealthCheck.HttpCheckMethod = common.StringPtr(*serviceListener.L4ListenerConfig.HealthCheck.HttpCheckMethod)
							hasChange = true
						}
					}
					if serviceListener.L4ListenerConfig.HealthCheck.HttpCheckDomain != nil {
						if listener.HealthCheck.HttpCheckDomain == nil || *listener.HealthCheck.HttpCheckDomain != *serviceListener.L4ListenerConfig.HealthCheck.HttpCheckDomain {
							request.HealthCheck.HttpCheckDomain = common.StringPtr(*serviceListener.L4ListenerConfig.HealthCheck.HttpCheckDomain)
							hasChange = true
						}
					}
					if serviceListener.L4ListenerConfig.HealthCheck.HttpCheckPath != nil {
						if listener.HealthCheck.HttpCheckPath == nil || *listener.HealthCheck.HttpCheckPath != *serviceListener.L4ListenerConfig.HealthCheck.HttpCheckPath {
							request.HealthCheck.HttpCheckPath = common.StringPtr(*serviceListener.L4ListenerConfig.HealthCheck.HttpCheckPath)
							hasChange = true
						}
					}
					if listener.Protocol != nil && *listener.Protocol != "TCP_SSL" && *listener.Protocol != "QUIC" && serviceListener.L4ListenerConfig.HealthCheck.HttpVersion != nil {
						if listener.HealthCheck.HttpVersion == nil || *listener.HealthCheck.HttpVersion != *serviceListener.L4ListenerConfig.HealthCheck.HttpVersion {
							request.HealthCheck.HttpVersion = common.StringPtr(*serviceListener.L4ListenerConfig.HealthCheck.HttpVersion)
							hasChange = true
						}
					}
				}
			}
		}
		if serviceListener.L4ListenerConfig.Session != nil {
			tscHasSessionExpireTime = true
			if (!serviceListener.L4ListenerConfig.Session.Enable && *listener.SessionExpireTime == 0) ||
				(serviceListener.L4ListenerConfig.Session.Enable && ((serviceListener.L4ListenerConfig.Session.SessionExpireTime == nil && *listener.SessionExpireTime == 30) || serviceListener.L4ListenerConfig.Session.SessionExpireTime != nil && *serviceListener.L4ListenerConfig.Session.SessionExpireTime == int32(*listener.SessionExpireTime))) {
			} else {
				if serviceListener.L4ListenerConfig.Session.Enable {
					var sessionExpireTime int64 = 30
					if serviceListener.L4ListenerConfig.Session.SessionExpireTime != nil {
						sessionExpireTime = int64(*serviceListener.L4ListenerConfig.Session.SessionExpireTime)
					}
					if sessionExpireTime != *listener.SessionExpireTime {
						request.SessionExpireTime = common.Int64Ptr(sessionExpireTime)
						hasChange = true
					}
				} else {
					request.SessionExpireTime = common.Int64Ptr(0)
					hasChange = true
				}
			}
		}
		if serviceListener.L4ListenerConfig.Scheduler != nil {
			if *serviceListener.L4ListenerConfig.Scheduler != *listener.Scheduler {
				request.Scheduler = serviceListener.L4ListenerConfig.Scheduler
				hasChange = true
			}
		}
		if *listener.Protocol == net.ProtocolTCP && serviceListener.L4ListenerConfig.DeregisterTargetRst != nil {
			if *serviceListener.L4ListenerConfig.DeregisterTargetRst != *listener.DeregisterTargetRst {
				request.DeregisterTargetRst = serviceListener.L4ListenerConfig.DeregisterTargetRst
				hasChange = true
			}
		}
	} else if *listener.Protocol == net.ProtocolTCP && serviceListener.L4ListenerConfig == nil {
		// 对于没有tsc的listener，且存在loadbalancer-source-endpoints，且service名称带有kubernetes-前缀，开启双向rst
		if service.Namespace() == metav1.NamespaceDefault && strings.HasPrefix(service.Name(), "kubernetes-") {
			if _, err := service.Service().LoadbalancerSourceEndpoints(); err == nil {
				if listener.DeregisterTargetRst == nil || !*listener.DeregisterTargetRst {
					if ipv6Mode == nil || strings.ToLower(*ipv6Mode) != "ipv6nat64" {
						request.DeregisterTargetRst = lo.ToPtr(true)
						hasChange = true
					}
				}
			}
		}
	}

	if serviceListener.SessionAffinityFallback && !tscHasSessionExpireTime {
		currentSessionExpireTime := listener.SessionExpireTime
		if currentSessionExpireTime == nil {
			currentSessionExpireTime = common.Int64Ptr(0)
		}
		if serviceListener.SessionAffinity != *currentSessionExpireTime {
			request.SessionExpireTime = &serviceListener.SessionAffinity
			hasChange = true
		}
	}

	if hasChange {
		return request
	} else {
		return nil
	}
}

func HealthCheckToInt64(enabled bool) *int64 {
	if enabled {
		return common.Int64Ptr(1)
	} else {
		return common.Int64Ptr(0)
	}
}

// getLbParamFromAnnotation get lb params when create lb
func getLbParamFromAnnotation(service service_wrapper.ServiceWrapper, request *tencentapi.ExpandCreateLoadBalancerRequest) error {
	if param, exist := utils.GetExtensiveParameters(service); exist {
		if err := json.Unmarshal([]byte(param), request); err != nil {
			return types.NewError(errcode.ExtensiveParametersFormatError, err.Error(), utils.ServiceName(service))
		}
	}
	return nil
}

func getClassicLbParamFromAnnotation(service service_wrapper.ServiceWrapper, request *v2Clb.CreateLoadBalancerArgs) error {
	if param, exist := utils.GetExtensiveParameters(service); exist {
		if err := json.Unmarshal([]byte(param), request); err != nil {
			return types.NewError(errcode.ExtensiveParametersFormatError, err.Error(), utils.ServiceName(service))
		}
	}
	return nil
}

// getListenerParamFromAnnotation get lb listener params when create lb listener
func getListenerParamFromAnnotation(service service_wrapper.ServiceWrapper, request *clbinternal.CreateListenerRequest) error {
	if param, exist := utils.GetListenerParameters(service); exist {
		if err := json.Unmarshal([]byte(param), request); err != nil {
			return types.NewError(errcode.ListenerParametersFormatError, err.Error(), utils.ServiceName(service))
		}
	}
	return nil
}

func getClassicListenerParamFromAnnotation(service service_wrapper.ServiceWrapper, request *v2Clb.CreateLoadBalancerListenersArgs) error {
	if param, exist := utils.GetListenerParameters(service); exist {
		if err := json.Unmarshal([]byte(param), request); err != nil {
			return types.NewError(errcode.ListenerParametersFormatError, err.Error(), utils.ServiceName(service))
		}
	}
	return nil
}

func getListenerKey(port int64, protocol string) string {
	return fmt.Sprintf("%d_%s", port, strings.ToUpper(protocol))
}

func getClassicListenerKey(port int64, instancePort int64, protocol string) string {
	return fmt.Sprintf("%d_%d_%s", port, instancePort, strings.ToUpper(protocol))
}

func getL7DomainKey(port int64, protocal string, domain string) string {
	return fmt.Sprintf("%d_%s_%s", port, strings.ToUpper(protocal), domain)
}

func getL7RuleKey(port int64, protocal string, domain string, url string) string {
	return fmt.Sprintf("%d_%s_%s_%s", port, strings.ToUpper(protocal), domain, url)
}

func getRuleKey(host string, path string) string {
	return fmt.Sprintf("%s_%s", host, path)
}

func parseListenerKey(key string) (int64, string) {
	indexA := strings.Index(key, "_")
	port, _ := strconv.ParseInt(key[0:indexA], 10, 64)
	protocal := key[indexA+1:]
	return port, protocal
}

func parseL7RuleKey(key string) (int64, string, string, string) {
	indexA := strings.Index(key, "_")
	port, _ := strconv.ParseInt(key[0:indexA], 10, 64)
	indexB := strings.Index(key[indexA+1:], "_")
	protocal := key[indexA+1 : indexA+1+indexB]
	indexC := strings.Index(key[indexA+1+indexB+1:], "_")
	domain := key[indexA+1+indexB+1 : indexA+1+indexB+1+indexC]
	rule := key[indexA+1+indexB+1+indexC+1:]
	return port, protocal, domain, rule
}

func wrapperInt32ToInt(data *int32) *int {
	if data == nil {
		return nil
	}
	return common.IntPtr(int(*data))
}

func wrapperInt32ToInt64(data *int32) *int64 {
	if data == nil {
		return nil
	}
	return common.Int64Ptr(int64(*data))
}

func IsProxyProtocolEnabled(listener *clb.Listener) bool {
	_, exists := lo.Find(listener.AttrFlags, func(item *string) bool { return *item == "ProxyProtocol" })
	return exists
}
