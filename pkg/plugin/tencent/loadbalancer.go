package tencent

import (
	"encoding/json"
	"strings"

	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	clbInner "git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/utils"
)

// kateway 向 CLB 同步LB相关配置
func EnsureLoadBalancerDetail(syncContext *SyncContext) error {
	loadBalancerContext := syncContext.LoadBalancerContext
	region := loadBalancerContext.Region
	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	// 同步负载均衡的网络配置
	// 这个错误不可避免的影响后端后端挂载，应该不能忽略。
	if err := syncLoadBalancerDetail(syncContext, loadBalancer, region); err != nil {
		return err
	}

	// 同步负载均衡删除保护
	if err := EnsureDeletionProtection(syncContext); err != nil {
		return err
	}

	// 同步负载均衡默认放通，错误不阻塞主流程
	syncPassToTarget(syncContext, loadBalancer, region) // kateway todo 这些是不能合并到 CreateLoadBalancer吗

	// 同步负载均衡配置保护，错误不阻塞主流程
	EnsureModificationProtection(syncContext)

	// 同步安全组配置
	syncSecurityGroups(syncContext, loadBalancer, region)

	// // 默认开启删除保护
	// if err := modifyDeletionProtection(syncContext.LoadBalancerContext, true); err != nil {
	// 	klog.Errorf("Failed to enable deletion protection: %s", err)
	// }

	return nil
}

// kateway(bitliu[49]): 跨域的细节，CLB 的 地域/VPC 信息是在创建的时候就指定了，
// 对于 MCS 来说新增了 ManagerOnly 类型的跨域，实际上这种类型的跨域是 fake 的
// 在下面的各种跨域 type 的处理中，可以看到没有对这种类型的跨域做任何操作

// kateway: 修改 Clb 各种网络配置
func syncLoadBalancerDetail(syncContext *SyncContext, loadBalancer *clb.LoadBalancer, region string) error {
	service := syncContext.Service

	crossRegionId, crossRegionIdExist := utils.GetCrossRegionId(service)
	crossVPCId, crossVPCIdExist := utils.GetCrossVPCId(service)
	crossType, crossTypeExist := utils.GetCrossType(service)
	hybridType := utils.GetHybridType(service)
	snatProInfo := utils.GetSnatProInfo(service)
	if snatProInfo != nil && len(snatProInfo.SnatIPs) > 10 {
		syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.SnatIPLimitExceeded, "", utils.ServiceName(service)))
		snatProInfo.SnatIPs = snatProInfo.SnatIPs[0:10]
	}

	modifyed := false
	addSnatIp := make([]*clb.SnatIp, 0)
	addSecondSnatIp := make([]*clb.SnatIp, 0)
	deleteSnatIp := make([]*string, 0)
	deleteSecondSnatIp := make([]*string, 0)
	request := clbInner.NewModifyLoadBalancerAttributesRequest()
	request.LoadBalancerId = loadBalancer.LoadBalancerId
	// kateway(bitliu[50]): 跨域的细节，如果使用 2.0 的跨域，MCS 默认开启 Snat Pro
	// 如果是混合云类型的跨域，以及 1.1（PVGW） 的跨域，也默认开启 Snat Pro
	// 传统型负载均衡不支持 SNAT Pro
	// SNAT IP 主要用于混合云部署中将请求转发至 IDC 内服务器的场景
	// 使用负载均衡绑定云联网打通的 IDC 内 IP 时，必须分配 SNAT IP。SNAT IP 是 VPC 的内网 IP
	if *loadBalancer.Forward == FORWARDTYPE {
		// 存量负载均衡在这里开启SnatPro，以支持GlobalRoute直连的能力
		// 1. 存量负载均衡需要使用GlobalRoute直连的能力 (GlobalRoute直连已不再要求开启SnatPro)
		// 2. 使用已有负载均衡的场景，需要跨地域接入
		if crossType == types.CrossType2_0 { // kateway todo 云联网+MCS 存在吗？
			// 1. MultiClusterService 只要显示声明跨地域方案即需要为负载均衡开启SNATPro
			// 2. Service在非本地域或非本VPC的情况下，也需要开启SNATPro
			if (service.ServiceType() == service_wrapper.MultiClusterService && crossTypeExist) ||
				(crossRegionIdExist && crossRegionId != config.Global.Region) || (crossVPCIdExist && crossVPCId != config.Global.VPCID) {
				if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro == false {
					request.SnatPro = common.BoolPtr(true)
					modifyed = true
				}
			}
		}

		if crossType == types.CrossType1_2 {
			if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro == false {
				request.SnatPro = common.BoolPtr(true)
				request.NoLBNat = common.BoolPtr(true)
				modifyed = true
			}
		}

		if hybridType == types.HybridTypePvgw || hybridType == types.HybridTypeCcn || crossType == types.CrossType1_1 {
			if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro == false {
				request.SnatPro = common.BoolPtr(true)
				modifyed = true
			}
			if snatProInfo == nil || snatProInfo.SnatIPs == nil || len(snatProInfo.SnatIPs) == 0 {
				if hybridType == types.HybridTypePvgw || hybridType == types.HybridTypeCcn {
					return types.NewError(errcode.HybridCloudWithoutSnatProSetting, "", utils.ServiceName(service))
				} else {
					return types.NewError(errcode.CrossRegionWithoutSnatProSetting, "", utils.ServiceName(service))
				}
			}
			addSnatIp, addSecondSnatIp, deleteSnatIp, deleteSecondSnatIp = diffSnatIps(snatProInfo, loadBalancer.SnatIps)
		}
	}
	// 负载均衡需要通过跨域绑定1.0的能力，进行跨域绑定。SNAT Pro和该能力冲突
	// kateway(bitliu[51]): 跨域的细节，MCS 如果使用 1.0 类型的跨域，需要指定目标地域的 region 和 vpc 信息
	// "service.cloud.tencent.com/target-cross-vpc-id"
	// "service.cloud.tencent.com/target-cross-region-id"
	// kateway todo 这里没有判断 loadBalancer.Forward, 不过文档里有说不支持
	if crossType == types.CrossType1_0 {
		targetCrossRegionID := config.Global.Region
		if targetRegionID, exist := utils.GetTargetCrossRegionID(service); exist {
			targetCrossRegionID = targetRegionID
		}
		targetCrossVPCId := config.Global.VPCID // kateway 这是单集群的默认值
		if targetVpcId, exist := utils.GetTargetCrossVPCId(service); exist {
			targetCrossVPCId = targetVpcId
		}
		if loadBalancer.TargetRegionInfo == nil ||
			loadBalancer.TargetRegionInfo.VpcId == nil || *loadBalancer.TargetRegionInfo.VpcId != targetCrossVPCId ||
			loadBalancer.TargetRegionInfo.Region == nil || *loadBalancer.TargetRegionInfo.Region != targetCrossRegionID {
			request.TargetRegionInfo = &clb.TargetRegionInfo{ // kateway: target 跨域的信息需要告诉CLB
				Region: common.StringPtr(targetCrossRegionID),
				VpcId:  common.StringPtr(targetCrossVPCId),
			}
			modifyed = true
			if loadBalancer.SnatPro == nil || *loadBalancer.SnatPro { // 考虑SNAT Pro对变更的影响
				innerRequest := clbInner.NewModifyLoadBalancerAttributesRequest()
				innerRequest.LoadBalancerId = loadBalancer.LoadBalancerId
				innerRequest.SnatPro = common.BoolPtr(false) // kateway 跨域绑定1.0 和 SNAT Pro 冲突
				if _, err := tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.New(service, region), innerRequest); err != nil {
					return types.NewError(errcode.SnatProNotSupportError, "", utils.ServiceName(service))
				}
			}
		}
	}
	if modifyed {
		// IPv6FullChain 资源开启SNATPro的同时，必须开启混绑能力。
		// Tips: "Code":"InvalidParameterValue","Message":"FullChain IPv6 CLB does not support to enable SnatPro before enable MixIpTarget","RequestId":"01c4db93-30bb-4114-872e-47f4d1bfafbb"
		if request.SnatPro != nil && *request.SnatPro == true {
			if loadBalancer.AddressIPVersion != nil && *loadBalancer.AddressIPVersion == "IPv6FullChain" && loadBalancer.MixIpTarget != nil && *loadBalancer.MixIpTarget == false {
				innerRequest := clb.NewModifyLoadBalancerMixIpTargetRequest()
				innerRequest.LoadBalancerIds = []*string{loadBalancer.LoadBalancerId}
				innerRequest.MixIpTarget = common.BoolPtr(true)
				if _, err := tencentapi.Instance.ModifyLoadBalancerMixIpTarget(cloudctx.New(service, region), innerRequest); err != nil {
					return types.NewError(errcode.MixIpTargetError, err.Error(), utils.ServiceName(service))
				}
			}
		}
		if _, err := tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.New(service, region), request); err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok { // Code=FailedOperation, Message=cross LoadBalancer is not supported snatPro
				if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "LoadBalancer is not supported snatPro") {
					return types.NewError(errcode.SnatProNotSupportForLoadbalancerError, sdkError.Error(), utils.ServiceName(service))
				}
			}
			return types.NewError(errcode.SnatProNotSupportError, "", utils.ServiceName(service))
		}
		if err := syncContext.LoadBalancerContext.UpdateLoadBalancer(); err != nil {
			return err
		}
	}

	if deleteSnatIp != nil && len(deleteSnatIp) != 0 {
		request := clb.NewDeleteLoadBalancerSnatIpsRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		request.Ips = deleteSnatIp
		if _, err := tencentapi.Instance.DeleteLoadBalancerSnatIps(cloudctx.New(service, region), request); err != nil {
			return err
		}
	}

	if addSnatIp != nil && len(addSnatIp) != 0 {
		request := clb.NewCreateLoadBalancerSnatIpsRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		request.SnatIps = addSnatIp
		if _, err := tencentapi.Instance.CreateLoadBalancerSnatIps(cloudctx.New(service, region), request); err != nil {
			return err
		}
	}

	if deleteSecondSnatIp != nil && len(deleteSecondSnatIp) != 0 {
		request := clb.NewDeleteLoadBalancerSnatIpsRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		request.Ips = deleteSecondSnatIp
		if _, err := tencentapi.Instance.DeleteLoadBalancerSnatIps(cloudctx.New(service, region), request); err != nil {
			return err
		}
	}

	if addSecondSnatIp != nil && len(addSecondSnatIp) != 0 {
		request := clb.NewCreateLoadBalancerSnatIpsRequest()
		request.LoadBalancerId = loadBalancer.LoadBalancerId
		request.SnatIps = addSecondSnatIp
		if _, err := tencentapi.Instance.CreateLoadBalancerSnatIps(cloudctx.New(service, region), request); err != nil {
			return err
		}
	}
	return nil
}

func diffSnatIps(snatProInfo *utils.SnatProInfo, snatIps []*clb.SnatIp) ([]*clb.SnatIp, []*clb.SnatIp, []*string, []*string) {
	subnetMap := make(map[string]bool)
	ipMap := make(map[string]map[string]bool)
	countMap := make(map[string]int)
	for _, snatIp := range snatProInfo.SnatIPs {
		subnetMap[snatIp.SubnetId] = true
		if snatIp.IP == nil {
			if _, exist := countMap[snatIp.SubnetId]; !exist {
				countMap[snatIp.SubnetId] = 0
			}
			countMap[snatIp.SubnetId] = countMap[snatIp.SubnetId] + 1
		} else {
			if _, exist := ipMap[snatIp.SubnetId]; !exist {
				ipMap[snatIp.SubnetId] = make(map[string]bool)
			}
			ipMap[snatIp.SubnetId][*snatIp.IP] = true
		}
	}

	addSnatIp := make([]*clb.SnatIp, 0)
	deleteSnatIp := make([]*string, 0)
	if snatIps != nil {
		for _, snatIp := range snatIps {
			if snatIp.SubnetId != nil && snatIp.Ip != nil {
				if _, exist := subnetMap[*snatIp.SubnetId]; !exist {
					deleteSnatIp = append(deleteSnatIp, common.StringPtr(*snatIp.Ip))
				}
			}
		}
	}

	for subnet, _ := range subnetMap {
		if snatIps != nil {
			for _, snatIp := range snatIps {
				if snatIp.SubnetId != nil && *snatIp.SubnetId == subnet && snatIp.Ip != nil {
					if snatIp.Ip != nil {
						if _, exist := ipMap[subnet]; exist {
							if _, exist := ipMap[subnet][*snatIp.Ip]; exist {
								delete(ipMap[subnet], *snatIp.Ip)
								continue
							}
						}
					}
					if count, exist := countMap[subnet]; exist {
						if count > 0 {
							countMap[subnet] = countMap[subnet] - 1
							continue
						}
					}
					deleteSnatIp = append(deleteSnatIp, common.StringPtr(*snatIp.Ip))
				}
			}
		}

		if _, exist := ipMap[subnet]; exist {
			for ip, _ := range ipMap[subnet] {
				addSnatIp = append(addSnatIp, &clb.SnatIp{SubnetId: common.StringPtr(subnet), Ip: common.StringPtr(ip)})
			}
		}

		if _, exist := countMap[subnet]; exist {
			for i := 0; i < countMap[subnet]; i++ {
				addSnatIp = append(addSnatIp, &clb.SnatIp{SubnetId: common.StringPtr(subnet)})
			}
		}
	}

	// 删除和添加的限制
	// 1. 删除SnatIP的时候，不能将全部SnatIP都删光。
	// 2. 添加SnatIP的时候，不能超过10个的数量限制。
	deleteFirstSnatIp := deleteSnatIp
	deleteSecondSnatIp := make([]*string, 0)
	if len(deleteSnatIp) != 0 && len(snatIps) == len(deleteSnatIp) {
		if len(deleteSnatIp) == 1 {
			deleteFirstSnatIp = make([]*string, 0)
		} else {
			deleteFirstSnatIp = deleteSnatIp[1:]
		}
		deleteSecondSnatIp = deleteSnatIp[0:1]
	}

	addFirstSnatIp := addSnatIp
	addSecondSnatIp := make([]*clb.SnatIp, 0)
	if len(addSnatIp) == 10 {
		addFirstSnatIp = addSnatIp[1:]
		addSecondSnatIp = addSnatIp[0:1]
	}

	return addFirstSnatIp, addSecondSnatIp, deleteFirstSnatIp, deleteSecondSnatIp
}

func syncPassToTarget(syncContext *SyncContext, loadBalancer *clb.LoadBalancer, region string) {
	service := syncContext.Service
	resource := syncContext.LoadBalancerContext.LoadBalancerResource
	if _, exist := utils.HasServicePassToTarget(service); exist {
		if loadBalancer.VpcId != nil && *loadBalancer.VpcId == "0" {
			// 基础网络负载均衡不支持默认放通，忽略该注解。
			return
		}

		if !resource.Spec.Created {
			syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.PassToTargetNotSupportError, "", utils.ServiceName(service)))
			return
		}

		passToTarget, err := utils.IsServicePassToTarget(service)
		if err != nil { // 已经在PreCheck流程记录Error
			return
		}

		if loadBalancer.LoadBalancerPassToTarget != nil && (*loadBalancer.LoadBalancerPassToTarget != passToTarget) {
			request := clbinternal.NewModifyLoadBalancerAttributesRequest()
			request.LoadBalancerId = loadBalancer.LoadBalancerId
			request.LoadBalancerPassToTarget = common.BoolPtr(passToTarget)
			if _, err := tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.New(service, region), request); err != nil {
				syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.PassToTargetNotSupportError, "", utils.ServiceName(service)))
			}
		}
	}
}

func syncSecurityGroups(syncContext *SyncContext, loadBalancer *clb.LoadBalancer, region string) {
	service := syncContext.Service
	resource := syncContext.LoadBalancerContext.LoadBalancerResource
	if _, exist := utils.HasServiceSecurityGroups(service); exist {
		if !resource.Spec.Created {
			syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.SecurityGroupsNotSupportError, "", utils.ServiceName(service)))
			return
		}

		securityGroups, err := utils.GetServiceSecurityGroups(service)
		if err != nil || securityGroups == nil { // 已经在PreCheck流程记录Error
			return
		}

		securityGroupsMap := make(map[string]bool)
		for _, securityGroup := range securityGroups {
			securityGroupsMap[securityGroup] = true
		}

		needToModifySecurityGroups := false
		if len(loadBalancer.SecureGroups) != len(securityGroups) {
			needToModifySecurityGroups = true
		} else {
			for _, secureGroup := range loadBalancer.SecureGroups {
				if _, exist := securityGroupsMap[*secureGroup]; !exist {
					needToModifySecurityGroups = true
					break
				}
			}
		}

		if needToModifySecurityGroups {
			securityGroupsRequest := clb.NewSetLoadBalancerSecurityGroupsRequest()
			securityGroupsRequest.LoadBalancerId = loadBalancer.LoadBalancerId
			if len(securityGroups) != 0 {
				securityGroupsRequest.SecurityGroups = common.StringPtrs(securityGroups)
			}
			if _, err := tencentapi.Instance.SetLoadBalancerSecurityGroups(cloudctx.New(service, region), securityGroupsRequest); err != nil {
				if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
					// [TencentCloudSDKError] Code=InvalidParameter.FormatError, Message=The format of SecurityGroupId '' is not valid.
					// [TencentCloudSDKError] Code=InvalidParameterValue.Length, Message=The length of SecurityGroupId 'sg-ocoafjc8ac' is not valid.
					// [TencentCloudSDKError] Code=InvalidParameterValue, Message=Check security groups failed, sg-ocoafjc9: USG doesn't exist
					if (sdkError.Code == "InvalidParameter.FormatError" || sdkError.Code == "InvalidParameterValue.Length") && strings.Contains(sdkError.Message, "SecurityGroupId") {
						syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.SecurityGroupsFormatError, sdkError.Error(), utils.ServiceName(service)))
						return
					} else if sdkError.Code == "InvalidParameterValue" && strings.Contains(sdkError.Message, "doesn't exist") {
						syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.SecurityGroupsNotExistError, sdkError.Error(), utils.ServiceName(service)))
						return
					}
				}
				syncContext.Errors = append(syncContext.Errors, err)
			}
		}
	}
}

func EnsureDeletionProtection(syncContext *SyncContext) error {
	if !syncContext.LoadBalancerContext.LoadBalancerResource.Spec.Created {
		return nil
	}
	service := syncContext.Service
	enabled := service.DeletionProtection()
	if enabled == nil {
		return nil
	}

	return modifyDeletionProtection(syncContext.LoadBalancerContext, *enabled)
}

func modifyDeletionProtection(lbc *LoadBalancerContext, enable bool) error {
	loadbalancer, err := lbc.GetLoadBalancer()
	if err != nil {
		return err
	}
	lb := types.NewLB(loadbalancer, lbc.LoadBalancerResource.Spec.Created)
	if lb.IsDeletionProtectionEnabled() == enable {
		return nil
	}
	req := clbinternal.NewModifyLoadBalancerAttributesRequest()
	req.LoadBalancerId = lb.LoadBalancerId
	req.DeleteProtect = &enable
	_, err = tencentapi.Instance.ModifyLoadBalancerAttributes(cloudctx.New(lbc.Service, lbc.Region), req)
	if err != nil {
		return err
	}
	return lbc.UpdateLoadBalancer()
}

// 确保修改保护信息
func EnsureModificationProtection(syncContext *SyncContext) {
	service := syncContext.Service
	modificationProtection, err := utils.IsModificationProtection(service)
	if err != nil { // 已经在PreCheck流程记录Error
		return
	}

	region := syncContext.LoadBalancerContext.Region
	resource := syncContext.LoadBalancerContext.LoadBalancerResource
	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		syncContext.Errors = append(syncContext.Errors, err)
		return
	}
	currentModificationProtection := false
	if loadBalancer.AttributeFlags != nil && len(loadBalancer.AttributeFlags) != 0 {
		for _, attributeFlag := range loadBalancer.AttributeFlags {
			if attributeFlag != nil && *attributeFlag == "OperateProtect" {
				currentModificationProtection = true
			}
		}
	}
	if modificationProtection != currentModificationProtection {
		if !resource.Spec.Created { // 使用已有与该功能相冲突
			syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ReuseNotSupportModificationProtectionError, "", utils.ServiceName(service)))
			return
		}

		if (modificationProtection == true && currentModificationProtection == false) || (modificationProtection == false && currentModificationProtection == true) {
			operateProtectRequest := clbInner.NewModifyLBOperateProtectRequest()
			operateProtectRequest.LoadBalancerId = loadBalancer.LoadBalancerId
			operateProtectRequest.OperateProtect = common.BoolPtr(modificationProtection)
			operateProtectRequest.RoleName = common.StringPtr("TKE_QCSRole")
			if modificationProtection {
				kv := make(map[string]string)
				kv["product"] = "tke"
				kv["clusterType"] = "tke"
				if env.IsInEKSCluster() {
					kv["clusterType"] = "eks"
				}
				kv["resourceType"] = strings.ToLower(string(service.ServiceType()))
				kv["regionName"] = config.Global.Region
				kv["clusterId"] = config.Global.ClusterName
				kv["namespace"] = service.GetObjectMeta().GetNamespace()
				if service.GetObjectMeta().GetNamespace() == "" {
					kv["namespace"] = "default"
				}
				kv["name"] = service.GetObjectMeta().GetName()

				kvJson, err := json.Marshal(kv)
				if err != nil {
					klog.Errorf("Unexpected error for %s. %v", utils.ServiceName(service), err)
					return
				}
				operateProtectRequest.LinkInfo = common.StringPtr(string(kvJson))
				operateProtectRequest.AllowOperate = common.BoolPtr(true)
			}
			if _, err = tencentapi.Instance.ModifyLBOperateProtect(cloudctx.New(service, region), operateProtectRequest); err != nil {
				if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
					if sdkError.Code == "FailedOperation" {
						if strings.Contains(sdkError.Message, "operate protect") && strings.Contains(sdkError.Message, "don't support") {
							syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ModificationProtectionDoNotSupportError, sdkError.Error(), utils.ServiceName(service)))
						} else if strings.Contains(sdkError.Message, "operate protect") && strings.Contains(sdkError.Message, "cannot modify") { // LoadBalancer lb-fa9iifa6 set operate protect by uin 100011044977, you cannot modify it
							syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ModificationProtectionConflictError, sdkError.Error(), utils.ServiceName(service)))
						}
					}
				}
				syncContext.Errors = append(syncContext.Errors, err)
			}
		}
	}
}
