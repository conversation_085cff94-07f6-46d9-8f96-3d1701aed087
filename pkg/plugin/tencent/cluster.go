package tencent

import (
	"errors"
	"strings"

	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"

	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/tencent/cloudctx"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/utils"
)

func GetClusterType(clusterId string) (string, error) {
	// 非cls-开头的集群，默认独立集群，独立部署时使用
	if !strings.HasPrefix(clusterId, "cls-") {
		return "INDEPENDENT_CLUSTER", nil
	}

	if utils.IsInEKSCluster() {
		return "MANAGED_CLUSTER", nil
	}
	request := tke.NewDescribeClustersRequest()
	request.ClusterIds = []*string{&clusterId}
	response, err := tencentapi.Instance.DescribeClusters(cloudctx.New(nil, config.Global.Region), request)
	if err != nil {
		return "", err
	}
	if len(response.Response.Clusters) == 0 {
		return "", errors.New("cluster not found")
	}
	return *response.Response.Clusters[0].ClusterType, nil
}
