package tencent

import (
	"context"

	gclone "github.com/huandu/go-clone/generic"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"k8s.io/apimachinery/pkg/util/intstr"

	loadBalancerResourcev1alpha1 "git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	"git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"

	"cloud.tencent.com/lb-controller/pkg/utils"
)

type SyncContext struct {
	Service             service_wrapper.ServiceWrapper
	LoadBalancerContext *LoadBalancerContext
	ServiceContext      *ServiceContext // kateway 这个是根据 service 解析出来的期望的监听器信息
	Errors              []error
}

func BuildSyncContext(service service_wrapper.ServiceWrapper) *SyncContext {
	syncContext := &SyncContext{
		Service: service,
		Errors:  make([]error, 0),
	}

	// // Add annotation into in-memory object 'service'
	// if utils.IsClusterIPAndNeedsCLB(service) {
	//	if service.Annotations == nil {
	//		service.Annotations = make(map[string]string)
	//	}
	//	if _, hasInternalSubnet := service.GetObjectMeta().GetAnnotations()[utils.AnnoServiceLBInternalUniqSubnetID]; !hasInternalSubnet {
	//		service.Annotations[utils.AnnoServiceLBInternalUniqSubnetID] = utils.GlobalConfigs.ClusterIPSubnet
	//		syncContext.SubnetIDAppended = true
	//	}
	// }
	return syncContext
}

type LoadBalancerContext struct {
	Service              service_wrapper.ServiceWrapper
	LoadBalancerResource *loadBalancerResourcev1alpha1.LoadBalancerResource

	Region         string
	LoadBalancerId string
	DefaultDomain  *string
	Forward        bool
	LoadBalancer   *clb.LoadBalancer // kateway CLB 信息
	// Rewrites           []*clb.RuleOutput
	Listeners          map[string]*clb.Listener
	ClassicalListeners map[string]*clb.ClassicalListener
	ListenersBackends  map[string]*clb.ListenerBackend // kateway 监听器key => 监听器结构（包括rules/targets）
}

// kateway: 按需将 CLB 真实属性同步过来, todo 重命名 GetClb
func (this *LoadBalancerContext) GetLoadBalancer() (*clb.LoadBalancer, error) {
	if this.LoadBalancer == nil {
		if err := this.UpdateLoadBalancer(); err != nil {
			return nil, err
		}
	}
	if this.LoadBalancer == nil {
		return nil, types.NewError(errcode.LoadBalancerNotExistError, "", utils.ServiceName(this.Service))
	}
	return this.LoadBalancer, nil
}

// kateway: 将 CLB 真实属性同步过来
func (this *LoadBalancerContext) UpdateLoadBalancer() error {
	request := clb.NewDescribeLoadBalancersRequest()
	request.LoadBalancerIds = []*string{&this.LoadBalancerId}
	ctx := cloudctx.WithRegion(context.Background(), this.Region)
	ctx = cloudctx.WithObject(ctx, this.Service)
	response, err := tencentapi.Instance.DescribeLoadBalancers(ctx, request)
	if err != nil {
		return err
	}
	if response.Response.TotalCount != nil && *response.Response.TotalCount != 0 && len(response.Response.LoadBalancerSet) != 0 {
		this.LoadBalancer = response.Response.LoadBalancerSet[0]
		this.Forward = *this.LoadBalancer.Forward == 1
		this.DefaultDomain = utils.GetDefaultDomain(this.LoadBalancer)

		// 负载均衡的标签不保证实时性、准确性，必须以标签服务的数据为准
		tags, err := GetLoadBalancerTags(this.Service, this.Region, this.LoadBalancerId)
		if err != nil {
			return err
		}
		this.LoadBalancer.Tags = utils.ConvertResourceTag(tags)
	}
	return nil
}

func (this *LoadBalancerContext) UpdateLoadBalancerTag() error {
	if this.LoadBalancer == nil {
		return this.UpdateLoadBalancer()
	}

	// 负载均衡的标签不保证实时性、准确性，必须以标签服务的数据为准
	tags, err := GetLoadBalancerTags(this.Service, this.Region, this.LoadBalancerId)
	if err != nil {
		return err
	}
	this.LoadBalancer.Tags = utils.ConvertResourceTag(tags)
	return nil
}

func (this *LoadBalancerContext) GetMixedListeners() (map[string]*MixedListener, error) {
	if this.LoadBalancer == nil {
		if err := this.UpdateLoadBalancer(); err != nil {
			return nil, err
		}
	}
	result := make(map[string]*MixedListener)
	if *this.LoadBalancer.Forward == 1 {
		listeners, err := this.GetListeners()
		if err != nil {
			return nil, err
		}
		for key, value := range listeners {
			result[key] = &MixedListener{Forward: true, Listener: value}
		}
	} else {
		listeners, err := this.GetClassicalListeners()
		if err != nil {
			return nil, err
		}
		for key, value := range listeners {
			result[key] = &MixedListener{Forward: false, ClassicalListener: value}
		}
	}
	return result, nil
}

func (this *LoadBalancerContext) GetListeners() (map[string]*clb.Listener, error) {
	if this.Listeners == nil {
		if err := this.UpdateListeners(); err != nil {
			return nil, err
		}
	}
	return this.Listeners, nil
}

func (this *LoadBalancerContext) UpdateListeners() error {
	listeners, err := GetLoadBalancerListener(this.Service, this.Region, this.LoadBalancerId)
	if err != nil {
		return err
	}
	this.Listeners = make(map[string]*clb.Listener)
	for index, listener := range listeners {
		this.Listeners[utils.GetListenerKey(*listener.Port, *listener.Protocol)] = listeners[index]
	}
	return nil
}

func (this *LoadBalancerContext) GetClassicalListeners() (map[string]*clb.ClassicalListener, error) {
	if this.ClassicalListeners == nil {
		if err := this.UpdateClassicalListeners(); err != nil {
			return nil, err
		}
	}
	return this.ClassicalListeners, nil
}

func (this *LoadBalancerContext) UpdateClassicalListeners() error {
	listeners, err := GetClassicLoadBalancerListener(this.Service, this.Region, this.LoadBalancerId)
	if err != nil {
		return err
	}
	this.ClassicalListeners = make(map[string]*clb.ClassicalListener)
	for index, listener := range listeners {
		this.ClassicalListeners[utils.GetClassicalListenerKey(*listener.ListenerPort, *listener.InstancePort, *listener.Protocol)] = listeners[index]
	}
	return nil
}

func (this *LoadBalancerContext) GetListenersBackend() (map[string]*clb.ListenerBackend, error) {
	if this.ListenersBackends == nil {
		if err := this.UpdateListenersBackend(); err != nil {
			return nil, err
		}
	}
	return this.ListenersBackends, nil
}

func (this *LoadBalancerContext) UpdateListenersBackend() error { // kateway: 用远程来更新内存rs, todo 重命名
	listenersBackends, err := GetLoadBalancerTargets(this.Service, this.Region, this.LoadBalancerId)
	if err != nil {
		return err
	}
	this.ListenersBackends = make(map[string]*clb.ListenerBackend)
	for index, listenerBackend := range listenersBackends {
		this.ListenersBackends[utils.GetListenerKey(*listenerBackend.Port, *listenerBackend.Protocol)] = listenersBackends[index]
	}
	return nil
}

func (this *LoadBalancerContext) UpdateLoadBalancerResource() error {
	loadbalancerId := this.LoadBalancerResource.Name
	loadBalancerResource, err := cluster.Instance.LoadBalancerResourceLister().Get(loadbalancerId)
	if err != nil {
		return err
	}
	this.LoadBalancerResource = loadBalancerResource
	return nil
}

// func (this *LoadBalancerContext) GetRewrites() ([]*clb.RuleOutput, error) {
//	if this.Rewrites == nil {
//		if err := this.UpdateRewrites(); err != nil {
//			return nil, err
//		}
//	}
//	return this.Rewrites, nil
// }
//
// func (this *LoadBalancerContext) UpdateRewrites() error {
//	rewrites, err := DescribeRewrite(nil, this.LoadBalancerId)
//	if err != nil {
//		return err
//	}
//	this.Rewrites = rewrites
//	return nil
// }

// kateway todo 重命名
type ServiceContext struct {
	// Classical Key: getClassicListenerKey
	// Normal Key: getListenerKey
	ServiceListeners map[string]*ServiceListener
}

type ServiceListener struct {
	Name       string
	Port       int64
	NodePort   int32
	TargetPort intstr.IntOrString

	// 在同步监听器时，若tsc中没有配置会话保持，是否主动使用service上的会话保持配置
	// 详细背景：https://tapd.woa.com/tapd_fe/70108010/bug/detail/1070108010129895267
	SessionAffinityFallback bool

	ListenerConfig // 根据这个结构是否相等，来判断各个监听器配置是否相同
}

type ListenerConfig struct {
	Protocol         string
	L4ListenerConfig *v1alpha1.L4ListenerConfig
	TlsCert          *types.TLSConfig
	L7ListenerConfig *v1alpha1.L7ListenerConfig
	TlsSniMap        map[string]*DomainConfig
	SessionAffinity  int64
}

func (l ServiceListener) GetDomain(host string) *types.Domain {
	cfg, exists := l.TlsSniMap[host]
	if !exists {
		return nil
	}
	d := &types.Domain{Host: host}
	if listenerCfg := l.L7ListenerConfig; listenerCfg != nil {
		d.Default = lo.FromPtr(listenerCfg.DefaultServer) != "" && *listenerCfg.DefaultServer == host
	}
	if certs := cfg.TlsCert; l.Protocol == "HTTPS" && certs != nil {
		d.TLSConfig = gclone.Clone(certs)
	}
	if domainCfg := cfg.L7DomainConfig; domainCfg != nil {
		d.HTTP2Enabled = domainCfg.Http2
	}
	return d
}

type DomainConfig struct {
	TlsCert        *types.TLSConfig
	L7DomainConfig *v1alpha1.L7DomainConfig
	RuleMap        map[string]*RuleConfig
}

type RuleConfig struct {
	L7RuleConfig *v1alpha1.L7RuleConfig
}

type MixedListener struct {
	Forward           bool
	Listener          *clb.Listener
	ClassicalListener *clb.ClassicalListener
}

func (this *MixedListener) GetListenerId() string {
	if this.Forward {
		return *this.Listener.ListenerId
	} else {
		return *this.ClassicalListener.ListenerId
	}
}

func (this *MixedListener) GetListenerName() string {
	if this.Forward {
		return *this.Listener.ListenerName
	} else {
		return *this.ClassicalListener.ListenerName
	}
}

func (this *MixedListener) GetProtocol() string {
	if this.Forward {
		return *this.Listener.Protocol
	} else {
		return *this.ClassicalListener.Protocol
	}
}

func (this *MixedListener) GetListenerPort() int64 {
	if this.Forward {
		return *this.Listener.Port
	} else {
		return *this.ClassicalListener.ListenerPort
	}
}

func (this *MixedListener) GetKeepaliveEnable() int64 {
	if this.Forward {
		if this.Listener.KeepaliveEnable != nil {
			return *this.Listener.KeepaliveEnable
		}
	}
	return 0
}
