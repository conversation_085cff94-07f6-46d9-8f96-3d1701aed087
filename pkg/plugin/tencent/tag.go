package tencent

import (
	"context"
	"fmt"
	"strings"

	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	"k8s.io/apimachinery/pkg/util/sets"
	glog "k8s.io/klog/v2"

	"git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/tencent/cloudctx"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/utils"
)

// clear tags associated with service, only call when delete exist type lb
func EnsureLoadBalancerTagsDeleted(loadBalancerContext *LoadBalancerContext) error {
	service := loadBalancerContext.Service
	region := loadBalancerContext.Region
	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	serviceUUID := string(service.GetObjectMeta().GetUID())
	tkeListenerTagPerfix := getTagPrefix(serviceUUID)

	deleteTagKey := make(sets.String)
	if service.ServiceType() == service_wrapper.CoreService {
		if utils.IsInEKSCluster() {
			deleteTagKey.Insert(serviceUUID)
		} else {
			deleteTagKey.Insert(types.TagKeyServiceUUIDOld.String())
			deleteTagKey.Insert(serviceUUID)
		}
	} else if service.ServiceType() == service_wrapper.MultiClusterService {
		deleteTagKey.Insert(types.TagKeyMultiClusterServiceUUID.String())
	}
	_, exclusiveUsed := getObjectsUsingTheSameLB(service, loadBalancerContext.LoadBalancerResource)

	deleteTags := make([]*tag.TagResource, 0)
	for _, lbTag := range loadBalancer.Tags {
		if lbTag.TagKey != nil && (strings.HasPrefix(*lbTag.TagKey, tkeListenerTagPerfix) || deleteTagKey.Has(*lbTag.TagKey)) {
			deleteTags = append(deleteTags, utils.NewTagResource(*lbTag.TagKey, *lbTag.TagValue))
		}
		if exclusiveUsed {
			if *lbTag.TagKey == types.TagKeyClusterID.String() && *lbTag.TagValue == config.Global.ClusterName {
				deleteTags = append(deleteTags, types.TagKeyClusterID.ToTagResource(*lbTag.TagValue))
			}
			if *lbTag.TagKey == types.TagKeyLifecycleOwner.String() {
				deleteTags = append(deleteTags, types.TagKeyLifecycleOwner.ToTagResource(*lbTag.TagValue))
			}
			if *lbTag.TagKey == types.TagKeyAutoCreated.String() || *lbTag.TagKey == types.TagKeyAutoCreatedInEKS.String() {
				deleteTags = append(deleteTags, utils.NewTagResource(*lbTag.TagKey, *lbTag.TagValue))
			}
		}
	}
	if len(deleteTags) == 0 {
		return nil
	}
	return ModifyAndRecycleLoadBalancerTags(service, region, *loadBalancer.LoadBalancerId, nil, deleteTags)
}

// ensureLoadBalancerTags ensure tags created for service
//
// tke-clusterId
// tke-lb-serviceuuid\tke-lb-serviceuuid
func EnsureLoadBalancerTags(lbc *LoadBalancerContext) error {
	service := lbc.Service
	region := lbc.Region
	lb, err := lbc.GetLoadBalancer()
	if err != nil {
		return err
	}

	// if utils.IsInEKSCluster() {
	//	if _, useExisted := utils.GetExistLB(service); !useExisted {
	//		return nil // Currently, EKS only re-add tags for when using existed LB
	//	}
	// }
	// kateway(bitliu[33])：获取 MCS 期望的标签
	desiredLbTags, err := GetDesiredTKEServiceTags(service, lbc)
	if err != nil {
		return err
	}
	currentLbTags, err := GetCurrentTKEServiceTags(service, lb)
	if err != nil {
		return err
	}

	toAddTags, toDelTags := tagDifference(currentLbTags, desiredLbTags)
	if len(toAddTags) != 0 || len(toDelTags) != 0 {
		glog.Infof("EnsureLoadBalancerTags for svc: %s/%s exist: %s target: %s add: %s del: %s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), utils.JsonWrapper(currentLbTags), utils.JsonWrapper(desiredLbTags), utils.JsonWrapper(toAddTags), utils.JsonWrapper(toDelTags))
		if err := ModifyAndRecycleLoadBalancerTags(service, region, *lb.LoadBalancerId, toAddTags, toDelTags); err != nil {
			glog.Errorf("ensureLoadBalancerTags for svc: %s/%s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
			return err
		}

		if err := lbc.UpdateLoadBalancerTag(); err != nil {
			return err
		}
	}
	return nil
}

// getCurrentTKEServiceTags get lb tags belong to tke,only lb level,not include listener tags
func GetCurrentTKEServiceTags(service service_wrapper.ServiceWrapper, loadBalancer *clb.LoadBalancer) ([]*tag.TagResource, error) {
	serviceUUID := string(service.GetObjectMeta().GetUID())
	tkeTags := make([]*tag.TagResource, 0)

	canReuse := true
	if service.ServiceType() == service_wrapper.CoreService && utils.IsInEKSCluster() {
		value, has := service.GetObjectMeta().GetAnnotations()[_serviceShareExistedLB]
		canReuse = has && value == "true"
	}
	for _, lbTag := range loadBalancer.Tags {
		if lbTag.TagKey == nil || *lbTag.TagKey == "" || lbTag.TagValue == nil || *lbTag.TagValue == "" { // 存在空值不是TKE Service标签，略过
			continue
		}

		key := *lbTag.TagKey
		value := *lbTag.TagValue

		if key == GetAutoCreatedTagKey(service) {
			tkeTags = append(tkeTags, utils.NewTagResource(key, value))
		}

		// need to only add tke related tags
		if service.ServiceType() == service_wrapper.CoreService && utils.IsInEKSCluster() {
			if key == types.TagKeyClusterID.String() || key == types.TagKeyObjectKind.String() || key == types.TagKeyObjectName.String() {
				tkeTags = append(tkeTags, utils.NewTagResource(key, value))
			} else {
				if strings.HasPrefix(*lbTag.TagValue, types.TagKeyClusterIDPrefix) {
					if *lbTag.TagKey == serviceUUID {
						tkeTags = append(tkeTags, utils.NewTagResource(key, value))
					} else if !canReuse { // kateway todo 待确认
						glog.Errorf("Found reuse lb: %s exist but reuse flag not enabled", *loadBalancer.LoadBalancerId)
						return nil, types.NewError(errcode.ReuseFeatureNotSupportError, "")
					}
				}
			}
			continue
		}

		if key == types.TagKeyServiceUUIDOld.String() || key == types.TagKeyMultiClusterServiceUUID.String() ||
			key == types.TagKeyClusterID.String() || key == types.TagKeyLifecycleOwner.String() {
			tkeTags = append(tkeTags, utils.NewTagResource(key, value))
		} else if value == types.TagKeyServiceUUID.String() && key == serviceUUID { // 复用型LB的标签，且被当前service使用
			tkeTags = append(tkeTags, utils.NewTagResource(key, value))
		}
	}
	return tkeTags, nil
}

func buildDefaultCreateTKEServiceTags(svc service_wrapper.ServiceWrapper) []*tag.TagResource {
	tags := []*tag.TagResource{
		types.TagKeyClusterID.ToTagResource(config.Global.ClusterName),
		types.TagKey(GetAutoCreatedTagKey(svc)).ToTagResource(GetAutoCreatedTagValue()),
		types.TagKeyLifecycleOwner.ToTagResource(types.TagValueLifecycleOwnedByTKE),
	}
	if svc.ServiceType() == service_wrapper.CoreService {
		if utils.IsInEKSCluster() {
			tags = append(tags, types.TagKeyObjectKind.ToTagResource(strings.ToLower(string(svc.ServiceType()))))
			tags = append(tags, types.TagKeyObjectName.ToTagResource(fmt.Sprintf("%s/%s", svc.GetObjectMeta().GetNamespace(), svc.GetObjectMeta().GetName())))
		} else {
			tags = append(tags, types.TagKeyServiceUUIDOld.ToTagResource(string(svc.GetObjectMeta().GetUID())))
		}
	} else if svc.ServiceType() == service_wrapper.MultiClusterService {
		tags = append(tags, types.TagKeyMultiClusterServiceUUID.ToTagResource(string(svc.GetObjectMeta().GetUID()))) // ServiceTag
	}
	return tags
}

// getDesiredTKEServiceTags get create lb tags.
func GetCreateTKEServiceTags(service service_wrapper.ServiceWrapper) ([]*tag.TagResource, error) {
	// add cluster tags
	clusterTags, err := GetTKEClusterTags(service, config.Global.ClusterName)
	if err != nil {
		return nil, err
	}

	return append(clusterTags, buildDefaultCreateTKEServiceTags(service)...), nil
}

// getDesiredTKEClusterAndServiceTags get descired service and cluster tags
// func GetDesiredTKEClusterAndServiceTags(service service_wrapper.ServiceWrapper, addClusterTagFlag bool) ([]*tag.TagResource, error) {
//	tags := GetDesiredTKEServiceTags(service)
//
//	if addClusterTagFlag {
//		// cluster tag
//		clusterTags, err := GetTkeClusterTags(service, ClusterName)
//		if err != nil {
//			return nil, err
//		}
//		if len(clusterTags) > 0 {
//			tags = append(tags, clusterTags...)
//		}
//	}
//	return tags, nil
// }

// getDesiredTKEServiceTags get desired lb tags
func GetDesiredTKEServiceTags(service service_wrapper.ServiceWrapper, lbc *LoadBalancerContext) ([]*tag.TagResource, error) {
	var (
		resource = lbc.LoadBalancerResource
	)
	lb, err := lbc.GetLoadBalancer()
	if err != nil {
		return nil, err
	}
	owner := services.LBService{}.DetermineLifecycleOwnerValue(context.TODO(), service.GetObjectMeta(),
		types.NewLB(lb, resource.Spec.Created))
	tags := []*tag.TagResource{
		types.TagKeyClusterID.ToTagResource(config.Global.ClusterName),
		types.TagKeyLifecycleOwner.ToTagResource(owner),
	}
	if resource.Spec.Created {
		// kateway(bitliu[34]): 自动创建的 MCS 会有 "tke-createdBy-flag" 标签
		tags = append(tags, types.TagKey(GetAutoCreatedTagKey(service)).ToTagResource(GetAutoCreatedTagValue()))
		if service.ServiceType() == service_wrapper.CoreService {
			if utils.IsInEKSCluster() {
				tags = append(tags, types.TagKeyObjectKind.ToTagResource(strings.ToLower(string(service.ServiceType()))))
				tags = append(tags, types.TagKeyObjectName.ToTagResource(fmt.Sprintf("%s/%s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName())))
			} else {
				tags = append(tags, types.TagKeyServiceUUIDOld.ToTagResource(string(service.GetObjectMeta().GetUID())))
			}
		} else if service.ServiceType() == service_wrapper.MultiClusterService {
			// kateway(bitliu[35]): 自动创建的 MCS 会有 ""tke-lb-multi-cluster-service-uuid"" 标签
			tags = append(tags, types.TagKeyMultiClusterServiceUUID.ToTagResource(string(service.GetObjectMeta().GetUID()))) // ServiceTag
		}
	}

	return tags, nil
}

func ModifyAndRecycleLoadBalancerTags(service service_wrapper.ServiceWrapper, region string, loadbalancerId string, replaceTagResources []*tag.TagResource, deleteTagResources []*tag.TagResource) error {
	replaceTags := utils.ConvertTagList(replaceTagResources)
	deleteTags := utils.ConvertTagKeyObjectList(deleteTagResources)
	if err := ModifyLoadBalancerTags(service, region, loadbalancerId, replaceTags, deleteTags); err != nil {
		return err
	}
	if len(deleteTagResources) > 0 {
		if err := RecycleLoadbalancerTags(service, deleteTagResources); err != nil {
			glog.Errorf("Fail to recycle tags for service: %s/%s request: %s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), utils.JsonWrapper(deleteTagResources), err)
		}
	}
	return nil
}

// recycleLoadbalancerTags delete tags after lb deleted
func RecycleLoadbalancerTags(service service_wrapper.ServiceWrapper, tags []*tag.TagResource) error {
	skipTagKeys := sets.NewString(
		GetAutoCreatedTagKey(service),
		types.TagKeyClusterID.String(),
		types.TagKeyObjectKind.String(),
		types.TagKeyLifecycleOwner.String(),
	)
	for _, lbTag := range tags {
		if lbTag.TagKey == nil || *lbTag.TagKey == "" || lbTag.TagValue == nil || *lbTag.TagValue == "" {
			continue
		}
		if skipTagKeys.Has(*lbTag.TagKey) {
			continue
		}

		if err := DeleteTags(service, *lbTag.TagKey, *lbTag.TagValue); err != nil {
			if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
				if sdkError.Code == "FailedOperation.TagAttachedResource" {
					continue
				}
			}
			glog.Errorf("recycleLoadbalancerTags deleteTag for service: %s/%s key: %s value: %s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *lbTag.TagKey, *lbTag.TagValue, err)
		}
	}
	return nil
}

// ensureLoadBalancerListenerTags
func EnsureLoadBalancerListenerTags(syncContext *SyncContext) error {
	service := syncContext.Service
	region := syncContext.LoadBalancerContext.Region
	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	fromTags, err := GetTKEListenerTags(service, loadBalancer.Tags)
	if err != nil {
		return err
	}

	toTags, err := GetDesiredListenerTags(syncContext)
	if err != nil {
		return err
	}

	toAddTags, toDelTags := tagDifference(fromTags, toTags)
	if len(toAddTags) != 0 || len(toDelTags) != 0 {
		glog.Infof("ensureLoadBalancerListenerTags for svc: %s/%s lb: %s exist: %s target: %s add: %s del: %s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, utils.JsonWrapper(fromTags), utils.JsonWrapper(toTags), utils.JsonWrapper(toAddTags), utils.JsonWrapper(toDelTags))

		if err := ModifyAndRecycleLoadBalancerTags(service, region, *loadBalancer.LoadBalancerId, toAddTags, toDelTags); err != nil {
			glog.Errorf("ensureLoadBalancerListenerTags for svc: %s/%s lb: %s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, err)
			return err
		}
		if err := syncContext.LoadBalancerContext.UpdateLoadBalancer(); err != nil {
			return err
		}
	}
	return nil
}

func GetDesiredListenerTags(syncContext *SyncContext) ([]*tag.TagResource, error) {
	// service := syncContext.Service
	// loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	// if err != nil {
	//	return nil, err
	// }
	// listeners := syncContext.ServiceContext.ServiceListeners
	//
	// toTags := make([]*tag.TagResource, 0)
	// if utils.IsInEKSCluster() {
	//	if _, exist := utils.GetExistLB(service); exist {
	//		lbListeners, err := syncContext.LoadBalancerContext.GetListeners()
	//		if err != nil {
	//			return nil, err
	//		}
	//		listenerTagPrefix := getTagPrefix(string(service.GetObjectMeta().GetUID()))
	//		for _, lbListener := range lbListeners {
	//			listenerKey := getListenerKey(*lbListener.Port, *lbListener.Protocol)
	//			if _, exist := listeners[listenerKey]; exist {
	//				listenerId := *lbListener.ListenerId
	//				toTags = append(toTags, utils.NewTagResource(fmt.Sprintf("%s%s", listenerTagPrefix, listenerId), listenerId))
	//			}
	//		}
	//	}
	//	return toTags, nil
	// }
	// if _, exist := utils.GetExistLB(service); exist && utils.GlobalConfigs.ReuseFlag {
	//	listenerTagPrefix := getTagPrefix(string(service.GetObjectMeta().GetUID()))
	//	//if utils.GetLoadbalancerType(service) == CLASSICTYPE { //classic
	//	if *loadBalancer.Forward == CLASSICTYPE { //classic
	//		lbListeners, err := syncContext.LoadBalancerContext.GetClassicalListeners()
	//		if err != nil {
	//			return nil, err
	//		}
	//		for _, lbListener := range lbListeners {
	//			listenerKey := getClassicListenerKey(*lbListener.ListenerPort, *lbListener.InstancePort, *lbListener.Protocol)
	//			// if multi service resue the same exist loadBalancer and port conflict?
	//			if _, exist := listeners[listenerKey]; exist {
	//				listenerId := *lbListener.ListenerId
	//				toTags = append(toTags, utils.NewTagResource(fmt.Sprintf("%s%s", listenerTagPrefix, listenerId), listenerId))
	//			}
	//		}
	//	} else {
	//		lbListeners, err := syncContext.LoadBalancerContext.GetListeners()
	//		if err != nil {
	//			return nil, err
	//		}
	//		for _, lbListener := range lbListeners {
	//			listenerKey := getListenerKey(*lbListener.Port, *lbListener.Protocol)
	//			// if multi service resue the same exist loadBalancer and port conflict?
	//			if _, exist := listeners[listenerKey]; exist {
	//				listenerId := *lbListener.ListenerId
	//				toTags = append(toTags, utils.NewTagResource(fmt.Sprintf("%s%s", listenerTagPrefix, listenerId), listenerId))
	//			}
	//		}
	//	}
	// }
	return []*tag.TagResource{}, nil
}

// describeTag describe tag key/value
// func DescribeTag(service service_wrapper.ServiceWrapper, tagKey string, tagValue string) (*tag.TagWithDelete, error) {
//	request := tag.NewDescribeTagsRequest()
//	request.TagKey = &tagKey
//	request.TagValue = &tagValue
//	request.Limit = common.Uint64Ptr(TAGSPERPAGE)
//	request.Offset = common.Uint64Ptr(0)
//
//	response, err := tencentapi.TencentAPIServiceInstance.DescribeTags(cloudctx.New(service, region), request)
//	if err != nil {
//		return nil, err
//	}
//	if response.Response.Tags != nil && len(response.Response.Tags) != 0 {
//		return response.Response.Tags[0], nil
//	}
//	return nil, nil
// }

// deleteTag delete tag
func DeleteTags(service service_wrapper.ServiceWrapper, tagKey string, tagValue string) error {
	request := tag.NewDeleteTagRequest()
	request.TagKey = &tagKey
	request.TagValue = &tagValue

	response, err := tencentapi.Instance.DeleteTag(cloudctx.New(service, config.Global.Region), request)
	if err != nil {
		return nil
	}

	glog.Infof("%s: %s/%s DeleteTag request: %s response: %s", service.ServiceType(), service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), utils.JsonWrapper(request), utils.JsonWrapper(response))
	return nil
}

func GetTKEListenerTags(service service_wrapper.ServiceWrapper, lbTags []*clb.TagInfo) ([]*tag.TagResource, error) {
	serviceUUID := string(service.GetObjectMeta().GetUID())

	tkeTags := make([]*tag.TagResource, 0)
	listenerTagPrefix := getTagPrefix(serviceUUID)
	for _, lbTag := range lbTags {
		if lbTag.TagKey == nil || lbTag.TagValue == nil {
			continue
		}
		// need to only add tke related tags
		if strings.HasPrefix(*lbTag.TagKey, listenerTagPrefix) {
			tkeTags = append(tkeTags, utils.NewTagResource(*lbTag.TagKey, *lbTag.TagValue))
		}
	}
	return tkeTags, nil
}

func GetTKEClusterTags(service service_wrapper.ServiceWrapper, clusterId string) ([]*tag.TagResource, error) {
	return GetResourceTags(service, config.Global.Region, "ccs", "cluster", []string{clusterId})
}

func GetLoadBalancerTags(service service_wrapper.ServiceWrapper, region string, lbId string) ([]*tag.TagResource, error) {
	return GetResourceTags(service, region, "clb", "clb", []string{lbId})
}

// 根据ServiceUUID，查找对应的LoadBalancer
// 标签在复用情况下倒置，优先查找倒置标签，然后查找旧版本标签
func GetLoadBalancerByTags(service service_wrapper.ServiceWrapper) (*LoadBalancerContext, string, error) {
	resourceTags, err := GetResourceTagsByService(service)
	if err != nil {
		return nil, "", err
	}

	lbIds := make([]string, 0)
	if len(resourceTags) != 0 {
		for _, row := range resourceTags {
			lbIds = append(lbIds, *row.ResourceId)
		}
	}

	if len(lbIds) == 0 {
		if utils.IsLoadBalancerType(service) {
			glog.Infof("No lb found for cluster:%s service: %s/%s UUID: %s", config.Global.ClusterName, service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), service.GetObjectMeta().GetUID())
		}
		return nil, "", nil
	} else if len(lbIds) > 1 { // kateway todo 什么时候会有这种异常
		services.UploadMetricsAndEvent(service, types.NewError(errcode.ServiceWithMultiLoadbalancer, "", utils.ServiceName(service), strings.Join(lbIds, ",")))
	}

	loadBalancerContext := &LoadBalancerContext{
		Service:        service,
		Region:         *resourceTags[0].ResourceRegion,
		LoadBalancerId: lbIds[0],
	}
	return loadBalancerContext, *resourceTags[0].ResourceRegion, nil
}

func GetResourceTagsByService(service service_wrapper.ServiceWrapper) ([]*tag.ResourceTag, error) {
	serviceUUID := string(service.GetObjectMeta().GetUID())
	var err error
	resourceTags := make([]*tag.ResourceTag, 0)
	if service.ServiceType() == service_wrapper.CoreService {
		if utils.IsInEKSCluster() {
			resourceTags, err = getLoadBalancer4EKS(service)
			if err != nil {
				return nil, err
			}
		} else { // 由于复用默认开启，因此优先使用开启复用的方式获取标签信息
			if resourceTags, err = GetLoadBalancerByTagFilter(service, serviceUUID, types.TagKeyServiceUUID.String()); err != nil { // kateway todo 啥时候用了这种方式？
				return nil, err
			}
			if len(resourceTags) == 0 { // 非复用方式的标签
				if resourceTags, err = GetLoadBalancerByTagFilter(service, types.TagKeyServiceUUIDOld.String(), serviceUUID); err != nil {
					return nil, err
				}
			}
		}
	} else if service.ServiceType() == service_wrapper.MultiClusterService {
		if resourceTags, err = GetLoadBalancerByTagFilter(service, types.TagKeyMultiClusterServiceUUID.String(), serviceUUID); err != nil {
			return nil, err
		}
	}
	return resourceTags, nil
}

// 根据IngressUUID，查找对应的LoadBalancer
func GetIngressLoadBalancerByTags(service service_wrapper.ServiceWrapper, ingress types.Ingress) (*clb.LoadBalancer, bool, error) {
	ingressUUID := ingress.UID()
	var err error

	resourceTags := make([]*tag.ResourceTag, 0)
	if service.ServiceType() == service_wrapper.CoreService && utils.IsInEKSCluster() {
		resourceTags, err = getIngressLoadBalancer4EKS(service, ingress)
	} else {
		resourceTags, err = GetIngressLoadBalancerByTagFilter(service, ingress, types.TagKeyIngressUUID.String(), ingressUUID)
	}
	if err != nil {
		return nil, false, err
	}

	lbIds := make([]string, 0)
	if len(resourceTags) != 0 {
		for _, row := range resourceTags {
			lbIds = append(lbIds, *row.ResourceId)
		}
	}

	if len(lbIds) == 0 {
		glog.Infof("No lb found for cluster:%s ingress: %s/%s UUID: %s", config.Global.ClusterName, ingress.Namespace(), ingress.Name(), ingressUUID)
		return nil, false, nil
	} else if len(lbIds) > 1 {
		services.UploadMetricsAndEvent(service, types.NewError(errcode.ServiceWithMultiLoadbalancer, "", utils.ServiceName(service), strings.Join(lbIds, ",")))
	}

	loadBalancer, err := getLoadBalancers(service, ingress, lbIds[0])
	if err != nil { // 标签获取资源，查询资源不存在只可能是执行中，LoadBalance出现删除
		return nil, false, err
	}
	return loadBalancer, loadBalancer != nil, nil
}

// TODO misakazhou 可能的性能优化点
func getLoadBalancer4EKS(service service_wrapper.ServiceWrapper) ([]*tag.ResourceTag, error) {
	request := tag.NewDescribeResourcesByTagsRequest()
	request.ResourcePrefix = common.StringPtr("clb")
	request.ServiceType = common.StringPtr("clb")
	request.Offset = common.Uint64Ptr(0)
	request.Limit = common.Uint64Ptr(TAGSPERPAGE)
	request.TagFilters = []*tag.TagFilter{
		types.TagKeyAutoCreatedInEKS.ToTagFilter(types.TagValueAutoCreated),
		types.TagKeyClusterID.ToTagFilter(config.Global.ClusterName),
		types.TagKeyObjectKind.ToTagFilter(strings.ToLower(string(service.ServiceType()))),
		types.TagKeyObjectName.ToTagFilter(utils.ServiceName(service)),
	}

	response, err := tencentapi.Instance.DescribeResourcesByTags(cloudctx.New(service, config.Global.Region), request)
	if err != nil {
		return nil, err
	}
	if response.Response != nil && response.Response.Rows != nil && len(response.Response.Rows) != 0 {
		return response.Response.Rows, nil
	}

	return GetLoadBalancerByTagFilter(service, string(service.GetObjectMeta().GetUID()),
		types.TagKeyClusterIDPrefix+config.Global.ClusterName)
}

func getIngressLoadBalancer4EKS(service service_wrapper.ServiceWrapper, ingress types.Ingress) ([]*tag.ResourceTag, error) {
	region := utils.GetIngressCrossRegionID(ingress)

	request := tag.NewDescribeResourcesByTagsRequest()
	request.ResourceRegion = &region
	request.ResourcePrefix = common.StringPtr("clb")
	request.ServiceType = common.StringPtr("clb")

	// Step 1. query tke-created CLB
	request.TagFilters = []*tag.TagFilter{
		types.TagKeyClusterID.ToTagFilter(config.Global.ClusterName),
		types.TagKey(GetAutoCreatedTagKey(service)).ToTagFilter(GetAutoCreatedTagValue()),
		types.TagKeyObjectKind.ToTagFilter("ingress"),
		types.TagKeyObjectName.ToTagFilter(fmt.Sprintf("%s/%s", ingress.Namespace(), ingress.Name())),
	}
	response, err := tencentapi.Instance.DescribeResourcesByTags(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}
	if response.Response != nil && response.Response.Rows != nil && len(response.Response.Rows) != 0 {
		return response.Response.Rows, nil
	}

	// Step 2. query use existed CLB
	return GetIngressLoadBalancerByTagFilter(service, ingress, ingress.UID(), types.TagKeyClusterIDPrefix+config.Global.ClusterName)
}

func GetLoadBalancerByTagFilter(service service_wrapper.ServiceWrapper, tagKey string, tagValue string) ([]*tag.ResourceTag, error) {
	request := tag.NewDescribeResourcesByTagsRequest()
	request.ResourcePrefix = common.StringPtr("clb")
	request.ServiceType = common.StringPtr("clb")
	request.Offset = common.Uint64Ptr(0)
	request.Limit = common.Uint64Ptr(TAGSPERPAGE)
	request.TagFilters = []*tag.TagFilter{
		utils.NewTagFilter(tagKey, tagValue),
	}

	response, err := tencentapi.Instance.DescribeResourcesByTags(cloudctx.New(service, config.Global.Region), request)
	if err != nil {
		return nil, err
	}

	if response.Response != nil && response.Response.Rows != nil {
		return response.Response.Rows, nil
	}
	return []*tag.ResourceTag{}, nil
}

func GetIngressLoadBalancerByTagFilter(service service_wrapper.ServiceWrapper, ingress types.Ingress, tagKey string, tagValue string) ([]*tag.ResourceTag, error) {
	region := utils.GetIngressCrossRegionID(ingress)

	request := tag.NewDescribeResourcesByTagsRequest()
	request.ResourceRegion = &region
	request.ResourcePrefix = common.StringPtr("clb")
	request.ServiceType = common.StringPtr("clb")
	request.Offset = common.Uint64Ptr(0)
	request.Limit = common.Uint64Ptr(TAGSPERPAGE)
	request.TagFilters = []*tag.TagFilter{
		utils.NewTagFilter(tagKey, tagValue),
	}

	response, err := tencentapi.Instance.DescribeResourcesByTags(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}
	if response.Response != nil && response.Response.Rows != nil {
		return response.Response.Rows, nil
	}
	return []*tag.ResourceTag{}, nil
}

func ModifyLoadBalancerTags(service service_wrapper.ServiceWrapper, region string, lbId string, replaceTags []*tag.Tag, deleteTags []*tag.TagKeyObject) error {
	if len(replaceTags) == 0 && len(deleteTags) == 0 {
		return nil
	}

	request := tag.NewModifyResourceTagsRequest()
	request.Resource = common.StringPtr(fmt.Sprintf("qcs::clb:%s:uin/%d:clb/%s", region, config.Global.OwnerUin, lbId))
	if len(replaceTags) != 0 {
		request.ReplaceTags = replaceTags
	}
	if len(deleteTags) != 0 {
		request.DeleteTags = deleteTags
	}

	if _, err := tencentapi.Instance.ModifyResourceTags(cloudctx.New(service, config.Global.Region), request); err != nil {
		return err
	}
	return nil
}

func GetResourceTags(service service_wrapper.ServiceWrapper, region string, serviceType string, resourcePrefix string, resourceIds []string) ([]*tag.TagResource, error) {
	request := tag.NewDescribeResourceTagsByResourceIdsRequest()
	request.Limit = common.Uint64Ptr(TAGSPERPAGE)
	request.Offset = common.Uint64Ptr(0)
	request.ServiceType = &serviceType
	request.ResourcePrefix = &resourcePrefix
	request.ResourceIds = common.StringPtrs(resourceIds)
	request.ResourceRegion = &region

	tagResource := make([]*tag.TagResource, 0)
	totalCount := uint64(TAGSPERPAGE)
	for *request.Offset < totalCount {
		response, err := tencentapi.Instance.DescribeResourceTagsByResourceIds(cloudctx.New(service, config.Global.Region), request)
		if err != nil {
			return nil, err
		}
		if response.Response.Tags != nil && len(response.Response.Tags) > 0 {
			tagResource = append(tagResource, response.Response.Tags...)
		}
		totalCount = *response.Response.TotalCount
		request.Offset = common.Uint64Ptr(*request.Offset + uint64(len(response.Response.Tags)))
	}
	return tagResource, nil
}

func checkInvalidTag(service service_wrapper.ServiceWrapper, tags []*tag.ResourceTag) error {
	loadBalancers := make(map[string]*clb.LoadBalancer)
	for _, row := range tags {
		request := clb.NewDescribeLoadBalancersRequest()
		request.LoadBalancerIds = []*string{common.StringPtr(*row.ResourceId)}
		ctx := cloudctx.WithRegion(context.Background(), *row.ResourceRegion)
		ctx = cloudctx.WithObject(ctx, service)
		response, err := tencentapi.Instance.DescribeLoadBalancers(ctx, request)
		if err != nil {
			return err
		}
		if response != nil && response.Response != nil && response.Response.LoadBalancerSet != nil {
			for index, loadbalancer := range response.Response.LoadBalancerSet {
				loadBalancers[*loadbalancer.LoadBalancerId] = response.Response.LoadBalancerSet[index]
			}
		}
	}

	for _, resourceTag := range tags {
		if _, exist := loadBalancers[*resourceTag.ResourceId]; !exist {
			if resourceTag.Tags != nil && len(resourceTag.Tags) != 0 {
				deleteTag := []*tag.TagKeyObject{{TagKey: resourceTag.Tags[0].TagKey}}
				if err := ModifyLoadBalancerTags(service, *resourceTag.ResourceRegion, *resourceTag.ResourceId, nil, deleteTag); err != nil {
					glog.Infof("delete ResourceTags error %s %s.", *resourceTag.ResourceId, *resourceTag.Tags[0].TagKey)
				}
			}
		}
	}
	return nil
}

func tagDifference(from, to []*tag.TagResource) ([]*tag.TagResource, []*tag.TagResource) {
	fromSet, fromMap := GetTagSetAndMap(from)
	toSet, toMap := GetTagSetAndMap(to)

	toDel := fromSet.Difference(toSet)
	toAdd := toSet.Difference(fromSet)

	toAddItems := make([]*tag.TagResource, 0)
	toDelItems := make([]*tag.TagResource, 0)
	toAddKey := make(sets.String)

	for _, add := range toAdd.List() {
		if item, exist := toMap[add]; exist {
			if item.TagKey == nil || item.TagValue == nil {
				continue
			}
			toAddItems = append(toAddItems, utils.NewTagResource(*item.TagKey, *item.TagValue))
			toAddKey.Insert(*item.TagKey)
		}
	}

	for _, del := range toDel.List() {
		if item, exist := fromMap[del]; exist {
			if item.TagKey == nil {
				continue
			}
			if !toAddKey.Has(*item.TagKey) {
				toDelItems = append(toDelItems, utils.NewTagResource(*item.TagKey, *item.TagValue))
			}

		}
	}

	return toAddItems, toDelItems
}

func GetTagSetAndMap(items []*tag.TagResource) (sets.String, map[string]*tag.TagResource) {
	itemSet := make(sets.String)
	itemMap := make(map[string]*tag.TagResource)
	for _, v := range items {
		item := *v
		if item.TagKey == nil {
			continue
		}
		itemKey := fmt.Sprintf("%s_%s", *item.TagKey, *item.TagValue)
		// itemKey := *item.TagKey
		itemSet.Insert(itemKey)
		itemMap[itemKey] = &item
	}
	return itemSet, itemMap
}

// checkCreatedByTKE check whether lbId with tags CreateByTKETag == CreateByTKEValue
// func checkCreatedByTKE(tags []*clb.TagInfo) bool {
//	for _, tag := range tags {
//		if tag.TagKey != nil && *tag.TagKey == CreateByTKETag() && tag.TagValue != nil && *tag.TagValue == CreateByTKEValue() {
//			return true
//		}
//	}
//	return false
// }

func checkCreatedByTKENew(resource *v1alpha1.LoadBalancerResource) bool {
	return resource.Spec.Created
}

// checkUsedByIngress check whether lbId with tags CreateByTKETag == CreateByTKEValue
func checkUsedByIngress(tags []*clb.TagInfo) bool {
	for _, tag := range tags {
		if tag.TagKey != nil && *tag.TagKey == types.TagKeyIngressUUID.String() {
			return true
		}
	}
	return false
}

func GetClusterIdByTags(lbTags []*clb.TagInfo) string {
	for _, lbTag := range lbTags {
		if lbTag.TagKey != nil && *lbTag.TagKey == types.TagKeyClusterID.String() && lbTag.TagValue != nil {
			return *lbTag.TagValue
		}
	}
	return ""
}

// getTagPrefix return tag prefix
func getTagPrefix(serviceUUID string) string {
	if len(serviceUUID) > 36 {
		return serviceUUID[0:36] + "_"
	} else {
		return serviceUUID + "_"
	}
}

func getTagKey(key, value string) string {
	return fmt.Sprintf("%s_%s", key, value)
}
