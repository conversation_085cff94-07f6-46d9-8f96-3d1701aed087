package tencent

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/sets"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	"github.com/google/uuid"
	v2Clb "github.com/howardshaw/qcloudapi-sdk-go/clb"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	cvm "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cvm/v20170312"
	tag "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag/v20180813"
	errors2 "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/util/retry"
	glog "k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/service/crossregion"
	"cloud.tencent.com/lb-controller/pkg/utils"
)

const (
	TKEDedicatedListenerName  = "TKE-DEDICATED-LISTENER"
	FORWARDTYPE               = 1
	CLASSICTYPE               = 0
	MAXTARGETPERREQUEST       = 20
	MAXBATCHTARGETPERREQUEST  = 500
	BATCH_LOADBALANCES_LIMIT  = 20
	TEMPMAXBATCHMODIFYREQUEST = 100
	MAXLISTENERPERREQUEST     = 50
	TAGSPERPAGE               = 50
	MAXCVMIPPERREQUEST        = 5
	STATUS_TASK_GOING         = 2
	LoadbalancerNameMaxSize   = 50
	ClassicLBPublicType       = 2
	ClassicLBInternalType     = 3

	LOADBALANCETYPEEXIST    = "EXIST"
	LOADBALANCETYPEOPEN     = "OPEN"
	LOADBALANCETYPEINTERNAL = "INTERNAL"
	IPV4AddressVersion      = "IPV4"
	IPV6AddressVersion      = "IPV6"

	AnnoServiceLBInternalUniqSubnetID = "service.kubernetes.io/qcloud-loadbalancer-internal-subnetid"
	AnnoExistedLBID                   = "service.kubernetes.io/tke-existed-lbid"
	AnnoExtensiveParameters           = "service.kubernetes.io/service.extensiveParameters"
	AnnoListenerParameters            = "service.kubernetes.io/service.listenerParameters"
	LabelNodeRoleMaster               = "node-role.kubernetes.io/master"

	AnnoInternetChargeType      = "service.kubernetes.io/qcloud-loadbalancer-internet-charge-type"
	AnnoInternetMaxBandwidthOut = "service.kubernetes.io/qcloud-loadbalancer-internet-max-bandwidth-out"

	QcloudCertId   = "qcloud_cert_id"
	QcloudCACertId = "qcloud_ca_cert_id"

	CERT_MODE_UNIDIRECTIONAL = "UNIDIRECTIONAL"
	CERT_MODE_MUTUAL         = "MUTUAL"

	_serviceShareExistedLB = "service.kubernetes.io/qcloud-share-existed-lb" // kateway 这个 anno 只对eks service 有效， eks service 必须加这个anno才能开启reuse
)

const (
	DESCRIBE_INSTANCES_LIMIT              = 100
	DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT = 5
)

/*
type loadBalancerConfig struct {
	Name             string
	Type             string
	ServiceUUID      string
	LBType           string
	Forward          int64
	VpcId            string
	SubnetId         string
	ProjectId        int64
	LBID             string
	AddressIpVersion string
} */

// getLoadBalancerByID get lb info by lbID
// 默认透出错误码运行中LB被删除
func getLoadBalancers(service service_wrapper.ServiceWrapper, ingress types.Ingress, lbID string) (*clb.LoadBalancer, error) {
	region := utils.GetIngressCrossRegionID(ingress)
	ctx := cloudctx.WithRegion(context.Background(), region)
	ctx = cloudctx.WithObject(ctx, service)
	request := clb.NewDescribeLoadBalancersRequest()
	request.LoadBalancerIds = []*string{&lbID}
	response, err := tencentapi.Instance.DescribeLoadBalancers(ctx, request)
	if err != nil {
		return nil, err
	}

	if response.Response.TotalCount != nil {
		if *response.Response.TotalCount == 0 || len(response.Response.LoadBalancerSet) == 0 {
			glog.Infof("cannot find the specific lb")
			return nil, nil
		} else {
			return response.Response.LoadBalancerSet[0], nil
		}
	}
	return nil, types.NewError(errcode.LoadBalancerNotExistError, "", utils.ServiceName(service))
}

// createLoadBalancer create lb for service by lbConfig
func CreateLoadBalancer(serviceContext *SyncContext) (string, error) {
	service := serviceContext.Service
	balancerName := getLoadBalancerName(service, config.Global.ClusterName)

	// kateway: clb接口幂等问题：https://iwiki.woa.com/p/4008365593
	clientToken := utils.GetClientToken(service)
	if clientToken == "" {
		clientToken = uuid.New().String() // kateway 创建 client token
		err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
			var err error
			annotations := make(map[string]string)
			if getAnnotations := service.GetObjectMeta().GetAnnotations(); getAnnotations != nil {
				for key, value := range getAnnotations {
					annotations[key] = value
				}
			}
			annotations[utils.ClientTokenAnnotation] = clientToken

			updated := service.DeepCopy()
			updated.GetObjectMeta().SetAnnotations(annotations)
			updated, err = updated.Update()
			if err != nil {
				var getError error
				if updated, getError = service.GetLatest(); getError == nil {
					service = updated
				}
				return err
			}
			serviceContext.Service = updated
			service = updated
			return nil
		})
		if err != nil {
			return "", err
		}
	}

	// try to create
	request := tencentapi.NewCreateLoadBalancerRequest()
	request.ClientToken = common.StringPtr(clientToken)
	request.LoadBalancerName = &balancerName
	if config.Global.ProjectID > 0 {
		request.ProjectId = common.Int64Ptr(config.Global.ProjectID)
	}
	request.VpcId = &config.Global.VPCID
	request.Forward = common.Int64Ptr(CLASSICTYPE)

	// TODO: TDCC集群中的LoadBalancer资源，应该在哪里定义VPCId
	if service.ServiceType() == service_wrapper.MultiClusterService {
		request.VpcId = &config.Global.VPCID
	}

	// request.AddressIPVersion = &lbConfig.AddressIpVersion
	if subnetId, existSubnetId := utils.GetSubnetId(service); existSubnetId {
		request.LoadBalancerType = common.StringPtr(LOADBALANCETYPEINTERNAL)
		request.SubnetId = &subnetId
	} else { // public
		// parse internet charge
		request.LoadBalancerType = common.StringPtr(LOADBALANCETYPEOPEN)
		serviceAnnontations := service.GetObjectMeta().GetAnnotations()
		internetChargeType := serviceAnnontations[AnnoInternetChargeType]
		internetMaxBandwidthOut := serviceAnnontations[AnnoInternetMaxBandwidthOut]
		if internetChargeType != "" && internetMaxBandwidthOut != "" {
			band, err := strconv.ParseInt(internetMaxBandwidthOut, 10, 64)
			if err == nil {
				internetAccessible := clb.InternetAccessible{
					InternetChargeType:      &internetChargeType,
					InternetMaxBandwidthOut: &band,
				}
				request.InternetAccessible = &internetAccessible
			}
		}
	}

	// 处理tag
	// 集群继承下来的tag,优先级最低
	clusterTags, err := GetTKEClusterTags(service, config.Global.ClusterName)
	if err != nil {
		return "", err
	}
	tagMap := lo.SliceToMap(clusterTags, func(t *tag.TagResource) (string, *clb.TagInfo) {
		return *t.TagKey, types.TagKey(*t.TagKey).ToCLBTagInfo(*t.TagValue)
	})
	// 用户自定义tag
	if err := getLbParamFromAnnotation(service, request); err != nil { // kateway 看起来可以用这个annotation扩展lb很多属性
		return "", err
	}
	for _, t := range request.Tags {
		tagMap[*t.TagKey] = t
	}
	for _, t := range buildDefaultCreateTKEServiceTags(service) {
		tagMap[*t.TagKey] = types.TagKey(*t.TagKey).ToCLBTagInfo(*t.TagValue)
	}

	request.Tags = lo.Values(tagMap)

	// verify lb type not changed
	if *request.Forward != FORWARDTYPE {
		request.Forward = common.Int64Ptr(FORWARDTYPE)
	}

	// kateway https://cloud.tencent.com/document/product/457/79749
	// 处理集群外VPC接入的场景
	crossType, _ := utils.GetCrossType(service)
	hybridType := utils.GetHybridType(service)
	if vpcId, exist := utils.GetCrossVPCId(service); exist {
		if vpcId != config.Global.VPCID {
			request.VpcId = common.StringPtr(vpcId)
			if crossType == types.CrossType2_0 {
				request.SnatPro = common.BoolPtr(true)
			}
		}
	}
	if hybridType == types.HybridTypePvgw || hybridType == types.HybridTypeCcn || crossType == types.CrossType1_1 {
		request.SnatPro = common.BoolPtr(true)
	}
	// 处理跨地域接入，后端绑定默认开启 SnatPro 功能
	region := config.Global.Region
	if crossRegionId, exist := utils.GetCrossRegionId(service); exist {
		if crossRegionId != config.Global.Region {
			region = crossRegionId
		}
	}

	// IPv6FullChain 资源开启SNATPro的同时，必须开启混绑能力。
	// Tips: "Code":"InvalidParameterValue","Message":"FullChain IPv6 CLB does not support to enable SnatPro before enable MixIpTarget","RequestId":"01c4db93-30bb-4114-872e-47f4d1bfafbb"
	if request.SnatPro != nil && *request.SnatPro == true {
		if request.AddressIPVersion != nil && *request.AddressIPVersion == "IPv6FullChain" {
			request.MixIpTarget = common.BoolPtr(true)
		}
	}

	// kateway: 注意这里的region参数，当没有使用跨域的CrossRegionID注解时，region为组件所在地域，否则region为跨域指定的地域
	response, err := tencentapi.Instance.ExpandCreateLoadBalancer(cloudctx.New(service, region), request)
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == "LimitExceeded" {
				return "", types.NewError(errcode.LoadBalancerLimitExceededError, sdkError.Error(), utils.ServiceName(service))
			} else if sdkError.Code == "InvalidParameter.FormatError" || sdkError.Code == "InvalidParameterValue.Length" {
				return "", types.NewError(errcode.LoadBalancerCreateParamFormatError, sdkError.Error(), utils.ServiceName(service))
			} else if sdkError.Code == "InvalidParameterValue" {
				if strings.Contains(sdkError.Message, "AddressIPVersion") { // Message: The value of parameter AddressIPVersion must be IPV4 or IPV6.
					return "", types.NewError(errcode.LoadBalancerCreateParamAddressIPVersionError, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "SubnetId") || strings.Contains(sdkError.Message, "subnet not exists") {
					// [TencentCloudSDKError] Code=InvalidParameterValue, Message=Invoke vpc failed: subnet not exists, RequestId=b5f6053b-f0d7-42f9-bb4a-f5da09339986
					// [TencentCloudSDKError] Code=InvalidParameterValue, Message=参数`SubnetId`中`sub`不合规范
					return "", types.NewError(errcode.LoadBalancerParamSubnetNotExistError, sdkError.Error(), utils.ServiceName(service))
				}
			} else if sdkError.Code == "ResourceInsufficient" {
				// 子网IP不足，导致无法创建内网负载均衡。Code=ResourceInsufficient, Message=The number of IP in subnet subnet-963gypx4 is not enough., RequestId=f8f0eea6-32fc-48a1-9f59-e4af557d79ee
				if strings.Contains(sdkError.Message, "The number of IP") {
					return "", types.NewError(errcode.LoadBalancerSubnetIPInsufficientError, sdkError.Error(), utils.ServiceName(service))
				}
				return "", types.NewError(errcode.LoadBalancerResourceInsufficientError, sdkError.Error(), utils.ServiceName(service)) // CLB资源不足，导致无法创建负载均衡。
			} else if sdkError.Code == "FailedOperation" {
				if strings.Contains(sdkError.Message, "Insufficient account balance") {
					return "", types.NewError(errcode.InsufficientAccountBalanceError, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "do not support create IPv6") { // Message: Region ap-hangzhou-ec do not support create IPv6 Nat loadbalancer
					return "", types.NewError(errcode.DoNotSupportIPv6Error, sdkError.Error(), utils.ServiceName(service))
				} else if strings.Contains(sdkError.Message, "The name of loadbalancer is invalid") { // Order parameter checking failed: The name of loadbalancer is invalid
					return "", types.NewError(errcode.LoadBalancerNameFormateError, sdkError.Error(), utils.ServiceName(service))
				}
			} else if sdkError.Code == "InvalidParameter.ClientTokenLimitExceeded" { // 幂等方式创建 CLB 不会成功了。Code=InvalidParameter.ClientTokenLimitExceeded, Message=Failed to create by ClientToken `system-XXX` limit exceeded
				// 需要清理已过时失效的 ClientToken
				err = retry.RetryOnConflict(retry.DefaultBackoff, func() error {
					var err error
					service, err = service.GetLatest()
					if err != nil {
						return err
					}
					serviceClone := service.DeepCopy()
					annotations := serviceClone.GetObjectMeta().GetAnnotations()
					if annotations != nil && len(annotations[utils.ClientTokenAnnotation]) != 0 {
						annotations[utils.ClientTokenAnnotation] = ""
						serviceClone.GetObjectMeta().SetAnnotations(annotations)
						service, err = serviceClone.Update()
					}
					return err
				})
				if err != nil {
					return "", err
				}
				return "", types.NewError(errcode.LoadBalancerClientTokenHasExpiredError, sdkError.Error(), utils.ServiceName(service))
			}
		}
		return "", err
	}

	lbIDs := response.Response.LoadBalancerIds
	if len(lbIDs) == 0 {
		glog.Errorf("CreateLoadBalancer response empty LoadBalancerIds, request: %s response: %s", utils.JsonWrapper(request), utils.JsonWrapper(response))
		return "", types.NewError(errcode.ServiceCLBUnexpectedError, "")
	}
	return *lbIDs[0], nil
}

// createClassicLoadBalancer create classic lb since clb no yunapiv3 support
func CreateClassicLoadBalancer(syncContext *SyncContext) (string, error) {
	service := syncContext.Service
	balancerName := getLoadBalancerName(service, config.Global.ClusterName)
	region, err := crossregion.CrossRegionServiceInstance.GetServiceRegion(service)
	if err != nil {
		return "", err
	}

	request := &v2Clb.CreateLoadBalancerArgs{
		LoadBalancerName: &balancerName,
		VpcId:            &config.Global.VPCID,
		Number:           common.IntPtr(1),
		Forward:          common.IntPtr(CLASSICTYPE),
		DomainPrefix:     common.StringPtr(getLbPrefixName(balancerName)),
	}
	if config.Global.ProjectID > 0 {
		request.ProjectId = common.IntPtr(int(config.Global.ProjectID))
	}

	serviceAnnontations := service.GetObjectMeta().GetAnnotations()

	// parse loadbalancertype public or internal and vips
	// 2：公网属性， 3：内网属性。
	if subnetId, existSubnetId := utils.GetSubnetId(service); existSubnetId && subnetId != "" {
		request.SubnetId = &subnetId
		request.LoadBalancerType = ClassicLBInternalType
	} else {
		request.LoadBalancerType = ClassicLBPublicType

		// parse internet charge
		internetChargeType := serviceAnnontations[AnnoInternetChargeType]
		internetMaxBandwidthOut := serviceAnnontations[AnnoInternetMaxBandwidthOut]
		if internetChargeType != "" && internetMaxBandwidthOut != "" {
			band, err := strconv.Atoi(internetMaxBandwidthOut)
			if err == nil {
				internetAccessible := v2Clb.InternetAccessible{
					InternetChargeType:      internetChargeType,
					InternetMaxBandwidthOut: band,
				}
				request.InternetAccessible = &internetAccessible
			}
		}
	}

	// parse params from annontation
	if err := getClassicLbParamFromAnnotation(service, request); err != nil {
		return "", err
	}
	// verify lb type not changed
	if *request.Forward != CLASSICTYPE {
		request.Forward = common.IntPtr(CLASSICTYPE)
	}

	response, err := tencentapi.Instance.CreateLoadBalancerV2(cloudctx.New(service, region), request)
	if err != nil {
		return "", err
	}
	lbIds := response.GetUnLoadBalancerIds()
	if len(lbIds) == 0 {
		return "", types.NewError(errcode.ServiceCLBUnexpectedError, "")
	} else if len(lbIds) > 1 {
		glog.Warningf("LB return multi lbids when create classic loadbalancer for svc: %s/%s request: %s response: %s", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), utils.JsonWrapper(request), utils.JsonWrapper(response))
	}

	//
	lbId := lbIds[0]
	tags, err := GetCreateTKEServiceTags(service)
	if err != nil {
		return "", err
	}
	if err = ModifyLoadBalancerTags(service, region, lbId, utils.ConvertTagList(tags), nil); err != nil { // 标签没有修改成功，回滚Loadbalancer创建
		if roolbackErr := DeleteLoadBalancer(service, region, lbId); roolbackErr != nil { // 标签和CLB同时出问题，导致回滚失败。导致资源泄露的严重问题，记录日志与错误码
			return "", types.NewError(errcode.CLBResourceLeakError, fmt.Sprintf("CLBResourceLeakError TagError:%s CLBError:%s", err.Error(), roolbackErr.Error()), utils.ServiceName(service))
		}
		// 回滚成功，回滚之后可能会再次重试
		return "", err
	}

	return lbId, nil
}

// deleteLoadBalancer delete lb for service
func DeleteLoadBalancer(service service_wrapper.ServiceWrapper, region string, lbID string) error {
	request := clb.NewDeleteLoadBalancerRequest()
	request.LoadBalancerIds = []*string{&lbID}
	request.ForceDelete = lo.ToPtr(service.ForceDelete())
	if _, err := tencentapi.Instance.DeleteLoadBalancer(cloudctx.New(service, region), request); err != nil {
		glog.Errorf("DeleteLoadBalancer Error, %v", err.Error())
		return err
	}
	return nil
}

// kateway 会创建CLB todo 重命名 BuildLoadBalancerContextTryCreateLbrAndClb
// ensureLoadBalancer ensure lb for service， return lb info, true for new create, otherwise already exist
func GetLoadBalancerContext(syncContext *SyncContext) error {
	service := syncContext.Service

	// Get Current LoadBalancer Context.

	loadBalancerContext, err := GetLoadBalancerByLoadbalancerResource(service) // kateway 非reuse情况下，如果clb存在且LBR不存在，则创建LBR
	if err != nil {
		return err
	}

	// kateway(bitliu[30]): loadBalancerContext 对于新建的 MCS 或者子集群 SVC 都会空，LBR 会在下面创建
	// 对于 resync 的时候，这里应该为 not nil，才会进入这里
	if loadBalancerContext != nil {
		loadbalancer, err := loadBalancerContext.GetLoadBalancer()
		if err != nil {
			if errcode.IsLoadBalancerNotExistError(err) {
				// 负载均衡存在数据不一致情况。标签数据还存留，但是实际资源已经不存在。
				// 需要对该资源清理所有标签资源，然后再开始同步
				// kateway: 这里主要是删除标签系统中残留的资源标签信息，例如lb-abcd资源已不存在了，但是在标签系统中还存在lb-abcd且该id上还绑定了tke-lb-serviceuuid:f4771c87-0fff-4ead-9243-fe23df044a42 这样的标签，这里将这些service关联的标签进行清理
				resourceTags, err := GetResourceTagsByService(service)
				if err != nil {
					return err
				}
				if err := checkInvalidTag(service, resourceTags); err != nil {
					return err
				}
			}
			return err
		}
		// 对TKE管理的资源，检查标签被用户是否误删的情况

		// kateway(bitliu[31]): 多集群场景 子集群 service 隐含条件是使用已有 CLB，不会进入
		// MCS 如果是自动创建的会进入，对 CLB 的 tag 进行更新
		if _, exist := utils.GetExistLB(syncContext.Service); !exist && loadBalancerContext.LoadBalancerResource.Spec.Created { // 当前资源是TKE创建资源
			if clusterId := GetClusterIdByTags(loadbalancer.Tags); clusterId == "" || clusterId == config.Global.ClusterName { // 当前资源没有被过渡到其他集群
				// kateway(bitliu[32]): 多集群场景 MCS 如果是自动创建的会进入，保证 CLB 标签正确
				if err := EnsureLoadBalancerTags(loadBalancerContext); err != nil {
					return err
				}
			}
		}
	}

	var existLoadBalancerContext *LoadBalancerContext = nil
	// kateway: 考虑新建(或者LBR被删除)的带有service.kubernetes.io/tke-existed-lbid的annotation的service，到这里时loadBalancerContext==nil，
	// 函数ValidateLBConfig会在校验的同时为这类型svc构建LBCtx
	if existLoadBalancerContext, err = ValidateLBConfig(loadBalancerContext, syncContext); err != nil {
		glog.Error(err)
		return err
	}
	if loadBalancerContext != nil {
		if err := CheckIntendToRecreate(loadBalancerContext, syncContext); err != nil {
			glog.Error(err)
			return err
		}
	}

	// kateway(bitliu[40]): 对于 MCS 或者 子集群 SVC，首次创建才会进入这里
	if loadBalancerContext == nil {
		// kateway(bitliu[41]): 对于 MCS 或者 子集群 SVC 首次创建并且是使用已有才会进入这里，创建 LBR
		if lbId, exist := utils.GetExistLB(service); exist { // kateway: LBR 不存在
			if existLoadBalancerContext.LoadBalancerResource == nil {
				checkRegion := config.Global.Region
				if crossRegionId, crossRegionIdExist := utils.GetCrossRegionId(service); crossRegionIdExist {
					checkRegion = crossRegionId
				}
				// kateway: 这里为新建(或者LBR被删除)的带有service.kubernetes.io/tke-existed-lbid的annotation的service创建LBR
				loadBalancerResource, err := CreateLoadBalancerResource(lbId, checkRegion, false, service) // kateway LBR创建之一：reuse 场景, created=false, CLB 已经存在，所以可以直接创建LBR
				if err != nil {
					return err
				}
				existLoadBalancerContext.LoadBalancerResource = loadBalancerResource
			}
			loadBalancerContext = existLoadBalancerContext
		} else { // kateway: CLB 不存在， LBR 也不存在
			// kateway: 全新创建的非复用service，需要为service创建clb

			// kateway(bitliu[42]): 对于 MCS 首次创建并且是自动创建才会进入这里，创建 LBR
			// 子集群 SVC 不会进入，因为只能是使用已有
			if utils.GetLoadbalancerType(service) == CLASSICTYPE { // classic
				lbId, err = CreateClassicLoadBalancer(syncContext) // kateway: 创建传统CLB
			} else { // default forward
				lbId, err = CreateLoadBalancer(syncContext) // kateway: 创建应用型CLB
			}
			if err != nil {
				return err
			}
			serviceRegion, err := crossregion.CrossRegionServiceInstance.GetServiceRegion(service)
			if err != nil {
				glog.Error(err)
				return err
			}
			loadBalancerResource, err := CreateLoadBalancerResource(lbId, serviceRegion, true, service) // kateway: LBR创建之一：先创建了CLB，再创建LBR
			if err != nil {
				glog.Errorf("CreateLoadBalancerResource %s for service %s error: %v", lbId, utils.ServiceName(service), err)
				// LB不做回滚，后续会重试
				return err
			}
			// 检查标签资源落地情况
			// 标签资源在一分钟之内落地失败，则回收创建的负载均衡资源。
			// TODO misakazhou 改为确认集群标签
			// if err := checkNewLoadBalancerTag(service, lbId); err != nil {
			//	glog.Errorf("Remove the loadBalancer. Tag not ready. lb: %s for service %s", lbId, utils.ServiceName(service))
			//	if err := DeleteLoadBalancer(service, lbId); err != nil {
			//		glog.Errorf("Remove the loadBalancer error. Tag not ready. lb: %s for service %s. err: %v", lbId, utils.ServiceName(service), err)
			//	}
			//	return err
			// }

			loadBalancerContext = &LoadBalancerContext{
				Service:              service,
				LoadBalancerId:       lbId,
				Region:               serviceRegion,
				LoadBalancerResource: loadBalancerResource,
			}
		}
	}

	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	// check loadBalancerContext status
	if *loadBalancer.Status != LBNORMALSTATUS {
		return types.NewError(errcode.LoadBalancerNotReadyError, "", utils.ServiceName(service))
	}
	syncContext.LoadBalancerContext = loadBalancerContext
	return nil
}

// 确认用户是否有重建的可能意图
// kateway: 用户修改service某些配置（annotation）涉及到CLB更改，提示用户需要重建，而不是原地更新，可能需要webhook直接拦截
func CheckIntendToRecreate(currentLoadBalancerContext *LoadBalancerContext, syncContext *SyncContext) error {
	service := currentLoadBalancerContext.Service
	loadBalancer, err := currentLoadBalancerContext.GetLoadBalancer()
	if err != nil {
		// if svcError, ok := err.(*types.Error); ok {
		//	if svcError.ErrorCode.Code == errcode.LoadBalancerNotExistError.Code {
		//		return false, nil
		//	}
		// }
		return err
	}

	// Region Change
	// kateway: 检查lb实例所在地域和controller所在地域是否一致，如果是跨域管理lb，则检查lb实例所在地域和crossregion配置的地域
	currentRegion := currentLoadBalancerContext.Region
	targetRegionID := config.Global.Region
	if regionId, exist := utils.GetCrossRegionId(service); exist {
		targetRegionID = regionId
	}
	if currentRegion != targetRegionID {
		syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.IntendToRecreateError, "", utils.ServiceName(service), fmt.Sprintf("region for clb: %s, region from Service annotation: %s", currentRegion, targetRegionID)))
		return nil
	}

	// TODO change spec.IpFamily need to recreate?
	createByTKE := checkCreatedByTKENew(currentLoadBalancerContext.LoadBalancerResource)
	if existLbId, exist := utils.GetExistLB(service); exist {
		// choose a new exist type lb, open/internal/exist => new exist
		if existLbId != *loadBalancer.LoadBalancerId {
			// kateway: 用户修改service.kubernetes.io/tke-existed-lbid指向的lb
			if createByTKE {
				syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.IntendToRecreateError, "", utils.ServiceName(service), fmt.Sprintf("current used clb: %s, clb from Service annotation: %s", *loadBalancer.LoadBalancerId, existLbId)))
				return nil
			} else {
				syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.IntendToRecreateError, "", utils.ServiceName(service), fmt.Sprintf("current used exist clb: %s, clb from Service annotation: %s", *loadBalancer.LoadBalancerId, existLbId)))
				return nil
			}
		}
		return nil
	}
	// VPC Changed
	targetVPCId := config.Global.VPCID
	if vpcId, exist := utils.GetCrossVPCId(service); exist {
		targetVPCId = vpcId
	}
	if loadBalancer.VpcId != nil && *loadBalancer.VpcId != targetVPCId {
		syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.IntendToRecreateError, "", utils.ServiceName(service), fmt.Sprintf("current clb vpc: %s, vpc from Service annotation: %s", *loadBalancer.VpcId, targetVPCId)))
		return nil
	}
	// if lb type changed,from classic <==> forward type
	loadbalancerType := utils.GetLoadbalancerType(service)
	if _, hasLBAnnon := service.GetObjectMeta().GetAnnotations()[utils.LoadBalancerTypeAnnontation]; hasLBAnnon && loadBalancer.Forward != nil && int64(*loadBalancer.Forward) != loadbalancerType {
		syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.IntendToRecreateError, "", utils.ServiceName(service), fmt.Sprintf("current clb type: %d, clb type from Service annotation: %d", *loadBalancer.Forward, loadbalancerType)))
		return nil
	}
	// public to internal or internal to public
	subnetId, existSubnetId := utils.GetSubnetId(service)
	if loadBalancer.LoadBalancerType != nil && *loadBalancer.LoadBalancerType != "" {
		if (*loadBalancer.LoadBalancerType == "OPEN" && existSubnetId) || (*loadBalancer.LoadBalancerType == "INTERNAL" && !existSubnetId) {
			syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.IntendToRecreateError, "", utils.ServiceName(service), fmt.Sprintf("current clb network type: %s, network type from Service annotation: %s", *loadBalancer.LoadBalancerType, networkType(existSubnetId))))
			return nil
		}
	}
	// change subnet id
	if existSubnetId && loadBalancer.SubnetId != nil && *loadBalancer.SubnetId != "" && subnetId != *loadBalancer.SubnetId {
		syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.IntendToRecreateError, "", utils.ServiceName(service), fmt.Sprintf("current clb subnetId: %s, subnetId from Service annotation: %s", *loadBalancer.SubnetId, subnetId)))
		return nil
	}
	// exist to public/internal
	if !createByTKE {
		syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.IntendToRecreateError, "", utils.ServiceName(service), fmt.Sprintf("current used exist clb: %s, but not `service.kubernetes.io/tke-existed-lbid` annotation from Service annotation", *loadBalancer.LoadBalancerId)))
		return nil
	}
	return nil
}

func networkType(existSubnetId bool) string {
	if existSubnetId {
		return "INTERNAL"
	} else {
		return "OPEN"
	}
}

// func checkNewLoadBalancerTag(service service_wrapper.ServiceWrapper, loadBalancerId string) error {
//	timeout, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
//	defer cancel()
//	ticker := time.NewTicker(5 * time.Second)
//	defer ticker.Stop()
//	for {
//		select {
//		case <-timeout.Done():
//			return types.NewError(errcode.LabelStoredError, "", utils.ServiceName(service)) // DO NOT CHANGE THIS ERROR CODE!
//		case <-ticker.C:
//			loadBalancerContext, err := GetLoadBalancerByLoadbalancerResource(service)
//			if err != nil {
//				return err
//			}
//			if loadBalancerContext != nil && loadBalancerContext.LoadBalancerId == loadBalancerId {
//				return nil
//			}
//		}
//	}
// }

// buildLBConfig get lb config from service annontation and nodes
// func BuildLBConfig(service service_wrapper.ServiceWrapper) (*loadBalancerConfig, error) {
//	lbConfig := new(loadBalancerConfig)
//	lbConfig.Name = getLoadBalancerName(service, utils.GlobalConfigs.ClusterName)
//	lbConfig.Forward = utils.GetLoadbalancerType(service)
//	lbConfig.ProjectId = utils.GlobalConfigs.ProjectId
//	lbConfig.VpcId = utils.GlobalConfigs.VpcId
//
//	annotations := service.ObjectMeta.Annotations
//
//	//TODO: 这里有问题
//	if subnetID, hasSubnetID := annotations[AnnoServiceLBInternalUniqSubnetID]; hasSubnetID {
//		lbConfig.SubnetId = subnetID
//		lbConfig.LBType = LOADBALANCETYPEINTERNAL
//	} else {
//		lbConfig.LBType = LOADBALANCETYPEOPEN
//	}
//	//parse subnet id
//	existLBID, haslbID := utils.GetExistLB(service)
//	if !haslbID {
//		lbConfig.Type = lbConfig.LBType
//	} else {
//		lbConfig.Type = LOADBALANCETYPEEXIST
//		lbConfig.LBID = existLBID
//	}
//	lbConfig.AddressIpVersion = IPV4AddressVersion
//	if service.Spec.IPFamily != nil && *service.Spec.IPFamily == v1.IPv6Protocol {
//		lbConfig.AddressIpVersion = IPV6AddressVersion
//	}
//
//	return lbConfig, nil
// }

// validateLBConfig validate lb config
func ValidateLBConfig(currentLoadBalancerContext *LoadBalancerContext, syncContext *SyncContext) (*LoadBalancerContext, error) {
	service := syncContext.Service

	loadbalancerType := utils.GetLoadbalancerType(service)
	if loadbalancerType == 0 && utils.IsServiceDirectAccess(service) { // 直绑用传统型LB，不支持
		return nil, types.NewError(errcode.DirectAccessClassicalSupportError, "", utils.ServiceName(service))
	}

	// 检查跨地域配置
	existLB, existLBExist := utils.GetExistLB(service)
	crossRegionId, crossRegionIdExist := utils.GetCrossRegionId(service)
	crossVPCId, crossVPCIdExist := utils.GetCrossVPCId(service)
	crossType, _ := utils.GetCrossType(service)
	checkRegion := config.Global.Region
	if crossRegionIdExist {
		checkRegion = crossRegionId
		if !crossVPCIdExist && !existLBExist { // 指定跨地域未指定VPCId或已有负载均衡Id
			return nil, types.NewError(errcode.CrossRegionConfigError, "", utils.ServiceName(service))
		}
		if config.Global.ValidRegion != nil {
			if _, exist := config.Global.ValidRegion[crossRegionId]; !exist {
				return nil, types.NewError(errcode.CrossRegionInvalidReginError, "", utils.ServiceName(service), crossRegionId)
			}
		}
	}
	if !existLBExist {
		// kateway: 本service不存在service.kubernetes.io/tke-existed-lbid的annotation，但是却存在service.cloud.tencent.com/backend-manage-only的annotation，
		// 说明是多集群场景，annotation数据缺失

		// kateway(bitliu[36]): 隐含校验：如果开启了 ManagerOnly 必须是使用已有 CLB 的模式
		if backendManageOnly, _, _ := utils.GetBackendManageOnly(service); backendManageOnly {
			return nil, types.NewError(errcode.BackendManageOnlyMustUseExistLBError, "", utils.ServiceName(service))
		}
		// MultiCluster的管控集群不需要数据面打通。kateway: 即管控集群和CLB所在的集群不需要打通
		// 所以MultiCluster场景不需要检查CLB和集群是否在同一个云联网，只要后端接入这个CLB的集群和CLB在同一个云联网即可。

		// kateway(bitliu[37]): 隐含校验：MCS 如果开启了跨域，父集群地域和 CLB 跨域的 VPC 不需要打通，而是子集群和 CLB 需要在一个云联网
		// 由于子集群 svc 不能是自动创建，所以子集群 clb 的云联网连通性校验不在这里，在下面
		if service.ServiceType() == service_wrapper.CoreService && crossVPCIdExist && crossType == types.CrossType2_0 {
			if ok := crossregion.CrossRegionServiceInstance.CheckCCNInstance(checkRegion, crossVPCId); !ok { // 其他地域VPC与集群VPC不在同一个云联网
				return nil, types.NewError(errcode.CrossRegionCNNConfigError, "", utils.ServiceName(service))
			}
		}
	}

	if hybridType := utils.GetHybridType(service); hybridType == types.HybridTypeCcn {
		// 验证当前集群所在VPC已经加入云联网。并且该云联网中已经声明云联网专线资源
		if ok := crossregion.CrossRegionServiceInstance.CheckCCNInstance(config.Global.Region, config.Global.VPCID); !ok {
			return nil, types.NewError(errcode.HybridCNNConfigErrorError, "", utils.ServiceName(service))
		}
	}

	// kateway: 这里LBCtx可能是nil，对应于新建svc或者灾难恢复的场景
	var loadBalancerContext = currentLoadBalancerContext
	if existLBExist {
		// check if user assign exist
		if currentLoadBalancerContext != nil && currentLoadBalancerContext.LoadBalancerId == existLB {
			loadBalancerContext = currentLoadBalancerContext
		} else {
			loadBalancerResource, err := GetLoadBalancerResource(existLB)
			if err != nil {
				return nil, err
			}
			region, err := crossregion.CrossRegionServiceInstance.GetServiceRegion(service)
			if err != nil {
				return nil, err
			}
			loadBalancerContext = &LoadBalancerContext{
				Service:        service,
				Region:         region,
				LoadBalancerId: existLB,
			}
			loadBalancerContext.LoadBalancerResource = loadBalancerResource
		}
	}
	// kateway: 这里对应了一个非复用service新建的场景
	if loadBalancerContext == nil { // 新建场景
		return nil, nil
	}

	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		if sdkError, ok := err.(*errors.TencentCloudSDKError); ok {
			if sdkError.Code == "InvalidParameter.FormatError" || sdkError.Code == "InvalidParameterValue.Length" { // Service注解中的内容不正确
				return nil, types.NewError(errcode.ReuseFormatInvalidError, sdkError.Error(), utils.ServiceName(service))
			}
		} else if errcode.IsLoadBalancerNotExistError(err) { // Service注解中的LoadBalancer不存在
			return nil, types.NewError(errcode.ReuseLBNotExistError, "", utils.ServiceName(service))
		}
		return nil, err
	}

	// not allow use exist loadBalancer for multi cluster
	clusterId := GetClusterIdByTags(loadBalancer.Tags)
	fromOtherCluster, _ := utils.GetFromOtherCluster(service)
	if fromOtherCluster != "" {
		// kateway(bitliu[38]): 隐含校验：多集群场景下，CLB 的 cluster id 应该和子集群 svc 的 from-other-cluster 一致
		if clusterId != fromOtherCluster { // kateway: 子集群用的lb， 其 clusterID tag 需要和service.cloud.tencent.com/from-other-cluster 一致
			return nil, types.NewError(errcode.NotBelongToSpecifiedClusterError, "", utils.ServiceName(service), fromOtherCluster)
		}
	} else {
		if clusterId != "" && clusterId != config.Global.ClusterName { // kateway 其他集群创建的 clb，或者其他集群已经 reuse 了这个非tke创建的lb
			return nil, types.NewError(errcode.BelongToAnotherClusterError, "", utils.ServiceName(service))
		}
	}

	// 禁止在EKS中使用传统型负载均衡
	if utils.IsInEKSCluster() && *loadBalancer.Forward == CLASSICTYPE {
		return nil, types.NewError(errcode.ClassicLoadBalancerNotSupportedError, "")
	}
	// 禁止在MultiClusterService中使用传统型负载均衡
	if service.ServiceType() == service_wrapper.MultiClusterService && *loadBalancer.Forward == CLASSICTYPE {
		return nil, types.NewError(errcode.ClassicLoadBalancerNotSupportedError, "")
	}

	if existLBExist {
		// check if create by TKE
		if loadBalancerContext.LoadBalancerResource != nil {
			if checkCreatedByTKENew(loadBalancerContext.LoadBalancerResource) { // 复用条件下使用TKE管理的LoadBalancer
				return nil, types.NewError(errcode.ReuseAlreadyInUsedError, "", utils.ServiceName(service))
			}

			otherUsedUUIDs, exclusiveUsed := getObjectsUsingTheSameLB(service, loadBalancerContext.LoadBalancerResource)
			canReuse := true
			if service.ServiceType() == service_wrapper.CoreService && utils.IsInEKSCluster() {
				value, has := service.GetObjectMeta().GetAnnotations()[_serviceShareExistedLB] // kateway eks service 必须加上这个 anno 才能开启reuse
				canReuse = has && value == "true"
			}
			if !canReuse && !exclusiveUsed { // not enable reuse, but already used by other service, not allowed,
				glog.Errorf("Service: %s/%s want to use LB: %s, but already used by services: %s, not allowed", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), *loadBalancer.LoadBalancerId, utils.JsonWrapper(otherUsedUUIDs))
				return nil, types.NewError(errcode.ReuseFeatureNotSupportError, "", utils.ServiceName(service))
			}
		}

		// check if used by other ingress
		// if checkUsedByIngress(loadBalancer.Tags) { // 复用条件下使用TKE管理的LoadBalancer
		//	glog.Errorf("LB: %s used by another ingress, not allowed", existLB)
		//	return nil, types.NewError(errcode.ReuseBelongToAnotherIngressError, "", utils.ServiceName(service))
		// }

		// check if type is match
		// if _, typeExist := service.GetObjectMeta().GetAnnotations()[utils.LoadBalancerTypeAnnontation]; typeExist && int64(*loadBalancer.Forward) != lbConfig.Forward {
		//	glog.Warningf("LB: %s type: %d not match type: %d", lbID, *loadBalancer.Forward, lbConfig.Forward)
		//	//return types.NewError(errcode.InvalidParameterErrorCode, fmt.Sprintf("LB: %s type: %d not match type: %d", lbID, *loadBalancer.Forward, lbConfig.Forward), "")
		// }

		// 确认跨地域VPC是否支持
		// MultiCluster的管控集群不需要数据面打通。
		// 所以MultiCluster场景不需要检查CLB和集群是否在同一个云联网，只要后端接入这个CLB的集群和CLB在同一个云联网即可。

		// kateway: 我理解这里的检查和上面也是重复的

		// kateway(bitliu[39]): 隐含校验：多集群场景下，子集群的 Service 如果开启了跨域， 需要校验子集群地域、 vpc 和云联网的连通性
		if service.ServiceType() == service_wrapper.CoreService {
			if loadBalancer.VpcId == nil {
				return nil, types.NewError(errcode.LoadBalancerDoNotSupportCNNError, "", utils.ServiceName(service))
			}
			if *loadBalancer.VpcId != config.Global.VPCID && crossType == types.CrossType2_0 {
				if ok := crossregion.CrossRegionServiceInstance.CheckCCNInstance(checkRegion, *loadBalancer.VpcId); !ok { // 其他地域VPC与集群VPC不在同一个云联网
					return nil, types.NewError(errcode.CrossRegionCNNConfigError, "", utils.ServiceName(service))
				}
			}
		}
	} else {
		subnetId, existSubnetId := utils.GetSubnetId(service)
		if existSubnetId && subnetId == "" {
			return nil, types.NewError(errcode.LoadBalancerParamSubnetNotExistError, "", utils.ServiceName(service))
		}
	}
	return loadBalancerContext, nil
}

func GetTLSConfig(service service_wrapper.ServiceWrapper, secretName string) (*types.TLSConfig, error) {
	if secretName == "" {
		return nil, types.NewError(errcode.TLSSecretEmptyError, "", utils.ServiceName(service))
	}
	secret, err := cluster.Instance.SecretLister().Secrets(service.GetObjectMeta().GetNamespace()).Get(secretName)
	if err != nil {
		if errors2.IsNotFound(err) {
			return nil, types.NewError(errcode.SecretNotFoundError, "", utils.ServiceName(service), secretName)
		}
		glog.Errorf("SecretLister Get error for secrets %s/%s. error %v", service.GetObjectMeta().GetNamespace(), secretName, err)
		return nil, err
	}
	certIds, ok := secret.Data[QcloudCertId]
	if !ok || string(certIds) == "" {
		return nil, types.NewError(errcode.SecretContentError, "", utils.ServiceName(service), secretName)
	}
	cfg := &types.TLSConfig{
		SSLMode:        types.SSLModeUnidirectional,
		CertificateIDs: sets.New(string(certIds)),
	}
	if caCertId, ok := secret.Data[QcloudCACertId]; ok {
		if string(caCertId) == "" {
			return nil, types.NewError(errcode.SecretContentError, "", utils.ServiceName(service), secretName)
		}
		cfg.SSLMode = types.SSLModeMutual
		cfg.CACertificateID = lo.ToPtr(string(caCertId))
	}
	return cfg, nil
}

// ServicePreCheck 预检查,对 Service 的 Annotations 进行预校验
func ServicePreCheck(syncContext *SyncContext) error {
	// 如果有非致命错误，则只记录错误
	servicePreCheckNonFatalErrors(syncContext)
	// 如果有致命错误，则终止sync流程
	if err := servicePreCheckFatalErrors(syncContext); err != nil {
		return err
	}
	return nil
}

// Service预检查，致命错误（终止Sync）
func servicePreCheckFatalErrors(syncContext *SyncContext) error {
	service := syncContext.Service
	// 校验特殊协议注解内容，如果校验失败，则终止sync流程
	if _, _, err := utils.GetSpecifyProtocol(service); err != nil {
		// 终止原因 如果继续Sync流程，后续对账Controller会认为这些扩展协议监听器需要被删除，会造成存量监听器误删除
		return err // 致命错误，返回并终止
	}
	return nil
}

// Service预检查，非致命错误（只记录错误）
func servicePreCheckNonFatalErrors(syncContext *SyncContext) {
	service := syncContext.Service

	if _, _, err := utils.IsPreventLoopback(service); err != nil {
		syncContext.Errors = append(syncContext.Errors, err)
	}

	if _, err := utils.IsServicePassToTarget(service); err != nil {
		syncContext.Errors = append(syncContext.Errors, err)
	}

	if _, err := utils.IsModificationProtection(service); err != nil {
		syncContext.Errors = append(syncContext.Errors, err)
	}

	if _, err := utils.GetServiceSecurityGroups(service); err != nil {
		syncContext.Errors = append(syncContext.Errors, err)
	}

	if service.ServiceType() == service_wrapper.CoreService {
		if availList, labelExists := utils.GetBackendsListLabel(service); labelExists {
			if _, err := labels.Parse(availList); err != nil {
				syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.BackendsLabelAnnotationError, err.Error(), utils.ServiceName(service)))
			}
		}
	}
}

// checkPortConflict check if reuse lb port conflict
func CheckPortConflict(syncContext *SyncContext) error {
	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	usedListeners, err := GetCurrentTKEOtherResourceListeners(syncContext.LoadBalancerContext)
	if err != nil {
		return err
	}
	usedListenerIDs := sets.New[string]()
	for _, listener := range usedListeners {
		usedListenerIDs.Insert(listener.GetListenerId())
	}

	desiredListeners := syncContext.ServiceContext.ServiceListeners
	desiredListenerPorts := sets.New[string]()
	for _, desiredListener := range desiredListeners {
		desiredListenerPorts.Insert(fmt.Sprintf("%d", desiredListener.Port))
	}

	if *loadBalancer.Forward == CLASSICTYPE {
		lbListeners, err := syncContext.LoadBalancerContext.GetClassicalListeners()
		if err != nil {
			return err
		}
		for _, lbListener := range lbListeners {
			if lbListener.ListenerId == nil || lbListener.ListenerPort == nil || lbListener.Protocol == nil {
				continue
			}
			// other svc
			if usedListenerIDs.Has(*lbListener.ListenerId) {
				listenerKey := getClassicListenerKey(*lbListener.ListenerPort, *lbListener.InstancePort, *lbListener.Protocol)
				protocol := strings.ToUpper(*lbListener.Protocol)
				// tcp udp can use same port,but tcp/udp can cannot use the same port with http/https
				if _, exist := desiredListeners[listenerKey]; exist {
					syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ReuseConflictListenerError, "", utils.ServiceName(syncContext.Service), fmt.Sprintf("%s_%s", *lbListener.ListenerId, listenerKey)))
					delete(desiredListeners, listenerKey)
				} else if protocol != "TCP" && protocol != "UDP" && desiredListenerPorts.Has(fmt.Sprintf("%d", *lbListener.ListenerPort)) {
					syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ReuseConflictListenerError, "", utils.ServiceName(syncContext.Service), fmt.Sprintf("%s_%s", *lbListener.ListenerId, listenerKey)))
					delete(desiredListeners, listenerKey)
				}
			}
		}
	} else {
		lbListeners, err := syncContext.LoadBalancerContext.GetListeners()
		if err != nil {
			return err
		}
		for _, lbListener := range lbListeners {
			if lbListener.ListenerId == nil || lbListener.Port == nil || lbListener.Protocol == nil {
				continue
			}
			// other svc
			if usedListenerIDs.Has(*lbListener.ListenerId) {
				// kateway: 如果当前监听器是被其他service或者用户使用
				if utils.IsUDPFamilyProtocol(*lbListener.Protocol) {
					// kateway: 且该监听器配置了UDP类型的协议，则当前service期望的所有UDP类型的协议都会冲突
					for _, protocol := range []string{utils.PROTOCOL_UDP, utils.PROTOCOL_QUIC} {
						listenerKey := getListenerKey(*lbListener.Port, protocol)
						if _, exist := desiredListeners[listenerKey]; exist {
							syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ReuseConflictListenerError, "", utils.ServiceName(syncContext.Service), fmt.Sprintf("%s_%s", *lbListener.ListenerId, listenerKey)))
							delete(desiredListeners, listenerKey)
							break
						}
					}
				} else if utils.IsTCPFamilyProtocol(*lbListener.Protocol) {
					for _, protocol := range []string{utils.PROTOCOL_HTTP, utils.PROTOCOL_HTTPS, utils.PROTOCOL_TCP, utils.PROTOCOL_TCP_SSL} {
						listenerKey := getListenerKey(*lbListener.Port, protocol)
						if _, exist := desiredListeners[listenerKey]; exist {
							syncContext.Errors = append(syncContext.Errors, types.NewError(errcode.ReuseConflictListenerError, "", utils.ServiceName(syncContext.Service), fmt.Sprintf("%s_%s", *lbListener.ListenerId, listenerKey)))
							delete(desiredListeners, listenerKey)
							break
						}
					}
				}
			}
		}
	}

	return nil
}

// kateway: 当单个LB资源被复用的时候，LBR中会记录多个service和ingress的信息，该函数遍历LBR中记录的引用关系，找出与service使用同一个LB资源的其他service与ingress。
// kateway: 所以这里正确的英文函数名应该是 GetResourcesUsingTheSameLBAsServiceProvided
func getObjectsUsingTheSameLB(service service_wrapper.ServiceWrapper, resource *v1alpha1.LoadBalancerResource) (UIDs []string, exclusiveUsed bool) {
	usedServiceUUIDs := sets.New[string]()
	for _, listener := range resource.Spec.Listeners {
		for _, reference := range listener.References {
			if reference.Kind == string(service.ServiceType()) && reference.Namespace == service.GetObjectMeta().GetNamespace() && reference.Name == service.GetObjectMeta().GetName() {
				continue
			}
			usedServiceUUIDs.Insert(string(reference.UID))
		}
	}
	return sets.List(usedServiceUUIDs), usedServiceUUIDs.Len() == 0
}

// getLBUsedServiceUUID check if lb used by other service,not the given one,return usedUUIDs
// func getLBUsedServiceUUIDNew(service service_wrapper.ServiceWrapper, lbTags []*clb.TagInfo) (UIDs []string, exclusiveUsed bool) {
//	if utils.IsInEKSCluster() {
//		exclusiveUsed = true
//		for _, one := range lbTags {
//			if strings.HasPrefix(*one.TagValue, types.TagKeyClusterIDPrefix) && *one.TagKey != string(service.GetObjectMeta().GetUID()) {
//				UIDs = append(UIDs, *one.TagKey)
//				exclusiveUsed = false
//			}
//		}
//		return
//	}
//
//	usedServiceUUIDs := make(sets.String)
//	for _, lbTag := range lbTags {
//		if lbTag.TagKey == nil || *lbTag.TagKey == "" || lbTag.TagValue == nil || *lbTag.TagValue == "" { // 存在空值不是TKE Service标签，略过
//			continue
//		}
//		if *lbTag.TagKey == types.TagKeyServiceUUIDOld { // tke-lb-serviceuuid = <uuid>
//			usedServiceUUIDs.Insert(*lbTag.TagValue)
//		} else if *lbTag.TagValue == types.TagKeyServiceUUID { // <uuid> = tke-lb-serviceId
//			usedServiceUUIDs.Insert(*lbTag.TagKey)
//		}
//	}
//	currentUUIDSet := make(sets.String)
//	currentUUIDSet.Insert(string(service.GetObjectMeta().GetUID()))
//	return usedServiceUUIDs.Difference(currentUUIDSet).List(), usedServiceUUIDs.Equal(currentUUIDSet)
// }

// kateway 用 instanceId 补全 Target 字段
// getInstanceIDByIP get lb instance by private ip
func GetInstanceIDByIP(service service_wrapper.ServiceWrapper, targets []*types.Target) ([]*types.Target, error) {
	ipList := make([]string, 0)
	for _, target := range targets {
		for _, address := range target.Node.Status.Addresses {
			if address.Type == "InternalIP" && address.Address != "" {
				if target.Target == "" { // 不需要转换，已经通过节点信息获得InstanceId
					ipList = append(ipList, address.Address)
				}
			}
		}
	}
	privateIpToInstanceId, err := GetBackendIDs(service, ipList)
	if err != nil {
		return nil, err
	}

	result := make([]*types.Target, 0)
	for index, target := range targets {
		if target.Target != "" {
			result = append(result, targets[index])
		} else {
			for _, address := range target.Node.Status.Addresses {
				if address.Type == "InternalIP" && address.Address != "" {
					if instanceId, exist := privateIpToInstanceId[address.Address]; exist {
						targets[index].Target = instanceId
						result = append(result, targets[index])
					}
				}
			}
		}
	}
	return result, nil
}

/*
 * 根据后端`private-ip-address`查询，批量数量限制过窄(5个)
 * 	 1. 查询VPC下的CVM列表，批量100个
 *   2. 查询VPC下指定`ip`的CVM列表，批量5个
 * 考虑优先获取VPC下的机器数量，哪种查询方式的请求次数少，就用哪种方式查询
 */
func GetBackendIDs(service service_wrapper.ServiceWrapper, ipList []string) (map[string]string, error) {
	if len(ipList) <= 0 {
		return map[string]string{}, nil
	}

	vpcIDList := &[]string{config.Global.VPCID}
	response, err := getInstancesByVpcList(service, vpcIDList, 0, DESCRIBE_INSTANCES_LIMIT)
	if err != nil {
		glog.Errorf("Get cvm (vpcId: %v) info error: %v", config.Global.VPCID, err)
		return map[string]string{}, err
	}

	result := make(map[string]string)
	backendMap := make(map[string]bool) // instanceId to Port
	for _, backend := range ipList {
		backendMap[backend] = true
	}

	totalCount := *response.Response.TotalCount
	pageVPCQuery := int((totalCount + DESCRIBE_INSTANCES_LIMIT - 1) / DESCRIBE_INSTANCES_LIMIT)                          // 在VPC下查询所有实例需要的次数（已经执行一次）
	pageFilterQuery := (len(ipList) + DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT - 1) / DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT // 在VPC下根据IP筛选查询需要的次数
	if pageVPCQuery-1 < pageFilterQuery {                                                                                // 直接通过VPC下所有CVM查询
		// 根据查询结果，筛选后端实例到result中
		filterRecord := func(instanceSet *[]*cvm.Instance, backendMap *map[string]bool, result *map[string]string) {
			for _, instance := range *instanceSet {
				for _, ip := range instance.PrivateIpAddresses {
					if _, ok := (*backendMap)[*ip]; ok {
						(*result)[*ip] = *instance.InstanceId
						break
					}
				}
			}
		}

		filterRecord(&response.Response.InstanceSet, &backendMap, &result)
		for i := 1; i < pageVPCQuery; i++ {
			response, err := getInstancesByVpcList(service, vpcIDList, int64(i*DESCRIBE_INSTANCES_LIMIT), DESCRIBE_INSTANCES_LIMIT)
			if err != nil {
				glog.Errorf("Get cvm (vpcId: %v) info error: %v", config.Global.VPCID, err)
				return map[string]string{}, err
			}
			filterRecord(&response.Response.InstanceSet, &backendMap, &result)
		}
	} else { // 通过`private-ip-address`筛选查询，每次最多筛选出五个后端实例的信息
		for i := 0; i < len(ipList); i += DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT {
			backend := ipList[i:min(i+DESCRIBE_INSTANCES_FILTER_VALUE_LIMIT, len(ipList))]

			response, err := getInstancesByBackendAndVpcList(service, &backend, vpcIDList)
			if err != nil {
				glog.Errorf("Get cvm (lanIP: %v) info error: %v", backend, err)
				return map[string]string{}, err
			}

			instanceSet := response.Response.InstanceSet
			for _, instance := range instanceSet {
				for _, ip := range instance.PrivateIpAddresses {
					if _, ok := backendMap[*ip]; ok {
						result[*ip] = *instance.InstanceId
						break
					}
				}
			}
		}
	}

	if len(ipList) != len(result) {
		instanceList := make([]string, len(ipList)-len(result))
		index := 0
		for _, ip := range ipList {
			if _, exist := result[ip]; !exist {
				instanceList[index] = ip
				index++
			}
		}
		glog.Errorf("meet error: some cvms cannot be found in cvm list (result list: %v, check list: %v)", instanceList, ipList)
	}

	return result, nil
}

func getInstancesByVpcList(service service_wrapper.ServiceWrapper, vpcIdList *[]string, offset int64, limit int64) (*cvm.DescribeInstancesResponse, error) {
	request := cvm.NewDescribeInstancesRequest()
	request.Offset = &offset
	request.Limit = &limit
	request.Filters = []*cvm.Filter{
		{Name: common.StringPtr("vpc-id"), Values: common.StringPtrs(*vpcIdList)},
	}

	region := config.Global.Region
	response, err := tencentapi.Instance.DescribeInstances(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func getInstancesByBackendAndVpcList(service service_wrapper.ServiceWrapper, backend *[]string, vpcIdList *[]string) (*cvm.DescribeInstancesResponse, error) {
	request := cvm.NewDescribeInstancesRequest()
	request.Offset = common.Int64Ptr(0)
	request.Limit = common.Int64Ptr(100)
	request.Filters = []*cvm.Filter{
		{Name: common.StringPtr("vpc-id"), Values: common.StringPtrs(*vpcIdList)},
		{Name: common.StringPtr("private-ip-address"), Values: common.StringPtrs(*backend)},
	}

	region := config.Global.Region
	response, err := tencentapi.Instance.DescribeInstances(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func GetBackendHealth(service service_wrapper.ServiceWrapper, region string, lbId string) (*clb.LoadBalancerHealth, error) {
	request := clb.NewDescribeTargetHealthRequest()
	request.LoadBalancerIds = []*string{&lbId}

	response, err := tencentapi.Instance.DescribeTargetHealth(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}

	if len(response.Response.LoadBalancers) != 0 {
		return response.Response.LoadBalancers[0], nil
	}
	return nil, nil
}

func GetIngressBackendHealth(service service_wrapper.ServiceWrapper, ingress types.Ingress, lbID string) (*clb.LoadBalancerHealth, error) {
	region := utils.GetIngressCrossRegionID(ingress)

	request := clb.NewDescribeTargetHealthRequest()
	request.LoadBalancerIds = []*string{&lbID}

	response, err := tencentapi.Instance.DescribeTargetHealth(cloudctx.New(service, region), request)
	if err != nil {
		return nil, err
	}

	if len(response.Response.LoadBalancers) != 0 {
		return response.Response.LoadBalancers[0], nil
	}
	return nil, nil
}

// getLoadBalancerName return load balancer name for service
func getLoadBalancerName(service service_wrapper.ServiceWrapper, clusterName string) string {
	lbName := fmt.Sprintf("%s_%s_%s", clusterName, service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName())

	if len(lbName) > LoadbalancerNameMaxSize {
		lbName = lbName[0:LoadbalancerNameMaxSize]
	}
	return lbName
}

// getLbPrefixName
func getLbPrefixName(lbName string) (pName string) {
	pName = strings.Replace(lbName, "_", "-", -1)
	if len(pName) > 20 {
		pName = pName[0:20]
	}
	return pName
}

// min return the min of two int
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func newTagInfo(tagKey string, tagValue string) *clb.TagInfo {
	return &clb.TagInfo{
		TagKey:   &tagKey,
		TagValue: &tagValue,
	}
}
