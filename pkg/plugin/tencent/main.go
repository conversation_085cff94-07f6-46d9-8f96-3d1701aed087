package tencent

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/client-go/kubernetes"
	glog "k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/controllercm"
	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/domain/event"
	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/net"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/utils"
	"cloud.tencent.com/lb-controller/pkg/utils/cluster_service"
)

const (
	QUERYLIMIT       = 3
	LBLISTENERMAXLEN = 50
	LBNORMALSTATUS   = 1

	dns1123LabelFmt     string = "[a-z0-9]([-a-z0-9]*[a-z0-9])?"
	dns1123SubdomainFmt string = dns1123LabelFmt + "(\\." + dns1123LabelFmt + ")*"
)

var dns1123SubdomainRegexp = regexp.MustCompile("^" + dns1123SubdomainFmt + "$")

type YunAPIV3FORWARDCLB struct {
	providerName string
	ownerUin     int64
	clusterName  string
	projectId    *int64
	vpcId        string
	regionName   string
	// 用户指定的quota
	backendQuota  int
	listenerQuota int
	// whether allow to using a exist
	reuseFlag bool
	// 实际上起作用的quota
	backendQuotaInUse int
}

// GetLoadBalancer return lb status for service
// func GetLoadBalancer(service service_wrapper.ServiceWrapper) (*v1.LoadBalancerStatus, bool, error) {
//	lb, exist, err := GetLoadBalancerByService(service)
//	if err != nil || !exist || lb == nil {
//		return nil, exist, err
//	}
//	// verify lb forward type match
//	//if *lb.Forward != FORWARDTYPE {
//	//	return nil, false, nil
//	//}
//	// vip empty
//	if lb.LoadBalancerVips == nil || len(lb.LoadBalancerVips) == 0 {
//		glog.Errorf("Cannot get loadbalance: %s vip", *lb.LoadBalancerId)
//		return nil, true, types.NewError(svcError.ServiceCLBUnexpectedError, "")
//	}
//
//	// multi vip
//	if len(lb.LoadBalancerVips) > 1 {
//		glog.Warningf("Get multi vip for LB: %s", *lb.LoadBalancerId)
//	}
//
//	lbDomain := ""
//	if lb.Domain != nil {
//		lbDomain = *lb.Domain
//	}
//	ingress := make([]v1.LoadBalancerIngress, 0)
//	for _, v := range lb.LoadBalancerVips {
//		ingress = append(ingress, v1.LoadBalancerIngress{IP: *v, Hostname: lbDomain})
//	}
//	if lb.AddressIPVersion != nil && lb.AddressIPv6 != nil &&
//		strings.ToLower(*lb.AddressIPVersion) == "ipv6" && *lb.AddressIPv6 != "" {
//		ingress = append(ingress, v1.LoadBalancerIngress{IP: *lb.AddressIPv6, Hostname: lbDomain})
//	}
//	return &v1.LoadBalancerStatus{Ingress: ingress}, true, nil
// }

// GetLoadBalancerName return lbname using service as readonly
// func GetLoadBalancerName(ctx context.Context, clusterName string, service service_wrapper.ServiceWrapper) string {
//	return fmt.Sprintf("%s/%s/%s", clusterName, service.Namespace, service.Name)
// }

// GetLoadBalancerByService get lb associated with service,first try to find lb using tag, then try to find user assigned lbs
// func GetLoadBalancerByService(service service_wrapper.ServiceWrapper) (*clb.LoadBalancer, bool, error) {
//	// first try to find associated lb using tags
//	lb, exist, err := GetLoadBalancerByTags(service)
//	if err != nil {
//		return lb, exist, err
//	}
//	if lb == nil { // if user assign exist type lb
//		if existedLBId, exist := utils.GetExistLB(service); exist {
//			loadBalancer, err := getLoadBalancers(service, existedLBId)
//			if serviceErr, ok := err.(*svcError.ServiceError); ok {
//				if serviceErr.ErrorCode == svcError.LoadBalancerNotExistError.Code { // 修改错误码，注解中描述的LoadBalancer不存在
//					return nil, false, types.NewError(svcError.ReuseLBNotExistError, "")
//				}
//			}
//			return loadBalancer, loadBalancer != nil, err
//		}
//	}
//	return lb, exist, err
// }

// EnsureLoadBalancer ensure lb created and nodes binds for service,
// kateway(bitliu[46]): 对于 Service 未开启 ManagerOnly 管理 CLB 信息、Listener 以及 RS
func EnsureLoadBalancer(syncContext *SyncContext) error {
	if err := EnsureLoadBalancerDetail(syncContext); err != nil { // kateway 向上同步 CLB 级别信息
		glog.Error(err)
		return err
	}
	if err := EnsureLoadBalancerListener(syncContext); err != nil { // kateway 向上同步 监听器级别信息
		glog.Error(err)
		return err
	}
	if _, exist, _ := utils.GetSpecifyProtocol(syncContext.Service); exist {
		if err := EnsureLoadBalancerListenerRule(syncContext); err != nil { // kateway 向上同步监听器的协议扩展（ListenerRule）
			glog.Error(err)
			return err
		}
	}

	// ensure targets
	if err := EnsureTargets(syncContext); err != nil {
		glog.Error(err)
		return err
	}

	if err := EnsureTkeServiceConfig(syncContext); err != nil { // 将端监听器信息同步到 tkeserviceconfig
		return err
	}
	return nil
}

// kateway(bitliu[47]): 对于 MCS 管理 CLB 信息、Listener
func EnsureLoadBalancerFrame(syncContext *SyncContext) error {
	if err := EnsureLoadBalancerDetail(syncContext); err != nil {
		glog.Error(err)
		return err
	}
	if err := EnsureLoadBalancerListener(syncContext); err != nil {
		glog.Error(err)
		return err
	}
	if _, exist, _ := utils.GetSpecifyProtocol(syncContext.Service); exist {
		if err := EnsureLoadBalancerListenerRule(syncContext); err != nil {
			glog.Error(err)
			return err
		}
	}
	if err := EnsureTkeServiceConfig(syncContext); err != nil {
		return err
	}
	return nil
}

// EnsureLoadBalancer ensure lb created and nodes binds for service,

// kateway(bitliu[48]): 对于 Service 如果开启了 ManagerOnly 执行 EnsureLoadBalancerBackend，只管理 RS
func EnsureLoadBalancerBackend(syncContext *SyncContext) error {
	if err := EnsureLoadBalancerListenerBackendManagerOnly(syncContext); err != nil { // kateway: 这里只解绑target
		glog.Error(err)
		return err
	}

	// ensure targets
	if err := EnsureTargets(syncContext); err != nil { // kateway: 这里只加target
		glog.Error(err)
		return err
	}
	return nil
}

// EnsureLoadBalancerDeleted ensure lb deleted for service
func EnsureLoadBalancerDeleted(service service_wrapper.ServiceWrapper, loadBalancerContext *LoadBalancerContext) error { // kateway(bitliu[20]): 不管是 managerOnly 的 service 还是 MCS 的删除具体处理逻辑
	deleted := false
	defer func() {
		if deleted {
			cluster.Instance.EventRecorder().Event(service.GetRuntimeObject(), v1.EventTypeNormal, "EnsuringService", "Deleted Loadbalancer")
		}
	}()
	// no lb found for service
	if loadBalancerContext == nil {
		if utils.IsLoadBalancerType(service) {
			glog.Infof("No lb found for %s: %s UUID: %s, skip delete", service.ServiceType(), utils.ServiceName(service), service.GetObjectMeta().GetUID())
		}
		return nil
	}
	// verify lb forward type match
	// if *lb.Forward != FORWARDTYPE {
	//	glog.Infof("LB %s for service: %s/%s UUID %s is not forward type, skip delete", *lb.LoadBalancerId, service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), string(service.GetObjectMeta().GetUID()))
	//	return nil
	// }

	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	if canProcess := LockLoadBalancerResource(*loadBalancer.LoadBalancerId, service); !canProcess {
		return types.NewError(errcode.ReuseConcurrentOperationError, "", utils.ServiceName(service))
	}
	defer func(loadBalancerId string) {
		if err := UnlockLoadBalancerResource(loadBalancerId); err != nil {
			glog.Errorf("LoadBalancerResource unlock failed for lbid: %s, err: %v", loadBalancerId, err)
		}
	}(*loadBalancer.LoadBalancerId)
	// kateway(bitliu[21]): 获取 CLB 上的云标签，clusterId
	clusterId := GetClusterIdByTags(loadBalancer.Tags)

	lb := types.NewLB(loadBalancer, checkCreatedByTKENew(loadBalancerContext.LoadBalancerResource))
	// kateway: clb 由该集群管理，有可能是tke 创建，也可能是reuse

	// kateway(bitliu[23]): 对于多集群场景的 子集群 svc 不会进入这个逻辑，因为 cluster ID 为父集群 id
	// MCS 或者单集群 Service 才会进入

	if clusterId != "" && clusterId == config.Global.ClusterName { // 当前资源属于该集群，清理负载均衡相关资源
		if err := checkBackendEmpty(service, loadBalancerContext); err != nil {
			return err
		}

		deletable := lb.IsDeletable()
		if !deletable { // kateway：reuse 场景, 只删rs或者监听器, 不删clb
			backendManageOnly, _, _ := utils.GetBackendManageOnly(service)
			if backendManageOnly { // kateway todo 这里应该进不来, mcs 场景下 clusterId != utils.GlobalConfigs.ClusterName
				// kateway(bitliu[24]): 对于使用已有的 CLB，如果开启 BackendOnly，只删除后端 RS
				// 1、对于 MCS 目前没有使用 managerOnly 的模式，不会进入这个逻辑
				// 2、 对于 普通 Service，同时开启 ManagerOnly，才会进入这里的逻辑（不过目前这个 ManagerOnly 只使用在了多集群的子集群 Service）
				if err := EnsureLoadBalancerTargetsDeleted(loadBalancerContext); err != nil {
					return err
				}
			} else { // kateway 传统的reuse
				// delete lb listener
				glog.Infof("EnsureLoadBalancerDeleted service: %s/%s loadBalancer: %s not create by TKE: %t",
					service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), utils.JsonWrapper(loadBalancer), lb.IsAutoCreatedByTKE())
				// kateway(bitliu[25]): 对于自使用已有的 CLB，如果没开启 BackendOnly，只删除监听器
				// 1、对于 MCS 会进入这个逻辑，删除管理的监听器
				// 2、对于 普通 Service 未开启 ManagerOnly，即普通的 Service，会进入这里的逻辑
				if err := EnsureLoadBalancerListenerDeleted(loadBalancerContext); err != nil {
					return err
				}
				// TODO need to clear classic lb backends?
			}
			if lb.IsAutoCreatedByTKE() {
				event.Instance.EventError(service,
					types.NewError(errcode.ExplicitCLBResourceLeak, "", utils.ServiceName(service), *lb.LoadBalancerId))
			}
			// clear lb exist tag
			if err := EnsureLoadBalancerTagsDeleted(loadBalancerContext); err != nil {
				return err
			}
		} else { // kateway： tke 创建场景，直接删除clb
			// delete lb created by tke

			// kateway(bitliu[26]): 对于自动创建的 CLB
			// 对于 MCS 会进入，删除整个 CLB
			// 对于单集群 Service 会进入，删除整个 CLB
			// 对于多集群子集群 Service 不会进入，因为 cluster ID 为父集群 id（但在多集群场景下，如果人为改 cluster id 为子集群 id，会导致 CLB 删除）
			glog.Infof("EnsureLoadBalancerDeleted service: %s/%s loadBalancer: %s create by TKE: %t, deletion protection status: %s",
				service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), utils.JsonWrapper(loadBalancer),
				lb.IsAutoCreatedByTKE(), lo.Ternary(lb.IsDeletionProtectionEnabled(), "enabled", "disabled"))

			// clb或者监听器的删除也可能导致node与clb的解绑，所以优雅删除开关打开时需要入队处理node的finalizer
			var nodesByID map[string]*v1.Node
			var ListenersBackendsByKey map[string]*clb.ListenerBackend
			if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
				nodesByID, err = services.GetNodesByID(cluster.Instance.NodeLister())
				if err != nil {
					return err
				}
				ListenersBackendsByKey, err = loadBalancerContext.GetListenersBackend()
				if err != nil {
					return err
				}
			}

			if err := DeleteLoadBalancer(service, loadBalancerContext.Region, *loadBalancer.LoadBalancerId); err != nil {
				if sdkError, ok := lo.ErrorsAs[*errors.TencentCloudSDKError](err); ok {
					if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "deletion protection enabled") {
						return types.NewError(errcode.DeletionProtectionError, sdkError.Message, utils.ServiceName(service))
					}
					if sdkError.Code == "FailedOperation" && strings.Contains(sdkError.Message, "be deleted") {
						return types.NewError(errcode.ForbiddenDeletionError, sdkError.Message, utils.ServiceName(service))
					}
				}
				return err
			} else if cluster.Instance.Enabled(featuregates.NodeGracefulDeletion) {
				nodes := services.GetNodesFromListenersBackendsByKey(ListenersBackendsByKey, nodesByID)
				for node := range nodes {
					cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnqueueNode(node)
				}
			}

			deleted = true

			tagResourceList := utils.ConvertTagResourceList(loadBalancer.Tags)
			if err := RecycleLoadbalancerTags(service, tagResourceList); err != nil {
				return err
			}
		}
	}
	// kateway(bitliu[27]): 对于删除事件，上面的逻辑没有处理到多集群场景的子集群 svc，下面这里是对子集群 svc 删除的处理
	// 子集群 svc 上有 "service.cloud.tencent.com/from-other-cluster" annotation，标识了 CLB 的归属集群（父集群）
	// 如果 annotation 内容正确，则会按预期进入下面删除逻辑
	// 如果 annotation 内容有误，或者云标签 clusterid 有误，不会进入下面的逻辑，导致脏数据
	// 对于普通 service 和 MCS 不会进入这个逻辑
	fromOtherCluster, _ := utils.GetFromOtherCluster(service)
	// kateway 当前是子集群，使用主集群管理的clb
	if clusterId != "" && clusterId == fromOtherCluster { // 当前资源被该集群使用，清理负载均衡相关资源
		if err := checkBackendEmpty(service, loadBalancerContext); err != nil {
			return err
		}

		backendManageOnly, _, _ := utils.GetBackendManageOnly(service)
		if backendManageOnly { // kateway 只管理rs，所以只需要卸载rs
			// kateway(bitliu[28]): 对于子集群 service，同时是 ManagerOnly 模式，删除会导致 RS 清理（按道理子集群 Service 预期是进入这个逻辑）
			if err := EnsureLoadBalancerTargetsDeleted(loadBalancerContext); err != nil {
				return err
			}
		} else { // kateway 卸载当前 service 的监听器 todo 存在的场景？这里应该进不来。有点像传统的 reuse
			// delete lb listener
			glog.Infof("EnsureLoadBalancerDeleted service: %s/%s loadBalancer: %s not create by TKE: %t",
				service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), utils.JsonWrapper(loadBalancer), lb.IsAutoCreatedByTKE())
			// kateway(bitliu[28]): 对于子集群 service，同时没有开启 ManagerOnly 模式，删除会导致 Listener 清理（按道理子集群 Service 不应该进入这个逻辑）
			if err := EnsureLoadBalancerListenerDeleted(loadBalancerContext); err != nil {
				return err
			}
		}
	}

	// kateway 开始清理LBR
	if !lb.IsAutoCreatedByTKE() { // kateway 不是tke 创建的 clb， 只需要删除 lbr
		// delete lb listener
		if err := DeleteLoadBalancerResourceByService(*loadBalancer.LoadBalancerId, service); err != nil { // kateway 局部删除? 存在的场景：同集群里 2 个svc reuse 一个外部clb
			return err
		}
	} else {
		// delete lb created by tke
		if err := DeleteLoadBalancerResource(*loadBalancer.LoadBalancerId); err != nil {
			return err
		}
	}

	if err := cluster.Instance.TkeServiceConfigClient().CloudV1alpha1().TkeServiceConfigs(service.GetObjectMeta().GetNamespace()).Delete(context.Background(), utils.ServiceAutoServiceConfigName(service), metav1.DeleteOptions{}); err != nil {
		if !k8serrors.IsNotFound(err) {
			glog.Errorf("Error deleting TkeServiceConfigs for service %s %v", utils.ServiceName(service), err)
		}
	}
	return nil
}

func checkBackendEmpty(service service_wrapper.ServiceWrapper, loadBalancerContext *LoadBalancerContext) error {
	// MultiClusterService 的场景下的误删防御
	if service.ServiceType() == service_wrapper.MultiClusterService {
		listeners, err := GetCurrentTKEServiceListeners(loadBalancerContext)
		if err != nil {
			return err
		}

		backends, err := loadBalancerContext.GetListenersBackend()
		if err != nil {
			return err
		}

		for listenerKey := range listeners {
			backend := backends[listenerKey]
			if backend.Protocol != nil {
				if utils.IsL4Protocol(*backend.Protocol) {
					if backend.Targets != nil && len(backend.Targets) != 0 {
						return types.NewError(errcode.MultiClusterDeleteWithBackend, "", utils.ServiceName(service))
					}
				} else {
					if backend.Rules != nil && len(backend.Rules) != 0 {
						for _, rule := range backend.Rules {
							if rule.Targets != nil && len(rule.Targets) != 0 {
								return types.NewError(errcode.MultiClusterDeleteWithBackend, "", utils.ServiceName(service))
							}
						}
					}
				}
			}
		}
	}
	return nil
}

// GetLoadBalancerByService get lb associated with service,first try to find lb using tag, then try to find user assigned lbs
func GetLoadBalancerIDByService(syncContext *SyncContext) (string, string, bool, error) {
	service := syncContext.Service
	lb, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return "", "", false, err
	}

	if lb.LoadBalancerId == nil {
		glog.Errorf("service: %s/%s lb instanceId is missing", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName())
		return "", "", false, types.NewError(errcode.ServiceCLBUnexpectedError, "")
	}

	natipv6 := ""
	if lb.AddressIPVersion != nil && strings.ToLower(*lb.AddressIPVersion) == "ipv6" {
		if lb.IPv6Mode != nil && lb.AddressIPv6 != nil && strings.ToLower(*lb.IPv6Mode) == "ipv6nat64" {
			natipv6 = *lb.AddressIPv6
		}
	}

	// check lb status
	// if *lb.Status != LBNORMALSTATUS {
	//	glog.Errorf("LB %s status is %d, not normal", *lb.LoadBalancerId, *lb.Status)
	//	return "", exist, types.NewError(svcError.InternalErrorCode, fmt.Sprintf("LB %s status is %d, not normal", *lb.LoadBalancerId, *lb.Status), "")
	// }

	return *lb.LoadBalancerId, natipv6, true, nil
}

// todo 这个判断缓存起来，定时更新, 这个函数判断放在这个位置也不合适
func IngressControllerDisabled(ctx context.Context, cli kubernetes.Interface) (bool, error) {
	if env.IsInEKSCluster() {
		return false, nil
	}

	var (
		disabledByConfigMap  = true
		disabledByDeployment bool
	)
	cm, err := cli.CoreV1().ConfigMaps(metav1.NamespaceSystem).Get(ctx, controllercm.ConfigMapService, metav1.GetOptions{})
	if err != nil {
		if !k8serrors.IsNotFound(err) {
			return false, err
		}
	} else if cm.Data[controllercm.KeyEnableIngressController] == "true" {
		disabledByConfigMap = false
	}

	deploy, err := cluster.Instance.KubeClient().AppsV1().Deployments(metav1.NamespaceSystem).
		Get(ctx, "l7-lb-controller", metav1.GetOptions{})
	if err != nil {
		if !k8serrors.IsNotFound(err) {
			glog.Errorf("Failed to get ingress controller: %v", err)
			return false, err
		}
		// Ingress deployment 不存在
		glog.Info("Ingress controller of standalone mode is disabled: deployment not found")
		disabledByDeployment = true
	} else if lo.FromPtrOr(deploy.Spec.Replicas, 1) == 0 { // Ingress 组件没有副本运行的场景。
		glog.Info("Ingress controller of standalone mode is disabled: replicas is 0")
		disabledByDeployment = true
	}
	return disabledByConfigMap && disabledByDeployment, nil
}

func BackendHealthCheckService(service service_wrapper.ServiceWrapper, pod *v1.Pod) error {
	if utils.GetServiceReadinessGateSkip(service) {
		return nil
	}
	if !utils.IsLoadBalancerType(service) {
		return nil
	}

	if !utils.IsServiceDirectAccess(service) {
		return nil
	}

	balancerContext, err := GetLoadBalancerByLoadbalancerResource(service)
	if err != nil {
		return err
	}
	// no lb found for service
	if balancerContext == nil {
		return nil // 可能是初次发布，lbr 还未创建好，不需要做后端健康检查
	}

	loadBalancer, err := balancerContext.GetLoadBalancer()

	if err != nil {
		if errcode.IsLoadBalancerNotExistError(err) {
			return nil // lb 实例被用户删除了，不需要做后端健康检查
		}
		return err
	}
	if *loadBalancer.Forward == 0 { // 传统型不支持弹性网卡后端
		return nil
	}

	loadBalancerHealthMap := make(map[string][]*clb.RuleHealth)
	loadBalancerHealth, err := GetBackendHealth(service, balancerContext.Region, *loadBalancer.LoadBalancerId)
	if err != nil {
		return err
	}
	if loadBalancerHealth == nil {
		message := fmt.Sprintf("Readiness check failed. can not get loadBalancerHealth for service %s", utils.ServiceName(service))
		updatePodReadinessGate(pod, message)
		return fmt.Errorf(message)
	}
	for _, listenerHealth := range loadBalancerHealth.Listeners {
		listenerKey := getListenerKey(*listenerHealth.Port, *listenerHealth.Protocol)
		loadBalancerHealthMap[listenerKey] = listenerHealth.Rules
	}

	currentListeners, err := GetCurrentTKEServiceListeners(balancerContext)
	if err != nil {
		return err
	}

	listenerIdsByResource := make(sets.String)
	listenerByService := GetLoadBalancerResourceListenerByService(service, balancerContext.LoadBalancerResource)
	for _, listener := range listenerByService {
		listenerIdsByResource.Insert(getListenerKey(int64(listener.Port), listener.Protocol))
	}

	// kateway 把 currentListeners 和远程 clb平台的 loadBalancerHealth 做对比，确保 currentListeners 全部健康
	for listenerKey, listener := range currentListeners {
		// 由于 GetCurrentTKEServiceListeners 会在TKE管理的模式下。将所有监听器都算进来，所以这里要加一个过滤。 kateway todo 没明白
		if !listenerIdsByResource.Has(listenerKey) {
			continue
		}

		healthMap := make(map[string][]*clb.RuleHealth)
		ruleList, exist := loadBalancerHealthMap[listenerKey]
		if !exist {
			message := fmt.Sprintf("Readiness check failed according to listener is not exist: %s(%s:%d) of CLB %s. Service: %s", listener.GetListenerId(), listener.GetProtocol(), listener.GetListenerPort(), *loadBalancer.LoadBalancerId, utils.ServiceName(service))
			updatePodReadinessGate(pod, message)
			return fmt.Errorf(message)
		}
		if utils.IsL4Protocol(listener.GetProtocol()) {
			healthMap[""] = ruleList
		} else {
			for index, rule := range ruleList {
				ruleKey := getRuleKey(*rule.Domain, *rule.Url)
				if healthMap[ruleKey] == nil {
					healthMap[ruleKey] = make([]*clb.RuleHealth, 0)
				}
				healthMap[ruleKey] = append(healthMap[ruleKey], ruleList[index])
			}
		}

		for _, healthList := range healthMap {
			for _, rule := range healthList {
				rsFound := false
				isHealth := false
			outer:
				for _, target := range rule.Targets {
					for _, podIP := range utils.GetPodIPs(pod) {
						if *target.IP == podIP {
							rsFound = true
							if *target.HealthStatusDetail == "Alive" || *target.HealthStatusDetail == "Close" {
								isHealth = true
								break outer
							}
						}
					}
				}
				if isHealth == false {
					var message string
					if rsFound {
						err = errcode.HealthCheckNotReadyError
					} else {
						err = errcode.HealthCheckServiceRSNotFoundError
					}

					if utils.IsL7Protocol(listener.GetProtocol()) {
						message = fmt.Sprintf("Readiness check failed because %s, belongs to l7 listener: %s(%s:%d) Host: %s Path: %s of CLB %s. Service: %s", err.Error(), listener.GetListenerId(), listener.GetProtocol(), listener.GetListenerPort(), *rule.Domain, *rule.Url, *loadBalancer.LoadBalancerId, utils.ServiceName(service))
					} else {
						message = fmt.Sprintf("Readiness check failed because %s, belongs to l4 listener: %s(%s:%d) of CLB %s. Service: %s", err.Error(), listener.GetListenerId(), listener.GetProtocol(), listener.GetListenerPort(), *loadBalancer.LoadBalancerId, utils.ServiceName(service))
					}
					updatePodReadinessGate(pod, message)

					return fmt.Errorf("health check for backend pod %s(ip: %s) error: %w. belongs to listener %s of LoadBalancer %s", utils.PodName(pod), utils.GetPodIPs(pod), err, listenerKey, *loadBalancer.LoadBalancerId)
				}
			}
		}
	}
	return nil
}

// 3次重试修改Pod状态，失败重新
func updatePodReadinessGate(pod *v1.Pod, message string) {
	var err error
	for i := 0; i < 3; i++ {
		if err = updatePodReadinessGateFailInfo(pod, message); err != nil {
			if pod, err := cluster.Instance.PodLister().Pods(pod.Namespace).Get(pod.Name); err != nil {
				if k8serrors.IsNotFound(err) {
					glog.Infof("Pod Conditions Change Error. Pod Not Exist. %v", err)
					return
				}
				glog.Errorf("PodLister Get error for pod %s. error %v", utils.PodName(pod), err)
				return
			}
		} else { // Pod的状态修改成功处理
			return
		}
	}
	glog.Errorf("Pods UpdateStatus error for pod %s three times. error %v", utils.PodName(pod), err)
}

func updatePodReadinessGateFailInfo(pod *v1.Pod, message string) error {
	for index, condition := range pod.Status.Conditions { // 处理并发可能，已经被其他线程处理掉Status的添加
		if condition.Type == utils.DirectAccessConditionType {
			if condition.Message == message {
				return nil
			}

			newPodCondition := v1.PodCondition{
				Type:               utils.DirectAccessConditionType,
				Status:             v1.ConditionFalse,
				LastProbeTime:      metav1.Time{},
				LastTransitionTime: metav1.Time{Time: time.Now()},
				Reason:             utils.DirectAccessConditionNotReady,
				Message:            message,
			}
			pod.Status.Conditions[index] = newPodCondition
			if _, err := cluster.Instance.KubeClient().CoreV1().Pods(pod.Namespace).UpdateStatus(context.Background(), pod, metav1.UpdateOptions{}); err != nil {
				return err
			}
			event.Instance.EventPod(pod, v1.EventTypeWarning, message)
			return nil
		}
	}
	return nil
}

func BackendHealthCheckIngress(service service_wrapper.ServiceWrapper, ingress types.Ingress, pod *v1.Pod) error {
	if !utils.IsQCLOUDIngress(ingress) { // 非TKE Ingress管理的资源，略过健康检查。
		glog.Infof("BackendHealthCheck Skip Ingress(%s). This resource is not manager by TKE.", utils.IngressName(ingress))
		return nil
	}

	lb, exist, err := GetLoadBalancerByLoadbalancerResourceIngress(service, ingress)
	if err != nil {
		return err
	}
	// no lb found for ingress
	if !exist {
		return nil // 如果 lb 被用户删除了，或者初次发布时 lb还未创建好，不需要做后端健康检查
	}
	if *lb.Forward == 0 { // 传统型不支持弹性网卡后端
		return nil
	}

	loadBalancerHealthMap := make(map[string][]*clb.TargetHealth)
	loadBalancerHealth, err := GetIngressBackendHealth(service, ingress, *lb.LoadBalancerId)
	if err != nil {
		return err
	}
	if loadBalancerHealth == nil {
		message := fmt.Sprintf("Readiness check failed. can not get loadBalancerHealth for ingress %s (which use service %s)", utils.IngressName(ingress), utils.ServiceName(service))
		updatePodReadinessGate(pod, message)
		return fmt.Errorf(message)
	}
	for _, listenerHealth := range loadBalancerHealth.Listeners {
		if utils.IsL7Protocol(strings.ToUpper(*listenerHealth.Protocol)) {
			for _, rule := range listenerHealth.Rules {
				listenerKey := getL7RuleKey(*listenerHealth.Port, strings.ToUpper(*listenerHealth.Protocol), *rule.Domain, *rule.Url)
				loadBalancerHealthMap[listenerKey] = rule.Targets
			}
		}
	}

	// 1. 弹性网卡IP
	// 2. Ingress的坐标依赖的Service确认
	defaultDomain := ""
	if domain := utils.GetDefaultDomain(lb); domain != nil {
		defaultDomain = *domain
	}
	httpRules, httpsRules, err := getIngressRules(ingress, defaultDomain)
	if err != nil {
		return err
	}

	portsByProtocol, err := services.ResolveListenPortsFromAnnotation(ingress.Annotations(), len(ingress.TLS()) > 0)
	if err != nil {
		return fmt.Errorf("failed to resolve listen ports annotation: %w", err)
	}

	for _, httpRule := range httpRules {
		if httpRule.Backend.ServiceName() == service.GetObjectMeta().GetName() { // Service Is Used
			for _, port := range portsByProtocol[net.ProtocolHTTP] {
				listenerKey := getL7RuleKey(int64(port), "HTTP", httpRule.Host, httpRule.RealPath)
				if err := checkIngressTarget(loadBalancerHealthMap, listenerKey, pod); err != nil {
					message := fmt.Sprintf("Readiness check failed because %s, belongs to l7 listener:(http:%d) Host: %s Path: %s. CLB %s. Ingress: %s",
						err.Error(), port, httpRule.Host, httpRule.RealPath, *lb.LoadBalancerId, utils.IngressName(ingress))
					updatePodReadinessGate(pod, message)
					return fmt.Errorf("health check for backend pod %s error: %w. Backend pod belong to listener %s of LoadBalancer %s", utils.PodName(pod), err, listenerKey, *lb.LoadBalancerId)
				}
			}
		}
	}
	for _, httpsRule := range httpsRules {
		if httpsRule.Backend.ServiceName() == service.GetObjectMeta().GetName() { // Service Is Used
			for _, port := range portsByProtocol[net.ProtocolHTTPS] {
				listenerKey := getL7RuleKey(int64(port), "HTTPS", httpsRule.Host, httpsRule.RealPath)
				if err := checkIngressTarget(loadBalancerHealthMap, listenerKey, pod); err != nil {
					message := fmt.Sprintf("Readiness check failed because %s, belongs to l7 listener:(https:%d) Host: %s Path: %s of CLB %s. Ingress: %s",
						err.Error(), port, httpsRule.Host, httpsRule.RealPath, *lb.LoadBalancerId, utils.IngressName(ingress))
					updatePodReadinessGate(pod, message)
					return fmt.Errorf("health check for backend pod %s error: %w. Backend pod belong to listener %s of LoadBalancer %s", utils.PodName(pod), err, listenerKey, *lb.LoadBalancerId)
				}
			}
		}
	}

	return nil
}

// TODO misaka check open?
// func IsHealthCheckOpen(service service_wrapper.ServiceWrapper, lbID string, host string, protocol string, port int, path string) (bool, error) {
//	listeners, err := GetLoadBalancerListener(service, lbID)
//	if err != nil {
//		return false, err
//	}
//	for _, listener := range listeners {
//		if int(*listener.Port) == port && strings.ToUpper(*listener.Protocol) == protocol {
//			for _, rule := range listener.Rules {
//				if *rule.Domain == host && *rule.Url == path {
//					if *rule.HealthCheck.HealthSwitch == 1 {
//						return true, nil
//					} else {
//						return false, nil
//					}
//				}
//			}
//		}
//	}
//	return false, fmt.Errorf("can't find the l7 rule that has host: %s, path: %s in lb %s", host, path, lbID)
// }

func checkIngressTarget(loadBalancerHealthMap map[string][]*clb.TargetHealth, listenerKey string, pod *v1.Pod) error {
	targetHealthList, exist := loadBalancerHealthMap[listenerKey]
	if !exist { // TODO misakazhou 可能性分析
		return fmt.Errorf("LoadBalancerHealth not exist ??? %s", listenerKey)
	}
	isHealth := false
	rsFound := false
outer:
	for _, targetHealth := range targetHealthList {
		for _, podIP := range utils.GetPodIPs(pod) {
			if *targetHealth.IP == podIP {
				rsFound = true
				if *targetHealth.HealthStatusDetail == "Alive" || *targetHealth.HealthStatusDetail == "Close" {
					isHealth = true
					break outer
				}
			}
		}
	}
	if isHealth == false {
		if rsFound {
			return errcode.HealthCheckNotReadyError
		} else {
			return errcode.HealthCheckIngressRSNotFoundError
		}
	}
	return nil
}

func GetAutoCreatedTagKey(service service_wrapper.ServiceWrapper) string {
	if service.ServiceType() == service_wrapper.CoreService && utils.IsInEKSCluster() {
		return types.TagKeyAutoCreatedInEKS.String()
	} else {
		return types.TagKeyAutoCreated.String()
	}
}

func GetAutoCreatedTagValue() string {
	return types.TagValueAutoCreated
}
