package tencent

// ==== 原代码 ====
// ...省略其他代码...

import (
	"testing"

	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

func TestFetchEndpoints(t *testing.T) {
	tests := []struct {
		name        string
		annotations map[string]string
		namespace   string
		svcName     string
		expected    string
		expectErr   bool
		errCode     string
	}{
		{
			name:      "无注解返回服务同名endpoints",
			namespace: "default",
			svcName:   "test-svc",
			expected:  "test-svc",
		},
		{
			name: "无效注解格式",
			annotations: map[string]string{
				"service.cloud.tencent.com/loadbalancer-source-endpoints": `invalid_json`,
			},
			namespace: "default",
			svcName:   "kubernetes-apiserver",
			expectErr: true,
			errCode:   "EndpointsAnnotationError",
		},
		{
			name: "非默认命名空间服务",
			annotations: map[string]string{
				"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"test-endpoints"}`,
			},
			namespace: "kube-system",
			svcName:   "kubernetes-apiserver",
			expectErr: true,
			errCode:   "EndpointsAnnotationError",
		},
		{
			name: "非kubernetes前缀服务名",
			annotations: map[string]string{
				"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"test-endpoints"}`,
			},
			namespace: "default",
			svcName:   "my-service",
			expectErr: true,
			errCode:   "EndpointsAnnotationError",
		},
		{
			name: "合法注解配置",
			annotations: map[string]string{
				"service.cloud.tencent.com/loadbalancer-source-endpoints": `{"name":"kubernetes-endpoints"}`,
			},
			namespace: "default",
			svcName:   "kubernetes-apiserver",
			expected:  "kubernetes-endpoints",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc := &v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name:        tt.svcName,
					Namespace:   tt.namespace,
					Annotations: tt.annotations,
				},
			}
			sw := service_wrapper.NewService(svc)

			result, err := fetchEndpoints(sw)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertEndpointsToTargets(t *testing.T) {
	// 准备测试数据
	readyIP := "********"
	notReadyIP := "********"

	// 测试用例
	tests := []struct {
		name        string
		listeners   map[string]*ServiceListener
		endpoints   *v1.Endpoints
		expectError bool
		expectKeys  map[string]int
		err         error
	}{
		{
			name: "正常情况-包含就绪和未就绪地址",
			listeners: map[string]*ServiceListener{
				"80_HTTP": {
					Name: "http",
					Port: 80,
					ListenerConfig: ListenerConfig{
						Protocol: string(v1.ProtocolTCP),
					},
					TargetPort: intstr.FromInt(80),
				},
			},
			endpoints: &v1.Endpoints{
				Subsets: []v1.EndpointSubset{
					{
						Ports: []v1.EndpointPort{
							{Name: "http", Port: 80, Protocol: v1.ProtocolTCP},
						},
						Addresses:         []v1.EndpointAddress{{IP: readyIP}},
						NotReadyAddresses: []v1.EndpointAddress{{IP: notReadyIP}},
					},
				},
			},
			expectKeys: map[string]int{
				"80_HTTP": 2,
			},
		},
		{
			name: "多端口监听器场景",
			listeners: map[string]*ServiceListener{
				"80_TCP": {
					Name: "http",
					Port: 80,
					ListenerConfig: ListenerConfig{
						Protocol: string(v1.ProtocolTCP),
					},
					TargetPort: intstr.FromInt(8080),
				},
				"443_TCP": {
					Name: "https",
					Port: 443,
					ListenerConfig: ListenerConfig{
						Protocol: string(v1.ProtocolTCP),
					},
					TargetPort: intstr.FromInt(8443),
				},
			},
			endpoints: &v1.Endpoints{
				Subsets: []v1.EndpointSubset{
					{
						Ports: []v1.EndpointPort{
							{Name: "http", Port: 8080, Protocol: v1.ProtocolTCP},
							{Name: "https", Port: 8443, Protocol: v1.ProtocolTCP},
						},
						Addresses: []v1.EndpointAddress{{IP: readyIP}},
					},
				},
			},
			expectKeys: map[string]int{
				"80_TCP":  1,
				"443_TCP": 1,
			},
		},
		{
			name: "混合协议场景",
			listeners: map[string]*ServiceListener{
				"80_TCP": {
					Name: "tcp",
					Port: 80,
					ListenerConfig: ListenerConfig{
						Protocol: string(v1.ProtocolTCP),
					},
					TargetPort: intstr.FromInt(80),
				},
				"80_UDP": {
					Name: "udp",
					Port: 80,
					ListenerConfig: ListenerConfig{
						Protocol: string(v1.ProtocolUDP),
					},
					TargetPort: intstr.FromInt(80),
				},
			},
			endpoints: &v1.Endpoints{
				Subsets: []v1.EndpointSubset{
					{
						Ports: []v1.EndpointPort{
							{Name: "tcp", Port: 80, Protocol: v1.ProtocolTCP},
							{Name: "udp", Port: 80, Protocol: v1.ProtocolUDP},
						},
						Addresses: []v1.EndpointAddress{{IP: readyIP}},
					},
				},
			},
			expectKeys: map[string]int{
				"80_TCP": 1,
				"80_UDP": 1,
			},
		},
		{
			name: "空Endpoints场景",
			listeners: map[string]*ServiceListener{
				"80_TCP": {
					Name: "tcp",
					Port: 80,
					ListenerConfig: ListenerConfig{
						Protocol: string(v1.ProtocolTCP),
					},
					TargetPort: intstr.FromInt(80),
				},
			},
			endpoints: &v1.Endpoints{},
			expectKeys: map[string]int{
				"80_TCP": 0,
			},
		},
		{
			name: "端口映射场景",
			listeners: map[string]*ServiceListener{
				"80_TCP": {
					Port: 80,
					ListenerConfig: ListenerConfig{
						Protocol: string(v1.ProtocolTCP),
					},
					TargetPort: intstr.FromString("http"), // 字符串类型端口映射
				},
			},
			endpoints: &v1.Endpoints{
				Subsets: []v1.EndpointSubset{
					{
						Ports: []v1.EndpointPort{
							{Name: "http", Port: 8080, Protocol: v1.ProtocolTCP},
						},
						Addresses: []v1.EndpointAddress{{IP: readyIP}},
					},
				},
			},
			expectError: true, // 明确标记预期错误
			expectKeys:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Mock service wrapper
			sw := service_wrapper.NewService(&v1.Service{
				ObjectMeta: metav1.ObjectMeta{
					Name: "test-svc",
				},
			})

			// 执行转换
			targets, err := convertEndpointsToTargets(sw, tt.endpoints, tt.listeners)

			// 错误断言处理
			if tt.expectError {
				assert.Error(t, err) // 仅验证有错误返回，不校验具体错误内容
				return
			}
			assert.NoError(t, err) // 正常情况需要验证无错误

			// 验证各监听器对应的目标数量
			assert.Len(t, targets, len(tt.expectKeys), "监听器数量不匹配")
			for key, expectedCount := range tt.expectKeys {
				if target, ok := targets[key]; ok {
					assert.Len(t, target.Targets, expectedCount, "监听器 %s 的目标数量不匹配", key)
				} else {
					t.Errorf("预期监听器 %s 不存在", key)
				}
			}
		})
	}
}
