package tencent

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.woa.com/kateway/loadbalancer-resource-api/pkg/apis/loadbalancerresource/v1alpha1"
	v1alpha12 "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"
	go_version "github.com/hashicorp/go-version"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	v12 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	k8stypes "k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/service/errcode"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	cluster2 "git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/tencent/cloudctx"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/service/cluster"
	"cloud.tencent.com/lb-controller/pkg/utils"
)

const (
	LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE = "service"
	LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE = "ingress"
)

var WITHOUT_SUBRESOURCE *go_version.Version

func init() {
	semver, _ := go_version.NewSemver("v1.12")
	WITHOUT_SUBRESOURCE = semver
}

/*
GetLoadBalancerByLoadbalancerResource
kateway: GetLoadBalancerByLoadBalancerResource 主要逻辑是尝试通过集群中已有的LBR资源来构建LoadBalancerContext：
kateway: 基于本地LBR 构建context， 如果CLB存在但LBR不存在则创建LBR， 返回的context 缺乏 CLB 主体, todo 重命名??? GetLbr_CreateLbrIfNotReuse ?

	1、根据LBR中的 svc-uuid：Service的标签对LBR进行获取，如果存在则直接使用LBR中的信息
	2、查看svc上是否有service.kubernetes.io/tke-existed-lbid的annotation，如果有则说明LBR被删除，需要重建
	3、利用资源标签（如tke-lb-serviceuuid:f4771c87-0fff-4ead-9243-fe23df044a42）直接到标签系统中list对应的资源id，资源类型是clb
	4、使用第3步拿到的lbid信息，调用clb接口获取lb实例
	5、利用lb实例创建LBR，并构建LoadBalancerContext
*/
func GetLoadBalancerByLoadbalancerResource(service service_wrapper.ServiceWrapper) (*LoadBalancerContext, error) {
	// kateway: 1、根据LBR中的 svc-uuid：Service的标签对LBR进行获取，如果存在则直接使用LBR中的信息
	selector := labels.SelectorFromSet(map[string]string{string(service.GetObjectMeta().GetUID()): strings.ToLower(string(service.ServiceType()))})
	loadBalancerResources, err := cluster2.Instance.LoadBalancerResourceLister().List(selector)
	if err != nil {
		return nil, err
	}

	if len(loadBalancerResources) >= 1 {
		if len(loadBalancerResources) > 1 { // kateway todo 什么情况下 一个 service 会有多个 LBR
			lbIdList := make([]string, len(loadBalancerResources))
			for index, loadBalancerResource := range loadBalancerResources {
				lbIdList[index] = loadBalancerResource.Name
			}
			// TODO misakazhou 建立清理机制？
			services.UploadMetricsAndEvent(service, types.NewError(errcode.ServiceWithMultiLoadbalancer, "", utils.ServiceName(service), strings.Join(lbIdList, ",")))
		}
		return &LoadBalancerContext{ // 有多个则取第一个
			Service:              service,
			LoadBalancerResource: loadBalancerResources[0],
			Region:               loadBalancerResources[0].Spec.Region,
			LoadBalancerId:       loadBalancerResources[0].Name,
		}, nil
	}
	// kateway 下面是 LBR 不存在的情况

	// 仅适用于TKE创建资源的灾难恢复
	// kateway: 2、查看svc上是否有service.kubernetes.io/tke-existed-lbid的annotation，如果有则说明LBR被删除，需要重建，为什么不在这里通过exist-lb的annotation构建LBCtx，而是在后边的ValidateLBConfig函数中对LBCtx进行构建
	if _, exist := utils.GetExistLB(service); exist {
		return nil, nil // reuse 情况下，这里 LBR 还不存在
	}

	// kateway: 3、利用资源标签（如tke-lb-serviceuuid:f4771c87-0fff-4ead-9243-fe23df044a42）直接到标签系统中list对应的资源id，资源类型是clb
	balancerContext, region, err := GetLoadBalancerByTags(service) // kateway: 本地没有 LBR 但远程 CLB 创建好了, 这里 context 里缺乏 CLB 主体信息
	if err != nil {
		return nil, err
	}
	if balancerContext == nil { // kateway: 本地 LBS 和远程 CLB 都不存在, 直接返回
		return nil, nil
	}

	// kateway: 4、使用第3步拿到的lbid信息，调用clb接口获取lb实例，校验lb实例上的标签（是否是TKE创建、集群ID是否符合）主要目的是在非resue情况下，如果CLB存在，就要创建LBR
	// kateway todo GetLoadBalancerByTags 不是已经返回了 tags 吗
	tags, err := GetLoadBalancerTags(service, balancerContext.Region, balancerContext.LoadBalancerId)
	if err != nil {
		return nil, err
	}
	createdByTKE := false
	clusterIdCheck := false
	for _, tag := range tags {
		if tag.TagKey == nil || *tag.TagKey == "" || tag.TagValue == nil || *tag.TagValue == "" { // 存在空值不是TKE Service标签，略过
			continue
		}
		if *tag.TagKey == types.TagKeyClusterID.String() && *tag.TagValue == config.Global.ClusterName { // kateway todo types.TagKeyClusterID 的设置逻辑
			clusterIdCheck = true
		}
		if *tag.TagKey == GetAutoCreatedTagKey(service) && *tag.TagValue == GetAutoCreatedTagValue() {
			createdByTKE = true
		}
	}
	// kateway: tag 中 clusterID 必须存在，且必须是 tke 创建的 clb，才会去创建LBR
	if !clusterIdCheck || !createdByTKE {
		return nil, nil // 首次对账，LB 不存在，返回nil，退出
	}
	// kateway: 5、利用lb实例创建LBR，并构建LoadBalancerContext
	loadBalancerResource, err := CreateLoadBalancerResource(balancerContext.LoadBalancerId, region, createdByTKE, service) // kateway: LBR创建之一：非reuse情况下，本地缺LBS但远程CLB存在
	if err != nil {
		return nil, err
	}
	return &LoadBalancerContext{
		Service:              service,
		LoadBalancerResource: loadBalancerResource,
		Region:               loadBalancerResource.Spec.Region,
		LoadBalancerId:       loadBalancerResource.Name,
	}, nil
}

func GetLoadBalancerByLoadbalancerResourceIngress(service service_wrapper.ServiceWrapper,
	ingress types.Ingress) (*clb.LoadBalancer, bool, error) {
	selector := labels.SelectorFromSet(map[string]string{ingress.UID(): LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE})
	loadBalancerResources, err := cluster2.Instance.LoadBalancerResourceLister().List(selector)
	if err != nil {
		return nil, false, err
	}

	if len(loadBalancerResources) >= 1 {
		if len(loadBalancerResources) > 1 {
			lbIdList := make([]string, len(loadBalancerResources))
			for index, loadBalancerResource := range loadBalancerResources {
				lbIdList[index] = loadBalancerResource.Name
			}
		}

		loadBalancerResource := loadBalancerResources[0]
		request := clb.NewDescribeLoadBalancersRequest()
		request.LoadBalancerIds = []*string{&loadBalancerResource.Name}
		response, err := tencentapi.Instance.DescribeLoadBalancers(cloudctx.New(service, loadBalancerResource.Spec.Region), request)
		if err != nil {
			return nil, false, err
		}
		if response.Response.TotalCount != nil && *response.Response.TotalCount != 0 && len(response.Response.LoadBalancerSet) != 0 {
			loadBalancer := response.Response.LoadBalancerSet[0]
			// 负载均衡的标签不保证实时性、准确性，必须以标签服务的数据为准
			tags, err := GetLoadBalancerTags(service, loadBalancerResource.Spec.Region, *loadBalancer.LoadBalancerId)
			if err != nil {
				return nil, false, err
			}
			loadBalancer.Tags = utils.ConvertResourceTag(tags)
			return loadBalancer, true, nil
		}
		klog.Errorf("Loadbalancer Not Found.")
		return nil, false, err
	}
	return nil, false, nil
}

// kateway 用 ctx 信息更新 LBS 的监听器信息
func EnsureLoadBalancerResource(syncContext *SyncContext) (err error) {
	service := syncContext.Service
	loadBalancerResource := syncContext.LoadBalancerContext.LoadBalancerResource
	labelValue, exist := loadBalancerResource.Labels[string(service.GetObjectMeta().GetUID())]

	desiredListenerSets := make(sets.String)
	listeners := syncContext.ServiceContext.ServiceListeners
	for _, listener := range listeners {
		desiredListenerSets.Insert(getListenerKey(listener.Port, listener.Protocol))
	}

	currentListenerSets := make(sets.String)
	listenerByService := GetLoadBalancerResourceListenerByService(service, loadBalancerResource)
	for _, listener := range listenerByService {
		currentListenerSets.Insert(getListenerKey(int64(listener.Port), listener.Protocol))
	}

	toAddListenerKeys := desiredListenerSets.Difference(currentListenerSets)
	toDelListenerKeys := currentListenerSets.Difference(desiredListenerSets)

	newLoadBalancerResource := loadBalancerResource
	if toAddListenerKeys.Len() != 0 || toDelListenerKeys.Len() != 0 || (!exist || labelValue != strings.ToLower(string(service.ServiceType()))) {
		newLoadBalancerResource, err = UpdateLoadBalancerResource(loadBalancerResource.Name, toAddListenerKeys, toDelListenerKeys, service) // kateway: 更新lbr里的监听器
		if err != nil {
			return err
		}
	}
	newLoadBalancerResource, err = UpdateLoadBalancerResourceStatus(loadBalancerResource.Name, service)
	if err != nil {
		return err
	}

	syncContext.LoadBalancerContext.LoadBalancerResource = newLoadBalancerResource
	return nil
}

func DeleteLoadBalancerResourceByService(loadBalancerId string, service service_wrapper.ServiceWrapper) error {
	loadBalancerResource, err := deleteLoadBalancerResourceByService(loadBalancerId, service)
	if err != nil {
		return err
	}
	if loadBalancerResource == nil {
		return nil
	}
	if len(loadBalancerResource.Labels) == 0 { // CLB资源已经不再被使用
		return DeleteLoadBalancerResource(loadBalancerId)
	}

	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		if _, err = cluster2.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Update(context.Background(), loadBalancerResource, v12.UpdateOptions{}); err != nil {
			if !errors.IsConflict(err) {
				return err
			}
			loadBalancerResource, err = deleteLoadBalancerResourceByService(loadBalancerId, service)
			if err != nil {
				return err
			}
			if loadBalancerResource == nil {
				return nil
			}
			if len(loadBalancerResource.Labels) == 0 { // CLB资源已经不再被使用
				return DeleteLoadBalancerResource(loadBalancerId)
			}
		} else {
			break
		}
	}

	if _, err = UpdateLoadBalancerResourceStatus(loadBalancerId, service); err != nil {
		return err
	}
	return nil
}

func deleteLoadBalancerResourceByService(loadBalancerId string, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerResource, err := GetLoadBalancerResource(loadBalancerId)
	if err != nil || loadBalancerResource == nil {
		return loadBalancerResource, err
	}

	// Update LoadBalancerResource Spec
	deepCopy := loadBalancerResource.DeepCopy()
	if value, exist := deepCopy.Labels[string(service.GetObjectMeta().GetUID())]; exist && value == strings.ToLower(string(service.ServiceType())) {
		delete(deepCopy.Labels, string(service.GetObjectMeta().GetUID()))
	}
	deepCopy.Spec.Listeners = GetLoadBalancerResourceListenerWithoutService(service, deepCopy)

	return deepCopy, nil
}

func LoadBalancerResourceAddService(loadBalancerId string, region string, created bool, service service_wrapper.ServiceWrapper) *v1alpha1.LoadBalancerResource {
	loadBalancerResource := &v1alpha1.LoadBalancerResource{
		ObjectMeta: v12.ObjectMeta{
			Name:   loadBalancerId,
			Labels: map[string]string{},
		},
		Spec: v1alpha1.LoadBalancerResourceSpec{
			Region:    region,
			Created:   created,
			Listeners: make([]*v1alpha1.LoadBalancerResourceListener, 0),
		},
		Status: v1alpha1.LoadBalancerResourceStatus{},
	}
	if service != nil {
		loadBalancerResource.Labels = map[string]string{
			string(service.GetObjectMeta().GetUID()): strings.ToLower(string(service.ServiceType())),
		}
	}
	return loadBalancerResource
}

func LoadBalancerResourceRebuild(loadBalancerId string, region string, created bool, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerResource := &v1alpha1.LoadBalancerResource{
		ObjectMeta: v12.ObjectMeta{
			Name:   loadBalancerId,
			Labels: map[string]string{},
		},
		Spec: v1alpha1.LoadBalancerResourceSpec{
			Region:    region,
			Created:   created,
			Listeners: make([]*v1alpha1.LoadBalancerResourceListener, 0),
		},
		Status: v1alpha1.LoadBalancerResourceStatus{},
	}

	loadBalancerContext := &LoadBalancerContext{
		Service:        service,
		Region:         region,
		LoadBalancerId: loadBalancerId,
	}
	// 标签被破坏的场景，对账用户声明和负载均衡监听器
	listeners, err := loadBalancerContext.GetMixedListeners()
	if err != nil {
		return nil, err
	}

	// LoadBalancerResource 资源初始化
	services, err := cluster2.Instance.KubeClient().CoreV1().Services(v1.NamespaceAll).List(context.Background(), v12.ListOptions{})
	if err != nil {
		return nil, err
	}
	// LoadBalancerResource 资源初始化
	var multiClusterServiceList *v1alpha12.MultiClusterServiceList
	if config.Global.EnableMultiClusterMaster {
		multiClusterServiceList, err = cluster2.Instance.MultiClusterServiceClient().CloudV1alpha1().MultiClusterServices(v1.NamespaceAll).List(context.Background(), v12.ListOptions{})
		if err != nil {
			return nil, err
		}
	}
	ingresses, err := cluster.ListQCloudIngress(v1.NamespaceAll, labels.Everything())
	if err != nil {
		return nil, err
	}

	alreadyRecordListener := make(map[string]bool)

	// 当前同步资源优先处理
	listenerKeyByService := GetListenerKeyByService(service)
	for listenerKey, _ := range listenerKeyByService {
		_, alreadyRecord := alreadyRecordListener[listenerKey]
		listener, listenerExist := listeners[listenerKey]
		if listenerExist && !alreadyRecord && listener.GetListenerName() == TKEDedicatedListenerName {
			loadBalancerResource.Labels[string(service.GetObjectMeta().GetUID())] = strings.ToLower(string(service.ServiceType()))
			alreadyRecordListener[getListenerKey(listener.GetListenerPort(), listener.GetProtocol())] = true
			loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(listener.GetListenerPort(), listener.GetProtocol(), service))
		}
	}

	for _, service := range services.Items {
		serviceWrapper := service_wrapper.NewService(&service)
		if existLB, exist := utils.GetExistLB(serviceWrapper); exist && existLB == loadBalancerId {
			listenerKeyByService := GetListenerKeyByService(serviceWrapper)
			for listenerKey, _ := range listenerKeyByService {
				_, alreadyRecord := alreadyRecordListener[listenerKey]
				listener, listenerExist := listeners[listenerKey]
				if listenerExist && !alreadyRecord && listener.GetListenerName() == TKEDedicatedListenerName {
					loadBalancerResource.Labels[string(serviceWrapper.GetObjectMeta().GetUID())] = strings.ToLower(string(serviceWrapper.ServiceType()))
					alreadyRecordListener[getListenerKey(listener.GetListenerPort(), listener.GetProtocol())] = true
					loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(listener.GetListenerPort(), listener.GetProtocol(), serviceWrapper))
				}
			}
		}
	}

	if multiClusterServiceList != nil && multiClusterServiceList.Items != nil && len(multiClusterServiceList.Items) != 0 {
		for _, service := range multiClusterServiceList.Items {
			serviceWrapper := service_wrapper.NewMultiClusterService(&service)
			if existLB, exist := utils.GetExistLB(serviceWrapper); exist && existLB == loadBalancerId { // kateway: mcs 通过 exist lb 做迁移
				listenerKeyByService := GetListenerKeyByService(serviceWrapper)
				for listenerKey, _ := range listenerKeyByService {
					_, alreadyRecord := alreadyRecordListener[listenerKey]
					listener, listenerExist := listeners[listenerKey]
					if listenerExist && !alreadyRecord && listener.GetListenerName() == TKEDedicatedListenerName {
						loadBalancerResource.Labels[string(serviceWrapper.GetObjectMeta().GetUID())] = strings.ToLower(string(serviceWrapper.ServiceType()))
						alreadyRecordListener[getListenerKey(listener.GetListenerPort(), listener.GetProtocol())] = true
						loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(listener.GetListenerPort(), listener.GetProtocol(), serviceWrapper))
					}
				}
			}
		}
	}

	for _, ingress := range ingresses {
		if existLB, exist := utils.GetIngressExistLbID(ingress); exist && existLB == loadBalancerId {
			lisKeys, err := GetListenerKeysByIngress(ingress)
			if err != nil {
				return nil, err
			}
			for _, listenerKey := range lisKeys {
				_, alreadyRecord := alreadyRecordListener[listenerKey]
				listener, listenerExist := listeners[listenerKey]
				if listenerExist && !alreadyRecord && listener.GetListenerName() == TKEDedicatedListenerName {
					loadBalancerResource.Labels[ingress.UID()] = LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE
					alreadyRecordListener[getListenerKey(listener.GetListenerPort(), listener.GetProtocol())] = true
					loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListenerIngress(listener.GetListenerPort(), listener.GetProtocol(), ingress))
				}
			}
		}
	}
	return loadBalancerResource, nil
}

func GetLoadBalancerResourceListener(loadBalancerResource *v1alpha1.LoadBalancerResource) []*v1alpha1.LoadBalancerResourceListener {
	listeners := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, _ := range loadBalancerResource.Spec.Listeners {
		listeners = append(listeners, loadBalancerResource.Spec.Listeners[index])
	}
	return listeners
}

// kateway: 根据LBR中保存的监听器信息过滤出不属于提供的service的监听器配置
func GetLoadBalancerResourceListenerWithoutService(service service_wrapper.ServiceWrapper, loadBalancerResource *v1alpha1.LoadBalancerResource) []*v1alpha1.LoadBalancerResourceListener {
	listeners := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, listener := range loadBalancerResource.Spec.Listeners {
		for _, reference := range listener.References {
			if reference.Kind == string(service.ServiceType()) && reference.Namespace == service.GetObjectMeta().GetNamespace() && reference.Name == service.GetObjectMeta().GetName() {
				continue
			}
			listeners = append(listeners, loadBalancerResource.Spec.Listeners[index])
		}
	}
	return listeners
}

func GetLoadBalancerResourceListenerByService(service service_wrapper.ServiceWrapper, loadBalancerResource *v1alpha1.LoadBalancerResource) []*v1alpha1.LoadBalancerResourceListener {
	listeners := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, listener := range loadBalancerResource.Spec.Listeners {
		for _, reference := range listener.References {
			if reference.Kind == string(service.ServiceType()) && reference.Namespace == service.GetObjectMeta().GetNamespace() && reference.Name == service.GetObjectMeta().GetName() {
				listeners = append(listeners, loadBalancerResource.Spec.Listeners[index])
			}
		}
	}
	return listeners
}

func GetLoadBalancerResourceListenerWithoutServiceUID(service service_wrapper.ServiceWrapper, loadBalancerResource *v1alpha1.LoadBalancerResource) []*v1alpha1.LoadBalancerResourceListener {
	listeners := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, listener := range loadBalancerResource.Spec.Listeners {
		for _, reference := range listener.References {
			if reference.Kind == string(service.ServiceType()) && reference.UID == service.GetObjectMeta().GetUID() {
				continue
			}
			listeners = append(listeners, loadBalancerResource.Spec.Listeners[index])
		}
	}
	return listeners
}

func UpdateLoadBalancerResource(loadBalancerId string, toAddListener sets.String, toDelListener sets.String, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerResource, err := updateLoadBalancerResource(loadBalancerId, toAddListener, toDelListener, service)
	if err != nil {
		return nil, err
	}

	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		if loadBalancerResource, err = cluster2.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Update(context.Background(), loadBalancerResource, v12.UpdateOptions{}); err != nil {
			if !errors.IsConflict(err) {
				return nil, err
			}
			if loadBalancerResource, err = updateLoadBalancerResource(loadBalancerId, toAddListener, toDelListener, service); err != nil {
				return nil, err
			}
		} else {
			break
		}
	}
	return loadBalancerResource, nil
}

func updateLoadBalancerResource(loadBalancerId string, toAddListener sets.String, toDelListener sets.String, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerResource, err := GetLoadBalancerResource(loadBalancerId)
	if err != nil || loadBalancerResource == nil { // 资源被删除，重新开始同步逻辑
		return loadBalancerResource, err
	}

	deepCopy := loadBalancerResource.DeepCopy()
	// Update LoadBalancerResource Spec
	if value, exist := deepCopy.Labels[string(service.GetObjectMeta().GetUID())]; !exist || value != strings.ToLower(string(service.ServiceType())) {
		if deepCopy.Labels == nil {
			deepCopy.Labels = make(map[string]string)
		}
		deepCopy.Labels[string(service.GetObjectMeta().GetUID())] = strings.ToLower(string(service.ServiceType()))
	}

	result := make([]*v1alpha1.LoadBalancerResourceListener, 0)
	for index, listener := range deepCopy.Spec.Listeners {
		if _, exist := toDelListener[getListenerKey(int64(listener.Port), listener.Protocol)]; !exist { // 非删除列表内的监听器声明
			result = append(result, deepCopy.Spec.Listeners[index])
		} else {
			references := removeByService(listener.References, service)
			// Service有对应监听器声明，但是还有其他资源正在使用该监听器。
			// 考虑未来的共用监听器Feature设置该逻辑。
			if len(references) != 0 {
				deepCopy.Spec.Listeners[index].References = references
				result = append(result, deepCopy.Spec.Listeners[index])
			}
		}
	}

	resourceListenerMap := make(map[string]*v1alpha1.LoadBalancerResourceListener)
	for index, listener := range result { // 注意: 这里要先处理删除，然后使用处理删除之后的监听器使用情况，去判断新增是否冲突。
		resourceListenerMap[getListenerKey(int64(listener.Port), listener.Protocol)] = result[index]
	}
	for listener, _ := range toAddListener {
		if resourceListener, exist := resourceListenerMap[listener]; exist {
			if !onlyUsedByService(resourceListener.References, service) { // 在修改中，发现监听器被其他资源占用。
				return nil, types.NewError(errcode.ReuseConflictListenerError, "", utils.ServiceName(service), listener)
			}
		}
		port, protocol := parseListenerKey(listener)
		result = append(result, BuildLoadBalancerResourceListener(port, protocol, service))
	}
	deepCopy.Spec.Listeners = result
	return deepCopy, nil
}

func BuildLoadBalancerResourceListener(port int64, protocol string, service service_wrapper.ServiceWrapper) *v1alpha1.LoadBalancerResourceListener {
	return &v1alpha1.LoadBalancerResourceListener{
		Port:     int32(port),
		Protocol: protocol,
		References: []*v1alpha1.ResourceObjectReference{
			ObjectReferenceFromService(service),
		},
		Domains: nil,
	}
}

func BuildLoadBalancerResourceListenerIngress(port int64, protocol string, ingress types.Ingress) *v1alpha1.LoadBalancerResourceListener {
	return &v1alpha1.LoadBalancerResourceListener{
		Port:     int32(port),
		Protocol: protocol,
		References: []*v1alpha1.ResourceObjectReference{
			ObjectReferenceFromIngress(ingress),
		},
		Domains: nil,
	}
}

// func CheckLoadBalancerResource(loadBalancerId string, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, error) {
//	return UpdateLoadBalancerResourceTemplate(loadBalancerId, service, checkLoadBalancerResource)
// }

func UpdateLoadBalancerResourceStatus(loadBalancerId string, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, error) {
	return UpdateLoadBalancerResourceTemplate(loadBalancerId, service, updateLoadBalancerResourceReference)
}

func LockLoadBalancerResource(loadBalancerId string, service service_wrapper.ServiceWrapper) bool {
	if config.Global.DryRunService {
		return true
	}
	if _, err := UpdateLoadBalancerResourceTemplate(loadBalancerId, service, lockLoadBalancerResource); err != nil {
		return false
	}
	return true
}

func UnlockLoadBalancerResource(loadBalancerId string) error {
	if config.Global.DryRunService {
		return nil
	}
	if _, err := UpdateLoadBalancerResourceTemplate(loadBalancerId, nil, unLockLoadBalancerResource); err != nil {
		return err
	}
	return nil
}

func UpdateLoadBalancerResourceTemplate(loadBalancerId string, service service_wrapper.ServiceWrapper, updateFunc func(string, service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, bool, error)) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerResource, isModify, err := updateFunc(loadBalancerId, service)
	if err != nil {
		return nil, err
	}
	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		if !isModify {
			break
		}
		if lo.Must(cluster2.Instance.CheckVersion("< v1.12")) {
			if loadBalancerResource, err = cluster2.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Update(context.Background(), loadBalancerResource, v12.UpdateOptions{}); err != nil {
				if !errors.IsConflict(err) {
					return nil, err
				}
				if loadBalancerResource, isModify, err = updateFunc(loadBalancerId, service); err != nil {
					return nil, err
				}
			} else {
				break
			}
		} else {
			if loadBalancerResource, err = cluster2.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().UpdateStatus(context.Background(), loadBalancerResource, v12.UpdateOptions{}); err != nil {
				if !errors.IsConflict(err) {
					return nil, err
				}
				if loadBalancerResource, isModify, err = updateFunc(loadBalancerId, service); err != nil {
					return nil, err
				}
			} else {
				break
			}
		}
		// 测试EKS开关内外网场景时发现，API Server会因重启而短暂不可用，可能导致LoadBalancerResource资源锁关闭失败。
		// 重试增加重试间隔后未复现
		time.Sleep(3 * time.Second)
	}
	return loadBalancerResource, nil
}

func updateLoadBalancerResourceReference(loadBalancerId string, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, bool, error) {
	loadBalancerResource, err := GetLoadBalancerResource(loadBalancerId)
	if err != nil || loadBalancerResource == nil {
		return loadBalancerResource, false, err
	}

	deepCopy := loadBalancerResource.DeepCopy()
	// Service Get References from Spec
	referencesMap := make(map[string]*v1alpha1.ResourceObjectReference)
	for _, listener := range deepCopy.Spec.Listeners {
		for index, reference := range listener.References {
			if reference.Kind == string(service.ServiceType()) {
				referencesMap[ObjectReferenceNamespaceName(reference)] = listener.References[index]
			}
		}
	}

	// Compare LoadBalancerResource Status
	isServiceModify := false
	if len(referencesMap) != len(deepCopy.Status.ServiceResource) {
		isServiceModify = true
	} else {
		for _, serviceResource := range deepCopy.Status.ServiceResource {
			if resource, exist := referencesMap[ObjectReferenceNamespaceName(serviceResource)]; !exist || resource.UID != serviceResource.UID {
				isServiceModify = true
				break
			}
		}
	}

	if isServiceModify {
		result := make([]*v1alpha1.ResourceObjectReference, len(referencesMap))
		index := 0
		for _, reference := range referencesMap {
			result[index] = reference
			index = index + 1
		}
		deepCopy.Status.ServiceResource = result
	}

	// Ingress Get References from Spec
	referencesMap = make(map[string]*v1alpha1.ResourceObjectReference)
	for _, listener := range deepCopy.Spec.Listeners {
		for index, reference := range listener.References {
			if reference.Kind == "Ingress" {
				referencesMap[ObjectReferenceNamespaceName(reference)] = listener.References[index]
			}
		}
	}

	// Compare LoadBalancerResource Status
	isIngressModify := false
	if len(referencesMap) != len(deepCopy.Status.IngressResource) {
		isIngressModify = true
	} else {
		for _, ingressResource := range deepCopy.Status.IngressResource {
			if resource, exist := referencesMap[ObjectReferenceNamespaceName(ingressResource)]; !exist || resource.UID != ingressResource.UID {
				isIngressModify = true
				break
			}
		}
	}

	if isIngressModify {
		result := make([]*v1alpha1.ResourceObjectReference, len(referencesMap))
		index := 0
		for _, reference := range referencesMap {
			result[index] = reference
			index = index + 1
		}
		deepCopy.Status.IngressResource = result
	}
	return deepCopy, isIngressModify || isServiceModify, nil
}

func lockLoadBalancerResource(loadBalancerId string, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, bool, error) {
	loadBalancerResource, err := GetLoadBalancerResource(loadBalancerId)
	if err != nil || loadBalancerResource == nil {
		return loadBalancerResource, false, err
	}

	deepCopy := loadBalancerResource.DeepCopy()
	// 如果Status字段不存在则初始化
	if deepCopy.Status.Lock == nil {
		deepCopy.Status.Lock = &v1alpha1.LoadBalancerResourceLock{
			Status:          "Unlock",
			UpdateTimestamp: v12.Now(),
		}
	}

	// 已经上锁的场景下。如果上锁超过15分钟，认为是锁超时的线程丢失，强制释放锁
	lock := deepCopy.Status.Lock
	if lock.Status == "Lock" {
		if v12.Now().Sub(lock.UpdateTimestamp.Time) >= 15*time.Minute {
			deepCopy.Status.Lock.Status = "Unlock"
		}
		if lock.Resource != nil && lock.Resource.Kind == string(service.ServiceType()) && lock.Resource.Namespace == service.GetObjectMeta().GetNamespace() && lock.Resource.Name == service.GetObjectMeta().GetName() { // fox todo 这是啥意思？ 自己解锁自己？
			deepCopy.Status.Lock.Status = "Unlock"
		}
	}

	// 如果当前锁是被抢占的，不用尝试上锁
	if deepCopy.Status.Lock.Status == "Lock" { // kateway 别的svc 正在锁住
		return deepCopy, false, types.NewError(errcode.ReuseConcurrentOperationError, "", utils.ServiceName(service))
	}

	deepCopy.Status.Lock = &v1alpha1.LoadBalancerResourceLock{
		Status:          "Lock",
		UpdateTimestamp: v12.Now(),
		Resource:        ObjectReferenceFromService(service),
	}
	return deepCopy, true, nil
}

func unLockLoadBalancerResource(loadBalancerId string, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, bool, error) {
	loadBalancerResource, err := GetLoadBalancerResource(loadBalancerId)
	if err != nil {
		return loadBalancerResource, false, err
	}
	if loadBalancerResource == nil { // 解锁时，资源不存在了，应该保持幂等。
		return loadBalancerResource, false, nil
	}

	deepCopy := loadBalancerResource.DeepCopy()
	deepCopy.Status.Lock = &v1alpha1.LoadBalancerResourceLock{
		Status:          "Unlock",
		UpdateTimestamp: v12.Now(),
	}
	return deepCopy, true, nil
}

// func checkLoadBalancerResource(loadBalancerId string, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, bool, error) {
//	loadBalancerResource, err := GetLoadBalancerResource(loadBalancerId)
//	if err != nil {
//		return loadBalancerResource, false, err
//	}
//
//	isModify := false
//	serviceUIDNotExist := make([]string, 0)
//	newLabel := make(map[string]string)
//	for key, value := range loadBalancerResource.Labels {
//		if value != LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE && value != LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE { // 莫名其妙的Label，删除
//			isModify = true
//			continue
//		}
//		if value == LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE { // Service Label 检查一下对应资源是否存在
//			if _, err := cluster.ClusterServiceInstance.ServiceIndexer().ByIndex("UID", key); err != nil {
//				klog.Errorf("Unexpected service uid: %s", key)
//				serviceUIDNotExist = append(serviceUIDNotExist, key)
//			}
//			isModify = true
//			continue
//		}
//		newLabel[key] = value
//	}
//	if !isModify {
//		return loadBalancerResource, false, nil
//	}
//
//	loadBalancerResource.Labels = newLabel
//	for _, serviceUID := range serviceUIDNotExist {
//		loadBalancerResource.Spec.Listeners = GetLoadBalancerResourceListenerWithoutServiceUID(serviceUID, loadBalancerResource)
//	}
//	return loadBalancerResource, true, nil
// }

func GetLoadBalancerResource(loadBalancerId string) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerResource, err := cluster2.Instance.LoadBalancerResourceLister().Get(loadBalancerId)
	if err != nil {
		if errors.IsNotFound(err) {
			loadBalancerResource, err = cluster2.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Get(context.Background(), loadBalancerId, v12.GetOptions{})
			if err == nil { // BackUpdate, 创建之后立刻查询没有缓存数据，改为Client查询
				return loadBalancerResource, nil
			}
			return nil, nil
		}
		return nil, err
	}
	return loadBalancerResource, nil
}

func DeleteLoadBalancerResource(loadBalancerId string) error {
	var err error
	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		if err = cluster2.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Delete(context.Background(), loadBalancerId, v12.DeleteOptions{}); err != nil {
			if !errors.IsNotFound(err) { // 幂等处理
				return err
			}
		} else {
			return nil
		}
	}
	return err
}

// kateway: 调用的前提是 CLB 一定存在了?
func CreateLoadBalancerResource(loadBalancerId string, region string, created bool, service service_wrapper.ServiceWrapper) (*v1alpha1.LoadBalancerResource, error) {
	if created {
		return createLoadBalancerResource(LoadBalancerResourceAddService(loadBalancerId, region, created, service))
	} else { // 使用已有负载均衡，资源不存在时，灾难恢复
		rebuildLoadBalancerResource, err := LoadBalancerResourceRebuild(loadBalancerId, region, created, service)
		if err != nil {
			return nil, err
		}
		return createLoadBalancerResource(rebuildLoadBalancerResource)
	}
}

func createLoadBalancerResource(loadBalancerResource *v1alpha1.LoadBalancerResource) (*v1alpha1.LoadBalancerResource, error) {
	loadBalancerId := loadBalancerResource.Name
	loadBalancerResource, err := cluster2.Instance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Create(context.Background(), loadBalancerResource, v12.CreateOptions{})
	if err != nil {
		if errors.IsAlreadyExists(err) {
			loadBalancerResource, err = cluster2.Instance.LoadBalancerResourceLister().Get(loadBalancerId)
			if err != nil {
				return nil, err
			}
			return loadBalancerResource, nil
		}
		return nil, err
	}
	return loadBalancerResource, nil
}

func ObjectReferenceFromService(service service_wrapper.ServiceWrapper) *v1alpha1.ResourceObjectReference {
	return &v1alpha1.ResourceObjectReference{
		ObjectReference: v1.ObjectReference{
			APIVersion: "v1",
			Kind:       string(service.ServiceType()),
			Namespace:  service.GetObjectMeta().GetNamespace(),
			Name:       service.GetObjectMeta().GetName(),
			UID:        k8stypes.UID(service.GetObjectMeta().GetUID()),
		},
	}
}

func ObjectReferenceFromIngress(ingress types.Ingress) *v1alpha1.ResourceObjectReference {
	return &v1alpha1.ResourceObjectReference{
		ObjectReference: v1.ObjectReference{
			Kind:      "Ingress",
			Namespace: ingress.Namespace(),
			Name:      ingress.Name(),
			UID:       k8stypes.UID(ingress.UID()),
		},
	}
}

func ObjectReferenceNamespaceName(objectReference *v1alpha1.ResourceObjectReference) string {
	return fmt.Sprintf("%s/%s", objectReference.Namespace, objectReference.Name)
}

// func ConvertTagToLoadbalancerResource(services []v1.Service, ingresses []types.Ingress) {
//	serviceMap := make(map[string]service_wrapper.ServiceWrapper)
//	ingressMap := make(map[string]types.Ingress)
//	normalService := make([]service_wrapper.ServiceWrapper, 0)
//	reuseLoadBalancer := make(map[string]service_wrapper.ServiceWrapper)
//	for index, service := range services {
//		if !utils.IsLoadBalancerType(&service) {
//			continue
//		}
//		serviceMap[service.GetObjectMeta().GetUID()] = &services[index]
//		if lbId, exist := utils.GetExistLB(&service); exist {
//			reuseLoadBalancer[lbId] = &services[index]
//		} else {
//			normalService = append(normalService, &services[index])
//		}
//	}
//	for index, ingress := range ingresses {
//		ingressMap[ingress.UID()] = ingresses[index]
//	}
//
//	convertNormalServiceList(normalService)
//	convertReuseServiceList(reuseLoadBalancer, serviceMap, ingressMap)
// }
//
// func convertNormalServiceList(services []service_wrapper.ServiceWrapper) {
//	for _, service := range services {
//		loadBalancerContext, region, err := GetLoadBalancerByTags(service)
//		if err != nil || loadBalancerContext == nil {
//			continue
//		}
//		_, err = cluster.ClusterServiceInstance.LoadBalancerResourceClient().NetworkingV1alpha1().LoadBalancerResources().Get(context.Background(), loadBalancerContext.LoadBalancerId, v12.GetOptions{})
//		if err == nil || !errors.IsNotFound(err) { // CRD资源已存在
//			continue
//		}
//		loadBalancer, err := loadBalancerContext.GetLoadBalancer()
//		if err != nil {
//			continue
//		}
//		createdByTKE := false
//		clusterIdCheck := false
//		for _, tag := range loadBalancer.Tags {
//			if tag.TagKey == nil || *tag.TagKey == "" || tag.TagValue == nil || *tag.TagValue == "" { // 存在空值不是TKE Service标签，略过
//				continue
//			}
//			if *tag.TagKey == types.TagKeyClusterID && *tag.TagValue == utils.GlobalConfigs.ClusterName {
//				clusterIdCheck = true
//			}
//			if *tag.TagKey == CreateByTKETag() && *tag.TagValue == CreateByTKEValue() {
//				createdByTKE = true
//			}
//		}
//		if !clusterIdCheck || !createdByTKE { // 仅适用于TKE创建资源的灾难恢复
//			continue
//		}
//
//		loadBalancerResource := &v1alpha1.LoadBalancerResource{
//			ObjectMeta: v12.ObjectMeta{
//				Name: *loadBalancer.LoadBalancerId,
//				Labels: map[string]string{
//					service.GetObjectMeta().GetUID(): LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE,
//				},
//			},
//			Spec: v1alpha1.LoadBalancerResourceSpec{
//				Region:    region,
//				Created:   createdByTKE,
//				Listeners: make([]*v1alpha1.LoadBalancerResourceListener, 0),
//			},
//			Status: v1alpha1.LoadBalancerResourceStatus{},
//		}
//
//		// 标签被破坏的场景，对账用户声明和负载均衡监听器
//		listeners, err := loadBalancerContext.GetMixedListeners()
//		if err != nil {
//			klog.Errorf("convert error Listener get error. %v", err.Error())
//			continue
//		}
//
//		// 当前同步资源优先处理
//		listenerKeyByService := GetListenerKeyByService(service)
//		for listenerKey, _ := range listenerKeyByService {
//			listener, listenerExist := listeners[listenerKey]
//			if listenerExist && listener.GetListenerName() == TKEDedicatedListenerName {
//				loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(listener.GetListenerPort(), listener.GetProtocol(), service))
//			}
//		}
//
//		loadBalancerResource, err = createLoadBalancerResource(loadBalancerResource)
//		if err != nil {
//			continue
//		}
//		loadBalancerContext.LoadBalancerResource = loadBalancerResource
//
//		_ = EnsureLoadBalancerTags(loadBalancerContext)
//		_, _ = UpdateLoadBalancerResourceStatus(*loadBalancer.LoadBalancerId, service)
//	}
// }
//
// func convertReuseServiceList(loadbalancers map[string]service_wrapper.ServiceWrapper, services map[string]service_wrapper.ServiceWrapper, ingresses map[string]types.Ingress) {
//	for loadbalancerId, s := range loadbalancers {
//		loadBalancerResource, err := GetLoadBalancerResource(loadbalancerId)
//		if loadBalancerResource != nil { // CRD资源已存在
//			continue
//		}
//		serviceRegion, err := crossregion.CrossRegionServiceInstance.GetServiceRegion(s)
//		if err != nil {
//			continue
//		}
//		loadBalancerContext := &LoadBalancerContext{
//			Service:        s,
//			Region:         serviceRegion,
//			LoadBalancerId: loadbalancerId,
//		}
//		loadBalancer, err := loadBalancerContext.GetLoadBalancer()
//		if err != nil {
//			klog.Errorf("convert error Loadbalancer get error. %v", err.Error())
//			continue
//		}
//		createdByTKE := false
//		clusterIdCheck := false
//		for _, tag := range loadBalancer.Tags {
//			if tag.TagKey == nil || *tag.TagKey == "" || tag.TagValue == nil || *tag.TagValue == "" { // 存在空值不是TKE Service标签，略过
//				continue
//			}
//			if *tag.TagKey == types.TagKeyClusterID && *tag.TagValue == utils.GlobalConfigs.ClusterName {
//				clusterIdCheck = true
//			}
//			if *tag.TagKey == CreateByTKETag() && *tag.TagValue == CreateByTKEValue() {
//				createdByTKE = true
//			}
//		}
//		if !clusterIdCheck || createdByTKE {
//			continue
//		}
//		region := loadBalancerContext.Region
//		alreadyRecordListener := make(map[string]bool)
//		loadBalancerResource = LoadBalancerResourceAddService(loadbalancerId, region, false, nil)
//		listenersMap, err := loadBalancerContext.GetMixedListeners()
//		if err != nil {
//			continue
//		}
//		listenersListenerIdMap := make(map[string]*MixedListener)
//		for _, listener := range listenersMap {
//			listenersListenerIdMap[listener.GetListenerId()] = listener
//		}
//
//		for _, tag := range loadBalancer.Tags {
//			ingressUID := *tag.TagKey
//			if *tag.TagKey == types.TagKeyIngressUUID {
//				ingressUID = *tag.TagValue
//			}
//			if ingress, exist := ingresses[ingressUID]; exist {
//				loadBalancerResource.Labels[ingressUID] = LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE
//				listenerKeySet := GetListenerKeyByIngress(ingress)
//				for listenerKey, _ := range listenerKeySet {
//					port, protocol := parseListenerKey(listenerKey)
//					alreadyRecordListener[getListenerKey(port, protocol)] = true
//					loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListenerIngress(port, protocol, ingress))
//				}
//			}
//		}
//
//		resuseUidMap := make(map[string]string)
//		uidMap := make(map[string]string)
//		for _, tag := range loadBalancer.Tags {
//			if tag.TagKey == nil || *tag.TagKey == "" || tag.TagValue == nil || *tag.TagValue == "" { // 存在空值不是TKE Service标签，略过
//				continue
//			}
//			if strings.HasPrefix(*tag.TagValue, types.TagKeyClusterIDPrefix) || *tag.TagValue == types.TagKeyServiceUUID {
//				resuseUidMap[*tag.TagKey] = *tag.TagValue
//			}
//			if *tag.TagKey == types.TagKeyServiceUUIDOld {
//				uidMap[*tag.TagKey] = *tag.TagValue
//			}
//		}
//		removeTags := make([]*v20180813.TagResource, 0)
//
//		// 开启复用的场景
//		for uid, value := range resuseUidMap {
//			uidTags := make([]*v20180317.TagInfo, 0)
//			for index, tag := range loadBalancer.Tags {
//				if strings.HasPrefix(*tag.TagKey, getTagPrefix(uid)) {
//					uidTags = append(uidTags, loadBalancer.Tags[index])
//				}
//			}
//			if service, exist := services[uid]; exist {
//				for _, uidTag := range uidTags {
//					if listener, exist := listenersListenerIdMap[*uidTag.TagValue]; exist {
//						loadBalancerResource.Labels[uid] = LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE
//						alreadyRecordListener[getListenerKey(listener.GetListenerPort(), listener.GetProtocol())] = true
//						loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(listener.GetListenerPort(), listener.GetProtocol(), service))
//					}
//				}
//			}
//			// 对应标签资源删除
//			removeTags = append(removeTags, utils.NewTagResource(uid, value))
//			for _, uidTag := range uidTags {
//				removeTags = append(removeTags, utils.NewTagResource(*uidTag.TagKey, *uidTag.TagValue))
//			}
//		}
//
//		// 未开启复用，使用已有的场景
//		for key, uid := range uidMap {
//			if service, exist := services[uid]; exist {
//				loadBalancerResource.Labels[uid] = LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE
//				serviceListener := make(map[string]bool)
//				specifyProtocolMap, _ := utils.GetSpecifyProtocol(service)
//				for _, port := range service.Spec.Ports {
//					if specifyProtocol, specifyProtocolExist := (*specifyProtocolMap)[port.Port]; specifyProtocolExist {
//						for _, protocol := range specifyProtocol.Protocol {
//							serviceListener[getListenerKey(int64(port.Port), strings.ToUpper(protocol))] = true
//							serviceListener[getClassicListenerKey(int64(port.Port), int64(port.NodePort), strings.ToUpper(protocol))] = true
//						}
//					} else {
//						serviceListener[getListenerKey(int64(port.Port), strings.ToUpper(string(port.Protocol)))] = true
//						serviceListener[getClassicListenerKey(int64(port.Port), int64(port.NodePort), strings.ToUpper(string(port.Protocol)))] = true
//					}
//				}
//				for key, listener := range listenersListenerIdMap {
//					if _, exist := serviceListener[key]; exist {
//						alreadyRecordListener[getListenerKey(listener.GetListenerPort(), listener.GetProtocol())] = true
//						loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(listener.GetListenerPort(), listener.GetProtocol(), service))
//					}
//				}
//			}
//			// 对应标签资源删除
//			removeTags = append(removeTags, utils.NewTagResource(key, uid))
//		}
//
//		// 标签被破坏的场景，对账用户声明和负载均衡监听器
//		if listeners, err := loadBalancerContext.GetMixedListeners(); err == nil {
//			for uid, service := range services {
//				if existLB, exist := utils.GetExistLB(service); exist && existLB == loadbalancerId {
//					listenerKeyByService := GetListenerKeyByService(service)
//					for listenerKey, _ := range listenerKeyByService {
//						_, alreadyRecord := alreadyRecordListener[listenerKey]
//						listener, listenerExist := listeners[listenerKey]
//						if listenerExist && !alreadyRecord && listener.GetListenerName() == TKEDedicatedListenerName {
//							loadBalancerResource.Labels[uid] = LOADBALANCERRESOURCE_LABEL_SERVICE_VALUE
//							alreadyRecordListener[getListenerKey(listener.GetListenerPort(), listener.GetProtocol())] = true
//							loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListener(listener.GetListenerPort(), listener.GetProtocol(), service))
//						}
//					}
//				}
//			}
//			for uid, ingress := range ingresses {
//				if existLB, exist := utils.GetIngressExistLbId(ingress); exist && existLB == loadbalancerId {
//					listenerKeySet := GetListenerKeyByIngress(ingress)
//					for listenerKey, _ := range listenerKeySet {
//						_, alreadyRecord := alreadyRecordListener[listenerKey]
//						listener, listenerExist := listeners[listenerKey]
//						if listenerExist && !alreadyRecord && listener.GetListenerName() == TKEDedicatedListenerName {
//							loadBalancerResource.Labels[uid] = LOADBALANCERRESOURCE_LABEL_INGRESS_VALUE
//							alreadyRecordListener[getListenerKey(listener.GetListenerPort(), listener.GetProtocol())] = true
//							loadBalancerResource.Spec.Listeners = append(loadBalancerResource.Spec.Listeners, BuildLoadBalancerResourceListenerIngress(listener.GetListenerPort(), listener.GetProtocol(), ingress))
//						}
//					}
//				}
//			}
//		}
//
//		if _, err := createLoadBalancerResource(loadBalancerResource); err != nil {
//			continue
//		}
//		if len(removeTags) != 0 {
//			_ = ModifyAndRecycleLoadBalancerTags(s, region, loadbalancerId, []*v20180813.TagResource{}, removeTags)
//		}
//		_, _ = UpdateLoadBalancerResourceStatus(*loadBalancer.LoadBalancerId, s)
//	}
// }

// 引用列表中是否只有指定的一个Service资源
func onlyUsedByService(references []*v1alpha1.ResourceObjectReference, service service_wrapper.ServiceWrapper) bool {
	onlyUsed := true
	for _, reference := range references {
		if reference.Kind == string(service.ServiceType()) && reference.Namespace == service.GetObjectMeta().GetNamespace() && reference.Name == service.GetObjectMeta().GetName() {
			continue
		} else {
			onlyUsed = false
			break
		}
	}
	return onlyUsed
}

// 在引用列表中删除一个Service资源
func removeByService(references []*v1alpha1.ResourceObjectReference, service service_wrapper.ServiceWrapper) []*v1alpha1.ResourceObjectReference {
	result := make([]*v1alpha1.ResourceObjectReference, 0)
	for index, reference := range references {
		if reference.Kind == string(service.ServiceType()) && reference.Namespace == service.GetObjectMeta().GetNamespace() && reference.Name == service.GetObjectMeta().GetName() {
			continue
		} else {
			result = append(result, references[index])
		}
	}
	return result
}
