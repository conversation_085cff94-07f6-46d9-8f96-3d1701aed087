package lbcontroller

import (
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
)

var timeRecord time.Time

func TestCalculateProtectPods(t *testing.T) {
	protectPods := &sync.Map{}

	pod1 := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name: "pod1",
		},
		Status: v1.PodStatus{
			PodIPs: []v1.PodIP{{"*******"}},
		},
	}
	pod2 := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name: "pod2",
		},
		Status: v1.PodStatus{
			PodIPs: []v1.PodIP{{"*******"}},
		},
	}
	pod3 := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name: "pod3",
		},
		Status: v1.PodStatus{
			PodIPs: []v1.PodIP{{"*******"}},
		},
	}
	pod4 := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name: "pod4",
		},
		Status: v1.PodStatus{
			PodIPs: []v1.PodIP{{"*******"}},
		},
	}
	pod5 := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name: "pod5",
		},
		Status: v1.PodStatus{ // without ip
		},
	}
	pod6 := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name: "pod6",
		},
		Status: v1.PodStatus{ // without ip
			PodIPs: []v1.PodIP{{"2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF"}}, // ipv6
		},
	}

	testcases := []struct {
		name                string
		pods                []*v1.Pod
		targets             sets.String
		cachedProtectPods   *sync.Map
		expectDeProtectPods map[string]*v1.Pod
		expectToProtectPods map[string]*v1.Pod
	}{
		// pods:			  1, 2, 3, 4, 5(no ip), 6(ipv6)
		// targets:              2, 3,                      7, 8
		// cachedProtectPods:       3, 4,                      8, 9
		{
			name: "no pods",
			pods: []*v1.Pod{
				pod1, pod2, pod3, pod4, pod5, pod6,
			},
			targets:           sets.NewString("*******", "*******", "*******", "*******"),
			cachedProtectPods: protectPods,
			expectDeProtectPods: map[string]*v1.Pod{
				"pod1": pod1,
				"pod4": pod4,
				"pod5": pod5, // pod5 缺乏ip，
				"pod6": pod6, // pod6 ipv6
				"pod8": nil,  // pod 已经不存在
				"pod9": nil,  // pod 已经不存在
			},
			expectToProtectPods: map[string]*v1.Pod{
				"pod2": pod2,
				"pod3": pod3,
			},
		},
	}
	protectPods.Store("pod3", "*******")
	protectPods.Store("pod4", "*******")
	protectPods.Store("pod8", "*******")
	protectPods.Store("pod9", "*******")

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			deProtectPods, toProtectPods := calculateProtectPods("ipv4", tc.pods, tc.targets, tc.cachedProtectPods)

			assert.Equal(t, tc.expectDeProtectPods, deProtectPods)
			assert.Equal(t, tc.expectToProtectPods, toProtectPods)
		})
	}
}
