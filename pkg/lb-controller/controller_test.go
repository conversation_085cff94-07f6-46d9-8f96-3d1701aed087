package lbcontroller

import (
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestGetServicesByEndpointsName(t *testing.T) {
	// 公共测试数据
	testNamespace := "test-ns"
	endpointsName := "test-ep"

	tests := []struct {
		name     string
		services []*v1.Service
		wantSWS  []string // 期望的service名称列表
		wantEPS  []string // 期望的endpoints列表
	}{
		{
			name: "同名Service匹配",
			services: []*v1.Service{
				createService(testNamespace, "test-ep", true, "", nil),
			},
			wantSWS: []string{"test-ep"},
			wantEPS: []string{},
		},
		{
			name: "注解匹配且Sync为true",
			services: []*v1.Service{
				createService(testNamespace, "svc1", true, `{"name":"test-ep","sync":true}`, nil),
			},
			wantSWS: []string{"svc1"},
			wantEPS: []string{"test-ns/svc1"},
		},
		{
			name: "注解匹配但Sync为false",
			services: []*v1.Service{
				createService(testNamespace, "svc2", true, `{"name":"test-ep","sync":false}`, nil),
			},
			wantSWS: []string{"svc2"},
			wantEPS: []string{},
		},
		{
			name: "不同namespace过滤",
			services: []*v1.Service{
				createService("other-ns", "svc3", true, `{"name":"test-ep"}`, nil),
			},
			wantSWS: []string{},
			wantEPS: []string{},
		},
		{
			name: "非LoadBalancer类型过滤",
			services: []*v1.Service{
				createService(testNamespace, "svc4", false, `{"name":"test-ep"}`, nil),
			},
			wantSWS: []string{},
			wantEPS: []string{},
		},
		{
			name: "有Selector的Service过滤",
			services: []*v1.Service{
				createService(testNamespace, "svc5", true, `{"name":"test-ep"}`, map[string]string{"app": "test"}),
			},
			wantSWS: []string{},
			wantEPS: []string{},
		},
		{
			name: "混合场景测试",
			services: []*v1.Service{
				// 应匹配的同名Service
				createService(testNamespace, "test-ep", true, "", nil),
				// 应匹配的注解Service
				createService(testNamespace, "anno-svc", true, `{"name":"test-ep","sync":true}`, nil),
				// 不同namespace的同名Service
				createService("other-ns", "test-ep", true, "", nil),
				// 无效的注解格式
				createService(testNamespace, "invalid-svc", true, "invalid-json", nil),
			},
			wantSWS: []string{"test-ep", "anno-svc"},
			wantEPS: []string{"test-ns/anno-svc"},
		},
		{
			name: "当前endpoints不能重复入队",
			services: []*v1.Service{
				createService(testNamespace, "test-ep", true, `{"name":"test-ep", "sync":true}`, nil),
			},
			wantSWS: []string{"test-ep"},
			wantEPS: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行
			sws := getServicesByEndpointsName(testNamespace, endpointsName, tt.services)

			// 验证ServiceWrapper
			var gotSWS []string
			for _, sw := range sws {
				gotSWS = append(gotSWS, sw.Name())
			}
			assert.ElementsMatch(t, tt.wantSWS, gotSWS, "ServiceWrapper列表不匹配")
		})
	}
}

// 创建测试用Service对象的helper函数
func createService(namespace, name string, isLB bool, annotation string, selector map[string]string) *v1.Service {
	svc := &v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
			Annotations: map[string]string{
				"service.cloud.tencent.com/loadbalancer-source-endpoints": annotation,
			},
		},
		Spec: v1.ServiceSpec{
			Selector: selector,
			Type:     v1.ServiceTypeClusterIP,
		},
	}

	if isLB {
		svc.Spec.Type = v1.ServiceTypeLoadBalancer
	}

	return svc
}
