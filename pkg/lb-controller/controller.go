/*
Copyright 2015 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package lbcontroller

import (
	"context"
	goerrors "errors"
	"fmt"
	"k8s.io/apimachinery/pkg/util/sets"
	"strings"
	"sync"
	"time"

	crd2 "git.woa.com/kateway/loadbalancer-resource-api/pkg/crd"
	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/domain/event"
	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/server"
	"git.woa.com/kateway/pkg/domain/service/errcode"
	servicelabels "git.woa.com/kateway/pkg/domain/service/labels"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	cluster2 "git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"git.woa.com/kateway/pkg/domain/types"
	"git.woa.com/kateway/pkg/runtime"
	clbInner "git.woa.com/kateway/pkg/tencent/cloud/clbinternal"
	"git.woa.com/kateway/pkg/tencent/cloudctx"
	ingcontrollerapp "git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app"
	ingressClusterService "git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
	"git.woa.com/kateway/tke-service-config/pkg/crd"
	"github.com/samber/lo"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tke "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tke/v20180525"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	metatypes "k8s.io/apimachinery/pkg/types"
	runtimeUtil "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/retry"
	"k8s.io/klog/v2"
	glog "k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/plugin/tencent"
	service2 "cloud.tencent.com/lb-controller/pkg/service"
	"cloud.tencent.com/lb-controller/pkg/service/cluster"
	"cloud.tencent.com/lb-controller/pkg/utils"
	"cloud.tencent.com/lb-controller/pkg/utils/cluster_service"
)

const (
	SYNC_CHECK_CRD_FREQUENCY     = 5 * time.Minute
	SYNC_BACKEND_QUOTA_FREQUENCY = 10 * time.Minute
	SYNC_PROJECT_ID_FREQUENCY    = 2 * time.Hour

	// Interval of synchronizing service status from apiserver
	serviceSyncPeriod = 30 * time.Second
	// Interval of synchronizing node status from apiserver
	nodeSyncPeriod = 100 * time.Second

	// How long to wait before retrying the processing of a service change.
	// If this changes, the sleep in hack/jenkins/e2e.sh before downing a cluster
	// should be changed appropriately.
	// minRetryDelay = 5 * time.Second
	// maxRetryDelay = 300 * time.Second

	clientRetryCount    = 5
	clientRetryInterval = 5 * time.Second
	storeSyncPollPeriod = 5 * time.Second

	// LabelNodeRoleMaster specifies that a node is a master
	// It's copied over to kubeadm until it's merged in core: https://github.com/kubernetes/kubernetes/pull/39112
	LabelNodeRoleMaster = "node-role.kubernetes.io/master"

	// LabelNodeRoleExcludeBalancer specifies that the node should be
	// exclude from load balancers created by a cloud provider.
	LabelNodeRoleExcludeBalancer = "alpha.service-controller.kubernetes.io/exclude-balancer"

	// // DefaultMetricsPort is used when option function MetricsPort is omitted
	// DefaultMetricsPort = 0
	// // DefaultMetricsAddress is used when option function MetricsAddress is omitted
	// DefaultMetricsAddress = "0.0.0.0"
	// // DefaultMetricsPath is used when option function MetricsPath is omitted
	// DefaultMetricsPath = "/metrics"

	LoadBalancerIDAnnontation      = "service.kubernetes.io/loadbalance-id"
	LoadBalancerTypeAnnontation    = "service.kubernetes.io/loadbalance-type"
	LoadBalancerIPV4Annontation    = "service.kubernetes.io/loadbalance-ipv4"
	LoadBalancerNATIPV6Annontation = "service.kubernetes.io/loadbalance-nat-ipv6"
	LoadBalancerIPV6Annontation    = "service.kubernetes.io/loadbalance-ipv6"

	ServiceClusterIPLBID = "service.kubernetes.io/qcloud-clusterip-loadbalance-id"

	// LocalSvcOnlyBindNodeWithPodAnnontation is used when externalTrafficPolicy=local svc only bind node with pod to lb
	LocalSvcOnlyBindNodeWithPodAnnontation = "service.kubernetes.io/local-svc-only-bind-node-with-pod"
	LocalSvcOnlyBindNodeWithPodTrue        = "true"
)

var KeyFunc = cache.DeletionHandlingMetaNamespaceKeyFunc

var systemServices = []metatypes.NamespacedName{
	{Namespace: "default", Name: "kubernetes"},
	{Namespace: "kube-system", Name: "hpa-metrics-service"},
}

type cachedService struct {
	// The cached state of the service
	state           service_wrapper.ServiceWrapper
	rsLimitExceeded bool
	protectPods     sync.Map // 该service 下所有因为开启优雅删除的pod
}

type serviceCache struct {
	mu         sync.Mutex // protects serviceMap
	serviceMap map[string]*cachedService
}

// LbController keeps cloud provider service resources
// (like load balancers) in sync with the registry.
type LbController struct {
	knownHosts       []*v1.Node
	servicesToUpdate []service_wrapper.ServiceWrapper
	clusterName      string
	cache            *serviceCache
	// services that need to be synced
	silent  bool
	workers int
}

func (s *LbController) updateUpdateStats(svc service_wrapper.ServiceWrapper, errs []error, startTime time.Time) {
	if len(errs) == 0 {
		services.UploadMetricsAndEvent(svc, types.NewError(errcode.Success, ""))
		metrics.Instance.UpdateSyncTime(svc, errcode.Success.Code, time.Since(startTime))
		return
	}

	errorCodeDeduplication := make(map[string]bool)
	for _, err := range errs {
		serviceError := types.NewError(errcode.UnexpectedError, err.Error(), utils.ServiceName(svc))
		returnCode := errcode.UnexpectedError.Code
		if wrapperErr, ok := lo.ErrorsAs[*types.Error](err); ok {
			serviceError = wrapperErr
			returnCode = wrapperErr.ErrorCode.Code
		}

		if _, exist := errorCodeDeduplication[returnCode]; exist { // 同一个资源对象同一个错误码的事件是会覆盖，没有必要再次上报。
			continue
		}
		errorCodeDeduplication[returnCode] = true

		services.UploadMetricsAndEvent(svc, serviceError)
		metrics.Instance.UpdateSyncTime(svc, returnCode, time.Since(startTime))
	}
}

// New returns a new service controller to keep cloud provider service resources
// (like load balancers) in sync with the registry.
// kateway(bitliu[8]): 新建 controller，同时初始化相关信息
func New(clusterName string, workers int) *LbController {
	s := &LbController{
		knownHosts:  []*v1.Node{},
		clusterName: clusterName,
		workers:     workers,
		cache:       &serviceCache{serviceMap: make(map[string]*cachedService)},
	}

	/*
		s.updateBackendQuota()
		updateClusterProjectID()
		doUploadOperationalMetrics()
		go CheckCRDAlready(wait.NeverStop)    // kateway: 确保自定义 crd 存在(TkeServiceConfig LoadBalancerResource)
		go s.syncBackendQuota(wait.NeverStop) // kateway: 用户可以调整的配置，需要定时同步。配额查询和调整：https://clb.woa.com/lb/setLbLimit
		go syncProjectID(wait.NeverStop)
		go uploadOperationalMetrics()
	*/

	return s
}

// Run starts a background goroutine that watches for changes to services that
// have (or had) LoadBalancers=true and ensures that they have
// load balancers created and deleted appropriately.
// serviceSyncPeriod controls how often we check the cluster's services to
// ensure that the correct load balancers exist.
// nodeSyncPeriod controls how often we check the cluster's nodes to determine
// if load balancers need to be updated to point to a new set.
//
// It's an error to call Run() more than once for a given LbController
// object.
// kateway(bitliu[9]): 初始化 队列，启动 clusterservice
func (s *LbController) Run(ctx context.Context) error {
	defer runtimeUtil.HandleCrash()
	defer cluster2.Instance.Stop()

	if err := s.syncBackendQuota(); err != nil {
		return err
	}
	if err := syncProjectID(); err != nil {
		return err
	}

	uploadMetrics()
	updateCRDs()
	cluster.InitQueueServiceInstance(s.syncService, s.syncMultiClusterService, s.syncHealth, s.syncStatus, s.syncProtect, s.syncEndpoints, s.SyncPodEvent, s.SyncPodDeleteEvent, s.workers) // kateway: 所有异步队列的并发度都是一样的（可优化）

	glog.Infof("Starting loadbalancer controller")
	cluster2.Instance.Run(ctx.Done())
	glog.Infof("Shutting down Loadbalancer Controller")
	return nil
}

func getServicesByEndpointsName(namespace, name string, svcs []*v1.Service) []service_wrapper.ServiceWrapper {
	sws := make([]service_wrapper.ServiceWrapper, 0)
	for _, svc := range svcs {
		if svc.Namespace != namespace {
			continue
		}
		serviceWrapper := service_wrapper.NewService(svc)
		if !utils.IsLoadBalancerType(serviceWrapper) {
			glog.V(5).Infof("syncNodes: service [%s] does not need loadbalancer , skip.\n", svc.Name)
			continue
		}

		if serviceWrapper.Name() == name {
			// 同名service必须入service队列
			glog.V(5).Infof("controller: syncEndpoints, enqueue service [%s/%s]\n", svc.Namespace, svc.Name)
			sws = append(sws, serviceWrapper)
			continue
		}
		// 入队通过注解关联的svc
		if !serviceWrapper.Service().IsNoSelector() {
			// 其余service入队需要满足no selector和 loadbalancer-source-endpoints 注解指向该endpoints
			continue
		}
		// 注解指向的service
		if epBinding, err := serviceWrapper.LoadbalancerSourceEndpoints(); err != nil || epBinding.Name != name {
			continue
		}
		sws = append(sws, serviceWrapper)
	}
	// 根据service名称去重
	sws = lo.UniqBy(sws, func(sw service_wrapper.ServiceWrapper) string { return sw.Name() })
	return sws
}

func tryEnqueueServicesByEndpoints(namespace, name string) {
	svcs, err := cluster2.Instance.ServiceIndexer().ByIndex("LoadBalancerSourceEndpoints", types.JoinKeyStrings("/", namespace, name))
	if err != nil {
		glog.Errorf("controller: syncEndpoints, get service by index LoadBalancerSourceEndpoints failed: %v", err)
		return
	}
	var validServices []*v1.Service
	for _, obj := range svcs {
		if svc, ok := obj.(*v1.Service); ok {
			validServices = append(validServices, svc)
		}
	}
	if svc, err := cluster2.Instance.ServiceLister().Services(namespace).Get(name); err == nil {
		// endpoints同名的service需要入队
		validServices = append(validServices, svc)
	}
	services := getServicesByEndpointsName(namespace, name, validServices)
	for _, svc := range services {
		cluster_service.QueueServiceInstance.ServiceQueue().Enqueue(taskqueue.Item{
			Data:   utils.ServiceName(svc),
			Weight: cluster.DetermineEnqueueweight(svc.GetObjectMeta()),
		})
	}
}

func (s *LbController) syncEndpoints(key string) []error {
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		return []error{err}
	}
	// 入队和当前endpoints有关的service
	tryEnqueueServicesByEndpoints(namespace, name)
	// 结束
	return nil
}

func (s *LbController) syncBackendQuota() error {
	if err := s.updateBackendQuota(); err != nil {
		return fmt.Errorf("initial update of backend quota failed: %w", err)
	}

	go wait.Forever(func() {
		if err := s.updateBackendQuota(); err != nil {
			glog.Errorf("update backend quota failed: %v", err)
		}
	}, SYNC_BACKEND_QUOTA_FREQUENCY)
	return nil
}

func (s *LbController) updateBackendQuota() error {
	oldQuota := config.Global.GetBackendQuotaObj()
	newQuota, err := DescribeQuota()
	if err != nil {
		return fmt.Errorf("retrieve backend quota error: %w", err)
	}
	if err = config.Global.UpdateBackendQuota(newQuota); err != nil {
		return fmt.Errorf("update backend quota failed: %w", err)
	}
	s.rejoinServiceByQuotaDiff(oldQuota, newQuota)
	return nil
}

func (s *LbController) rejoinServiceByQuotaDiff(old, new *config.BackendQuota) {
	reJoinServices := sets.Set[string]{}
	// 针对直连类型的服务，判断CLB用户配额是否变化
	flagReJoinDirectService := new.LBUserQuota != old.LBUserQuota
	// 针对非直连类型的服务，判断实际使用的配额是否变化
	flagReJoinIndirectService := min(new.OptionQuota, new.LBUserQuota) != min(old.OptionQuota, old.LBUserQuota)
	// 将 获取的最新的实例配额 和 系统配置中的旧的实例配额 比较，得到新增的、配额发生变化的实例ID
	changedInstanceQuotaKeys := lo.Filter(lo.Keys(new.LBInstanceQuota), func(k string, _ int) bool {
		oldVal, exists := old.LBInstanceQuota[k]
		return !exists || oldVal != new.LBInstanceQuota[k]
	})

	// 打印发生变化的直连访问类型使用的配额
	if flagReJoinDirectService {
		glog.Infof("backendQuota for direct-service changed: old value:%d, new value:%d.",
			old.LBUserQuota, new.LBUserQuota)
	}
	// 打印发生变化的非直连访问类型使用的配额
	if flagReJoinIndirectService {
		glog.Infof("backendQuota for indirect-service changed: old value=%d, new value=%d.",
			min(old.OptionQuota, old.LBUserQuota), min(new.OptionQuota, new.LBUserQuota))
	}
	// 打印发生变化的实例配额
	for _, lbID := range changedInstanceQuotaKeys {
		glog.Infof("backendQuota for Instance CLB Quota (clbID: %s) changed: old value=%d, new value=%d.",
			lbID, old.LBInstanceQuota[lbID], new.LBInstanceQuota[lbID])
	}

	// 使用该map存储rs超限的 ingress 和 lbId
	rsLimitExceededMapInstanceIDToService := map[string]string{}
	s.cache.forEach(func(_ string, cs *cachedService) {
		if cs.rsLimitExceeded {
			// 当实例配额发生变化时，从service缓存中遍历获取rs配额受限的service，并解析注解拿到关联的lbID
			if len(changedInstanceQuotaKeys) > 0 {
				annotations := cs.state.GetObjectMeta().GetAnnotations()
				if annotations != nil {
					curServiceIbId := annotations[LoadBalancerIDAnnontation]
					rsLimitExceededMapInstanceIDToService[curServiceIbId] = utils.ServiceName(cs.state)
				}
			}
			// 1. 收集缓存中rs配额超限的、可能受到CLB中用户配额影响的直连类型service, 或者
			// 2. 收集缓存中rs配额超限的、可能受到启动参数中的配额影响的非直连类型service
			if flagReJoinDirectService && cs.state.Service().IsDirectAccess() ||
				flagReJoinIndirectService && cs.state.Service().IsIndirectAccess() {
				reJoinServices.Insert(utils.ServiceName(cs.state))
			}
		}
	})
	// 将rs配额受限的、且关联的clb实例配额发生变化的service记录下来，这些受到影响的service需要重新入队
	for _, lbID := range changedInstanceQuotaKeys {
		if service, exists := rsLimitExceededMapInstanceIDToService[lbID]; exists {
			reJoinServices.Insert(service)
		}
	}
	for _, serviceKey := range reJoinServices {
		cluster_service.QueueServiceInstance.ServiceQueue().Enqueue(taskqueue.Item{
			Data:   serviceKey,
			Weight: 1,
		})
	}
	return
}

func DescribeQuota() (*config.BackendQuota, error) {
	request := clbInner.NewDescribeQuotaRequest()
	request.ResourceQuota = common.BoolPtr(true)
	if rsp, err := tencentapi.Instance.DescribeQuota(cloudctx.New(nil, config.Global.Region), request); err != nil {
		glog.Errorf("DescribeQuota Error, %v", err)
		return nil, err
	} else {
		quota := config.NewBackendQuota(-1, config.Global.BackendQuota)
		for _, item := range rsp.Response.QuotaSet {
			if *item.QuotaId == "TOTAL_TARGET_BIND_QUOTA" {
				if *item.ResourceId == "default" {
					quota.LBUserQuota = int(*item.QuotaLimit)
				} else {
					quota.LBInstanceQuota[*item.ResourceId] = int(*item.QuotaLimit)
				}
			}
		}
		if quota.LBUserQuota == -1 {
			return nil, fmt.Errorf("can't find UserQuota in response")
		}
		return quota, nil
	}
}

func syncProjectID() error {
	if env.IsInEKSCluster() {
		return nil
	}

	if config.Global.ProjectID == -1 { // 独立部署时，指定为-1，可解除对tke接口的依赖
		klog.V(4).Infof("skip sync project id")
		return nil
	}

	if err := updateClusterProjectID(); err != nil {
		return fmt.Errorf("initial update of the project id failed: %w", err)
	}

	go wait.Forever(func() {
		if err := updateClusterProjectID(); err != nil {
			glog.Errorf("update of the project id failed: %v", err)
		}
	}, SYNC_PROJECT_ID_FREQUENCY)
	return nil
}

func updateClusterProjectID() error {
	request := tke.NewDescribeClustersRequest()
	request.ClusterIds = []*string{&config.Global.ClusterName}
	response, err := tencentapi.Instance.DescribeClusters(cloudctx.New(nil, config.Global.Region), request)
	if err != nil {
		return fmt.Errorf("describe clusters error: %w", err)
	}
	if len(response.Response.Clusters) == 0 {
		return goerrors.New("cluster not found")
	}
	pID := common.Int64Ptr(int64(*response.Response.Clusters[0].ProjectId))
	if config.Global.ProjectID != *pID {
		glog.Infof("projectId update to: %d", *pID)
		config.Global.ProjectID = *pID
	}
	return nil
}

func updateCRDs() {
	go wait.Forever(func() {
		apiextensionsKubeClient := cluster2.Instance.ApiextensionsKubeclient()
		_ = crd.TkeServiceConfigInitTransaction(apiextensionsKubeClient)
		_ = crd2.LoadBalancerResourceInitTransaction(apiextensionsKubeClient)
	}, SYNC_CHECK_CRD_FREQUENCY)
}

// kateway 将 clb 信息同步到 service 的 annotation/status, todo 重命名。。。。。 SyncToCLB_SyncCLB2Service
// createLoadBalancerIfNeeded ensures that service's status is synced up with loadbalancer
// i.e. creates loadbalancer for service if requested and deletes loadbalancer if the service
// doesn't want a loadbalancer no more. Returns whatever error occurred.
func (s *LbController) createLoadBalancerIfNeeded(syncContext *tencent.SyncContext) error {
	// Note: It is safe to just call EnsureLoadBalancer.  But, on some clouds that requires a delete & create,
	// which may involve service interruption.  Also, we would like user-friendly events.

	// Save the state so we can avoid a write if it doesn't change
	service := syncContext.Service

	// Make a copy so we don't mutate the shared informer cache.
	if err := s.GodOnlyKnows(syncContext); err != nil {
		glog.Errorf("failed to ensure load balancer for service %s error: %v", utils.ServiceName(service), err)
		// cluster.ClusterServiceInstance.EventRecorder().Eventf(service, v1.EventTypeWarning, "EnsureLoadBalancerFailed", "failed to ensure load balancer for service %s error: %v", key, err)
		return err
	}

	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}
	// 将 clb 信息同步到 service 的 annotation
	errAnno := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		annotations := make(map[string]string)
		if getAnnotations := service.GetObjectMeta().GetAnnotations(); getAnnotations != nil {
			for key, value := range getAnnotations {
				annotations[key] = value
			}
		}

		persistUpdateFlag := false
		serviceIPStack := service.GetServiceIPStack()

		// 将非当前网络栈的VIP，写入注解中
		oldlbID, ok := annotations[LoadBalancerIDAnnontation]
		oldnatipv6, natIPv6exist := annotations[LoadBalancerNATIPV6Annontation]
		oldipv6, ipv6exist := annotations[LoadBalancerIPV6Annontation]
		oldipv4, ipv4exist := annotations[LoadBalancerIPV4Annontation]
		if !ok || oldlbID != *loadBalancer.LoadBalancerId {
			annotations[LoadBalancerIDAnnontation] = *loadBalancer.LoadBalancerId // kateway 用户如果乱写lb id，会被强制覆盖回来
			persistUpdateFlag = true
		}
		if serviceIPStack == "mixed" { // 双栈，所有IP写到Status中，注解不需要提供
			if natIPv6exist {
				delete(annotations, LoadBalancerNATIPV6Annontation)
				persistUpdateFlag = true
			}
			if ipv6exist {
				delete(annotations, LoadBalancerIPV6Annontation)
				persistUpdateFlag = true
			}
			if ipv4exist {
				delete(annotations, LoadBalancerIPV4Annontation)
				persistUpdateFlag = true
			}
		} else if serviceIPStack == "ipv4" {
			if ipv4exist {
				delete(annotations, LoadBalancerIPV4Annontation)
				persistUpdateFlag = true
			}
			if loadBalancer.AddressIPVersion != nil && strings.ToLower(*loadBalancer.AddressIPVersion) == "ipv6" && loadBalancer.AddressIPv6 != nil {
				if loadBalancer.IPv6Mode != nil && strings.ToLower(*loadBalancer.IPv6Mode) == "ipv6nat64" {
					if !natIPv6exist || oldnatipv6 != *loadBalancer.AddressIPv6 {
						annotations[LoadBalancerNATIPV6Annontation] = *loadBalancer.AddressIPv6
						persistUpdateFlag = true
					}
					if ipv6exist {
						delete(annotations, LoadBalancerIPV6Annontation)
						persistUpdateFlag = true
					}
				} else if loadBalancer.IPv6Mode != nil && strings.ToLower(*loadBalancer.IPv6Mode) == "ipv6fullchain" {
					if !ipv6exist || oldipv6 != *loadBalancer.AddressIPv6 {
						annotations[LoadBalancerIPV6Annontation] = *loadBalancer.AddressIPv6
						persistUpdateFlag = true
					}
					if natIPv6exist {
						delete(annotations, LoadBalancerNATIPV6Annontation)
						persistUpdateFlag = true
					}
				}
			}
		} else if serviceIPStack == "ipv6" {
			if natIPv6exist {
				delete(annotations, LoadBalancerNATIPV6Annontation)
				persistUpdateFlag = true
			}
			if ipv6exist {
				delete(annotations, LoadBalancerIPV6Annontation)
				persistUpdateFlag = true
			}
			if len(loadBalancer.LoadBalancerVips) != 0 {
				ipv4 := utils.StringSortJoin(loadBalancer.LoadBalancerVips, ",")
				if !ipv4exist || oldipv4 != ipv4 {
					annotations[LoadBalancerIPV4Annontation] = ipv4
					persistUpdateFlag = true
				}
			}
		}

		if utils.IsClusterIPAndNeedsCLB(service) {
			if serviceClusterIPLBID, exist := annotations[ServiceClusterIPLBID]; !exist || serviceClusterIPLBID != *loadBalancer.LoadBalancerId {
				annotations[ServiceClusterIPLBID] = *loadBalancer.LoadBalancerId
				persistUpdateFlag = true
			}
			if _, hasInternalSubnet := annotations[utils.AnnoServiceLBInternalUniqSubnetID]; !hasInternalSubnet {
				annotations[utils.AnnoServiceLBInternalUniqSubnetID] = config.Global.ClusterIPSubnet
				persistUpdateFlag = true
			}
		}

		if service.ServiceType() == service_wrapper.CoreService {
			if utils.IsClusterIPAndNeedsCLB(service) || utils.IsLoadBalancerAndNeedsClusterIP(service) {
				// 针对域名化改造的备注
				// ClusterIP模拟的方案仅适用于内网负载均衡，所以域名化改造之后，目前这个逻辑还是能够正常运行的。
				updatedService := service.RawService()
				if len(updatedService.Status.LoadBalancer.Ingress) > 0 && updatedService.Spec.ClusterIP != updatedService.Status.LoadBalancer.Ingress[0].IP {
					persistUpdateFlag = true
				}
			}
		}

		// 同步lb类型到labels中
		newLabels, ok := services.SetLabel(service.GetObjectMeta().GetLabels(), servicelabels.LoadBalancerType, *loadBalancer.LoadBalancerType)
		if ok {
			glog.V(4).InfoS("change to labels", service.Kind(), service.String(), "labels", newLabels)
			persistUpdateFlag = true
			service.GetObjectMeta().SetLabels(newLabels)
		} else {
			glog.V(4).InfoS("no change to labels", service.Kind(), service.String())
		}

		if !persistUpdateFlag {
			return nil
		}

		updated := service.DeepCopy()
		updated.GetObjectMeta().SetAnnotations(annotations)
		// 该逻辑是用 LB 模拟 cluster ip，风险比较高，且这种老集群已经不再支持新建和更新， 因此直接删除掉这段逻辑
		// 异常单：https://tapd.woa.com/tapd_fe/70108010/story/detail/1070108010119688823
		// if service.ServiceType() == service_wrapper.CoreService {
		//	if utils.IsClusterIPAndNeedsCLB(service) || utils.IsLoadBalancerAndNeedsClusterIP(service) {
		//		svc := service_wrapper.GetService(updated)
		//		if len(svc.Status.LoadBalancer.Ingress) > 0 && svc.Spec.ClusterIP != svc.Status.LoadBalancer.Ingress[0].IP {
		//			svc.Spec.ClusterIP = svc.Status.LoadBalancer.Ingress[0].IP
		//		}
		//	}
		// }
		updated, err = updated.Update()
		if err != nil {
			glog.Errorf("Services patch error for service %s. error %s", utils.ServiceName(service), err.Error())
			var getError error
			if updated, getError = service.GetLatest(); getError != nil {
				return getError
			}
			service = updated
			return err
		}
		syncContext.Service = updated
		service = updated
		return nil
	})

	// 将 clb 信息同步到 service 的 status
	errStatus := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		statusLoadBalancer := service.GetStatusLoadBalancer()
		previousState := LoadBalancerStatusDeepCopy(&statusLoadBalancer)
		// get LoadBalancerIngress
		newState := &v1.LoadBalancerStatus{
			Ingress: make([]v1.LoadBalancerIngress, 0),
		}
		vips, domain := GetVipAndDomain(syncContext.Service, loadBalancer)
		if len(vips) != 0 {
			for _, v := range vips { // IPv4、IPv6
				newState.Ingress = append(newState.Ingress, v1.LoadBalancerIngress{IP: *v})
			}
			if domain != nil {
				for index := range newState.Ingress {
					newState.Ingress[index].Hostname = *domain
				}
			}
		} else if domain != nil {
			newState.Ingress = append(newState.Ingress, v1.LoadBalancerIngress{Hostname: *domain})
		}

		// Write the state if changed
		// TODO: Be careful here ... what if there were other changes to the service?
		if LoadBalancerStatusEqual(previousState, newState) {
			return nil
		}
		updated := service.DeepCopy()
		updated.SetStatusLoadBalancer(*newState)
		updated, err = updated.UpdateStatus()
		if err != nil {
			glog.Errorf("Services patch error for service %s. error %s", utils.ServiceName(service), err.Error())
			var getError error
			if updated, getError = service.GetLatest(); getError != nil {
				glog.Errorf("Update Error. service get error. %s", getError)
				return getError
			}
			service = updated
			return err
		}
		syncContext.Service = updated
		service = updated
		return nil
	})
	// 更新 service 的annotation和status失败，需要抛出错误
	if errAnno != nil {
		glog.Errorf("Update Service Annotations Error. %s", errAnno)
		return errAnno
	} else if errStatus != nil {
		glog.Errorf("Update Service Status Error. %s", errStatus)
		return errStatus
	}
	return nil
}

// kateway(bitliu[45]): 针对不同类型的 SVC，执行不同的逻辑
// 1. 对于 MCS 执行 EnsureLoadBalancerFrame，只管理 Listener
// 2. 对于 Service 如果开启了 ManagerOnly 执行 EnsureLoadBalancerBackend，只管理 RS
// 3. 对于 Service 执行 EnsureLoadBalancer，管理 Listener 以及 RS
// kateway todo: 重命名 ???? EnsureClbAttributes_Listener_RS
func (s *LbController) GodOnlyKnows(syncContext *tencent.SyncContext) error {
	var err error
	service := syncContext.Service
	if service.ServiceType() == service_wrapper.CoreService {
		backendManageOnly, _, _ := utils.GetBackendManageOnly(syncContext.Service)
		if backendManageOnly { // kateway 多集群模式的子集群
			if err := tencent.EnsureLoadBalancerBackend(syncContext); err != nil {
				glog.Errorf("Ensure load balancer type for svc: %s/%s error: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
				return err
			}
		} else {
			if err := tencent.EnsureLoadBalancer(syncContext); err != nil {
				glog.Errorf("Ensure load balancer type for svc: %s/%s error: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
				return err
			}
		}
		if err := s.ensureProtectPods(syncContext); err != nil {
			glog.Errorf("Ensure protect pods for svc: %s/%s error: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
			return err
		}
	} else if service.ServiceType() == service_wrapper.MultiClusterService {
		if err := tencent.EnsureLoadBalancerFrame(syncContext); err != nil {
			glog.Errorf("Ensure load balancer type for svc: %s/%s error: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
			return err
		}
	} else {
		return nil
	}

	if service.ServiceType() == service_wrapper.CoreService {
		originService := service.RawService()

		if utils.IsServiceDirectAccess(service) {
			pods := make([]*v1.Pod, 0)
			if !utils.IsNoSelectorService(service) {
				if pods, err = cluster2.Instance.PodLister().Pods(service.GetObjectMeta().GetNamespace()).List(labels.SelectorFromSet(originService.Spec.Selector)); err != nil {
					glog.Errorf("PodLister List error for service %s. error %s", utils.ServiceName(service), err.Error())
					return err
				}
			}

			for _, pod := range pods {
				if service2.IsPodDirectBackend(pod) && !utils.IsPodUnableBind(pod) { // Pod支持直绑、并且Pod状态可以被绑定
					// TODO 原有Pod开启直绑后可能不存在ReadinessGate, 并不一定是AdmissionWebhook出现了问题
					// if pod.Spec.ReadinessGates == nil || len(pod.Spec.ReadinessGates) == 0 {
					//	glog.Infof("Warning AdmissionWebhook?")
					// }
					for _, condition := range pod.Status.Conditions {
						if condition.Type == utils.DirectAccessConditionType && condition.Status != v1.ConditionTrue { // TODO 执行在健康检查时间之后
							podName := utils.PodName(pod)
							time.AfterFunc(5*time.Second, func() {
								cluster_service.QueueServiceInstance.HealthQueue().Enqueue(taskqueue.Item{ // kateway: HealthQueue 入队
									Data:   podName,
									Weight: 1,
								})
							})
							break
						}
					}
				}
			}
		}
	}

	return err
}

// ListKeys implements the interface required by DeltaFIFO to list the keys we
// already know about.
func (s *serviceCache) ListKeys() []string {
	s.mu.Lock()
	defer s.mu.Unlock()
	keys := make([]string, 0, len(s.serviceMap))
	for k := range s.serviceMap {
		keys = append(keys, k)
	}
	return keys
}

// GetByKey returns the value stored in the serviceMap under the given key
func (s *serviceCache) GetByKey(key string) (interface{}, bool, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	if v, ok := s.serviceMap[key]; ok {
		return v, true, nil
	}
	return nil, false, nil
}

func (s *serviceCache) forEach(fn func(string, *cachedService)) {
	copy := make(map[string]*cachedService, len(s.serviceMap))
	s.mu.Lock()
	for k, v := range s.serviceMap {
		vv := &cachedService{state: v.state.DeepCopy(), rsLimitExceeded: v.rsLimitExceeded}
		copy[k] = vv
	}
	s.mu.Unlock()
	for k, v := range copy {
		fn(k, v)
	}
}

func (s *serviceCache) get(key string) (*cachedService, bool) {
	s.mu.Lock()
	defer s.mu.Unlock()
	service, ok := s.serviceMap[key]
	return service, ok
}

func (s *serviceCache) getOrCreate(key string) *cachedService {
	s.mu.Lock()
	defer s.mu.Unlock()
	_, ok := s.serviceMap[key]
	if !ok {
		s.serviceMap[key] = &cachedService{}
	}
	return s.serviceMap[key]
}

func (s *serviceCache) set(key string, service *cachedService) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.serviceMap[key] = service
}

func (s *serviceCache) delete(key string) {
	// todo delete时check下 pods
	s.mu.Lock()
	defer s.mu.Unlock()
	delete(s.serviceMap, key)
}

// kateway: Service 处理器
// syncService will sync the Service with the given key if it has had its expectations fulfilled,
// meaning it did not expect to see any more of its pods created or deleted. This function is not meant to be
// invoked concurrently with the same key.
func (s *LbController) syncService(key string) []error {
	errs := make([]error, 0)

	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		errs = append(errs, err)
		return errs
	}
	if utils.IsInEKSCluster() {
		if isSpecialService(namespace, name) { // kateway todo: 梳理这些特殊svc 在 eks/tke 的区别
			return errs
		}
	}

	service, err := cluster2.Instance.ServiceLister().Services(namespace).Get(name)
	if err != nil {
		if !apierrors.IsNotFound(err) {
			glog.Infof("Unable to retrieve Service %v from store: %v", key, err)
			errs = append(errs, err)
			return errs
		}
	}
	// 当service已经被删除，不存在时，也会执行到这里，此时service为nil
	serviceWrapper := service_wrapper.NewService(service)
	return s.syncLifeCycle(key, service_wrapper.CoreService, serviceWrapper)
}

// kateway MCS 处理器
// kateway(bitliu[16]): MCS 事件处理函数入口，解析 key，并通过 ServiceWrapper 封装进入控制逻辑
//
//	SyncLifeCycle 和普通 Service 没有区别
func (s *LbController) syncMultiClusterService(key string) []error {
	errs := make([]error, 0)
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		errs = append(errs, err)
		return errs
	}

	// multiClusterService holds the latest multiClusterService info from apiserver
	multiClusterService, err := cluster2.Instance.MultiClusterServiceLister().MultiClusterServices(namespace).Get(name)
	if err != nil {
		if !apierrors.IsNotFound(err) {
			glog.Infof("Unable to retrieve multiClusterService %v from store: %v", key, err)
			errs = append(errs, err)
			return errs
		}
	}

	service := service_wrapper.NewMultiClusterService(multiClusterService)
	return s.syncLifeCycle(key, service_wrapper.MultiClusterService, service)
}

func (s *LbController) syncLifeCycle(key string, serviceType service_wrapper.ServiceType, service service_wrapper.ServiceWrapper) (errs []error) {
	cacheKey := serviceCacheKey(serviceType, key) // fmt.Sprintf("%s/%s", serviceType, key)
	startTime := time.Now()

	klog.Infof("Syncing %v", key)

	shouldDeleteSvc := false
	if service.GetRuntimeObject() == nil { // kateway 已经被删除 svc，没有 object
		cs, ok := s.cache.get(cacheKey) // kateway todo 作用推测：svc删除时第一次同步失败了，cache 里会有记录；如果svc删除第一次同步成功，cache里没有记录（此种情况）, 但有Finalizer后似乎也用不到这个逻辑.
		if !ok {
			glog.Errorf("%s %s not in cache even though the watcher thought it was. Ignoring the deletion", service.ServiceType(), key)
			return nil
		}
		service = cs.state.DeepCopy()
		shouldDeleteSvc = true
	} else {
		service = service.DeepCopy()
		if service.GetObjectMeta().GetDeletionTimestamp() != nil {
			shouldDeleteSvc = true
		}
	}
	service.SetSyncBeginTime(startTime)

	syncContext := tencent.BuildSyncContext(service)
	defer func() {
		glog.Infof("Finished syncing %s %q (%v)", serviceType, key, time.Since(startTime))

		if r := recover(); r != nil {
			server.RecordErrors(service, runtime.GetPanicError(r))
			errs = []error{types.NewError(errcode.UnexpectedError, "internal error", utils.ServiceName(service))}
			defer panic(r)
		}
		if errCode := service.GetChaosErrorcode(); errCode != "" {
			if obj, ok := errcode.Registry.Get(errCode); ok {
				errs = append(errs, types.NewError(obj, "inject chaos error", utils.ServiceName(service)))
			}
		}
		s.updateUpdateStats(service, errs, startTime)
		if updateError := service.Finish(errs); updateError != nil {
			glog.Infof("Unable to update %s status to end %v: error %v", serviceType, key, updateError)
			errs = append(errs, updateError)
			return
		}
		glog.Infof("%s %q updated successfully (%v)", serviceType, key, time.Since(startTime))
	}()

	if shouldDeleteSvc {
		defer func() {
			if len(errs) != 0 {
				s.updateUpdateStats(service, errs, startTime)
			} else {
				metrics.Instance.RemoveObjectMetrics(service)
				// s.cache.delete(cacheKey)
			}
		}()
		// kateway(bitliu[17]): 这里删除事件没有区分 MCS 或者普通 Service，具体删除行为的差异需要关注
		if deleteError := s.deleteCachedLoadBalancer(service, cacheKey); deleteError != nil { // kateway 删除clb场景1：svc 正在被删除
			errs = append(errs, deleteError)
			return errs
		}
		if _, removeErr := RemoveFinalizer(service, types.ServiceFinalizer); removeErr != nil {
			if !apierrors.IsNotFound(removeErr) {
				errs = append(errs, removeErr)
				return errs
			}
		}
		return nil
	}

	// 重要：这里开始处理同步过程
	var cs = s.cache.getOrCreate(cacheKey)
	// cachedService, ok := s.cache.get(cacheKey)
	// 同名，但是uid不同说明原来的service已经删了。所以要删除LB
	if cs.state != nil {
		if cs.state.GetObjectMeta().GetUID() != service.GetObjectMeta().GetUID() {
			if deleteError := s.deleteCachedLoadBalancer(cs.state, cacheKey); deleteError != nil { // kateway 删除clb场景2：clb 对应的 svc 已经被删除
				errs = append(errs, deleteError)
				return errs
			}
			if _, removeErr := RemoveFinalizer(service, types.ServiceFinalizer); removeErr != nil {
				if !apierrors.IsNotFound(removeErr) {
					errs = append(errs, removeErr)
					return errs
				}
			}
			// s.cache.delete(cacheKey) // kateway 只是删掉老的cache对象这里不删
		}
	} // 后续开启对新 service 的调谐
	cs.state = service
	// s.cache.set(cacheKey, cachedService)

	// 如果service不再是LoadBalancer类型，那么就不应该有CLB与其对应
	// kateway(bitliu[17]): 新增 tke-management annotation 用于 CAMP 标识 MCS 是否 ready
	if !utils.IsLoadBalancerType(service) { // kateway 此事件能进来，说明是update 事件， 之前的 svc 是一个LoadBalancer 类型
		glog.Infof("Deleting existing load balancer type for service %s that no longer needs a load balancer.", key)
		if deleteError := s.deleteCachedLoadBalancer(cs.state, cacheKey); deleteError != nil { // kateway 删除clb场景3：service type 从LoadBalancer 转换为其他
			glog.Errorf("Deleting load balancer Error for service: %s. (will retry): %v", key, deleteError)
			errs = append(errs, deleteError)
			return errs
		}

		newLabels, ok := services.DeleteLabel(service.GetObjectMeta().GetLabels(), servicelabels.LoadBalancerType)
		if ok {
			glog.V(4).InfoS("change to labels", service.Kind(), service.String(), "labels", newLabels)
			service.GetObjectMeta().SetLabels(newLabels)
			_, err := service.Update()
			if err != nil {
				errs = append(errs, err)
				return errs
			}
		} else {
			glog.V(4).InfoS("no change to labels", service.Kind(), service.String())
		}

		err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
			var err, getError error
			_, loadBalancerIdExist := service.GetObjectMeta().GetAnnotations()[LoadBalancerIDAnnontation]
			_, clientTokenExist := service.GetObjectMeta().GetAnnotations()[utils.ClientTokenAnnotation]
			if !loadBalancerIdExist && !clientTokenExist { // 幂等返回
				return nil
			}

			updated := service.DeepCopy()
			annotations := updated.GetObjectMeta().GetAnnotations()
			if annotations != nil {
				delete(annotations, LoadBalancerIDAnnontation)
				delete(annotations, utils.ClientTokenAnnotation)
			}
			updated.GetObjectMeta().SetAnnotations(annotations)
			updated, err = updated.Update()
			if err != nil {
				if updated, getError = service.GetLatest(); getError == nil {
					service = updated
				}
				return err
			}
			service = updated
			return nil
		})
		if err != nil {
			glog.Errorf("Deleting load balancer status for service: %s. err: %s.", key, err)
		}

		err = retry.RetryOnConflict(retry.DefaultBackoff, func() error { // kateway: 去掉状态里的lb 信息
			var err, getError error
			if len(service.GetStatusLoadBalancer().Ingress) == 0 { // 幂等返回
				return nil
			}

			updated := service.DeepCopy()
			updated.SetStatusLoadBalancer(v1.LoadBalancerStatus{})
			updated, err = updated.UpdateStatus()
			if err != nil {
				if updated, getError = service.GetLatest(); getError == nil {
					service = updated
				}
				return err
			}
			service = updated
			return nil
		})
		if err != nil {
			glog.Errorf("Deleting load balancer status for service: %s. err: %s.", key, err)
			errs = append(errs, err)
			return errs
		}

		if _, removeErr := RemoveFinalizer(service, types.ServiceFinalizer); removeErr != nil {
			if !apierrors.IsNotFound(removeErr) {
				errs = append(errs, removeErr)
				return errs
			}
		}
		metrics.Instance.RemoveObjectMetrics(service)
		// s.cache.delete(cacheKey)
		return nil
	}

	// kateway(bitliu[29]): 新增或者更新事件的处理，不区分 MCS 或者子集群 Service 或者普通 Service
	if err := s.syncMain(syncContext); err != nil { // kateway service 正式同步开始
		errs = append(errs, err)
	}
	errs = append(errs, syncContext.Errors...)

	cs.rsLimitExceeded = errcode.ContainsRSLimitExceededErrOrWarn(errs)
	// Always update the cache upon success.
	// NOTE: Since we update the cached multiClusterService if and only if we successfully
	// processed it, a cached multiClusterService being nil implies that it hasn't yet
	// been successfully processed.
	// s.cache.set(key, cachedService)
	return errs
}

func (s *LbController) syncMain(syncContext *tencent.SyncContext) error {
	service := syncContext.Service
	if updated, err := AddFinalizer(service, types.ServiceFinalizer); err != nil {
		return err
	} else {
		syncContext.Service = updated
		service = updated
	}
	if err := tencent.GetLoadBalancerContext(syncContext); err != nil { // kateway: 这里会创建 CLB 和 LBR todo 重命名
		return err
	}
	if err := tencent.GetServiceContext(syncContext); err != nil {
		return err
	}
	// Service 对 Annotation 进行预检查
	if err := tencent.ServicePreCheck(syncContext); err != nil {
		return err
	}

	resource := syncContext.LoadBalancerContext.LoadBalancerResource
	if !resource.Spec.Created {
		// kateway: lb复用
		if err := tencent.CheckPortConflict(syncContext); err != nil {
			return err
		}
	}

	loadBalancer, err := syncContext.LoadBalancerContext.GetLoadBalancer()
	if err != nil {
		return err
	}

	if canProcess := tencent.LockLoadBalancerResource(*loadBalancer.LoadBalancerId, syncContext.Service); !canProcess {
		err := types.NewError(errcode.ReuseConcurrentOperationError, "", utils.ServiceName(service))
		return err
	}
	defer func(loadBalancerId string) {
		if lockErr := tencent.UnlockLoadBalancerResource(loadBalancerId); lockErr != nil {
			glog.Errorf("LoadBalancerResource unlock failed for lbid: %s, err: %v", loadBalancerId, lockErr)
			err = lockErr
		}
	}(*loadBalancer.LoadBalancerId)

	// 当前资源没有被过渡到其他集群，外部引用场景忽略标签同步
	if clusterId := tencent.GetClusterIdByTags(loadBalancer.Tags); clusterId == "" || clusterId == config.Global.ClusterName {
		if err = tencent.EnsureLoadBalancerTags(syncContext.LoadBalancerContext); err != nil {
			glog.Errorf("ensureLoadBalancerTags failed for service: %s/%s err: %v", service.GetObjectMeta().GetNamespace(), service.GetObjectMeta().GetName(), err)
			return err
		}
	}

	// kateway(bitliu[44]): 上面对 MCS 或者 Service 的 CLB 以及 LBR 进行了创建和同步
	// 这里是对 listener、监听规则，以及后端 RS 的管理
	if err = s.createLoadBalancerIfNeeded(syncContext); err != nil {
		return err
	}
	return nil
}

// kateway(bitliu[18]): 不管是 managerOnly 的 service 还是 MCS 的删除都会进入这个函数
func (s *LbController) deleteCachedLoadBalancer(service service_wrapper.ServiceWrapper, cacheKey string) (err error) {
	if err = s.cleanProtectPods(context.TODO(), service); err != nil {
		return err
	}

	loadbalancerContent, err := tencent.GetLoadBalancerByLoadbalancerResource(service)
	if err != nil {
		return err
	}

	// kateway 如果clb 不存在，则直接删除LBR
	if loadbalancerContent != nil {
		if _, e := loadbalancerContent.GetLoadBalancer(); e != nil {
			if errcode.IsLoadBalancerNotExistError(e) { // 幂等处理
				if e := tencent.DeleteLoadBalancerResource(loadbalancerContent.LoadBalancerId); e != nil {
					return e
				}
				return nil
			}
			return e
		}
	}
	if err = s.processLoadBalancerDelete(service, loadbalancerContent); err != nil { // kateway(bitliu[19]): managerOnly 的 service 还是 MCS 资源删除时的差异，在这个函数内
		return err
	}

	s.cache.delete(cacheKey)
	return nil
}

func (s *LbController) SyncPodEvent(key string) []error {
	errs := make([]error, 0)
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		errs = append(errs, err)
		return errs
	}
	pod, err := cluster2.Instance.PodLister().Pods(namespace).Get(name)
	if err != nil {
		if apierrors.IsNotFound(err) {
			return errs
		}
		errs = append(errs, err)
		return errs
	}
	glog.Infof("Controller: Update event, pod [%s/%s]\n", pod.Namespace, pod.Name)
	// 入队所有关联的service
	svcs, err := cluster2.Instance.ListServiceByPod(pod)
	if err != nil {
		errs = append(errs, err)
		return errs
	}
	s.syncPodEvent(svcs)

	if !ingcontrollerapp.GetIngressController().IsRunning() {
		return errs
	}
	ings, err := cluster.ListQCloudIngress(pod.Namespace, labels.Everything())
	if err != nil {
		errs = append(errs, err)
		return errs
	}
	s.syncIngressByPodEvent(ings, svcs)

	return errs
}

func (s *LbController) getPod(e *taskqueue.Element) (*v1.Pod, error) {
	var pod *v1.Pod

	namespace, name, err := cache.SplitMetaNamespaceKey(e.String())
	if err != nil {
		return nil, err
	}
	// 优先从lister缓存中获取pod，获取不到则尝试从element中解析
	if pod, err = cluster2.Instance.PodLister().Pods(namespace).Get(name); err != nil {
		// not found，可能是pod delete event，需要尝试从element中获取
		if pod, ok := e.Event.Core.(*v1.Pod); ok && pod != nil {
			return pod, nil
		}
		// elemenet中没获取到则返回error
		return nil, err
	}
	// 获取到pod，则返回
	return pod, nil
}

func (s *LbController) SyncPodDeleteEvent(e taskqueue.Element) []error {
	errs := make([]error, 0)

	pod, err := s.getPod(&e)
	if err != nil {
		if !apierrors.IsNotFound(err) {
			errs = append(errs, err)
		}
		// 获取不到pod，则直接返回
		return errs
	}

	// 入队所有关联的service
	svcs, err := cluster2.Instance.ServiceLister().Services(pod.Namespace).List(labels.Everything())
	if err != nil {
		glog.Errorf("Unexpected Error. API Service list services error for pod %s", utils.PodName(pod))
		errs = append(errs, err)
		return errs
	}
	for _, svc := range svcs {
		sw := service_wrapper.NewService(svc)
		ep, err := cluster2.Instance.EndpointsLister().Endpoints(pod.Namespace).Get(svc.Name)
		if ep == nil || err != nil {
			continue
		}
		if utils.IsServiceDirectAccess(sw) || utils.IsExternalTrafficPolicyLocal(sw) {
			// kateway: 直连类型service后端rs更新需要监听Pod删除（https://iwiki.woa.com/p/4009572395）
			if utils.IsPodSelectByService(sw, ep, pod) {
				glog.Infof("enqueue svc [%s/%s] for pod [%s/%s] deletion", svc.Namespace, svc.Name, pod.Namespace, pod.Name)
				cluster_service.QueueServiceInstance.ServiceQueue().Enqueue(taskqueue.Item{
					Data:   utils.ServiceName(sw),
					Weight: 1,
				})
			}
		}
	}

	return errs
}

func (s *LbController) syncIngressByPodEvent(ings []types.Ingress, svcs []*v1.Service) {
	var ingressKeys []string
	for _, svc := range svcs {
		for _, ing := range ings {
			if !utils.IsIngressSelectByService(ing, svc.GetName()) {
				continue
			}

			if utils.HasIngressDirectAccess(ing) { // Ingress明确组件声明是否直连
				if utils.IsIngressDirectAccess(ing) {
					ingressKeys = append(ingressKeys, utils.IngressName(ing))
				}
			} else if types.NewService(svc).IsDirectAccess() { // Ingress没有明确注解声明直连，依赖Service的注解判断
				ingressKeys = append(ingressKeys, utils.IngressName(ing))
			}
		}
	}
	for _, key := range lo.Uniq(ingressKeys) {
		ingressClusterService.QueueServiceInstance.IngressQueue().Enqueue(taskqueue.Item{
			Data:   key,
			Weight: 1,
		})
	}
}

func (s *LbController) syncPodEvent(svcs []*v1.Service) {
	for _, svc := range svcs {
		if types.NewService(svc).IsDirectAccess() {
			sw := service_wrapper.NewService(svc)
			cluster_service.QueueServiceInstance.ServiceQueue().Enqueue(taskqueue.Item{
				Data:   utils.ServiceName(sw),
				Weight: 1,
			})
		}
	}
}

type checkList struct {
	service   service_wrapper.ServiceWrapper
	ingresses []types.Ingress
}

// kateway: StatusQueue 处理器
// 给声明了`ReadinessGates`，`Condition`不存在的Pod，加上初始的 `Condition`
func (s *LbController) syncStatus(key string) []error {
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		return []error{err}
	}

	pod, err := cluster2.Instance.PodLister().Pods(namespace).Get(name)
	if err != nil {
		if apierrors.IsNotFound(err) { // Pod已销毁，移出队列不处理。当Pod重新建立时，会走正常流程进入健康检查。
			return nil
		}
		glog.Errorf("PodLister Get error for pod %s. error %v", key, err)
		return []error{err}
	}

	for _, condition := range pod.Status.Conditions { // 处理并发可能，已经被其他线程处理掉Status的添加
		if condition.Type == utils.DirectAccessConditionType {
			return nil
		}
	}

	pod = pod.DeepCopy()
	if _, err = updatePodReadinessGateStatus(pod); err != nil {
		glog.Errorf("Pods UpdateStatus error for pod %s for three times. error %v", utils.PodName(pod), err)
		return []error{fmt.Errorf("Pod Conditions Change Error.")}
	}

	glog.Infof("Pod Conditions Change Success. %s", utils.PodName(pod))
	return nil
}

// Attention
//
//	Operation cannot be fulfilled on pods "nginx-deployment-eni-8598755858-lcfm6": the object has been modified;
//	please apply your changes to the latest version and try again
func updatePodReadinessGateStatus(pod *v1.Pod) (*v1.Pod, error) {
	updateFunc := func(pod *v1.Pod) (*v1.Pod, bool) {
		for _, condition := range pod.Status.Conditions { // 处理并发可能，已经被其他线程处理掉Status的添加
			if condition.Type == utils.DirectAccessConditionType {
				return pod, false
			}
		}

		newPodCondition := v1.PodCondition{
			Type:               utils.DirectAccessConditionType,
			Status:             v1.ConditionFalse,
			LastProbeTime:      metav1.Time{},
			LastTransitionTime: metav1.Time{Time: time.Now()},
			Reason:             utils.DirectAccessConditionNotReady,
			Message:            "Waiting for pod to become healthy.",
		}
		pod.Status.Conditions = append(pod.Status.Conditions, newPodCondition)
		return pod, true
	}

	var err error
	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		var updated *v1.Pod
		var modify bool
		pod, modify = updateFunc(pod)
		if !modify {
			return pod, nil
		}

		updated, err = cluster2.Instance.KubeClient().CoreV1().Pods(pod.Namespace).UpdateStatus(context.Background(), pod, metav1.UpdateOptions{})
		if err == nil {
			return updated, nil
		}
		if !apierrors.IsConflict(err) {
			return nil, err
		}

		// 重试使用Client获取最新资源情况
		pod, err = cluster2.Instance.KubeClient().CoreV1().Pods(pod.Namespace).Get(context.Background(), pod.Name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
	}
	return nil, err
}

// kateway:  healthQueue 处理器
// 对后端绑定Pod的健康检查确认
// 对任意一个Pod，需要检查其被引用的所有Service，以及这些Service被引用的所有Ingress。
//
// Pod 1-->N Service 1--->N Ingress
func (s *LbController) syncHealth(key string) []error {
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		return []error{err}
	}

	pod, err := cluster2.Instance.PodLister().Pods(namespace).Get(name)
	if err != nil {
		if apierrors.IsNotFound(err) { // Pod已销毁，移出队列不处理。当Pod重新建立时，会走正常流程进入健康检查。
			return nil
		}
		glog.Errorf("PodLister Get error for pod %s. error %v", key, err)
		return []error{err}
	}

	hasGate := false
	for _, readinessGate := range pod.Spec.ReadinessGates {
		if readinessGate.ConditionType == utils.DirectAccessConditionType {
			hasGate = true
			break
		}
	}
	if !hasGate {
		return nil
	}

	needCheck := false
	for _, condition := range pod.Status.Conditions {
		if condition.Type == utils.DirectAccessConditionType && condition.Status != v1.ConditionTrue {
			needCheck = true
			break
		}
	}
	if !needCheck {
		return nil
	}

	// 对所有被依赖的Service服务进行后端的健康检查，全部通过则将Pod标记为通过
	services, err := cluster2.Instance.ListServiceByPod(pod)
	if err != nil {
		glog.Errorf("ServiceLister List error. error %v", err)
		return []error{err}
	}

	checkLists := make([]checkList, 0)
	for index, service := range services {
		serviceWrapper := service_wrapper.NewService(services[index])
		endpoint, err := cluster2.Instance.EndpointsLister().Endpoints(service.Namespace).Get(service.Name)
		if err != nil {
			if apierrors.IsNotFound(err) {
				// 没有Endpoint，忽略不处理。Without Selector类型的Service，没有指定后端。
				// 参考 https://kubernetes.io/docs/concepts/services-networking/service/#services-without-selectors
				continue
			}
			glog.Errorf("EndpointsLister List error for service %s. error %v", utils.ServiceName(serviceWrapper), err)
			return []error{err}
		}
		if utils.IsPodSelectByService(serviceWrapper, endpoint, pod) {
			ingresses, err := cluster.ListQCloudIngress(service.Namespace, labels.Everything())
			if err != nil {
				return []error{err}
			}

			ingressList := make([]types.Ingress, 0)
			for index, ingress := range ingresses {
				if !utils.IsIngressSelectByService(ingress, serviceWrapper.GetObjectMeta().GetName()) { // 该Ingress没有被Service选中，下一个
					continue
				}

				if utils.HasIngressDirectAccess(ingress) { // Ingress明确组件声明是否直连
					if utils.IsIngressDirectAccess(ingress) {
						ingressList = append(ingressList, ingresses[index])
					}
				} else if utils.IsServiceDirectAccess(serviceWrapper) { // Ingress没有明确注解声明直连，依赖Service的注解判断
					ingressList = append(ingressList, ingresses[index])
				}
			}
			checkLists = append(checkLists, checkList{
				service:   serviceWrapper,
				ingresses: ingressList,
			})
		}
	}

	flagSkip := true
	for _, checkList := range checkLists { // kateway 看看有没有跳过ReadinessGate的注解
		if (utils.IsLoadBalancerType(checkList.service) && utils.IsServiceDirectAccess(checkList.service)) || len(checkList.ingresses) != 0 {
			// 必须所有的 service 都表示跳过，pod 才能跳过
			if !utils.GetServiceReadinessGateSkip(checkList.service) { // kateway todo: 跳过的逻辑为什么不直接在 webhook 上做(不加自定义ReadinessGate)？
				flagSkip = false
				break
			}
		}
	}
	if flagSkip {
		if err := markPodReady(pod); err != nil {
			return []error{err}
		}
		return nil
	}

	// Pod自身的健康检查必须通过. 否则没有健康检查的必要
	if !types.NewPod(pod).IsContainersReadyConditionTrue() {
		// TODO misakazhou（增强）在这里可以做Pod的细节检查，对时间超长的错误Pod提供错误码。
		return []error{fmt.Errorf("pod(%s) containers not ready yet, skip backend health check", utils.PodName(pod))}
	}

	// Ingress 是固定在用户环境的，用户可以关闭甚至删除该组件。
	// 当用户关闭或删除该组件时，默认Ingress的规则甚至都会发生改变，Service不再能够期望管理TKE Ingress的直绑逻辑。
	// 		1. 当Ingress组件被删除或关闭时，认为用户不再使用TKE Ingress，需要略过Ingress的后端检查。
	ingressDisabled, err := tencent.IngressControllerDisabled(context.TODO(), cluster2.Instance.KubeClient())
	if err != nil {
		return []error{err}
	}

	for _, checkList := range checkLists {
		if err := tencent.BackendHealthCheckService(checkList.service, pod); err != nil {
			// 健康检查失败分两种情况：1）RS还没绑上；2）RS绑上了，但RS的Pod没有通过健康检查。
			// 对于第一种情况，需要再次调谐 pod 对应的 service
			// 这里处理 service 的 rs not found 异常
			if goerrors.Is(err, errcode.HealthCheckServiceRSNotFoundError) {
				cluster_service.QueueServiceInstance.ServiceQueue().Enqueue(taskqueue.Item{
					Data:   utils.ServiceName(checkList.service),
					Weight: 1,
				})
			}
			return []error{err}
		}

		if !ingressDisabled {
			for _, ingress := range checkList.ingresses {
				if err := tencent.BackendHealthCheckIngress(checkList.service, ingress, pod); err != nil {
					// 这里处理 ingress 的 rs not found 异常, 只在 ingress 已经 merge 的架构下支持
					if ingcontrollerapp.GetIngressController().IsRunning() && goerrors.Is(err, errcode.HealthCheckIngressRSNotFoundError) {
						ingressClusterService.QueueServiceInstance.IngressQueue().Enqueue(taskqueue.Item{
							Data:   utils.IngressName(ingress),
							Weight: 1,
						})
					}
					return []error{err}
				}
			}
		}
	}

	if err := markPodReady(pod); err != nil {
		return []error{err}
	}
	return nil
}

func markPodReady(pod *v1.Pod) error {
	pod = pod.DeepCopy()

	updateFunc := func(pod *v1.Pod) (*v1.Pod, bool) {
		for index := range pod.Status.Conditions {
			condition := pod.Status.Conditions[index]
			if condition.Type == utils.DirectAccessConditionType && (condition.Reason == utils.DirectAccessConditionNotReady || condition.Status != v1.ConditionTrue) {
				pod.Status.Conditions[index].Message = fmt.Sprintf("Marking condition \"%s\" to True.", utils.DirectAccessConditionType)
				pod.Status.Conditions[index].Status = v1.ConditionTrue
				pod.Status.Conditions[index].Type = utils.DirectAccessConditionType
				pod.Status.Conditions[index].Reason = utils.DirectAccessConditionReady
				pod.Status.Conditions[index].LastTransitionTime = metav1.Time{Time: time.Now()}
				return pod, true
			}
		}
		return pod, false
	}

	var err error
	// 三次重试修改状态，失败重新
	for i := 0; i < 3; i++ {
		var modifyed bool
		pod, modifyed = updateFunc(pod)
		if !modifyed {
			return nil
		}

		_, err = cluster2.Instance.KubeClient().CoreV1().Pods(pod.Namespace).UpdateStatus(context.Background(), pod, metav1.UpdateOptions{})
		if err == nil {
			event.Instance.EventPod(pod, v1.EventTypeNormal, "ReadinessGate Success")
			return nil
		}
		if !apierrors.IsConflict(err) {
			return err
		}

		// 重试使用Client获取最新资源情况
		pod, err = cluster2.Instance.KubeClient().CoreV1().Pods(pod.Namespace).Get(context.Background(), pod.Name, metav1.GetOptions{})
		if err != nil {
			return err
		}
	}
	return err
}

func (s *LbController) processLoadBalancerDelete(service service_wrapper.ServiceWrapper, loadBalancerContext *tencent.LoadBalancerContext) error {
	if err := tencent.EnsureLoadBalancerDeleted(service, loadBalancerContext); err != nil {
		cluster2.Instance.EventRecorder().Eventf(service.GetRuntimeObject(), v1.EventTypeWarning, "EnsuringServiceFailed", "Deleting Loadbalancer Error. will retry.")
		return err
	}
	return nil
}

func isSpecialService(namespace, name string) bool {
	for _, service := range systemServices {
		if service.Namespace == namespace && service.Name == name {
			return true
		}
	}
	return false
}

func GetVipAndDomain(service service_wrapper.ServiceWrapper, loadBalancer *clb.LoadBalancer) ([]*string, *string) {
	serviceIPStack := service.GetServiceIPStack()
	vips := make([]*string, 0)
	if (serviceIPStack == "ipv4" || serviceIPStack == "mixed") && loadBalancer != nil && len(loadBalancer.LoadBalancerVips) != 0 {
		vips = append(vips, loadBalancer.LoadBalancerVips...)
	}
	if (serviceIPStack == "ipv6" || serviceIPStack == "mixed") && loadBalancer != nil && loadBalancer.AddressIPv6 != nil {
		vips = append(vips, loadBalancer.AddressIPv6)
	}

	if loadBalancer.Forward != nil && *loadBalancer.Forward == 1 && loadBalancer.Domain != nil && *loadBalancer.Domain != "" {
		// 域名化改造账户，应用型资源有域名的场景
		// 1. 有域名返回域名，有VIP返回VIP，两个都有都返回
		return vips, common.StringPtr(*loadBalancer.Domain)
	}
	return vips, nil
}

func serviceCacheKey(serviceType service_wrapper.ServiceType, serviceKey string) string {
	return fmt.Sprintf("%s/%s", serviceType, serviceKey)
}
