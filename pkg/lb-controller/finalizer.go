package lbcontroller

import (
	"k8s.io/client-go/util/retry"

	"git.woa.com/kateway/pkg/domain/service/service_wrapper"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
)

func AddFinalizer(service service_wrapper.ServiceWrapper, finalizer string) (service_wrapper.ServiceWrapper, error) {
	if config.Global.DryRunService {
		return service, nil
	}

	// 如果是 DryRun 模式，则直接返回，不添加 finalizer，等非 DryRun 模式时再添加
	if service.IsDryRun() {
		return service, nil
	}

	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		var err, getError error
		if HasFinalizer(service, finalizer) {
			return nil
		}

		updated := service.DeepCopy()
		updated.GetObjectMeta().SetFinalizers(append(updated.GetObjectMeta().GetFinalizers(), finalizer))
		updated, err = updated.Update()
		if err != nil {
			if updated, getError = service.GetLatest(); getError == nil {
				service = updated
			}
			return err
		}
		service = updated
		return nil
	})
	return service, err
}

func RemoveFinalizer(service service_wrapper.ServiceWrapper, finalizer string) (service_wrapper.ServiceWrapper, error) {
	if config.Global.DryRunService {
		return service, nil
	}
	err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		var err, getError error
		if !HasFinalizer(service, finalizer) {
			return nil
		}

		updated := service.DeepCopy()
		finalizers := updated.GetObjectMeta().GetFinalizers()
		for i := 0; i < len(finalizers); i++ {
			if finalizers[i] == finalizer {
				finalizers = append(finalizers[:i], finalizers[i+1:]...)
				i--
			}
		}
		updated.GetObjectMeta().SetFinalizers(finalizers)
		updated, err = updated.Update()
		if err != nil {
			if updated, getError = service.GetLatest(); getError == nil {
				service = updated
			}
			return err
		}
		service = updated
		return nil
	})
	return service, err
}

func HasFinalizer(svc service_wrapper.ServiceWrapper, finalizer string) bool {
	f := svc.GetObjectMeta().GetFinalizers()
	for _, e := range f {
		if e == finalizer {
			return true
		}
	}
	return false
}
