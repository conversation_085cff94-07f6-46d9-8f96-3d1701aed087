package lbcontroller

import (
	"context"
	goerrors "errors"
	"fmt"
	"strings"
	"sync"

	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	ingcontrollerapp "git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app"
	"git.woa.com/kateway/tke-ingress-controller/pkg/controller"
	clb "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb/v20180317"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/client-go/tools/cache"
	glog "k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/pkg/plugin/tencent"
	"cloud.tencent.com/lb-controller/pkg/utils"
)

// ensureProtectPods
// 方案：https://iwiki.woa.com/p/4010284815
// 当前 pods 和当前 targets 的交集，是期望开启删除保护的集合
// 当前 pods 去掉当前 targets， 是期望去掉删除保护的集合
func (s *LbController) ensureProtectPods(syncContext *tencent.SyncContext) (err error) {
	ctx := context.TODO()
	svc := syncContext.Service
	ns := svc.GetObjectMeta().GetNamespace()
	svcName := svc.GetObjectMeta().GetName()
	if svc.ServiceType() != service_wrapper.CoreService {
		return nil
	}

	if !utils.IsServiceEnableGraceDeletion(svc) || !utils.IsServiceDirectAccess(svc) {
		return s.cleanProtectPods(ctx, svc)
	}

	cacheKey := serviceCacheKey(service_wrapper.CoreService, fmt.Sprintf("%s/%s", ns, svcName))
	cs, ok := s.cache.get(cacheKey)
	if !ok { // 不应该出现
		return fmt.Errorf("cache of service %s/%s is missing", ns, svcName)
	}

	loadBalancerContext := syncContext.LoadBalancerContext
	loadBalancerResource := loadBalancerContext.LoadBalancerResource
	loadBalancer, err := loadBalancerContext.GetLoadBalancer()
	if err != nil {
		return
	}

	backendType := utils.GetBackendType(loadBalancer)
	listenerIDs := make(sets.String)                                                         // 该 service 当前的所有监听器ID
	currentTargets := make(sets.String)                                                      // 属于当前 service 的所有 targets
	listeners := tencent.GetLoadBalancerResourceListenerByService(svc, loadBalancerResource) // 该 service 当前的监听器

	// 1. 先获取属于当前 service 的所有监听器
	for _, listener := range listeners {
		listenerIDs.Insert(utils.GetListenerKey(int64(listener.Port), listener.Protocol))
	}
	// 2. 获取当前 LB 的所有 targets
	listenerBackends, err := tencent.GetLoadBalancerTargets(svc, loadBalancerContext.Region, loadBalancerContext.LoadBalancerId)
	if err != nil {
		return
	}

	// 3. {currentTargets}: 通过上述两个数据集， 过滤出属于当前 service 的 targets
	for _, listenerBackend := range listenerBackends {
		listenerKey := utils.GetListenerKey(*listenerBackend.Port, *listenerBackend.Protocol) // 一个监听器的key：80_TCP
		if _, exist := listenerIDs[listenerKey]; !exist {
			continue
		}

		var targets []*clb.Backend
		if utils.IsL4Protocol(*listenerBackend.Protocol) {
			targets = listenerBackend.Targets
		} else if utils.IsL7Protocol(*listenerBackend.Protocol) {
			for _, rule := range listenerBackend.Rules {
				targets = append(targets, rule.Targets...)
			}
		}
		for _, target := range targets {
			if target.Type == nil {
				continue
			}
			if target.Weight != nil && utils.IsHealthyWeight(*target.Weight) && utils.IsENILikeType(*target.Type) {
				for _, privateIp := range target.PrivateIpAddresses {
					currentTargets.Insert(*privateIp)
				}
			}
		}
	}

	// 4. {pods}: 获取 service 所有的 pods
	pods, err := cluster.Instance.PodLister().Pods(ns).List(labels.SelectorFromSet(svc.Service().Spec.Selector))
	if err != nil {
		return
	}

	// 5. 计算删除保护
	deProtectPods, toProtectPods := calculateProtectPods(backendType, pods, currentTargets, &cs.protectPods)

	// 6. 去掉删除保护
	for podName, pod := range deProtectPods { // todo 考虑并发提速
		// pod 为 nil 表示这是在 cached protectPods 中，但不在当前 pods 中的数据。
		// 如果 pod 不存在了， 则直接删除 cache
		// 如果 pod 存在，则走删除保护逻辑
		if pod == nil {
			p, e := cluster.Instance.KubeClient().CoreV1().Pods(ns).Get(ctx, podName, metav1.GetOptions{})
			if e != nil {
				if errors.IsNotFound(e) {
					cs.protectPods.Delete(podName)
				} else {
					err = goerrors.Join(e)
				}
				continue
			}

			pod = p // 这种情况可能是用户调整了 service 和 pod 的选择关系， 导致之前开启了删除保护的pod，这次通过service selector没有查出来, 这种pod也应该去掉删除保护
		}

		e := removePodProtectServices(ctx, pod, svcName)
		if e == nil {
			cs.protectPods.Delete(podName)
		} else {
			err = goerrors.Join(e)
		}
	}

	// 6. 添加删除保护
	for _, pod := range toProtectPods { // todo 考虑并发提速
		e := addPodProtectService(ctx, pod, svcName)
		if e == nil {
			cs.protectPods.Store(pod.Name, pod.Status.PodIP) // value 暂时没使用
		} else {
			err = goerrors.Join(e)
		}
	}

	return
}

// calculateProtectPods
// 期望加上删除保护的pod toProtectPods = {pods} ∩ {targets}
// 期望去掉删除保护的pod deProtectPods = ({pods} - {targets}) + ({protectPods} - {pods})
// https://iwiki.woa.com/p/4010284815#%E9%80%9A%E8%BF%87-Service-%E8%B0%83%E8%B0%90%E8%BF%87%E7%A8%8B%EF%BC%9A
func calculateProtectPods(backendType string, pods []*v1.Pod, targets sets.String, cachedProtectPods *sync.Map) (map[string]*v1.Pod, map[string]*v1.Pod) {
	deProtectPods := make(map[string]*v1.Pod)
	toProtectPods := make(map[string]*v1.Pod)
	podSet := make(sets.String)

	// toProtectPods = {pods} ∩ {targets}
	// deProtectPods = {pods} - {targets}
	for _, pod := range pods {
		if podSet.Has(pod.Name) {
			continue
		}
		podSet.Insert(pod.Name)
		podIP, exist := utils.GetPodBackend(pod, backendType)
		if exist {
			if targets.Has(podIP) {
				toProtectPods[pod.Name] = pod
			} else {
				deProtectPods[pod.Name] = pod
			}
		} else {
			deProtectPods[pod.Name] = pod
		}
	}

	// {protectPods} - {pods}: protectPods 中已经不是属于该service 的pods，需要被去掉删除保护
	// 因此 deProtectPods = deProtectPods + ({protectPods} - {pods}) - {pods not found}
	// 另外不存在的 pods 也需要去掉删除保护
	cachedProtectPods.Range(func(key, _ any) bool {
		podName := key.(string)
		if !podSet.Has(podName) {
			deProtectPods[podName] = nil
		}
		return true
	})

	return deProtectPods, toProtectPods
}

// syncProtect 处理 deleting 且开了删除保护的pod, 去掉 pod 删除保护
// 方案：https://iwiki.woa.com/p/4010284815#%E9%80%9A%E8%BF%87-Terminating-Pod-%E8%B0%83%E8%B0%90%E8%BF%87%E7%A8%8B
// 主要场景:
// 1. 控制器重启时(缓存中无数据), 一些已经开启了删除保护但 'service选择关系已经变化' 的pod, 导致删除保护无法去掉
// 2. 用户手动添加了一个无效或者无关系的 service
func (s *LbController) syncProtect(key string) (err []error) {
	ns, name, e := cache.SplitMetaNamespaceKey(key)
	if e != nil {
		return []error{e}
	}

	pod, e := cluster.Instance.PodLister().Pods(ns).Get(name)
	if e != nil {
		if errors.IsNotFound(e) {
			return nil
		}
		return []error{e}
	}

	if e := s.handleServiceProtectLeak(pod); e != nil {
		err = append(err, e)
	}

	if e := s.handleIngressProtectLeak(pod); e != nil {
		err = append(err, e)
	}

	return err
}

func (s *LbController) handleServiceProtectLeak(pod *v1.Pod) error {
	if !types.IsPodEnableDeleteProtectionOfService(pod) {
		return nil
	}

	var servicesNeedRemove []string
	svcNames := strings.Split(pod.Annotations[types.PodProtectedByServices], ",")
	for _, svcName := range svcNames {
		cs, ok := s.cache.get(serviceCacheKey(service_wrapper.CoreService, fmt.Sprintf("%s/%s", pod.Namespace, svcName)))
		if ok {
			_, ok := cs.protectPods.Load(pod.Name)
			if ok {
				continue // 保留, 让 service 去调谐
			}
		}
		servicesNeedRemove = append(servicesNeedRemove, svcName)
	}

	if len(servicesNeedRemove) > 0 {
		glog.Infof("remove delete-protection services %s from pod %s, previous services: %s", servicesNeedRemove, utils.PodName(pod), svcNames)
		return removePodProtectServices(context.TODO(), pod, servicesNeedRemove...)
	}

	return nil
}

func (s *LbController) handleIngressProtectLeak(pod *v1.Pod) error {
	if !types.IsPodEnableDeleteProtectionOfIngress(pod) {
		return nil
	}

	// ingress controller 没有启动, 直接删除所有 ingress 删除保护
	if !ingcontrollerapp.GetIngressController().IsRunning() {
		return controller.RemoveAllPodProtectIngresses(context.TODO(), pod)
	}

	return ingcontrollerapp.GetIngressController().Controller.HandleIngressProtectLeak(pod)
}

// addPodProtectService 将 svcName 添加到 pod 的 annotation service.cloud.tencent.com/protected-by-services 中
func addPodProtectService(ctx context.Context, pod *v1.Pod, svcName string) error {
	currentNames := strings.Split(pod.Annotations[types.PodProtectedByServices], ",")
	svcMap := make(sets.String)
	var existed bool
	for _, name := range currentNames {
		name := strings.TrimSpace(name)
		if name != "" {
			if name == svcName {
				existed = true
			}
			svcMap.Insert(name)
		}
	}

	if types.IsPodEnableDeleteProtectionOfService(pod) && existed {
		return nil
	}

	if pod.Annotations == nil {
		pod.Annotations = make(map[string]string)
	}

	glog.Infof("add delete-protection service %s to pod %s", svcName, utils.PodName(pod))
	svcMap.Insert(svcName)
	pod.Annotations[types.PodEnableDeleteProtectionByService] = "true"
	pod.Annotations[types.PodProtectedByServices] = strings.Join(svcMap.List(), ",")

	_, err := cluster.Instance.KubeClient().CoreV1().Pods(pod.Namespace).Update(ctx, pod, metav1.UpdateOptions{})
	return err
}

// removePodProtectServices 将 svcNames 从 pod 的 annotation service.cloud.tencent.com/protected-by-services 中移除
// 然后会重新计算 pod 是否还需要开启删除保护
func removePodProtectServices(ctx context.Context, pod *v1.Pod, svcNames ...string) error {
	svcMap := make(sets.String) // todo rename
	currentNames := strings.Split(pod.Annotations[types.PodProtectedByServices], ",")
	for _, name := range currentNames {
		name := strings.TrimSpace(name)
		if name != "" {
			svcMap.Insert(name)
		}
	}

	var existedServices []string
	for _, svcName := range svcNames {
		if svcMap.Has(svcName) {
			existedServices = append(existedServices, svcName)
			svcMap.Delete(svcName)
		}
	}

	if len(existedServices) == 0 {
		return nil
	}

	glog.Infof("remove delete-protection services %s from pod %s", existedServices, utils.PodName(pod))
	if len(svcMap) == 0 {
		delete(pod.Annotations, types.PodEnableDeleteProtectionByService)
		delete(pod.Annotations, types.PodProtectedByServices)
	} else {
		pod.Annotations[types.PodProtectedByServices] = strings.Join(svcMap.List(), ",")
	}

	_, err := cluster.Instance.KubeClient().CoreV1().Pods(pod.Namespace).Update(ctx, pod, metav1.UpdateOptions{})
	return err
}

func (s *LbController) cleanProtectPods(ctx context.Context, svc service_wrapper.ServiceWrapper) (err error) {
	if svc.ServiceType() != service_wrapper.CoreService {
		return
	}
	ns := svc.GetObjectMeta().GetNamespace()
	svcName := svc.GetObjectMeta().GetName()
	cacheKey := serviceCacheKey(service_wrapper.CoreService, fmt.Sprintf("%s/%s", ns, svcName))

	cs, ok := s.cache.get(cacheKey)
	if !ok {
		return
	}

	cs.protectPods.Range(func(key, _ any) bool {
		podName := key.(string)
		pod, e := cluster.Instance.PodLister().Pods(ns).Get(podName)
		if e != nil {
			if errors.IsNotFound(e) {
				cs.protectPods.Delete(podName)
				return true
			}

			err = goerrors.Join(err, e)
			return true
		}

		if types.IsPodEnableDeleteProtectionOfService(pod) {
			e := removePodProtectServices(ctx, pod, svcName)
			if e != nil {
				err = goerrors.Join(err, e)
			} else {
				cs.protectPods.Delete(podName)
			}
		} else {
			cs.protectPods.Delete(podName)
		}
		return true
	})

	return
}
