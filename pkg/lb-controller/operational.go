package lbcontroller

import (
	"math"
	"strconv"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/domain/featuregates"
	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/service/cluster/nodestatus"
	"cloud.tencent.com/lb-controller/pkg/utils"
)

const (
	SYNC_OPERATIONAL_METRTC_FREQUENCY = 10 * time.Minute

	// 统计指标
	// 内外网数量
	INTERNAL_LOADBALANCER = "internal_loadbalancer" // 内网负载均衡数量
	INTERNET_LOADBALANCER = "internet_loadbalancer" // 外网负载均衡数量
	CLUSTERIP_SERVICE     = "clusterip_service"     // ClusterIP类型的Service资源数量
	NODEPORT_SERVICE      = "nodeport_service"      // ClusterIP类型的Service资源数量
	LOADBALANCER_SERVICE  = "loadbalancer_service"  // ClusterIP类型的Service资源数量
	TRAFFIC_LOCAL_SERVICE = "trafic_local_service"  // Traffic Local Service资源数量（Loadbalancer）

	// 产品监听器后端使用情况
	PROTOCOL_TCP     = "protocol_tcp"     // 使用TCP协议暴露服务
	PROTOCOL_UDP     = "protocol_udp"     // 使用UDP协议暴露服务
	PROTOCOL_QUIC    = "protocol_quic"    // 使用QUIC协议暴露服务
	PROTOCOL_TCP_SSL = "protocol_tcp_ssl" // 使用TCP_SSL协议暴露服务
	PROTOCOL_HTTP    = "protocol_http"    // 使用HTTP协议暴露服务
	PROTOCOL_HTTPS   = "protocol_https"   // 使用HTTP协议暴露服务
	PROTOCOL_TCP_UDP = "protocol_tcp_udp" // 同时使用TCP\UDP协议族，监听同一端口暴露服务
	BACKEND_TOTAL    = "backend_total"    // 后端绑定总数

	// 产品功能开关
	FLAG_GLOBAL_ROUTE_ACCESS = "flag_global_route_access" // 是否开启GlobalRoute直连
	FLAG_WORKERS_COUNT       = "flag_workers_count"       // 工作线程数量
	FLAG_REUSE               = "flag_reuse"               // 是否开启复用
	FLAG_MCS                 = "flag_mcs"                 // 是否开启MCS
	WEBHOOK_STATUS           = "webhook_status"           // 当前webhook状态。-1:组件关闭，0:不健康，1: 健康

	// 产品功能指标
	EXIST_LB                = "exist_lb"                // 使用已有负载均衡 service.kubernetes.io/tke-existed-lbid
	REUSE_LB                = "reuse_lb"                // 使用已有负载均衡 service.kubernetes.io/tke-existed-lbid，并且被复用的数量
	DIRECT_ACCESS           = "direct_access"           // 直连数量 service.cloud.tencent.com/direct-access
	BACKENDS_LIST_LABEL     = "backends_list_label"     // NodePort 自定义后端 service.kubernetes.io/qcloud-loadbalancer-backends-label
	GRACE_SHUTDOWN          = "grace_shutdown"          // 优雅停机 service.cloud.tencent.com/enable-grace-shutdown
	GRACE_SHUTDOWN_TKEX     = "grace_shutdown_tkex"     // 优雅踢除 service.cloud.tencent.com/enable-grace-shutdown-tkex
	TKE_SERVICE_CONFIG      = "tke_service_config"      // 外挂配置 service.cloud.tencent.com/tke-service-config
	TKE_SERVICE_CONFIG_AUTO = "tke_service_config_auto" // 自动外挂配置 service.cloud.tencent.com/tke-service-config-auto
	LOADBALANCE_NAT_IPV6    = "loadbalance_nat_ipv6"    // NAT-IPv6 service.kubernetes.io/loadbalance-nat-ipv6
	LOADBALANCE_IPV6        = "loadbalance_ipv6"        // IPv6 service.cloud.tencent.com/loadbalance-ipv6
	LOADBALANCE_IPV4        = "loadbalance_ipv4"        // IPv4 service.cloud.tencent.com/loadbalance-ipv4
	CROSS_TYPE_CCN          = "cross_type_ccn"          // 跨地域绑定方案 service.cloud.tencent.com/cross-type
	CROSS_TYPE_CROSSTARGET  = "cross_type_crosstarget"  // 跨地域绑定方案 service.cloud.tencent.com/cross-type
	CROSS_TYPE_PVGW         = "cross_type_pvgw"         // 跨地域绑定方案 service.cloud.tencent.com/cross-type
	CROSS_TYPE_PVGW_PRO     = "cross_type_pvgw_pro"     // 跨地域绑定方案 service.cloud.tencent.com/cross-type
	CROSS_TYPE_MANAGERONLY  = "cross_type_manageronly"  // 跨地域绑定方案 service.cloud.tencent.com/cross-type
	CROSS_VPC_ID            = "cross_vpc_id"            // 跨VPC绑定 service.cloud.tencent.com/cross-vpc-id
	HYBRID_TYPE_PVGW        = "hybrid_type_pvgw"        // 混合云绑定方案 service.cloud.tencent.com/hybrid-type
	HYBRID_TYPE_CCN         = "hybrid_type_ccn"         // 混合云绑定方案 service.cloud.tencent.com/hybrid-type
	MODIFICATION_PROTECTION = "modification_protection" // 配置保护 service.cloud.tencent.com/modification-protection
	PREVENT_LOOPBACK        = "prevent_loopback"        // 防止重定向循环 service.cloud.tencent.com/prevent-loopback
	LB_RS_WEIGHT            = "lb_rs_weight"            // 自定义后端权重 service.cloud.tencent.com/lb-rs-weight
	SPECIFY_PROTOCOL        = "specify_protocol"        // Service扩展协议 service.cloud.tencent.com/specify-protocol
	READINESS_GATE_SKIP     = "readiness_gate_skip"     // 跳过就绪检查 service.cloud.tencent.com/readiness-gate-skip
	SECURITY_GROUPS         = "security_groups"         // 安全组配置 service.cloud.tencent.com/security-groups
	PASS_TO_TARGET          = "pass_to_target"          // 默认放通配置 service.cloud.tencent.com/pass-to-target
)

func uploadMetrics() {
	go wait.Forever(func() {
		doUploadOperationalMetrics()
	}, SYNC_OPERATIONAL_METRTC_FREQUENCY)
}

func doUploadOperationalMetrics() {
	var services []service_wrapper.ServiceWrapper
	if config.Global.EnableMultiClusterMaster {
		svcs, err := cluster.Instance.MultiClusterServiceLister().List(labels.Everything())
		if err != nil {
			return
		}
		for _, svc := range svcs {
			services = append(services, service_wrapper.NewMultiClusterService(svc))
		}
	} else {
		svcs, err := cluster.Instance.ServiceLister().List(labels.Everything())
		if err != nil {
			return
		}
		for _, svc := range svcs {
			services = append(services, service_wrapper.NewService(svc))
		}
	}

	metricMap := make(map[string]int)
	metricMap[NODEPORT_SERVICE] = 0
	metricMap[CLUSTERIP_SERVICE] = 0
	metricMap[LOADBALANCER_SERVICE] = 0
	metricMap[TRAFFIC_LOCAL_SERVICE] = 0
	metricMap[INTERNAL_LOADBALANCER] = 0
	metricMap[INTERNET_LOADBALANCER] = 0

	metricMap[PROTOCOL_HTTP] = 0
	metricMap[PROTOCOL_HTTPS] = 0
	metricMap[PROTOCOL_TCP] = 0
	metricMap[PROTOCOL_UDP] = 0
	metricMap[PROTOCOL_QUIC] = 0
	metricMap[PROTOCOL_TCP_SSL] = 0
	metricMap[PROTOCOL_TCP_UDP] = 0 // 同一个端口暴露TCP、UDP双协议栈
	metricMap[BACKEND_TOTAL] = 0

	metricMap[FLAG_GLOBAL_ROUTE_ACCESS] = 0
	metricMap[FLAG_REUSE] = 0
	metricMap[FLAG_WORKERS_COUNT] = 0
	metricMap[FLAG_MCS] = 0

	metricMap[EXIST_LB] = 0
	metricMap[REUSE_LB] = 0
	metricMap[DIRECT_ACCESS] = 0
	metricMap[BACKENDS_LIST_LABEL] = 0
	metricMap[GRACE_SHUTDOWN] = 0
	metricMap[GRACE_SHUTDOWN_TKEX] = 0
	metricMap[TKE_SERVICE_CONFIG] = 0
	metricMap[TKE_SERVICE_CONFIG_AUTO] = 0
	metricMap[LOADBALANCE_NAT_IPV6] = 0
	metricMap[LOADBALANCE_IPV6] = 0
	metricMap[LOADBALANCE_IPV4] = 0
	metricMap[CROSS_TYPE_CCN] = 0
	metricMap[CROSS_TYPE_CROSSTARGET] = 0
	metricMap[CROSS_TYPE_PVGW] = 0
	metricMap[CROSS_TYPE_PVGW_PRO] = 0
	metricMap[CROSS_VPC_ID] = 0
	metricMap[HYBRID_TYPE_PVGW] = 0
	metricMap[MODIFICATION_PROTECTION] = 0
	metricMap[PREVENT_LOOPBACK] = 0
	metricMap[LB_RS_WEIGHT] = 0
	metricMap[SPECIFY_PROTOCOL] = 0
	metricMap[READINESS_GATE_SKIP] = 0
	metricMap[SECURITY_GROUPS] = 0
	metricMap[PASS_TO_TARGET] = 0

	if isGlobalRoute := cluster.Instance.Enabled(featuregates.GlobalRouteDirectAccess); isGlobalRoute {
		metricMap[FLAG_GLOBAL_ROUTE_ACCESS] = 1
	}
	if config.Global.EnableMultiClusterMaster {
		metricMap[FLAG_MCS] = 1
	}
	metricMap[FLAG_REUSE] = 1

	metricMap[WEBHOOK_STATUS] = config.Global.WebhookStatus
	metricMap[FLAG_WORKERS_COUNT] = config.Global.Workers

	IngressMap := make(map[string]service_wrapper.ServiceWrapper)
	for index, service := range services {
		IngressMap[utils.ServiceName(service)] = services[index]
	}

	defaultBackendCount := 0 // 默认情况下NodePort后端的数量，注意后端上线限制。
	nodes, err := getAvailableNode()
	if err != nil {
		defaultBackendCount = len(nodes)
		if defaultBackendCount > config.Global.BackendQuotaInUse.UserQuota {
			defaultBackendCount = config.Global.BackendQuotaInUse.UserQuota
		}
	}

	loadbalancerIPMap := make(map[string]bool)
	loadbalancerReuseCount := make(map[string]int)

	for _, service := range services {
		// 服务类型：ClusterIP、NodePort、LoadBalancer
		if service.Type() != v1.ServiceTypeLoadBalancer {
			if service.Type() == v1.ServiceTypeNodePort {
				metricMap[NODEPORT_SERVICE] = metricMap[NODEPORT_SERVICE] + 1
			} else if service.Type() == v1.ServiceTypeClusterIP {
				metricMap[CLUSTERIP_SERVICE] = metricMap[CLUSTERIP_SERVICE] + 1
			}
			continue
		}
		metricMap[LOADBALANCER_SERVICE] = metricMap[LOADBALANCER_SERVICE] + 1

		if !config.Global.EnableMultiClusterMaster {
			if service.ExternalTrafficPolicy() == v1.ServiceExternalTrafficPolicyTypeLocal {
				metricMap[TRAFFIC_LOCAL_SERVICE] = metricMap[TRAFFIC_LOCAL_SERVICE] + 1
			}

		}

		// 内外网数量
		// VIP使用的协议栈。IPv4、NAT IPv6、IPv6
		if service.GetStatusLoadBalancer().Ingress != nil && len(service.GetStatusLoadBalancer().Ingress) != 0 {
			vip := service.GetStatusLoadBalancer().Ingress[0].IP
			if _, exist := loadbalancerIPMap[vip]; !exist { // 复用场景去重
				loadbalancerIPMap[vip] = true

				if utils.CheckBackendType(vip, "ipv4") && isInnerIP(vip) {
					metricMap[INTERNAL_LOADBALANCER] = metricMap[INTERNAL_LOADBALANCER] + 1
				} else {
					metricMap[INTERNET_LOADBALANCER] = metricMap[INTERNET_LOADBALANCER] + 1
				}

				_, ipv4exist := service.GetObjectMeta().GetAnnotations()[LoadBalancerIPV4Annontation]
				_, ipv6exist := service.GetObjectMeta().GetAnnotations()[LoadBalancerIPV6Annontation]
				if !ipv6exist {
					_, ipv6exist = service.GetObjectMeta().GetAnnotations()[LoadBalancerNATIPV6Annontation]
				}
				for _, ingress := range service.GetStatusLoadBalancer().Ingress {
					if utils.CheckBackendType(ingress.IP, "ipv4") {
						ipv4exist = true
					} else if utils.CheckBackendType(ingress.IP, "ipv6") {
						ipv6exist = true
					}
				}
				if ipv4exist && ipv6exist {
					metricMap[LOADBALANCE_NAT_IPV6] = metricMap[LOADBALANCE_NAT_IPV6] + 1
				} else if ipv4exist {
					metricMap[LOADBALANCE_IPV4] = metricMap[LOADBALANCE_IPV4] + 1
				} else if ipv6exist {
					metricMap[LOADBALANCE_IPV6] = metricMap[LOADBALANCE_IPV6] + 1
				}
			}
		}

		// 监听器协议使用情况：（扩展协议）
		exceptListener := make(map[int32]int)
		if specifyProtocolMap, exist, _ := utils.GetSpecifyProtocol(service); exist {
			metricMap[SPECIFY_PROTOCOL] = metricMap[SPECIFY_PROTOCOL] + 1
			for port, specifyProtocol := range *specifyProtocolMap {
				exceptListener[port] = 0
				if specifyProtocol.Protocol != nil {
					exceptListener[port] = len(specifyProtocol.Protocol)
				}
				TcpFamily := false
				UdpFamily := false
				for _, protocol := range specifyProtocol.Protocol {
					if protocol == "TCP" {
						metricMap[PROTOCOL_TCP] = metricMap[PROTOCOL_TCP] + 1
						TcpFamily = true
					} else if protocol == "UDP" {
						metricMap[PROTOCOL_UDP] = metricMap[PROTOCOL_UDP] + 1
						UdpFamily = true
					} else if protocol == "QUIC" {
						metricMap[PROTOCOL_QUIC] = metricMap[PROTOCOL_QUIC] + 1
					} else if protocol == "TCP_SSL" {
						metricMap[PROTOCOL_TCP_SSL] = metricMap[PROTOCOL_TCP_SSL] + 1
						TcpFamily = true
					} else if protocol == "HTTP" {
						metricMap[PROTOCOL_HTTP] = metricMap[PROTOCOL_HTTP] + 1
						TcpFamily = true
					} else if protocol == "HTTPS" {
						metricMap[PROTOCOL_HTTPS] = metricMap[PROTOCOL_HTTPS] + 1
						TcpFamily = true
					}
				}
				if TcpFamily && UdpFamily {
					metricMap[PROTOCOL_TCP_UDP] = metricMap[PROTOCOL_TCP_UDP] + 1
				}
			}
		}
		for port, protocol := range service.Protocols() {
			if _, exist := exceptListener[port]; exist { // 扩展协议覆盖了端口定义
				continue
			}
			exceptListener[port] = 1
			if protocol == v1.ProtocolTCP {
				metricMap[PROTOCOL_TCP] = metricMap[PROTOCOL_TCP] + 1
			} else {
				metricMap[PROTOCOL_UDP] = metricMap[PROTOCOL_UDP] + 1
			}
		}

		if !config.Global.EnableMultiClusterMaster {
			// 统计后端数量
			totalListener := 0
			for _, count := range exceptListener {
				totalListener = totalListener + count
			}
			if isDirectAccess := utils.IsServiceDirectAccess(service); isDirectAccess { // 直连场景
				if !utils.IsNoSelectorService(service) {
					pods, err := cluster.Instance.PodLister().Pods(service.GetObjectMeta().GetNamespace()).List(labels.SelectorFromSet(service.Selector()))
					if err == nil {
						metricMap[BACKEND_TOTAL] = metricMap[BACKEND_TOTAL] + (totalListener * len(pods))
					}
				}
			} else { // NodePort接入场景
				if service.ExternalTrafficPolicy() == v1.ServiceExternalTrafficPolicyTypeLocal { // Local模式下只绑定工作负载所在节点
					if !utils.IsNoSelectorService(service) {
						pods, err := cluster.Instance.PodLister().Pods(service.GetObjectMeta().GetNamespace()).List(labels.SelectorFromSet(service.Selector()))
						if err == nil {
							nodes := make(map[string]bool)
							for _, pod := range pods {
								if pod.Status.HostIP != "" {
									nodes[pod.Status.HostIP] = true
								}
							}
							metricMap[BACKEND_TOTAL] = metricMap[BACKEND_TOTAL] + (totalListener * len(nodes))
						}
					}
				} else { // 正常情况下，可能会有后端节点选择的场景
					backendCount := defaultBackendCount
					if availList, exist := utils.GetBackendsListLabel(service); exist {
						if labelList, err := labels.Parse(availList); err == nil {
							backendCount = 0
							for _, node := range nodes {
								labelSet := labels.Set(node.ObjectMeta.Labels)
								if labelList.Matches(labelSet) {
									backendCount = backendCount + 1
								}
							}
						}
						lbId := service.GetObjectMeta().GetAnnotations()[LoadBalancerIDAnnontation]
						quotaInUse := config.Global.BackendQuotaInUse.GetQuotaInUse(lbId)
						if backendCount > quotaInUse {
							backendCount = quotaInUse
						}
					}
					metricMap[BACKEND_TOTAL] = metricMap[BACKEND_TOTAL] + (totalListener * backendCount)
				}
			}
		}

		// 其他功能统计
		if lbId, exist := utils.GetExistLB(service); exist {
			metricMap[EXIST_LB] = metricMap[EXIST_LB] + 1
			if count, exist := loadbalancerReuseCount[lbId]; !exist {
				loadbalancerReuseCount[lbId] = 1
			} else {
				if count == 1 { // 第一个复用多 +1
					metricMap[REUSE_LB] = metricMap[REUSE_LB] + 2
				} else {
					metricMap[REUSE_LB] = metricMap[REUSE_LB] + 1
				}
				loadbalancerReuseCount[lbId] = count + 1
			}
		}
		if _, exist := utils.GetBackendsListLabel(service); exist {
			metricMap[BACKENDS_LIST_LABEL] = metricMap[BACKENDS_LIST_LABEL] + 1
		}
		if isDirectAccess := utils.IsServiceDirectAccess(service); isDirectAccess {
			metricMap[DIRECT_ACCESS] = metricMap[DIRECT_ACCESS] + 1
		}
		if _, exist := service.GetObjectMeta().GetAnnotations()[utils.TkeServiceConfigAnnontation]; exist {
			metricMap[TKE_SERVICE_CONFIG] = metricMap[TKE_SERVICE_CONFIG] + 1
		}
		if exist, _ := utils.IsTkeServiceConfigAuto(service); exist {
			metricMap[TKE_SERVICE_CONFIG_AUTO] = metricMap[TKE_SERVICE_CONFIG_AUTO] + 1
		}
		if customizedWeight, _ := utils.IsServiceEnableCustomizedWeight(service); customizedWeight != nil {
			metricMap[LB_RS_WEIGHT] = metricMap[LB_RS_WEIGHT] + 1
		}

		if crossRegionID, exist := utils.GetCrossRegionId(service); exist && crossRegionID != config.Global.Region {
			if crossType, _ := utils.GetCrossType(service); crossType != "err" {
				switch crossType {
				case types.CrossType2_0:
					metricMap[CROSS_TYPE_CCN] = metricMap[CROSS_TYPE_CCN] + 1
				case types.CrossType1_0:
					metricMap[CROSS_TYPE_CROSSTARGET] = metricMap[CROSS_TYPE_CROSSTARGET] + 1
				case types.CrossType1_1:
					metricMap[CROSS_TYPE_PVGW] = metricMap[CROSS_TYPE_PVGW] + 1
				case types.CrossType1_2:
					metricMap[CROSS_TYPE_PVGW_PRO] = metricMap[CROSS_TYPE_PVGW_PRO] + 1
				case types.CrossType0_0:
					metricMap[CROSS_TYPE_MANAGERONLY] = metricMap[CROSS_TYPE_MANAGERONLY] + 1
				}
			}
		}
		if hybridType := utils.GetHybridType(service); hybridType != "err" {
			switch hybridType {
			case types.HybridTypePvgw:
				metricMap[HYBRID_TYPE_PVGW] = metricMap[HYBRID_TYPE_PVGW] + 1
			case types.HybridTypeCcn:
				metricMap[HYBRID_TYPE_CCN] = metricMap[HYBRID_TYPE_CCN] + 1
			}
		}
		if isModificationProtection, _ := utils.IsModificationProtection(service); isModificationProtection {
			metricMap[MODIFICATION_PROTECTION] = metricMap[MODIFICATION_PROTECTION] + 1
		}
		if isPreventLoopback, _, _ := utils.IsPreventLoopback(service); isPreventLoopback {
			metricMap[PREVENT_LOOPBACK] = metricMap[PREVENT_LOOPBACK] + 1
		}
		if readinessGateSkip := utils.GetServiceReadinessGateSkip(service); readinessGateSkip {
			metricMap[READINESS_GATE_SKIP] = metricMap[READINESS_GATE_SKIP] + 1
		}
		if _, exist := utils.HasServiceSecurityGroups(service); exist {
			metricMap[SECURITY_GROUPS] = metricMap[SECURITY_GROUPS] + 1
		}
		if passToTarget, _ := utils.IsServicePassToTarget(service); passToTarget {
			metricMap[PASS_TO_TARGET] = metricMap[PASS_TO_TARGET] + 1
		}
	}

	if config.Global.EnableMultiClusterMaster {
		metrics.Instance.SetOperationalCount(string(service_wrapper.MultiClusterService), metricMap)
	} else {
		metrics.Instance.SetOperationalCount(string(service_wrapper.CoreService), metricMap)
	}
}

func isInnerIP(ip string) bool {
	if isBelong(ip, "*******/8") {
		return true
	}
	if isBelong(ip, "********/8") {
		return true
	}
	if isBelong(ip, "10.0.0.0/8") {
		return true
	}
	if isBelong(ip, "**********/12") {
		return true
	}
	if isBelong(ip, "***********/16") {
		return true
	}
	return false
}

func isBelong(ip, cidr string) bool {
	cidrArr := strings.Split(cidr, `/`)
	if len(cidrArr) < 2 {
		return false
	}
	ipInt := ip2Int(ip)
	cidrInt := ip2Int(cidrArr[0])

	mask := string2Int(cidrArr[1])
	if ipInt >= cidrInt && ipInt <= (cidrInt+(int64)(math.Pow(2, float64(32-mask)))-1) {
		return true
	}
	return false
}

func ip2Int(ip string) int64 {
	if len(ip) == 0 {
		return 0
	}
	bits := strings.Split(ip, ".")
	if len(bits) < 4 {
		return 0
	}
	b0 := string2Int(bits[0])
	b1 := string2Int(bits[1])
	b2 := string2Int(bits[2])
	b3 := string2Int(bits[3])

	var sum int64
	sum += int64(b0) << 24
	sum += int64(b1) << 16
	sum += int64(b2) << 8
	sum += int64(b3)

	return sum
}

func string2Int(in string) (out int) {
	out, _ = strconv.Atoi(in)
	return
}

func getAvailableNode() ([]*v1.Node, error) {
	predicate := func(node *v1.Node) bool {
		if node.Labels != nil {
			if value, exist := node.Labels["node.kubernetes.io/exclude-from-external-load-balancers"]; exist && value == "true" {
				return false
			}
		}

		// If we have no info, don't accept
		if len(node.Status.Conditions) == 0 {
			return false
		}

		// If we have node, but no InternalIP
		flag := false
		for _, address := range node.Status.Addresses {
			if address.Type == "InternalIP" && address.Address != "" {
				flag = true
				break
			}
		}
		if flag == false {
			return false
		}

		if utils.IsEKSNode(node) {
			return false
		}

		if utils.IsIDCNode(node) {
			return false
		}

		if types.NewNode(node).HasWaitFinalizer() || types.NewNode(node).DeletionTimestamp != nil {
			return false
		}
		for _, cond := range node.Status.Conditions {
			// We consider the node for load balancing only when its NodeReady condition status
			// is ConditionTrue
			if cond.Type == v1.NodeReady && cond.Status != v1.ConditionTrue {
				// 容忍节点抖动
				if _, exist := nodestatus.NodeStatusMapInstance.Get(node.Name); !exist {
					klog.V(4).Infof("Ignoring node %v with %v condition status %v", node.Name, cond.Type, cond.Status)
					return false
				}
			}
		}
		return true
	}

	nodes, err := cluster.Instance.NodeLister().List(labels.Everything())
	if err != nil {
		return nil, err
	}

	var filtered []*v1.Node
	for i := range nodes {
		if predicate(nodes[i]) {
			filtered = append(filtered, nodes[i])
		}
	}

	return filtered, nil
}
