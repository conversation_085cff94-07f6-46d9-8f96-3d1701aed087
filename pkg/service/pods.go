package service

import (
	"strings"

	v1 "k8s.io/api/core/v1"

	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
)

// Pod 注解存在时，优先以Pod注解为准。
// Pod 注解不存在时，以集群默认配置为准。
func IsTKERouteENI(pod *v1.Pod) bool {
	// qyzhaoxun/multus-cni 参考文档：https://github.com/qyzhaoxun/multus-cni/blob/master/doc/default-delegates.md
	if network, exist := pod.Annotations[types.PodNetworkAnnotation]; exist { // kateway: GR 开启vpc-cni后，工作负载需要设置此annotation 以开启共享cni
		return strings.Contains(network, types.DelegatesTKERouteENI)
	}

	return cluster.Instance.IsTKEENISupported()
}

// kateway todo: 命名不太合适? 包括 TKERouteENI 和 TKEDirectENI
func IsTKEDirectENI(pod *v1.Pod) bool {
	// qyzhaoxun/multus-cni 参考文档：https://github.com/qyzhaoxun/multus-cni/blob/master/doc/default-delegates.md
	if network, exist := pod.Annotations[types.PodNetworkAnnotation]; exist { // kateway: GR 开启vpc-cni后，工作负载需要设置此annotation 以开启共享cni
		return strings.Contains(network, types.DelegatesTKEDirectENI)
	}

	return cluster.Instance.IsTKEENISupported()
}

func IsTKESubENI(pod *v1.Pod) bool {
	if network, exist := pod.Annotations[types.PodNetworkAnnotation]; exist {
		return strings.Contains(network, types.DelegatesTKESubENI)
	}

	return cluster.Instance.IsTKEENISupported()
}

func IsHostNetwork(pod *v1.Pod) bool {
	return pod.Spec.HostNetwork
}

func isTkePODInEKS(pod *v1.Pod) bool {
	value, has := pod.Annotations["tke.cloud.tencent.com/pod-type"] // kateway todo：这个anno 的场景?
	return has && strings.ToLower(value) == "eklet"
}

// kateway: 可以支持直绑的pod： 共享eni，独立eni，eks pod, hostnetwork
func IsPodDirectBackend(pod *v1.Pod) bool {
	if env.IsInEKSCluster() {
		return true // For EKS, all Pods IsPodDirectBackend
	}

	if config.Global.ClusterSupportDirect {
		return true
	}

	return IsTKEDirectENI(pod) || IsTKERouteENI(pod) || IsTKESubENI(pod) || isTkePODInEKS(pod) || IsHostNetwork(pod)
}
