package cluster

import (
	"fmt"

	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/tencentapi/dryrun"
	"git.woa.com/kateway/pkg/domain/types"
	go_version "github.com/hashicorp/go-version"
	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/service/cluster/nodestatus"
	"cloud.tencent.com/lb-controller/pkg/utils"
	"cloud.tencent.com/lb-controller/pkg/utils/cluster_service"
)

type taskTraversalClusterService struct {
	cluster.Interface
}

func newTaskTraversalClusterService(basic cluster.Interface) (*taskTraversalClusterService, error) {
	nodestatus.InitNodeStatusMap()

	return &taskTraversalClusterService{
		Interface: basic,
	}, nil
}

func (cs *taskTraversalClusterService) Run(stopCh <-chan struct{}) {
	cluster_service.QueueServiceInstance.ServiceQueue().Run()

	// 启动Service Informer
	cs.Interface.Run(stopCh)

	// 检查Service资源
	services, err := cs.ServiceLister().List(labels.Everything())
	if err != nil {
		klog.Infof("service lister error. %v", err)
		return
	}

	skipCount := 0
	mockCount := 0
	canSkipClusterIP := canSkipClusterIPService()
	for _, service := range services {
		serviceWrapper := service_wrapper.NewService(service)
		if utils.IsSkip(serviceWrapper) {
			klog.Infof("mock enqueue: service %s is marked to skip", utils.ServiceName(serviceWrapper))
			continue
		}

		if canSkipClusterIP && !isServiceNeedMock(service) {
			klog.Infof("skip mock service %s", utils.ServiceName(serviceWrapper))
			skipCount++
			continue
		}

		weight := 1
		mockCount++
		if service.Spec.Type == v1.ServiceTypeLoadBalancer {
			weight = 10
		}

		klog.Infof("enqueue service %s", utils.ServiceName(serviceWrapper))
		cluster_service.QueueServiceInstance.ServiceQueue().Enqueue(taskqueue.Item{
			Data:   utils.ServiceName(serviceWrapper),
			Weight: weight,
		})
	}
	cluster_service.QueueServiceInstance.ServiceQueue().Shutdown()

	if config.Global.EnableMultiClusterMaster {
		cluster_service.QueueServiceInstance.MultiClusterServiceQueue().Run()
		// 检查MultiClusterService资源
		multiClusterServices, err := cs.MultiClusterServiceLister().List(labels.Everything())
		if err != nil {
			klog.Infof("multi cluster service lister error. %v", err)
			return
		}
		for _, multiClusterService := range multiClusterServices {
			serviceWrapper := service_wrapper.NewMultiClusterService(multiClusterService)
			klog.Infof("enqueue multiClusterService %s", utils.ServiceName(serviceWrapper))
			cluster_service.QueueServiceInstance.MultiClusterServiceQueue().Enqueue(taskqueue.Item{
				Data:   utils.ServiceName(serviceWrapper),
				Weight: 1,
			})
		}
		cluster_service.QueueServiceInstance.MultiClusterServiceQueue().Shutdown()
	}

	fmt.Printf("Mock Summary: finish mock with %d error, mock %d services, skip %d services\n", len(dryrun.Records), mockCount, skipCount)
}

// canSkipClusterIPService 判断是否跳过 ClusterIP Service 的 Mock
// 对于 v2.2.1 之前的版本，不允许跳过mock
// 对于 v2.2.1 之后的版本，可以根据某些特征来跳过mock
func canSkipClusterIPService() bool {
	if config.Global.DryRunOldVersion == "" {
		return false
	}

	baseVersion, _ := go_version.NewSemver("v2.2.1") // v2.2.1 支持了 Finalizer，ClientToken 等特性
	oldVersion, err := go_version.NewSemver(config.Global.DryRunOldVersion)
	if err != nil {
		return false
	}

	return oldVersion.GreaterThanOrEqual(baseVersion)
}

func isServiceNeedMock(svc *v1.Service) bool {
	if svc.Spec.Type == v1.ServiceTypeLoadBalancer {
		return true
	}
	if _, ok := svc.Annotations[types.ServiceAnnotationLoadBalancerID]; ok {
		return true
	}
	if _, ok := svc.Annotations[types.ServiceAnnotationExistingLoadBalancerID]; ok {
		return true
	}
	if _, ok := svc.Annotations[types.ServiceAnnotationClientToken]; ok { // v2.2.1 增加
		return true
	}
	if lo.Contains(svc.Finalizers, types.ServiceFinalizer) { // v2.2.1 增加
		return true
	}
	if svc.Status.LoadBalancer.Ingress != nil {
		return true
	}

	return false
}
