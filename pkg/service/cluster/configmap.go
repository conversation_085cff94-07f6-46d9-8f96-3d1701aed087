package cluster

import (
	"context"
	"reflect"
	"strconv"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kateway/pkg/app/version"
	"git.woa.com/kateway/pkg/domain/controllercm"
	"git.woa.com/kateway/pkg/domain/env"
	"git.woa.com/kateway/pkg/domain/featuregates"
	clustersvc "git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
	ingcontrollerapp "git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/utils/cluster_service"
)

// BuildConfigMapSyncFunc
// 构建 用户集群内配置文件 configmap 更新后的处理程序
func BuildConfigMapSyncFunc(ingController *ingcontrollerapp.IngressController) func(context.Context, kubernetes.Interface) error {
	handlers := []func(context.Context, *corev1.ConfigMap) error{
		// 将系统参数和启动参数同步到 configmap
		func(_ context.Context, cm *corev1.ConfigMap) error {
			reuseExistLBFlag := controllercm.ValueTrue
			gitVersion := version.Get().GitVersion

			if cm.Data == nil {
				cm.Data = map[string]string{}
			}
			if cm.Data[controllercm.KeyLoadbalancerReuseEnabled] != reuseExistLBFlag {
				cm.Data[controllercm.KeyLoadbalancerReuseEnabled] = reuseExistLBFlag
			}
			if cm.Data[controllercm.KeyLoadbalancerCRDEnabled] != controllercm.ValueTrue {
				cm.Data[controllercm.KeyLoadbalancerCRDEnabled] = controllercm.ValueTrue
			}
			if oldVersionStr := cm.Data[controllercm.KeyVersion]; oldVersionStr != gitVersion {
				cm.Data[controllercm.KeyVersion] = gitVersion
			}
			if _, exists := cm.Data[controllercm.KeyEnableIngressController]; !exists && config.Global.EnableIngressControllerDefault {
				cm.Data[controllercm.KeyEnableIngressController] = controllercm.ValueTrue
			}
			if _, exists := cm.Data[controllercm.KeyEnableNodeGracefulDeletion]; !exists { // 是不是可以去掉？
				cm.Data[controllercm.KeyEnableNodeGracefulDeletion] = strconv.FormatBool(clustersvc.Instance.Enabled(featuregates.NodeGracefulDeletion))
			}
			return nil
		},
		func(_ context.Context, cm *corev1.ConfigMap) error {
			// eks环境特殊处理原因见：https://iwiki.woa.com/p/4012085107#eks%E9%9B%86%E7%BE%A4%E7%9A%84%E8%BF%81%E7%A7%BB
			if env.IsInEKSCluster() {
				if cm.Annotations == nil {
					return nil
				}
				_, awarenessEnabled := cm.Annotations[types.AnnotationEnableIngressControllerAwareness]
				if !awarenessEnabled {
					return nil
				}
			}
			v := cm.Data[controllercm.KeyEnableIngressController]
			if v == controllercm.ValueTrue {
				ingController.Enable()
				clustersvc.Instance.Enable(featuregates.MergedIngressController)
			} else if v == controllercm.ValueFalse {
				ingController.Disable()
				clustersvc.Instance.Disable(featuregates.MergedIngressController)
			}
			return nil
		},
		func(ctx context.Context, cm *corev1.ConfigMap) error {
			expectEnabled, err := strconv.ParseBool(cm.Data[controllercm.KeyEnableNodeGracefulDeletion])
			if err == nil {
				if cluster_service.QueueServiceInstance != nil {
					err = cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().EnsureQueueState(expectEnabled)
					if err != nil {
						return err
					}
				}
				if expectEnabled {
					clustersvc.Instance.Enable(featuregates.NodeGracefulDeletion)
				} else {
					clustersvc.Instance.Disable(featuregates.NodeGracefulDeletion)
				}
			}
			return nil
		},
	}
	return buildSyncFunc(handlers...)
}

func buildSyncFunc(handlers ...func(context.Context, *corev1.ConfigMap) error) func(context.Context, kubernetes.Interface) error {
	return func(ctx context.Context, cli kubernetes.Interface) error {
		cm, err := cli.CoreV1().ConfigMaps(metav1.NamespaceSystem).Get(ctx, controllercm.ConfigMapService, metav1.GetOptions{})
		if err != nil {
			if !apierrors.IsNotFound(err) {
				klog.Errorf("Get config map %s/%s error: %v", metav1.NamespaceSystem, controllercm.ConfigMapService, err)
				return err
			}
			cm, err = cli.CoreV1().ConfigMaps(metav1.NamespaceSystem).Create(ctx, &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: metav1.NamespaceSystem,
					Name:      controllercm.ConfigMapService,
				},
				Data: initConfigMapData(),
			}, metav1.CreateOptions{})
			if err != nil {
				if !apierrors.IsAlreadyExists(err) {
					klog.Errorf("Create config map %s/%s error: %v", metav1.NamespaceSystem, controllercm.ConfigMapService, err)
					return err
				}
			}
			klog.Infof("Create config map %s/%s successfully", metav1.NamespaceSystem, controllercm.ConfigMapService)
		}

		origin := cm.DeepCopy()
		for _, h := range handlers {
			h(ctx, cm)
		}
		if !reflect.DeepEqual(origin.Data, cm.Data) {
			if _, err := cli.CoreV1().ConfigMaps(metav1.NamespaceSystem).Update(ctx, cm, metav1.UpdateOptions{}); err != nil {
				klog.Errorf("Update config map %s/%s error: %v", metav1.NamespaceSystem, controllercm.ConfigMapService, err)
				return err
			}
			klog.Infof("Update config map %s/%s successfully", metav1.NamespaceSystem, controllercm.ConfigMapService)
		}
		return nil
	}
}

func initConfigMapData() map[string]string {
	data := map[string]string{
		controllercm.KeyVersion:                       version.Get().GitVersion,
		controllercm.KeyLoadbalancerReuseEnabled:      controllercm.ValueTrue,
		controllercm.KeyLoadbalancerCRDEnabled:        controllercm.ValueTrue,
		controllercm.KeyEnableGlobalRouteDirectAccess: controllercm.ValueTrue,
		controllercm.KeyEnableNodeGracefulDeletion:    controllercm.ValueFalse,
	}
	if config.Global.EnableIngressControllerDefault {
		data[controllercm.KeyEnableIngressController] = controllercm.ValueTrue
	}
	return data
}
