package cluster

import (
	"context"

	"git.woa.com/kateway/pkg/domain/services/cluster"
)

type ServiceType string

const (
	Basic         ServiceType = "Basic"         // 提供 ClusterClient 基础服务
	TaskQueue     ServiceType = "TaskQueue"     // 提供 ClusterClient 基础服务，并对相关资源进行持续监听，持续进行Ingress的对账
	TaskTraversal ServiceType = "TaskTraversal" // 提供 ClusterClient 基础服务，对所有Ingress进行一次对账后退出
)

func Init(ctx context.Context, basic cluster.Interface, serviceType ServiceType) error {
	var (
		err                    error
		clusterServiceInstance = basic
	)

	switch serviceType {
	case TaskQueue:
		clusterServiceInstance, err = newTaskQueueClusterService(clusterServiceInstance)
		if err != nil {
			return err
		}
	case TaskTraversal:
		clusterServiceInstance, err = newTaskTraversalClusterService(clusterServiceInstance)
		if err != nil {
			return err
		}
	}
	cluster.Instance = clusterServiceInstance

	return nil
}
