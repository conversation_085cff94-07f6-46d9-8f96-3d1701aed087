package cluster

import (
	"time"

	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/tencentapi"
	"k8s.io/client-go/util/workqueue"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/utils"
	"cloud.tencent.com/lb-controller/pkg/utils/cluster_service"
)

type DefaultQueueService struct {
	serviceQueue             *taskqueue.TaskQueue
	multiClusterServiceQueue *taskqueue.TaskQueue

	healthQueue                 *taskqueue.TaskQueue                  // kateway lb直绑的后端 pod: 进行clb健康检查
	statusQueue                 *taskqueue.TaskQueue                  // kateway 给声明了`ReadinessGates`，`Condition`不存在的Pod，加上初始的 `Condition`, todo 感觉可以合并到其他控制器中
	protectQueue                *taskqueue.TaskQueue                  // 处理开启删除保护且处于 deleting 的 pod
	nodeGracefulDeletionManager *services.NodeGracefulDeletionManager // 处理绑定或者解绑成功的node的finalizer
	endpointsQueue              *taskqueue.TaskQueue                  // 处理endpoints事件的调协
	podQueue                    *taskqueue.GenericTaskQueue[string]   // 处理pod事件的调协
	podDeleteQueue              *taskqueue.GenericTaskQueue[taskqueue.Element]
}

func NewNodeGracefulDeletionManagerParam() services.NodeGracefulDeletionManagerParam {
	return services.NodeGracefulDeletionManagerParam{
		MockRun:                    config.Global.DryRunService,
		VpcID:                      config.Global.VPCID,
		RegionName:                 config.Global.Region,
		KubeClient:                 cluster.Instance.KubeClient(),
		CLBRsProtection:            utils.CLBRsProtection,
		DescribeLBListenersAPI:     tencentapi.Instance.DescribeLBListeners,
		LoadBalancerResourceLister: cluster.Instance.LoadBalancerResourceLister(),
		LoadBalancerResourceClient: *cluster.Instance.LoadBalancerResourceClient(),
		NodeLister:                 cluster.Instance.NodeLister(),
		ManagerType:                services.SvcManager,
	}
}

// kateway(bitliu[10]): 初始化 controllers 的队列、以及事件处理函数
// 包含：health、status、service、mcs 队列
func InitQueueServiceInstance(sync func(string) []error, syncMultiClusterService func(string) []error, syncHealth, syncStatus, syncProtect, syncEndpoints, syncPodEvent func(string) []error, syncPodDeleteEvent func(taskqueue.Element) []error, workers int) {
	if cluster_service.QueueServiceInstance == nil {
		cluster_service.QueueServiceInstanceLock.Lock()
		defer cluster_service.QueueServiceInstanceLock.Unlock()

		if cluster_service.QueueServiceInstance == nil {
			cluster_service.QueueServiceInstance = newDefaultQueueService(sync, syncMultiClusterService, syncHealth, syncStatus, syncProtect, syncEndpoints, syncPodEvent, syncPodDeleteEvent, workers)
		}
	}
}

func newDefaultQueueService(sync func(string) []error, syncMultiClusterService func(string) []error, syncHealth, syncStatus, syncProtect, syncEndpoints, syncPodEvent func(string) []error, syncPodDeleteEvent func(taskqueue.Element) []error, workers int) DefaultQueueService {
	healthQueue := taskqueue.NewTaskQueueRateLimit(syncHealth, workers, "health", 0*time.Second, 0, taskqueue.NewMaxOfRateLimiter(
		taskqueue.NewItemExponentialFailureRateLimiter(1*time.Millisecond, 10*time.Minute),
		taskqueue.NewMinRateLimiter(3*time.Second),
	))
	statusQueue := taskqueue.NewTaskQueueRateLimit(syncStatus, workers, "status", 0*time.Second, 0, taskqueue.NewMaxOfRateLimiter(
		taskqueue.NewItemExponentialFailureRateLimiter(1*time.Millisecond, 10*time.Minute),
		taskqueue.NewMinRateLimiter(1*time.Second),
	))
	serviceQueue := taskqueue.NewTaskQueueRateLimit(sync, workers, "service", 0*time.Second, 0, workqueue.NewMaxOfRateLimiter(
		workqueue.NewItemExponentialFailureRateLimiter(1*time.Millisecond, 15*time.Minute),
		taskqueue.NewMinRateLimiter(3*time.Second),
	))
	protectQueue := taskqueue.NewTaskQueueRateLimit(syncProtect, workers, "protect", 0*time.Second, 0, workqueue.NewMaxOfRateLimiter( // todo 参数
		workqueue.NewItemExponentialFailureRateLimiter(1*time.Millisecond, 15*time.Minute),
		taskqueue.NewMinRateLimiter(3*time.Second),
	))
	nodeGracefulDeletionManager := services.NewNodeGracefulDeletionManager(NewNodeGracefulDeletionManagerParam())
	multiClusterServiceQueue := taskqueue.NewTaskQueueRateLimit(syncMultiClusterService, workers, "multiClusterService", 0*time.Second, 0, workqueue.NewMaxOfRateLimiter(
		workqueue.NewItemExponentialFailureRateLimiter(1*time.Millisecond, 15*time.Minute),
		taskqueue.NewMinRateLimiter(3*time.Second),
	))

	endpointsQueue := taskqueue.NewTaskQueueRateLimit(syncEndpoints, workers, "endpoints", 0*time.Second, 0, workqueue.NewMaxOfRateLimiter( // todo 参数
		workqueue.NewItemExponentialFailureRateLimiter(1*time.Millisecond, 15*time.Minute),
		taskqueue.NewMinRateLimiter(3*time.Second),
	))

	podQueue := taskqueue.NewGenericTaskQueueRateLimitWithOps[string](
		&taskqueue.TaskQueueOps[string]{
			SyncFn:        syncPodEvent,
			Workers:       workers,
			Name:          "pod",
			RequeueDelay:  0 * time.Second,
			RequeueWeight: 0,
			RateLimiter: workqueue.NewMaxOfRateLimiter(
				workqueue.NewItemExponentialFailureRateLimiter(1*time.Millisecond, 15*time.Minute),
				taskqueue.NewMinRateLimiter(3*time.Second),
			),
		},
	)

	podDeleteQueue := taskqueue.NewGenericTaskQueueRateLimitWithOps[taskqueue.Element](
		&taskqueue.TaskQueueOps[taskqueue.Element]{
			SyncFn:        syncPodDeleteEvent,
			Workers:       workers,
			Name:          "deletedpod",
			RequeueDelay:  0 * time.Second,
			RequeueWeight: 0,
			RateLimiter: workqueue.NewMaxOfRateLimiter(
				workqueue.NewItemExponentialFailureRateLimiter(1*time.Millisecond, 15*time.Minute),
				taskqueue.NewMinRateLimiter(3*time.Second),
			),
		},
	)

	return DefaultQueueService{
		serviceQueue:                serviceQueue,
		multiClusterServiceQueue:    multiClusterServiceQueue,
		healthQueue:                 healthQueue,
		statusQueue:                 statusQueue,
		protectQueue:                protectQueue,
		nodeGracefulDeletionManager: nodeGracefulDeletionManager,
		endpointsQueue:              endpointsQueue,
		podQueue:                    podQueue,
		podDeleteQueue:              podDeleteQueue,
	}
}

func (service DefaultQueueService) ServiceQueue() *taskqueue.TaskQueue {
	return service.serviceQueue
}

func (service DefaultQueueService) MultiClusterServiceQueue() *taskqueue.TaskQueue {
	return service.multiClusterServiceQueue
}

func (service DefaultQueueService) HealthQueue() *taskqueue.TaskQueue {
	return service.healthQueue
}

func (service DefaultQueueService) StatusQueue() *taskqueue.TaskQueue {
	return service.statusQueue
}

func (service DefaultQueueService) ProtectQueue() *taskqueue.TaskQueue {
	return service.protectQueue
}

func (service DefaultQueueService) NodeGracefulDeletionManager() *services.NodeGracefulDeletionManager {
	return service.nodeGracefulDeletionManager
}

func (service DefaultQueueService) EndpointsQueue() *taskqueue.TaskQueue {
	return service.endpointsQueue
}

func (service DefaultQueueService) PodQueue() *taskqueue.GenericTaskQueue[string] {
	return service.podQueue
}

func (service DefaultQueueService) PodDeleteQueue() *taskqueue.GenericTaskQueue[taskqueue.Element] {
	return service.podDeleteQueue
}
