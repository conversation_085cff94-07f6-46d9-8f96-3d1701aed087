package nodestatus

import (
	"sync"
	"time"

	v1 "k8s.io/api/core/v1"
)

var (
	NodeStatusMapInstanceLock                = new(sync.Mutex)
	NodeStatusMapInstance     *NodeStatusMap = nil
)

type NodeStatus struct {
	Node *v1.Node
	Time time.Time
}

type NodeStatusMap struct {
	rw         *sync.RWMutex
	NodeStatus map[string]*NodeStatus
}

func InitNodeStatusMap() {
	if NodeStatusMapInstance == nil {
		NodeStatusMapInstanceLock.Lock()
		defer NodeStatusMapInstanceLock.Unlock()

		if NodeStatusMapInstance == nil {
			NodeStatusMapInstance = &NodeStatusMap{
				rw:         new(sync.RWMutex),
				NodeStatus: map[string]*NodeStatus{},
			}
		}
	}
}

func (n *NodeStatusMap) Add(name string, value *NodeStatus) {
	n.rw.Lock()
	defer n.rw.Unlock()

	n.NodeStatus[name] = value
}

func (n *NodeStatusMap) Get(name string) (*NodeStatus, bool) {
	n.rw.RLock()
	defer n.rw.RUnlock()

	status, exist := n.NodeStatus[name]
	return status, exist
}

func (n *NodeStatusMap) Delete(name string) {
	n.rw.Lock()
	defer n.rw.Unlock()

	delete(n.NodeStatus, name)
}

func (n *NodeStatusMap) Each(callback func(string, *NodeStatus)) {
	n.rw.RLock()
	defer n.rw.RUnlock()

	for k, v := range n.NodeStatus {
		callback(k, v)
	}
}
