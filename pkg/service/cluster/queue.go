package cluster

import (
	"context"
	"reflect"
	"strings"
	"time"

	v1alpha12 "git.woa.com/kateway/multi-cluster-service-api/apis/multiclusterservice/v1alpha1"
	"git.woa.com/kateway/pkg/domain/metrics"
	"git.woa.com/kateway/pkg/domain/service/service_wrapper"
	"git.woa.com/kateway/pkg/domain/services"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/taskqueue"
	"git.woa.com/kateway/pkg/domain/types"
	ingcontrollerapp "git.woa.com/kateway/tke-ingress-controller/cmd/ingress-controller/app"
	ingressClusterService "git.woa.com/kateway/tke-ingress-controller/pkg/utils/cluster_service"
	"git.woa.com/kateway/tke-service-config/pkg/apis/tkeservice/v1alpha1"
	"github.com/samber/lo"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/wait"
	clientsetscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog"
	glog "k8s.io/klog/v2"

	"cloud.tencent.com/lb-controller/cmd/service-controller/app/config"
	"cloud.tencent.com/lb-controller/pkg/service/cluster/nodestatus"
	"cloud.tencent.com/lb-controller/pkg/utils"
	"cloud.tencent.com/lb-controller/pkg/utils/cluster_service"
)

const (
	SyncPeriod        = 30 * time.Second
	serviceSyncPeriod = 5 * time.Minute
)

type taskQueueClusterService struct {
	cluster.Interface

	silent bool
}

// kateway(bitliu[5]): 初始化 basic cluster service
func newTaskQueueClusterService(basic cluster.Interface) (*taskQueueClusterService, error) {
	nodestatus.InitNodeStatusMap()

	silent := services.GetSilentStart()
	return &taskQueueClusterService{
		Interface: basic,
		silent:    silent,
	}, nil
}

// kateway(bitliu[11]): 启动队列、监听事件、启动 informer
func (service *taskQueueClusterService) Run(stopCh <-chan struct{}) {
	// 启动Worker
	// Worker启动前，静默解除
	service.silent = false
	cluster_service.QueueServiceInstance.ServiceQueue().Run()
	cluster_service.QueueServiceInstance.MultiClusterServiceQueue().Run()
	cluster_service.QueueServiceInstance.ProtectQueue().Run()
	cluster_service.QueueServiceInstance.HealthQueue().Run()
	cluster_service.QueueServiceInstance.StatusQueue().Run()
	cluster_service.QueueServiceInstance.EndpointsQueue().Run()
	cluster_service.QueueServiceInstance.PodQueue().Run()
	cluster_service.QueueServiceInstance.PodDeleteQueue().Run()

	// 注册Kubernetes的资源监听
	service.registryEventHandler()

	// 节点状态变更的延时处理
	service.startWatchNode()

	// 启动Informer
	service.Interface.Run(stopCh)

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().Run(ctx)

	<-stopCh
}

func (service *taskQueueClusterService) Stop() {
	cluster_service.QueueServiceInstance.ServiceQueue().Shutdown()
	cluster_service.QueueServiceInstance.MultiClusterServiceQueue().Shutdown()
	cluster_service.QueueServiceInstance.HealthQueue().Shutdown()
	cluster_service.QueueServiceInstance.StatusQueue().Shutdown()
	cluster_service.QueueServiceInstance.ProtectQueue().Shutdown()
	cluster_service.QueueServiceInstance.NodeGracefulDeletionManager().Shutdown()
	cluster_service.QueueServiceInstance.EndpointsQueue().Shutdown()
	cluster_service.QueueServiceInstance.PodQueue().Shutdown()
	cluster_service.QueueServiceInstance.PodDeleteQueue().Shutdown()
}

// kateway: registerEventHandler
func (service *taskQueueClusterService) registryEventHandler() {
	service.podEventHandler()      // Pod watch handlers
	service.serviceEventHandler()  // Service watch handlers
	service.nodeEventHandler()     // node watch handlers
	service.endpointEventHandler() // endpoint watcher handlers
	service.secretEventHandler()   // secret watcher handlers

	_ = v1alpha1.AddToScheme(clientsetscheme.Scheme)
	service.tkeserviceConfigEventHandler()      // secret watcher handlers
	if config.Global.EnableMultiClusterMaster { // kateway(bitliu[12]): 开启 mcs 才监听 mcs 事件
		_ = v1alpha12.AddToScheme(clientsetscheme.Scheme)
		service.MultiClusterServiceEventHandler() // MultiClusterService watcher handlers
	}
}

// pod 事件处理器
func (service *taskQueueClusterService) podEventHandler() {
	service.PodInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				pod, isPod := cur.(*v1.Pod)
				if !isPod {
					glog.Infof("Controller: Add event contained non-Pod object: %v", cur)
					return
				}
				glog.Infof("Controller: Add event, pod [%s/%s]", pod.Namespace, pod.Name)
				service.syncPods(pod)
			},
			UpdateFunc: func(old, cur interface{}) {
				oldPod, ok1 := old.(*v1.Pod)
				curPod, ok2 := cur.(*v1.Pod)
				if !ok1 || !ok2 {
					glog.Infof("Controller: Add event contained non-Pod old object: %v, cur object: %v", old, cur)
					return
				}
				if oldPod.ResourceVersion == curPod.ResourceVersion {
					// resync 会触发更新事件，但 ResourceVersion 不变
					return
				}
				service.syncPods(curPod)
				if !podUpdateNeedSync(types.NewPod(oldPod), types.NewPod(curPod)) {
					return
				}
				service.enqueuePod(curPod)
			},
			DeleteFunc: func(obj interface{}) {
				pod, isPod := obj.(*v1.Pod)
				if !isPod {
					deletedState, ok := obj.(cache.DeletedFinalStateUnknown)
					if !ok {
						glog.Errorf("Received unexpected object: %v", obj)
						return
					}
					pod, ok = deletedState.Obj.(*v1.Pod)
					if !ok {
						glog.Errorf("DeletedFinalStateUnknown contained non-Pod object: %v", deletedState.Obj)
						return
					}
				}
				service.enqueueDeletedPod(pod)
			},
		},
		serviceSyncPeriod,
	)
}

func (service *taskQueueClusterService) secretEventHandler() {
	service.SecretInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				secret, isSecret := cur.(*v1.Secret)
				if !isSecret {
					glog.Infof("Controller: Add event contained non-Secret object: %v", cur)
					return
				}
				glog.Infof("Controller: Add event, secret [%s/%s]\n", secret.Namespace, secret.Name)
				service.syncSecrets(secret)
			},
			UpdateFunc: func(old, cur interface{}) {
				oldPod, ok1 := old.(*v1.Secret)
				curPod, ok2 := cur.(*v1.Secret)
				if !ok1 || !ok2 {
					glog.Infof("Controller: Add event contained non-Secret old object: %v, cur object: %v", old, cur)
					return
				}
				if !reflect.DeepEqual(oldPod, curPod) {
					glog.Infof("Controller: Update event, secret [%s/%s]\n", curPod.Namespace, curPod.Name)
					service.syncSecrets(curPod)
				}
			},
		},
		serviceSyncPeriod,
	)
}

/**
 * 1. 将处于 deleting 且开了删除保护的pod，加入 protect 队列
 * 2. 只处理声明了`ReadinessGates`的Pod
 * 3. 给声明了`ReadinessGates`，`Condition`不为`True`的Pod，加入健康检查队列
 * 4. 给声明了`ReadinessGates`，`Condition`不存在的Pod，加上`Condition`
 */
func (service *taskQueueClusterService) syncPods(pod *v1.Pod) {
	if pod == nil {
		return
	}

	if pod.DeletionTimestamp != nil && (types.IsPodEnableDeleteProtectionOfService(pod) || types.IsPodEnableDeleteProtectionOfIngress(pod)) {
		cluster_service.QueueServiceInstance.ProtectQueue().Enqueue(taskqueue.Item{
			Data:   utils.PodName(pod),
			Weight: 1,
		})
	}

	hasGate := false
	for _, readinessGate := range pod.Spec.ReadinessGates {
		if readinessGate.ConditionType == types.DirectAccessConditionType { // kateway: 通过 webhook 增加的
			hasGate = true
			break
		}
	}
	if !hasGate {
		return
	}

	for index, _ := range pod.Status.Conditions {
		condition := pod.Status.Conditions[index]
		if condition.Type == types.DirectAccessConditionType {
			if condition.Reason == types.DirectAccessConditionNotReady || condition.Status != v1.ConditionTrue { // Already Has Gate Conditions
				if !utils.IsPodUnableBind(pod) {
					cluster_service.QueueServiceInstance.HealthQueue().Enqueue(taskqueue.Item{ // kateway HealthQueue 入队: 2. 声明了`ReadinessGates` 且 `Condition`不为`True`的Pod，加入健康检查队列
						Data:   utils.PodName(pod),
						Weight: 1,
					})
				}
			}
			return
		}
	}

	cluster_service.QueueServiceInstance.StatusQueue().Enqueue(taskqueue.Item{ // kateway StatusQueue 入队: 3. 给声明了`ReadinessGates`，`Condition`不存在的Pod，加上`Condition`
		Data:   utils.PodName(pod),
		Weight: 1,
	})
}

// syncServiceAndIngressByPod
// pod 属性和状态变化需要重新对 service 和 ingress 对账，场景：
// 1. pod 上的权重属性变化
// 2. pod 状态变为 ContainersReady 需要触发 rs 注册
func (service *taskQueueClusterService) syncServiceAndIngressByPod(oldPod *types.Pod, pod *types.Pod) {
	if !podUpdateNeedSync(oldPod, pod) {
		return
	}

	services, err := cluster.Instance.ListServiceByPod(pod.Pod)
	if err != nil {
		glog.Errorf("Failed to list service by pod: %v", err)
		return
	}

	for _, svc := range services {
		if types.NewService(svc).IsDirectAccess() {
			service.enqueueService(svc)
		}
	}

	// 如果 ingress controller 没有在合并版本中运行，则不触发 pod 关联的 ingress 重新对账
	if !ingcontrollerapp.GetIngressController().IsRunning() {
		return
	}

	ingresses, err := ListQCloudIngress(pod.Namespace, labels.Everything())
	if err != nil {
		glog.Errorf("Failed to list ingresses by pod: %v", err)
		return
	}

	var ingressKeys []string
	for _, svc := range services {
		for _, ing := range ingresses {
			if !utils.IsIngressSelectByService(ing, svc.GetName()) {
				continue
			}

			if utils.HasIngressDirectAccess(ing) { // Ingress明确组件声明是否直连
				if utils.IsIngressDirectAccess(ing) {
					ingressKeys = append(ingressKeys, utils.IngressName(ing))
				}
			} else if types.NewService(svc).IsDirectAccess() { // Ingress没有明确注解声明直连，依赖Service的注解判断
				ingressKeys = append(ingressKeys, utils.IngressName(ing))
			}
		}
	}

	for _, key := range lo.Uniq(ingressKeys) {
		ingressClusterService.QueueServiceInstance.IngressQueue().Enqueue(taskqueue.Item{
			Data:   key,
			Weight: 1,
		})
	}
}

func podUpdateNeedSync(oldPod *types.Pod, pod *types.Pod) bool {
	if !reflect.DeepEqual(oldPod.Attributes(), pod.Attributes()) {
		return true
	}

	// pod 延迟注册：
	// not ready -> ready: pod 初始化时，service/ingress 可能缺乏触发事件，因此需要由 pod 事件触发
	// ready -> not ready: 这种情况可以由原来的 endpoints 事件触发
	if !oldPod.IsContainersReadyConditionTrue() && pod.IsContainersReadyConditionTrue() && // not ready -> ready
		!pod.IsDirectAccessReadyConditionTrue() { // 如果 gate 已经通过，说明 rs 已经挂载到 clb了，不需要再次对账
		return true
	}

	return false
}

func (service *taskQueueClusterService) serviceEventHandler() {
	service.ServiceInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				svc, isService := cur.(*v1.Service)
				if !isService {
					glog.Infof("Controller: Add event contained non-Service object: %v", cur)
					return
				}
				serviceWrapper := service_wrapper.NewService(svc)
				if !utils.IsLoadBalancerType(serviceWrapper) { // 避免启动时大量ClusterIP、NodePort类型的Service挤占队列
					service.enqueueServiceWithWeight(svc, taskqueue.EnqueueLowPriorityWeight)
				} else {
					service.enqueueService(svc)
				}
			},
			UpdateFunc: func(old, cur interface{}) {
				oldSvc, ok1 := old.(*v1.Service)
				curSvc, ok2 := cur.(*v1.Service)
				if !ok1 || !ok2 {
					glog.Infof("Controller: Add event contained non-Service old object: %v, cur object: %v", old, cur)
					return
				}
				if service.needsUpdate(oldSvc, curSvc) {
					service.enqueueService(curSvc)
				}
			},
			DeleteFunc: func(obj interface{}) {
				svc, isService := obj.(*v1.Service)
				if !isService {
					deletedState, ok := obj.(cache.DeletedFinalStateUnknown)
					if !ok {
						glog.Errorf("Received unexpected object: %v", obj)
						return
					}
					svc, ok = deletedState.Obj.(*v1.Service)
					if !ok {
						glog.Errorf("DeletedFinalStateUnknown contained non-Service object: %v", deletedState.Obj)
						return
					}
				}
				serviceWrapper := service_wrapper.NewService(svc)
				if !utils.IsLoadBalancerType(serviceWrapper) { // 删除Service的任务降级处理
					service.enqueueServiceWithWeight(svc, taskqueue.EnqueueLowPriorityWeight)
				} else {
					service.enqueueService(svc)
				}
			},
		},
		serviceSyncPeriod,
	)
}

func (service *taskQueueClusterService) needsUpdate(oldSvc *v1.Service, newSvc *v1.Service) bool {
	oldService := service_wrapper.NewService(oldSvc)
	newService := service_wrapper.NewService(newSvc)
	if !utils.IsLoadBalancerType(oldService) && !utils.IsLoadBalancerType(newService) {
		return false
	}
	if utils.IsLoadBalancerType(oldService) != utils.IsLoadBalancerType(newService) {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "Type", oldSvc.Spec.Type, newSvc.Spec.Type)
		return true
	}

	if utils.IsLoadBalancerType(newService) && !reflect.DeepEqual(oldSvc.Spec.LoadBalancerSourceRanges, newSvc.Spec.LoadBalancerSourceRanges) {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "LoadBalancerSourceRanges", oldSvc.Spec.LoadBalancerSourceRanges, newSvc.Spec.LoadBalancerSourceRanges)
		return true
	}

	if !portSlicesEqualForLB(oldSvc.Spec.Ports, newSvc.Spec.Ports) {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "Ports", oldSvc.Spec.LoadBalancerIP, newSvc.Spec.LoadBalancerIP)
		return true
	}
	if oldSvc.Spec.SessionAffinity != newSvc.Spec.SessionAffinity {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "SessionAffinity", oldSvc.Spec.SessionAffinity, newSvc.Spec.SessionAffinity)
		return true
	}
	// 考虑字段变更和新增字段的情况
	if oldSvc.Spec.SessionAffinity == v1.ServiceAffinityClientIP &&
		newSvc.Spec.SessionAffinityConfig != nil && newSvc.Spec.SessionAffinityConfig.ClientIP != nil && newSvc.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds != nil &&
		(oldSvc.Spec.SessionAffinityConfig == nil || oldSvc.Spec.SessionAffinityConfig.ClientIP == nil || oldSvc.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds == nil || *oldSvc.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds != *newSvc.Spec.SessionAffinityConfig.ClientIP.TimeoutSeconds) {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "SessionAffinityConfig", *oldSvc.Spec.SessionAffinityConfig, *newSvc.Spec.SessionAffinityConfig)
		return true
	}
	if oldSvc.Spec.LoadBalancerIP != newSvc.Spec.LoadBalancerIP {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "LoadbalancerIP", oldSvc.Spec.LoadBalancerIP, newSvc.Spec.LoadBalancerIP)
		return true
	}
	if len(oldSvc.Spec.ExternalIPs) != len(newSvc.Spec.ExternalIPs) {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "ExternalIP", utils.JsonWrapper(oldSvc.Spec.ExternalIPs), utils.JsonWrapper(newSvc.Spec.ExternalIPs))
		return true
	}
	for i := range oldSvc.Spec.ExternalIPs {
		if oldSvc.Spec.ExternalIPs[i] != newSvc.Spec.ExternalIPs[i] {
			glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "ExternalIP", utils.JsonWrapper(oldSvc.Spec.ExternalIPs), utils.JsonWrapper(newSvc.Spec.ExternalIPs))
			return true
		}
	}
	if !reflect.DeepEqual(oldService.FilterReadOnly(), newService.FilterReadOnly()) {
		glog.Infof("Service(%s) %s Changed.", utils.ServiceName(newService), "Annotations")
		return true
	}
	if oldSvc.UID != newSvc.UID {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "UID", oldSvc.UID, newSvc.UID)
		return true
	}
	if oldSvc.Spec.ExternalTrafficPolicy != newSvc.Spec.ExternalTrafficPolicy {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "ExternalTrafficPolicy", oldSvc.Spec.ExternalTrafficPolicy, newSvc.Spec.ExternalTrafficPolicy)
		return true
	}
	if oldSvc.Spec.HealthCheckNodePort != newSvc.Spec.HealthCheckNodePort {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "HealthCheckNodePort", oldSvc.Spec.HealthCheckNodePort, newSvc.Spec.HealthCheckNodePort)
		return true
	}
	if oldService.GetServiceIPStack() != newService.GetServiceIPStack() {
		glog.Infof("Service(%s) %s Changed. %v -> %v", utils.ServiceName(newService), "IPFamilies", utils.JsonWrapper(oldSvc.Spec.IPFamilies), utils.JsonWrapper(oldSvc.Spec.IPFamilies))
		return true
	}
	if !oldSvc.DeletionTimestamp.Equal(newSvc.DeletionTimestamp) {
		glog.Infof("Service(%s) %s Changed.", utils.ServiceName(newService), "DeletionTimestamp")
		return true
	}
	if !reflect.DeepEqual(oldSvc.Spec.Selector, newSvc.Spec.Selector) {
		glog.Infof("Service(%s) %s Changed.", utils.ServiceName(newService), "Selector")
		return true
	}
	return false
}

func portSlicesEqualForLB(x, y []v1.ServicePort) bool {
	if len(x) != len(y) {
		return false
	}

	for i := range x {
		if !portEqualForLB(&x[i], &y[i]) {
			return false
		}
	}
	return true
}

func portEqualForLB(x, y *v1.ServicePort) bool {
	if x.Name != y.Name || x.Protocol != y.Protocol || x.Port != y.Port || x.TargetPort != y.TargetPort || x.NodePort != y.NodePort {
		return false
	}
	return true
}

func (service *taskQueueClusterService) nodeEventHandler() {
	service.NodeInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				node, isNode := cur.(*v1.Node)
				if !isNode {
					glog.Infof("Controller: Add event contained non-Node object: %v", cur)
					return
				}
				glog.Infof("controller: Add event, nodes [%s/%s]\n", node.Namespace, node.Name)
				service.syncNodes(node)
			},
			UpdateFunc: func(old, new interface{}) {
				node1, ok1 := old.(*v1.Node)
				node2, ok2 := new.(*v1.Node)
				if !ok1 || !ok2 {
					glog.Infof("Controller: Add event contained non-Node old object: %v, cur object: %v", old, new)
					return
				}
				if types.NewNode(node2).IsEKS() {
					return
				}
				if nodeChanged(node1, node2) {
					glog.Infof("controller: Update, nodes [%s/%s]\n", node1.Namespace, node1.Name)
					service.syncNodes(node2)
				}
			},
			DeleteFunc: func(cur interface{}) {
				node, isNode := cur.(*v1.Node)
				if !isNode {
					deletedState, ok := cur.(cache.DeletedFinalStateUnknown)
					if !ok {
						glog.Errorf("Received unexpected object: %v", cur)
						return
					}
					node, ok = deletedState.Obj.(*v1.Node)
					if !ok {
						glog.Errorf("DeletedFinalStateUnknown contained non-Node object: %v", deletedState.Obj)
						return
					}
				}
				glog.Infof("controller: Delete event, nodes [%s/%s]\n", node.Namespace, node.Name)
				service.syncNodes(node)
			},
		},
		SyncPeriod,
	)
}

// kateway: node 事件处理器
// 对于node 的变化事件，如果node 是not ready 则延迟1分钟再处理，如果是ready 则马上处理。
// 这里的差别原因，是因为担心 node not ready 这种是临时抖动,不想太快去摘除lb/监听器。
func (service *taskQueueClusterService) syncNodes(node *v1.Node) {
	ready := nodeReady(node)
	if ready { // Node Ready情况
		if _, exist := nodestatus.NodeStatusMapInstance.Get(node.Name); exist {
			nodestatus.NodeStatusMapInstance.Delete(node.Name)
		}
		service.enqueueServiceForNode(node) // kateway: 有新的 ready node 加入，直接全量同步 svc
	} else {
		// 节点主动标记剔除，不对节点状态进行等待处理。
		if node.Labels != nil {
			if value, exist := node.Labels["node.kubernetes.io/exclude-from-external-load-balancers"]; exist && value == "true" {
				service.enqueueServiceForNode(node)
				return
			}
		}
		// kateway: NodeStatusMapInstance里边只会有NotReady的Node
		if _, exist := nodestatus.NodeStatusMapInstance.Get(node.Name); !exist {
			nodestatus.NodeStatusMapInstance.Add(node.Name, &nodestatus.NodeStatus{ // kateway NodeStatusMapInstance 唯一 add, not ready 做延迟同步
				Node: node,
				Time: time.Now(),
			})
		}
	}
}

// kateway 节点状态变更的延时处理
func (service *taskQueueClusterService) startWatchNode() {
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()
		for { // kateway: 在 node 状态变化的一分钟后, 执行 enqueueIngressForNode
			select {
			case <-ticker.C:
				nodeStatusList := make([]*nodestatus.NodeStatus, 0)
				nodestatus.NodeStatusMapInstance.Each(func(name string, nodeStatus *nodestatus.NodeStatus) {
					if time.Now().After(nodeStatus.Time.Add(1 * time.Minute)) {
						nodeStatusList = append(nodeStatusList, nodeStatus)
					}
				})

				for _, nodeStatus := range nodeStatusList { // kateway todo: 这里似乎没必要重复执行? enqueueIngressForNode 其实和node 无关(同步所有svc)
					nodestatus.NodeStatusMapInstance.Delete(nodeStatus.Node.Name)
					service.enqueueServiceForNode(nodeStatus.Node)
				}
			case <-wait.NeverStop:
				return
			}
		}
	}()
}

// kateway: node 变化， 重新同步所有 LoadBalancer 类型的 svc
func (service *taskQueueClusterService) enqueueServiceForNode(node *v1.Node) {
	glog.Errorf("Enqueue Service For Node (%v).", node.Name)
	metrics.Instance.IncNodeChangeCount(node.Name)
	services, err := service.ServiceLister().Services(v1.NamespaceAll).List(labels.Everything())
	if err != nil {
		glog.Errorf("error node %v. enqueueServiceForNode %v.", node.Name, err.Error())
		return
	}
	for _, svc := range services {
		serviceWrapper := service_wrapper.NewService(svc)
		if utils.IsLoadBalancerType(serviceWrapper) { // kateway todo:  如果是直通的 svc，是不是不需要重新处理?
			service.enqueueServiceWithWeight(svc, taskqueue.EnqueueNodeSyncWeight) // kateway todo: 注意这个 2, 提取常量
		}
	}
}

// endpoint 处理器
func (service *taskQueueClusterService) endpointEventHandler() {
	service.EndpointsInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				endpoint, isEndpoint := cur.(*v1.Endpoints)
				if !isEndpoint {
					glog.Infof("Controller: Add event contained non-Endpoints object: %v", cur)
					return
				}
				glog.V(5).Infof("controller: Add event, endpoints [%s/%s]\n", endpoint.Namespace, endpoint.Name)
				service.enqueueEndpoints(endpoint)
			},
			UpdateFunc: func(obja, objb interface{}) {
				old, ok1 := obja.(*v1.Endpoints)
				cur, ok2 := objb.(*v1.Endpoints)
				if !ok1 || !ok2 {
					glog.Infof("Controller: Add event contained non-Endpoints old object: %v, cur object: %v", old, cur)
					return
				}
				if !reflect.DeepEqual(old.Subsets, cur.Subsets) {
					glog.V(5).Infof("controller: Update event, endpoints [%s/%s]\n", old.Namespace, old.Name)
					service.enqueueEndpoints(cur)
				}
			},
			DeleteFunc: func(cur interface{}) {
				endpoint, isEndpoint := cur.(*v1.Endpoints)
				if !isEndpoint {
					deletedState, ok := cur.(cache.DeletedFinalStateUnknown)
					if !ok {
						glog.Errorf("Received unexpected object: %v", cur)
						return
					}
					endpoint, ok = deletedState.Obj.(*v1.Endpoints)
					if !ok {
						glog.Errorf("DeletedFinalStateUnknown contained non-Endpoints object: %v", deletedState.Obj)
						return
					}
				}
				service.enqueueEndpoints(endpoint)
			},
		},
		serviceSyncPeriod,
	)
}

func DetermineEnqueueweight(obj metav1.Object) int {
	if obj.GetNamespace() == metav1.NamespaceDefault && strings.HasPrefix(obj.GetName(), "kubernetes") {
		return taskqueue.EnqueueHighPriorityWeight
	}
	return taskqueue.EnqueueDefaultWeight
}

func (service *taskQueueClusterService) tkeserviceConfigEventHandler() {
	service.TkeServiceConfigInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				tkeServiceConfig, isTkeServiceConfig := cur.(*v1alpha1.TkeServiceConfig)
				if !isTkeServiceConfig {
					glog.Infof("Controller: Add event contained non-TkeServiceConfig object: %v", cur)
					return
				}
				service.syncTkeServiceConfig(tkeServiceConfig)
			},
			UpdateFunc: func(old, cur interface{}) {
				tkeServiceConfigOld, ok1 := old.(*v1alpha1.TkeServiceConfig)
				tkeServiceConfigNew, ok2 := cur.(*v1alpha1.TkeServiceConfig)
				if !ok1 || !ok2 {
					glog.Infof("Controller: Add event contained non-TkeServiceConfig old object: %v, cur object: %v", old, cur)
					return
				}
				if !tkeServiceConfigDeepEqual(tkeServiceConfigOld, tkeServiceConfigNew) {
					glog.V(5).Infof("controller: Update event, tkeServiceConfig [%s/%s]\n", tkeServiceConfigNew.Namespace, tkeServiceConfigNew.Name)
					service.syncTkeServiceConfig(tkeServiceConfigNew)
				}
			},
			DeleteFunc: func(cur interface{}) {
				tkeServiceConfig, isTkeServiceConfig := cur.(*v1alpha1.TkeServiceConfig)
				if !isTkeServiceConfig {
					deletedState, ok := cur.(cache.DeletedFinalStateUnknown)
					if !ok {
						glog.Errorf("Received unexpected object: %v", cur)
						return
					}
					tkeServiceConfig, ok = deletedState.Obj.(*v1alpha1.TkeServiceConfig)
					if !ok {
						glog.Errorf("DeletedFinalStateUnknown contained non-TkeServiceConfig object: %v", deletedState.Obj)
						return
					}
				}
				service.syncTkeServiceConfig(tkeServiceConfig)
			},
		},
		serviceSyncPeriod,
	)
}

// ClientGo有缺陷，new对象的列表为空时是nil，old对象的列表为空时是空列表，导致DeepEqual不相等
func tkeServiceConfigDeepEqual(old *v1alpha1.TkeServiceConfig, new *v1alpha1.TkeServiceConfig) bool {
	if (len(old.Spec.LoadBalancer.L4Listeners) != 0) || (len(new.Spec.LoadBalancer.L4Listeners) != 0) {
		if !reflect.DeepEqual(old.Spec.LoadBalancer.L4Listeners, new.Spec.LoadBalancer.L4Listeners) {
			return false
		}
	}
	if (len(old.Spec.LoadBalancer.L7Listeners) != 0) || (len(new.Spec.LoadBalancer.L7Listeners) != 0) {
		if !reflect.DeepEqual(old.Spec.LoadBalancer.L7Listeners, new.Spec.LoadBalancer.L7Listeners) {
			return false
		}
	}
	return true
}

func (s *taskQueueClusterService) syncTkeServiceConfig(tkeServiceConfig *v1alpha1.TkeServiceConfig) {
	services, err := s.ServiceLister().Services(tkeServiceConfig.Namespace).List(labels.Everything())
	if err != nil {
		glog.Infof("controller: can not get cached service for tkeServiceConfig[%s/%s], skip sync tkeServiceConfig.\n", tkeServiceConfig.Namespace, tkeServiceConfig.Name)
		return
	}
	for _, service := range services {
		serviceWrapper := service_wrapper.NewService(service)
		if configName, _ := utils.GetTkeServiceConfig(serviceWrapper); configName != "" && configName == tkeServiceConfig.Name {
			s.enqueueService(service) // kateway tkeserviceconfig 变化直接触发 service resync
		}
	}

	if config.Global.EnableMultiClusterMaster {
		multiClusterServices, err := s.MultiClusterServiceLister().MultiClusterServices(tkeServiceConfig.Namespace).List(labels.Everything())
		if err != nil {
			glog.Infof("controller: can not get cached multiClusterService for tkeServiceConfig[%s/%s], skip sync tkeServiceConfig.\n", tkeServiceConfig.Namespace, tkeServiceConfig.Name)
			return
		}
		for _, multiClusterService := range multiClusterServices {
			serviceWrapper := service_wrapper.NewMultiClusterService(multiClusterService)
			if configName, _ := utils.GetTkeServiceConfig(serviceWrapper); configName != "" && configName == tkeServiceConfig.Name {
				s.enqueueMultiClusterService(multiClusterService)
			}
		}
	}
}

func (s *taskQueueClusterService) syncSecrets(secret *v1.Secret) {
	services, err := s.ServiceLister().Services(secret.Namespace).List(labels.Everything())
	if err != nil {
		glog.Infof("controller: can not get cached service for secrets[%s/%s], skip sync tkeServiceConfig.\n", secret.Namespace, secret.Name)
		return
	}
	for _, service := range services {
		serviceWrapper := service_wrapper.NewService(service)
		if specifyProtocolMap, exist, _ := utils.GetSpecifyProtocol(serviceWrapper); exist {
			for _, specifyProtocol := range *specifyProtocolMap {
				if specifyProtocol.Tls != nil && *specifyProtocol.Tls == secret.Name {
					s.enqueueService(service)
				} else {
					for _, host := range specifyProtocol.Hosts {
						if host.Tls != nil && *host.Tls == secret.Name {
							s.enqueueService(service)
						}
					}
				}
			}
		}
	}
	// kateway(bitliu[15]): 如果 secret 变化，当扩展协议使用到的 secret 变化，会导致 mcs 事件入队
	if config.Global.EnableMultiClusterMaster {
		multiClusterServices, err := s.MultiClusterServiceLister().MultiClusterServices(secret.Namespace).List(labels.Everything())
		if err != nil {
			glog.Infof("controller: can not get cached multiClusterService for secrets[%s/%s], skip sync tkeServiceConfig.\n", secret.Namespace, secret.Name)
			return
		}
		for _, multiClusterService := range multiClusterServices {
			serviceWrapper := service_wrapper.NewMultiClusterService(multiClusterService)
			if specifyProtocolMap, exist, _ := utils.GetSpecifyProtocol(serviceWrapper); exist {
				for _, specifyProtocol := range *specifyProtocolMap {
					if specifyProtocol.Tls != nil && *specifyProtocol.Tls == secret.Name {
						s.enqueueMultiClusterService(multiClusterService)
					} else {
						for _, host := range specifyProtocol.Hosts {
							if host.Tls != nil && *host.Tls == secret.Name {
								s.enqueueMultiClusterService(multiClusterService)
							}
						}
					}
				}
			}
		}
	}
}

// kateway(bitliu[13]): 具体 mcs 增删改事件入队
func (service *taskQueueClusterService) MultiClusterServiceEventHandler() {
	service.MultiClusterServiceInformer().Informer().AddEventHandlerWithResyncPeriod(
		cache.ResourceEventHandlerFuncs{
			AddFunc: func(cur interface{}) {
				multiClusterService, isMultiClusterService := cur.(*v1alpha12.MultiClusterService)
				if !isMultiClusterService {
					glog.Infof("Controller: Add event contained non-MultiClusterService object: %v", cur)
					return
				}
				service.enqueueMultiClusterService(multiClusterService)
			},
			UpdateFunc: func(old, cur interface{}) {
				multiClusterServiceOld, ok1 := old.(*v1alpha12.MultiClusterService)
				multiClusterServiceNew, ok2 := cur.(*v1alpha12.MultiClusterService)
				if !ok1 || !ok2 {
					glog.Infof("Controller: Add event contained non-MultiClusterService old object: %v, cur object: %v", old, cur)
					return
				}
				oldMCS := service_wrapper.NewMultiClusterService(multiClusterServiceOld.DeepCopy())
				newMCS := service_wrapper.NewMultiClusterService(multiClusterServiceNew.DeepCopy())
				oldMCS.GetObjectMeta().SetAnnotations(oldMCS.FilterReadOnly())
				newMCS.GetObjectMeta().SetAnnotations(newMCS.FilterReadOnly())
				oldMCS.GetObjectMeta().SetResourceVersion("")
				newMCS.GetObjectMeta().SetResourceVersion("")
				if !reflect.DeepEqual(oldMCS.Raw(), newMCS.Raw()) {
					klog.V(4).Infof("Controller: Update event, oldMCS %s, newMCS %s", string(oldMCS.JSON()), string(newMCS.JSON()))
					service.enqueueMultiClusterService(multiClusterServiceNew)
				}
			},
			DeleteFunc: func(cur interface{}) {
				multiClusterService, isMultiClusterService := cur.(*v1alpha12.MultiClusterService)
				if !isMultiClusterService {
					deletedState, ok := cur.(cache.DeletedFinalStateUnknown)
					if !ok {
						glog.Errorf("Received unexpected object: %v", cur)
						return
					}
					multiClusterService, ok = deletedState.Obj.(*v1alpha12.MultiClusterService)
					if !ok {
						glog.Errorf("DeletedFinalStateUnknown contained non-MultiClusterService object: %v", deletedState.Obj)
						return
					}
				}
				service.enqueueMultiClusterService(multiClusterService)
			},
		},
		serviceSyncPeriod,
	)
}

func nodeReady(node *v1.Node) bool {
	// if node.Spec.Unschedulable {
	//	return false
	// }
	if node.Labels != nil {
		if value, exist := node.Labels["node.kubernetes.io/exclude-from-external-load-balancers"]; exist && value == "true" {
			return false
		}
	}
	return nodeStatusReady(node)
}

// Node状态变化需要同步的几种情况
// 1. 节点可调度性或健康状态发生变化
// 2. 节点Label发生变化
func nodeChanged(old, cur *v1.Node) bool {
	// if old.Spec.Unschedulable != cur.Spec.Unschedulable {
	//	return true
	// }
	if nodeStatusReady(old) != nodeStatusReady(cur) {
		return true
	}
	if !reflect.DeepEqual(old.Labels, cur.Labels) {
		return true
	}
	if types.NewNode(old).HasWaitFinalizer() != types.NewNode(cur).HasWaitFinalizer() {
		return true
	}
	if old.DeletionTimestamp != nil && cur.DeletionTimestamp != nil {
		return !old.DeletionTimestamp.Equal(cur.DeletionTimestamp)
	}
	if old.DeletionTimestamp != nil || cur.DeletionTimestamp != nil {
		return true
	}

	return false
}

func nodeStatusReady(node *v1.Node) bool {
	for i := range node.Status.Conditions {
		condition := &node.Status.Conditions[i]
		if condition.Type == v1.NodeReady {
			return condition.Status == v1.ConditionTrue
		}
	}
	return false
}

func (service *taskQueueClusterService) enqueueService(svc *v1.Service) {
	service.enqueueServiceWithWeight(svc, DetermineEnqueueweight(svc.GetObjectMeta()))
}

func (service *taskQueueClusterService) enqueueServiceWithWeight(svc *v1.Service, weight int) {
	if !service.silent {
		serviceWrapper := service_wrapper.NewService(svc)
		if utils.IsSkip(serviceWrapper) {
			glog.Infof("enqueueServiceWithWeight: service %s is marked to skip", utils.ServiceName(serviceWrapper))
			return
		}

		// track.Info(utils.ServiceName(serviceWrapper), "enqueueServiceWithWeight", slog.String("service", svc.Name), slog.Int("weight", weight))
		cluster_service.QueueServiceInstance.ServiceQueue().Enqueue(taskqueue.Item{
			Data:   utils.ServiceName(serviceWrapper),
			Weight: weight,
		})
	}
}

func (service *taskQueueClusterService) enqueueMultiClusterService(multiClusterService *v1alpha12.MultiClusterService) {
	service.enqueueMultiClusterServiceWithWeight(multiClusterService, 1)
}

// kateway(bitliu[14]): 创建 mcs 的 key 并入 MCS 的队列
func (service *taskQueueClusterService) enqueueMultiClusterServiceWithWeight(multiClusterService *v1alpha12.MultiClusterService, weight int) {
	if !service.silent {
		serviceWrapper := service_wrapper.NewMultiClusterService(multiClusterService)
		cluster_service.QueueServiceInstance.MultiClusterServiceQueue().Enqueue(taskqueue.Item{
			Data:   utils.ServiceName(serviceWrapper),
			Weight: weight,
		})
	}
}

func (service *taskQueueClusterService) enqueueEndpoints(endpoints *v1.Endpoints) {
	service.enqueueEndpointsWithWeight(types.JoinKeyStrings("/", endpoints.Namespace, endpoints.Name), DetermineEnqueueweight(endpoints.GetObjectMeta()))
}

// 将 endpoints 的 key 并入 endpoints 的队列
func (service *taskQueueClusterService) enqueueEndpointsWithWeight(key string, weight int) {
	if !service.silent {
		cluster_service.QueueServiceInstance.EndpointsQueue().Enqueue(taskqueue.Item{
			Data:   key,
			Weight: weight,
		})
	}
}

func (service *taskQueueClusterService) enqueuePod(pod *v1.Pod) {
	service.enqueuePodWithWeight(pod, 1)
}

func (service *taskQueueClusterService) enqueuePodWithWeight(pod *v1.Pod, weight int) {
	if !service.silent {
		cluster_service.QueueServiceInstance.PodQueue().Enqueue(taskqueue.Item{
			Data:   utils.PodName(pod),
			Weight: weight,
		})
	}
}

func (service *taskQueueClusterService) enqueueDeletedPod(pod *v1.Pod) {
	service.enqueueDeletedPodWithWeight(pod, 1)
}

func (service *taskQueueClusterService) enqueueDeletedPodWithWeight(pod *v1.Pod, weight int) {
	if !service.silent {
		cluster_service.QueueServiceInstance.PodDeleteQueue().Enqueue(taskqueue.Item{
			Data: taskqueue.Element{
				Key: lo.Must1(cache.MetaNamespaceKeyFunc(pod)),
				Event: taskqueue.Event{
					Type: taskqueue.DeleteEvent,
					Core: pod,
				},
			},
			Weight: weight,
		})
	}
}
