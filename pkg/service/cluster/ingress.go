package cluster

import (
	"github.com/samber/lo"
	"k8s.io/apimachinery/pkg/labels"

	"cloud.tencent.com/lb-controller/pkg/utils"

	"git.woa.com/kateway/pkg/domain/ingress/ingress_wrapper"
	"git.woa.com/kateway/pkg/domain/services/cluster"
	"git.woa.com/kateway/pkg/domain/types"
)

func ListQCloudIngress(namespace string, selector labels.Selector) ([]types.Ingress, error) {
	var res []types.Ingress
	supported, err := cluster.Instance.IsExtensionsAPIGroupSupported()
	if err != nil {
		return nil, err
	}

	if supported {
		ingresses, err := cluster.Instance.ExtensionIngressLister().Ingresses(namespace).List(selector)
		if err != nil {
			return nil, err
		}
		res = ingress_wrapper.NewIngressWrapperExtensionsList(ingresses)
	} else {
		ingresses, err := cluster.Instance.NetworkingIngressLister().Ingresses(namespace).List(selector)
		if err != nil {
			return nil, err
		}
		res = ingress_wrapper.NewIngressWrapperNetworkingList(ingresses)
	}
	return lo.Filter(res, func(ing types.Ingress, _ int) bool {
		return utils.IsQCLOUDIngress(ing)
	}), nil
}
