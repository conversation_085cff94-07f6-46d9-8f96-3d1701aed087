package processing

import (
	"k8s.io/apimachinery/pkg/util/sets"
	"sync"
)

var (
	ProcessingServiceInstanceLock                   = new(sync.Mutex)
	ProcessingServiceInstance     ProcessingService = nil
)

type ProcessingService interface {
	ProcessingStart(loadbalancerId string) bool
	ProcessingEnd(loadbalancerId string)
}

// TODO misakazhou 检查用户输入非法RegionID的可能
type defaultProcessingService struct {
	ProcessingMap     sets.String // Service To Region
	ProcessingMapLock sync.Mutex
}

func (this *defaultProcessingService) ProcessingStart(loadbalancerId string) bool {
	this.ProcessingMapLock.Lock()
	defer this.ProcessingMapLock.Unlock()
	
	if this.ProcessingMap.Has(loadbalancerId) {
		return false
	}
	this.ProcessingMap.Insert(loadbalancerId)
	return true
}

func (this *defaultProcessingService) ProcessingEnd(loadbalancerId string) {
	this.ProcessingMapLock.Lock()
	defer this.ProcessingMapLock.Unlock()

	this.ProcessingMap.Delete(loadbalancerId)
}

func InitProcessingService() {
	if ProcessingServiceInstance == nil {
		ProcessingServiceInstanceLock.Lock()
		defer ProcessingServiceInstanceLock.Unlock()

		if ProcessingServiceInstance == nil {
			processingService := &defaultProcessingService{
				ProcessingMap:     sets.NewString(),
				ProcessingMapLock: sync.Mutex{},
			}

			ProcessingServiceInstance = processingService
		}
	}
	return
}
