package track

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"cloud.tencent.com/lb-controller/pkg/service/track/log"
)

type Config struct {
	Dir string
}

type service struct {
	*Config
	sync.RWMutex
	trackServices map[string]log.LogWrapper
}

var (
	_service *service
)

func Init(config *Config) {
	_service = &service{
		Config:        config,
		trackServices: make(map[string]log.LogWrapper),
	}
}

func Add(service string) {
	_service.Add(service)
}

func Remove(service string) {
	_service.Remove(service)
}

func Info(service string, msg string, args ...any) {
	logger := _service.logger(service)
	if logger == nil {
		return
	}

	logger.Info(msg, args...)
}

func (s *service) logFile(service string) string {
	filename := fmt.Sprintf("%s/%s.log", s.Dir, service)

	dir := filepath.Dir(filename)

	err := os.MkdirAll(dir, 0755)
	if err != nil {
		panic(err)
	}

	return filename
}

func (s *service) Add(service string) {
	s.Lock()
	defer s.Unlock()

	logger, err := log.NewLogger(s.logFile(service))
	if err != nil {
		panic(err.Error())
	}
	s.trackServices[service] = logger
}

func (s *service) Remove(service string) {
	s.Lock()
	defer s.Unlock()

	if val, ok := s.trackServices[service]; ok {
		val.Close()
		delete(s.trackServices, service)
	}
}

func (s *service) logger(service string) log.LogWrapper {
	s.RLock()
	defer s.RUnlock()

	logger, ok := s.trackServices[service]
	if !ok {
		return nil
	}

	return logger
}
