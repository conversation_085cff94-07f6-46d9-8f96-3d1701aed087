package log

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"
)

type LogWrapperWithFile struct {
	Logger *slog.Logger
	File   *os.File
	Path   string
}

func NewLogger(path string) (LogWrapper, error) {
	_, err := os.Stat(filepath.Dir(path))
	if err != nil {
		return nil, err
	}
	file, err := os.OpenFile(path, os.O_CREATE|os.O_RDWR|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return nil, err
	}
	return &LogWrapperWithFile{
		Logger: slog.New(slog.NewJSONHandler(file, nil)),
		File:   file,
		Path:   path,
	}, nil
}

func (log *LogWrapperWithFile) Controller() string {
	return "service-Controller"
}

func (log *LogWrapperWithFile) HandlerType() string {
	return "json"
}

func (log *LogWrapperWithFile) LogPath() string {
	return log.Path
}

func (log *LogWrapperWithFile) SetLogFile(path string) (*os.File, error) {
	log.Close()
	file, err := os.OpenFile(path, os.O_CREATE|os.O_RDWR|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return nil, err
	}
	log.File = file
	log.Path = path
	return file, nil
}

func (log *LogWrapperWithFile) Close() {
	if log.File == nil {
		return
	}
	log.File.Close()
	os.Remove(log.Path)
	log.Path = ""
}

func (log *LogWrapperWithFile) Debug(msg string, args ...any) {
	extern := []any{
		slog.String("Controller", log.Controller()),
		slog.String("Source", LogFileAndLine(2)),
		slog.Time("Time", time.Now()),
	}
	log.Logger.Debug(msg, append(extern, args...)...)
}

func (log *LogWrapperWithFile) DebugContext(ctx context.Context, msg string, args ...any) {
	extern := []any{
		slog.String("Controller", log.Controller()),
		slog.String("Source", LogFileAndLine(2)),
		slog.Time("Time", time.Now()),
	}
	log.Logger.DebugContext(ctx, msg, append(extern, args...)...)
}

func (log *LogWrapperWithFile) Info(msg string, args ...any) {
	extern := []any{
		slog.String("Controller", log.Controller()),
		slog.String("Source", LogFileAndLine(2)),
		slog.Time("Time", time.Now()),
	}
	log.Logger.Info(msg, append(extern, args...)...)
}

func (log *LogWrapperWithFile) InfoContext(ctx context.Context, msg string, args ...any) {
	extern := []any{
		slog.String("Controller", log.Controller()),
		slog.String("Source", LogFileAndLine(2)),
		slog.Time("Time", time.Now()),
	}
	log.Logger.InfoContext(ctx, msg, append(extern, args...)...)
}

func (log *LogWrapperWithFile) Warn(msg string, args ...any) {
	extern := []any{
		slog.String("Controller", log.Controller()),
		slog.String("Source", LogFileAndLine(2)),
		slog.Time("Time", time.Now()),
	}
	log.Logger.Warn(msg, append(extern, args...)...)
}

func (log *LogWrapperWithFile) WarnContext(ctx context.Context, msg string, args ...any) {
	extern := []any{
		slog.String("Controller", log.Controller()),
		slog.String("Source", LogFileAndLine(2)),
		slog.Time("Time", time.Now()),
	}
	log.Logger.WarnContext(ctx, msg, append(extern, args...)...)
}

func (log *LogWrapperWithFile) Error(msg string, args ...any) {
	extern := []any{
		slog.String("Controller", log.Controller()),
		slog.String("Source", LogFileAndLine(2)),
		slog.Time("Time", time.Now()),
	}
	log.Logger.Error(msg, append(extern, args...)...)
}

func (log *LogWrapperWithFile) ErrorContext(ctx context.Context, msg string, args ...any) {
	extern := []any{
		slog.String("Controller", log.Controller()),
		slog.String("Source", LogFileAndLine(2)),
		slog.Time("Time", time.Now()),
	}
	log.Logger.ErrorContext(ctx, msg, append(extern, args...)...)
}

func LogFileAndLine(i int) string {
	pc, file, line, ok := runtime.Caller(i)
	if !ok {
		panic("runtime.Caller error")
	}
	return fmt.Sprintf("%v:%v:%v", runtime.FuncForPC(pc).Name(), file, line)
}

type LogWrapper interface {
	Debug(msg string, args ...any)
	DebugContext(ctx context.Context, msg string, args ...any)
	Info(msg string, args ...any)
	InfoContext(ctx context.Context, msg string, args ...any)
	Warn(msg string, args ...any)
	WarnContext(ctx context.Context, msg string, args ...any)
	Error(msg string, args ...any)
	ErrorContext(ctx context.Context, msg string, args ...any)
	Close()
}

var DefaultLoggerInstance LogWrapper
var on sync.Once
var specTestFeature bool

func init() {
	InitSpectest()
}

func InitSpectest() {
	on.Do(func() {
		var err error
		if specTestFeature, err = strconv.ParseBool(os.Getenv("LOG_TEST_FEATURE")); err != nil {
			return
		}
		if !specTestFeature {
			// DefaultLoggerInstance = slog.New(slog.NewJSONHandler(os.Stdout, nil))
			return
		}
		filePath := os.Getenv("LOG_PATH")
		if filePath == "" {
			filePath = "./logfile"
		}
		DefaultLoggerInstance, err = NewLogger(filePath)
		if err != nil {
			panic(err.Error())
		}
	})
}
